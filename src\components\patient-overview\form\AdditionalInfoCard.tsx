
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { Baby, AlertTriangle, ChevronDown, ChevronUp } from "lucide-react";
import { useState } from "react";

export interface RiskFactor {
  id: string;
  label: string;
  description?: string;
}

interface AdditionalInfoCardProps {
  exclusiveBreastfeeding: boolean;
  riskFactors: string[];
  onBreastfeedingChange: (checked: boolean) => void;
  onRiskFactorsChange: (riskFactors: string[]) => void;
}

export function AdditionalInfoCard({
  exclusiveBreastfeeding,
  riskFactors,
  onBreastfeedingChange,
  onRiskFactorsChange,
}: AdditionalInfoCardProps) {
  const [showRiskFactors, setShowRiskFactors] = useState(false);

  const availableRiskFactors: RiskFactor[] = [
    { id: "prematurity", label: "Prematuridade", description: "Nascimento antes de 37 semanas" },
    { id: "low_birth_weight", label: "Baixo peso ao nascer", description: "Peso ao nascer < 2500g" },
    { id: "poor_iron_diet", label: "Alimentação pobre em ferro", description: "Introdução alimentar inadequada" },
    { id: "exclusive_breastfeeding_gt_6m_without_supplement", label: "AME prolongado sem suplementação", description: "Além de 6 meses sem ferro" },
    { id: "multiple_pregnancy", label: "Gestação múltipla", description: "Gêmeos, trigêmeos, etc." },
    { id: "maternal_anemia", label: "Anemia materna", description: "Durante gestação ou lactação" },
    { id: "frequent_infections", label: "Infecções frequentes", description: "Processos infecciosos recorrentes" }
  ];



  const handleRiskFactorChange = (riskFactorId: string, checked: boolean) => {
    if (checked) {
      onRiskFactorsChange([...riskFactors, riskFactorId]);
    } else {
      onRiskFactorsChange(riskFactors.filter(id => id !== riskFactorId));
    }
  };



  return (
    <Card className="p-4 bg-white/80 dark:bg-slate-800/90 border border-gray-200 dark:border-slate-700">
      <div className="space-y-4">
        {/* Aleitamento Materno */}
        <div className="flex items-center justify-between">
          <Label htmlFor="exclusiveBreastfeeding" className="flex items-center gap-2 cursor-pointer">
            <Baby className="h-4 w-4 text-purple-500 dark:text-purple-400" />
            <span className="text-gray-800 dark:text-gray-200">Aleitamento Materno Exclusivo</span>
          </Label>
          <Switch
            id="exclusiveBreastfeeding"
            checked={exclusiveBreastfeeding}
            onCheckedChange={onBreastfeedingChange}
            className="data-[state=checked]:bg-purple-500 data-[state=checked]:border-purple-500"
          />
        </div>

        {/* Fatores de Risco */}
        <div className="space-y-3">
          <div
            className="flex items-center justify-between cursor-pointer"
            onClick={() => setShowRiskFactors(!showRiskFactors)}
          >
            <Label className="flex items-center gap-2 cursor-pointer">
              <AlertTriangle className="h-4 w-4 text-yellow-500 dark:text-yellow-400" />
              <span className="text-gray-800 dark:text-gray-200">
                Fatores de Risco {riskFactors.length > 0 && `(${riskFactors.length} selecionados)`}
              </span>
            </Label>
            {showRiskFactors ?
              <ChevronUp className="h-4 w-4 text-gray-500" /> :
              <ChevronDown className="h-4 w-4 text-gray-500" />
            }
          </div>

          {showRiskFactors && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 p-3 bg-gray-50 dark:bg-slate-700/50 rounded-lg">
              {availableRiskFactors.map((factor) => (
                <div key={factor.id} className="flex items-start space-x-2">
                  <Checkbox
                    id={factor.id}
                    checked={riskFactors.includes(factor.id)}
                    onCheckedChange={(checked) => handleRiskFactorChange(factor.id, checked as boolean)}
                    className="mt-1"
                  />
                  <div className="grid gap-1.5 leading-none">
                    <Label
                      htmlFor={factor.id}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                    >
                      {factor.label}
                    </Label>
                    {factor.description && (
                      <p className="text-xs text-muted-foreground">
                        {factor.description}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>


      </div>
    </Card>
  );
}
