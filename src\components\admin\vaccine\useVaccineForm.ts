import { useState, useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { RelatedVaccine } from "./types";

export const useVaccineForm = (vaccine: any, onOpenChange: (open: boolean) => void) => {
  const [name, setName] = useState(vaccine?.name || "");
  const [description, setDescription] = useState(vaccine?.description || "");
  const [selectedVaccines, setSelectedVaccines] = useState<Map<string, RelatedVaccine>>(new Map());
  const { toast } = useToast();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (vaccine) {
      setName(vaccine.name || "");
      setDescription(vaccine.description || "");
    } else {
      setName("");
      setDescription("");
    }
  }, [vaccine]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      let vaccineId = vaccine?.id;

      if (vaccine) {
        const { error: updateError } = await supabase
          .from("pedbook_vaccines")
          .update({ name, description })
          .eq("id", vaccine.id);

        if (updateError) throw updateError;
      } else {
        const { data: newVaccine, error: insertError } = await supabase
          .from("pedbook_vaccines")
          .insert([{ name, description }])
          .select()
          .single();

        if (insertError) throw insertError;
        vaccineId = newVaccine.id;
      }

      if (vaccineId) {
        const { error: deleteError } = await supabase
          .from("pedbook_vaccine_relationships")
          .delete()
          .eq("parent_vaccine_id", vaccineId);

        if (deleteError) throw deleteError;

        if (selectedVaccines.size > 0) {
          const relationships = Array.from(selectedVaccines.values()).map(vaccine => ({
            parent_vaccine_id: vaccineId,
            child_vaccine_id: vaccine.id,
            dose_number: vaccine.doseNumber || null,
            dose_type: vaccine.doseType || 'dose'
          }));

          const { error: relationshipError } = await supabase
            .from("pedbook_vaccine_relationships")
            .insert(relationships);

          if (relationshipError) throw relationshipError;
        }
      }

      toast({
        title: vaccine ? "Vacina atualizada com sucesso!" : "Vacina criada com sucesso!",
        description: `A vacina ${name} foi ${vaccine ? 'atualizada' : 'adicionada'}.`,
      });

      queryClient.invalidateQueries({ queryKey: ["vaccines"] });
      onOpenChange(false);
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao salvar vacina",
        description: error.message,
      });
    }
  };

  const handleVaccineSelection = (vaccineId: string, checked: boolean) => {
    const newSelection = new Map(selectedVaccines);
    if (checked) {
      newSelection.set(vaccineId, { id: vaccineId, doseType: 'dose' });
    } else {
      newSelection.delete(vaccineId);
    }
    setSelectedVaccines(newSelection);
  };

  const handleDoseNumberChange = (vaccineId: string, doseNumber: number) => {
    const newSelection = new Map(selectedVaccines);
    const vaccine = newSelection.get(vaccineId);
    if (vaccine) {
      newSelection.set(vaccineId, { ...vaccine, doseNumber });
      setSelectedVaccines(newSelection);
    }
  };

  const handleDoseTypeChange = (vaccineId: string, doseType: string) => {
    const newSelection = new Map(selectedVaccines);
    const vaccine = newSelection.get(vaccineId);
    if (vaccine) {
      newSelection.set(vaccineId, { ...vaccine, doseType });
      setSelectedVaccines(newSelection);
    }
  };

  return {
    name,
    setName,
    description,
    setDescription,
    selectedVaccines,
    setSelectedVaccines,
    handleSubmit,
    handleVaccineSelection,
    handleDoseNumberChange,
    handleDoseTypeChange
  };
};