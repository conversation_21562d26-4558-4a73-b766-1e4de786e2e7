import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Plus, Loader2 } from "lucide-react";
import { GrowthCurveDialog } from "@/components/admin/growth-curve/GrowthCurveDialog";
import { GrowthCurveList } from "@/components/admin/growth-curve/GrowthCurveList";
import { supabase } from "@/integrations/supabase/client";

export default function GrowthCurves() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const { data: growthCurves, isLoading } = useQuery({
    queryKey: ["growth-curves"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_growth_curves")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data;
    },
  });

  return (
    <div className="container mx-auto px-4 py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Curvas de Crescimento</h1>
        <Button onClick={() => setIsDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Nova Curva
        </Button>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : (
        <GrowthCurveList curves={growthCurves || []} />
      )}

      <GrowthCurveDialog open={isDialogOpen} onOpenChange={setIsDialogOpen} />
    </div>
  );
}