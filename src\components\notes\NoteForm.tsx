import { useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useNotes } from "@/hooks/useNotes";
import { useFolders } from "@/hooks/useFolders";
import { useTags } from "@/hooks/useTags";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { NoteEditor } from "./NoteEditor";
import { TagSelector } from "./TagSelector";
import { toast } from "sonner";
import { Plus } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface NoteFormProps {
  noteId?: string;
  initialTitle?: string;
  initialContent?: string;
  initialFolderId?: string;
  initialTags?: string[];
  isOpen: boolean;
  onClose: () => void;
}

export const NoteForm = ({
  noteId,
  initialTitle = '',
  initialContent = '',
  initialFolderId = '',
  initialTags = [],
  isOpen,
  onClose,
}: NoteFormProps) => {
  const [title, setTitle] = useState(initialTitle);
  const [content, setContent] = useState(initialContent);
  const [folderId, setFolderId] = useState(initialFolderId);
  const [selectedTags, setSelectedTags] = useState<string[]>(initialTags);
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [newFolderName, setNewFolderName] = useState("");

  const { createNote, updateNote, addTag } = useNotes();
  const { folders, createFolder } = useFolders();
  const { tags } = useTags();
  const queryClient = useQueryClient();

  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) return;

    try {
      const folder = await createFolder.mutateAsync(newFolderName);
      setFolderId(folder.id);
      setNewFolderName("");
      setIsCreatingFolder(false);
      toast.success('Pasta criada com sucesso!');
    } catch (error) {
      console.error('Error creating folder:', error);
      toast.error('Erro ao criar pasta');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (noteId) {
        // Update existing note
        await updateNote.mutateAsync({
          id: noteId,
          title,
          content,
          folder_id: folderId === 'no-folder' ? null : folderId || null,
        });

        // Handle tags for existing note
        if (selectedTags.length > 0) {
          for (const tagName of selectedTags) {
            await addTag.mutateAsync({ noteId, tagName });
          }
        }

        toast.success('Nota atualizada com sucesso!');
      } else {
        // Create new note
        const note = await createNote.mutateAsync({
          title,
          content,
          folder_id: folderId === 'no-folder' ? null : folderId || null,
        });

        // Handle tags for new note
        if (note && selectedTags.length > 0) {
          for (const tagName of selectedTags) {
            await addTag.mutateAsync({ noteId: note.id, tagName });
          }
        }
        
        toast.success('Nota criada com sucesso!');
      }

      // Refresh notes list
      await queryClient.invalidateQueries({ queryKey: ['notes'] });
      
      onClose();
    } catch (error) {
      console.error('Error saving note:', error);
      toast.error('Erro ao salvar nota');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-hidden flex flex-col">
        <form onSubmit={handleSubmit} className="space-y-6 flex-1 overflow-y-auto px-1">
          <div className="space-y-4">
            <div>
              <Label htmlFor="title">Título</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => {
                  const newTitle = e.target.value;
                  if (newTitle.length <= 30) {
                    setTitle(newTitle);
                  }
                }}
                placeholder="Digite o título da nota..."
                maxLength={30}
                className="w-full break-words"
              />
              <div className="text-xs text-gray-500 mt-1">
                {title.length}/30 caracteres
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <Label htmlFor="folder">Pasta</Label>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => setIsCreatingFolder(!isCreatingFolder)}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {isCreatingFolder ? (
                <div className="space-y-2">
                  <Input
                    type="text"
                    placeholder="Nome da nova pasta"
                    value={newFolderName}
                    onChange={(e) => setNewFolderName(e.target.value)}
                    className="h-8 text-sm"
                  />
                  <div className="flex gap-2">
                    <Button type="button" size="sm" onClick={handleCreateFolder}>
                      Criar
                    </Button>
                    <Button 
                      type="button" 
                      variant="ghost" 
                      size="sm"
                      onClick={() => {
                        setIsCreatingFolder(false);
                        setNewFolderName("");
                      }}
                    >
                      Cancelar
                    </Button>
                  </div>
                </div>
              ) : (
                <Select value={folderId} onValueChange={setFolderId}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Selecione uma pasta" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="no-folder">Nenhuma pasta</SelectItem>
                    {folders?.map((folder) => (
                      <SelectItem key={folder.id} value={folder.id}>
                        {folder.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>

            <div>
              <Label>Tags</Label>
              <TagSelector
                noteId={noteId || ""}
                selectedTags={selectedTags}
                onTagsChange={setSelectedTags}
                availableTags={tags?.map(tag => tag.name) || []}
              />
            </div>

            <div>
              <Label>Conteúdo</Label>
              <div className="min-h-[300px] max-h-[50vh] overflow-y-auto">
                <NoteEditor content={content} onChange={setContent} />
              </div>
            </div>
          </div>

          <Button type="submit" className="w-full">
            {noteId ? 'Atualizar Nota' : 'Criar Nota'}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
};