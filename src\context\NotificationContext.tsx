import React, { createContext, useContext, useState, ReactNode } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON><PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { CheckCircle, AlertCircle, Info } from "lucide-react";

export type NotificationType = "success" | "error" | "info";

interface NotificationContextType {
  showNotification: (props: NotificationProps) => void;
}

interface NotificationProps {
  title: string;
  description: string;
  type?: NotificationType;
  buttonText?: string;
  onButtonClick?: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const NotificationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [open, setOpen] = useState(false);
  const [notification, setNotification] = useState<NotificationProps>({
    title: "",
    description: "",
    type: "info",
    buttonText: "Fechar",
  });

  const showNotification = (props: NotificationProps) => {
    setNotification(props);
    setOpen(true);
  };

  const handleButtonClick = () => {
    if (notification.onButtonClick) {
      notification.onButtonClick();
    }
    setOpen(false);
  };

  return (
    <NotificationContext.Provider value={{ showNotification }}>
      {children}
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent
          className="sm:max-w-[425px] rounded-xl overflow-hidden p-0"
          aria-describedby="notification-description"
        >
          <div className={`w-full py-3 ${
            notification.type === "success" ? "bg-green-50 dark:bg-green-900/20" :
            notification.type === "error" ? "bg-red-50 dark:bg-red-900/20" : "bg-blue-50 dark:bg-blue-900/20"
          }`}>
            <DialogHeader className="flex flex-col items-center text-center gap-2 px-6">
              <div className={`w-16 h-16 rounded-full flex items-center justify-center ${
                notification.type === "success" ? "bg-green-100 dark:bg-green-800/30" :
                notification.type === "error" ? "bg-red-100 dark:bg-red-800/30" : "bg-blue-100 dark:bg-blue-800/30"
              }`}>
                {notification.type === "success" && <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />}
                {notification.type === "error" && <AlertCircle className="h-8 w-8 text-red-600 dark:text-red-400" />}
                {notification.type === "info" && <Info className="h-8 w-8 text-blue-600 dark:text-blue-400" />}
              </div>
              <DialogTitle className="text-xl font-bold mt-2">
                {notification.title}
              </DialogTitle>
            </DialogHeader>
          </div>

          <div className="p-6">
            <DialogDescription
              id="notification-description"
              className="text-center text-base mb-6"
            >
              {notification.description}
            </DialogDescription>

            <DialogFooter>
              <Button
                className="w-full py-6 text-base font-medium rounded-lg"
                onClick={handleButtonClick}
                variant={notification.type === "success" ? "default" : notification.type === "error" ? "destructive" : "outline"}
              >
                {notification.buttonText || "Fechar"}
              </Button>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>
    </NotificationContext.Provider>
  );
};

export const useNotification = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error("useNotification must be used within a NotificationProvider");
  }
  return context;
};
