import { useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useStudySession } from '@/hooks/useStudySession';
import { useFilterQuery } from './useFilterQuery';
import { ensureUserId } from '@/utils/ensureUserId';
import { useUserData } from '@/hooks/useUserData';
import { supabase } from '@/integrations/supabase/client';
import type { SelectedFilters } from '@/types/question';
import type { StudySessionRow } from '@/types/study-session';
import { useDomain } from '@/hooks/useDomain';

export const useSessionCreation = () => {
  const { toast } = useToast();
  const { createSession } = useStudySession();
  const { fetchPediatricQuestions } = useFilterQuery();
  const { user } = useUserData();
  const { domain } = useDomain();

  const handleCreateSession = useCallback(async (
    filters: SelectedFilters,
    title: string,
    preFilteredQuestions?: any[]
  ): Promise<StudySessionRow | null> => {
    try {
      // If we have pre-filtered questions, use them directly
      if (preFilteredQuestions && preFilteredQuestions.length > 0) {
        const questionIds = preFilteredQuestions.map(q => q.id);
        const userId = await ensureUserId();

        const session = await createSession(userId, questionIds, title);
        if (!session?.id) {
          toast({
            title: "Erro ao criar sessão",
            description: "Não foi possível criar a sessão de estudos",
            variant: "destructive"
          });
          return null;
        }
        return session;
      }

      // Otherwise, fetch questions based on filters using pediatric-specific function
      let questions, error;

      const { data: rpcData, error: rpcError } = await fetchPediatricQuestions(
        filters,
        1, // page_number
        1000, // items_per_page - Get all questions for session
        filters.excludeAnswered && user?.id ? user.id : undefined
      );

      if (rpcError) {
        error = rpcError;
      } else {
        questions = rpcData?.questions || [];
      }

      if (error) {
        throw error;
      }

      if (!questions?.length) {
        toast({
          title: "Nenhuma questão encontrada",
          description: "Tente outros filtros para encontrar questões de pediatria",
          variant: "destructive"
        });
        return null;
      }

      const userId = await ensureUserId();
      const session = await createSession(userId, questions.map(q => q.id), title);

      if (!session?.id) {
        toast({
          title: "Erro ao criar sessão",
          description: "Não foi possível criar a sessão de estudos",
          variant: "destructive"
        });
        return null;
      }

      return session;

    } catch (error: any) {
      console.error('❌ [useSessionCreation] Error creating session:', error);
      toast({
        title: "Erro ao criar sessão",
        description: error.message,
        variant: "destructive"
      });
      return null;
    }
  }, [toast, createSession, user?.id, domain]);

  return { handleCreateSession };
};
