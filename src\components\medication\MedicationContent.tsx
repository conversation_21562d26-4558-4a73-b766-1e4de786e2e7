import { AlertTriangle } from "lucide-react";
import { PatientInfoSection } from "../patient/PatientInfoSection";
import { MedicationUseCases } from "./MedicationUseCases";

interface MedicationContentProps {
  medication: any;
  weight: number;
  onWeightChange: (weight: number) => void;
  onWeightCommit: (weight: number) => void;
  age: number;
  onAgeChange: (age: number) => void;
  onAgeCommit: (age: number) => void;
}

export const MedicationContent = ({
  medication,
  weight,
  onWeightChange,
  onWeightCommit,
  age,
  onAgeChange,
  onAgeCommit,
}: MedicationContentProps) => {
  return (
    <div className="space-y-6">
      <PatientInfoSection
        weight={weight}
        onWeightChange={onWeightChange}
        onWeightCommit={onWeightCommit}
        age={age}
        onAgeChange={onAgeChange}
        onAgeCommit={onAgeCommit}
      />

      <MedicationUseCases
        useCases={medication?.pedbook_medication_use_cases || []}
        weight={weight}
        age={age}
        medicationId={medication?.id}
      />

      {medication?.contraindications && (
        <div className="border-l-4 border-destructive bg-destructive/5 p-4 rounded-r-lg space-y-2">
          <h4 className="font-semibold flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            Contra-indicações
          </h4>
          <div className="ml-7 space-y-2 text-sm whitespace-pre-line">
            {medication.contraindications}
          </div>
        </div>
      )}
    </div>
  );
};