import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Slider } from "@/components/ui/slider";
import { SymptomsSelector } from "../SymptomsSelector";
import { useState } from "react";

interface SymptomsSectionProps {
  formData: any;
  setFormData: (data: any) => void;
}

export function SymptomsSection({ formData, setFormData }: SymptomsSectionProps) {
  const [showManualSymptoms, setShowManualSymptoms] = useState(false);

  return (
    <>
      <div className="space-y-4">
        <Label className="text-xl font-semibold text-gray-800 bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent">
          Sinais e Sintomas
        </Label>
        
        <div className="space-y-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => setShowManualSymptoms(!showManualSymptoms)}
            className="w-full py-6 text-base font-medium bg-accent-blue/50 hover:bg-accent-blue/70 border-primary/20 hover:border-primary/40 text-primary/80 hover:text-primary transition-all duration-300 whitespace-normal h-auto"
          >
            Clique aqui para escrever os sinais/sintomas ou caso clínico manualmente
          </Button>
          
          {showManualSymptoms && (
            <Textarea
              placeholder="Descreva aqui sinais/sintomas relevantes e/ou caso clínico completo."
              value={formData.manualSymptoms || ""}
              onChange={(e) => setFormData({ ...formData, manualSymptoms: e.target.value })}
              className="min-h-[100px] text-base bg-white/50 backdrop-blur-sm resize-none animate-fade-in"
            />
          )}
          
          <SymptomsSelector
            selectedSymptoms={formData.symptoms}
            onSymptomsChange={(symptoms) => setFormData({ ...formData, symptoms })}
          />
        </div>
      </div>

      <div className="space-y-4">
        <Label className="text-xl font-semibold text-gray-800 bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent">
          Intensidade dos sintomas
        </Label>
        <Slider
          value={[formData.symptomsIntensity]}
          onValueChange={(value) => setFormData({ ...formData, symptomsIntensity: value[0] })}
          max={10}
          min={1}
          step={1}
          className="py-4"
        />
        <div className="text-center text-3xl font-semibold bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent">
          {formData.symptomsIntensity}/10
        </div>
      </div>
    </>
  );
}