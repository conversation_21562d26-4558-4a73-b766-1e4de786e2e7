import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { motion } from "framer-motion";
import { ArrowLeft, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { calculateReadingTime } from "@/utils/readingTime";

interface BlogPostHeaderProps {
  post: {
    title: string;
    featured_image?: string;
    published_at?: string;
    author?: { full_name: string };
    pedbook_blog_categories?: { name: string };
    content: string;
  };
}

export function BlogPostHeader({ post }: BlogPostHeaderProps) {
  const navigate = useNavigate();
  const readingTime = calculateReadingTime(post.content);

  return (
    <>
      <Button
        variant="ghost"
        className="mb-6 hover:bg-white"
        onClick={() => navigate(-1)}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Voltar
      </Button>

      {post.featured_image && (
        <div className="relative h-[400px] mb-8 rounded-xl overflow-hidden shadow-xl">
          <img
            src={post.featured_image}
            alt={post.title}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
        </div>
      )}
      
      <motion.h1 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="text-4xl md:text-5xl font-bold mb-6 text-gray-900"
      >
        {post.title}
      </motion.h1>
      
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
        className="flex flex-wrap items-center gap-4 text-gray-600 mb-8 text-sm md:text-base"
      >
        <span className="flex items-center">
          <span className="font-medium">Por {post.author?.full_name}</span>
        </span>
        <span>•</span>
        <span>
          {post.published_at
            ? format(new Date(post.published_at), "dd 'de' MMMM 'de' yyyy", {
                locale: ptBR,
              })
            : "Não publicado"}
        </span>
        <span>•</span>
        <span className="flex items-center gap-1">
          <Clock className="h-4 w-4" />
          {readingTime} min de leitura
        </span>
        {post.pedbook_blog_categories && (
          <>
            <span>•</span>
            <span className="bg-primary/10 px-3 py-1 rounded-full text-primary">
              {post.pedbook_blog_categories.name}
            </span>
          </>
        )}
      </motion.div>
    </>
  );
}