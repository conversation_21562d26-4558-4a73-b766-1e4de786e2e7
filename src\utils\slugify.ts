export function slugify(text: string): string {
  const accentsMap: { [key: string]: string } = {
    'á': 'a', 'à': 'a', 'ã': 'a', 'â': 'a', 'ä': 'a',
    'é': 'e', 'è': 'e', 'ê': 'e', 'ë': 'e',
    'í': 'i', 'ì': 'i', 'î': 'i', 'ï': 'i',
    'ó': 'o', 'ò': 'o', 'õ': 'o', 'ô': 'o', 'ö': 'o',
    'ú': 'u', 'ù': 'u', 'û': 'u', 'ü': 'u',
    'ý': 'y', 'ÿ': 'y',
    'ñ': 'n',
    'ç': 'c',
  };

  return text
    .toString()
    .toLowerCase()
    // Replace accented characters with their non-accented equivalents
    .replace(/[áàãâäéèêëíìîïóòõôöúùûüýÿñç]/g, (match) => accentsMap[match] || match)
    // Remove remaining diacritics
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    // Remove trademark and registered symbols
    .replace(/[®™]/g, '')
    // Replace special characters and spaces with hyphens
    .replace(/[^a-z0-9]+/g, '-')
    // Remove hyphens from start and end
    .replace(/^-+|-+$/g, '')
    // Replace multiple consecutive hyphens with a single hyphen
    .replace(/-+/g, '-');
}