export interface Formula {
  id: string;
  name: string;
  brand: string;
  description: string;
  age_range: string;
  price: number;
  image_url: string;
  nutrients: Record<string, string | number>;
  ingredients: string;
  category_id: string;
}

export interface Category {
  id: string;
  name: string;
  description: string;
}

export interface FAQItem {
  question: string;
  answer: string;
}

export interface VaccineDose {
  id: string;
  dose_number: number;
  age_recommendation: string;
  description: string | null;
  type: string;
  dose_type: string;
  vaccine: {
    id: string;
    name: string;
    description: string | null;
  };
  related_vaccines?: {
    id: string;
    name: string;
    description?: string | null;
  }[];
}