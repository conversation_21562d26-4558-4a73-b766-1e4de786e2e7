import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Info } from 'lucide-react';
import { Stage } from '../types';

interface LoxoscelicQuestionProps {
  onAnswer: (stage: Stage) => void;
}

export const LoxoscelicQuestion: React.FC<LoxoscelicQuestionProps> = ({ onAnswer }) => {
  return (
    <div className="space-y-6">
      <Card className="p-6 bg-white/80 backdrop-blur-sm">
        <div className="flex items-center gap-3 mb-4">
          <Info className="h-6 w-6 text-amber-600" />
          <h2 className="text-xl font-semibold text-gray-800">
            Início
          </h2>
        </div>
        <p className="text-gray-600 mb-6">
          Suspeita de acidente com aranha marrom (Loxosceles sp.)
        </p>
        <div className="space-y-2">
          <h3 className="text-lg font-medium text-gray-700 mb-4">
            Qual é a forma clínica?
          </h3>
          <div className="space-y-3">
            <Button
              variant="outline"
              onClick={() => onAnswer("cutaneous")}
              className="w-full justify-between bg-amber-50 hover:bg-amber-100 border-2 border-amber-100 text-gray-700"
            >
              Forma Cutânea
            </Button>
            <Button
              variant="outline"
              onClick={() => onAnswer("cutaneous-hemolytic")}
              className="w-full justify-between bg-red-50 hover:bg-red-100 border-2 border-red-100 text-gray-700"
            >
              Forma Cutâneo-Hemolítica
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};