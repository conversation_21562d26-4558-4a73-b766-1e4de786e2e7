import { PatientInfoSection } from "@/components/patient/PatientInfoSection";
import { MedicationInfoTabs } from "@/components/medication/MedicationInfoTabs";

interface MedicationInfoProps {
  medication: any;
  weight: number;
  displayWeight: number;
  setTempWeight: (weight: number) => void;
  setWeight: (weight: number) => void;
  age: number;
  setAge: (age: number) => void;
}

export const MedicationInfo = ({
  medication,
  weight,
  displayWeight,
  setTempWeight,
  setWeight,
  age,
  setAge,
}: MedicationInfoProps) => {
  // Obtém as medidas requeridas do medicamento ou usa o padrão
  const requiredMeasures = medication?.required_measures || ["weight", "age"];

  return (
    <div className="space-y-6">
      <PatientInfoSection
        weight={displayWeight}
        onWeightChange={setTempWeight}
        onWeightCommit={setWeight}
        age={age}
        onAgeChange={setAge}
        onAgeCommit={setAge}
        requiredMeasures={requiredMeasures}
      />

      <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg p-2 sm:p-6 border border-primary/10">
        <MedicationInfoTabs
          medication={medication}
          weight={weight}
          age={age}
          requiredMeasures={requiredMeasures}
        />
      </div>
    </div>
  );
};