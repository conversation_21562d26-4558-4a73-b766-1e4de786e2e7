import { useState } from "react";
import { Plus, Info, Trash2, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface MedicationUseCasesProps {
  medicationId: string;
}

export function MedicationUseCases({ medicationId }: MedicationUseCasesProps) {
  const [newUseCase, setNewUseCase] = useState({ name: "", description: "" });
  const [deletingUseCase, setDeletingUseCase] = useState<string | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: useCases, refetch } = useQuery({
    queryKey: ["medication-use-cases", medicationId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_medication_use_cases")
        .select("*")
        .eq("medication_id", medicationId)
        .order("display_order", { ascending: true });

      if (error) throw error;
      return data;
    },
  });

  const handleMoveUseCase = async (useCaseId: string, direction: 'up' | 'down') => {
    if (!useCases) return;
    
    const currentIndex = useCases.findIndex(uc => uc.id === useCaseId);
    if (currentIndex === -1) return;
    
    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= useCases.length) return;
    
    const currentUseCase = useCases[currentIndex];
    const targetUseCase = useCases[newIndex];
    
    try {
      const { error: error1 } = await supabase
        .from("pedbook_medication_use_cases")
        .update({ display_order: targetUseCase.display_order })
        .eq("id", currentUseCase.id);

      const { error: error2 } = await supabase
        .from("pedbook_medication_use_cases")
        .update({ display_order: currentUseCase.display_order })
        .eq("id", targetUseCase.id);

      if (error1 || error2) throw error1 || error2;

      refetch();
      toast({
        title: "Ordem atualizada",
        description: "A ordem das indicações foi atualizada com sucesso.",
      });
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao reordenar indicações",
        description: error.message,
      });
    }
  };

  const handleAddUseCase = async () => {
    try {
      // Get max display order
      const { data: maxOrderData, error: maxOrderError } = await supabase
        .from("pedbook_medication_use_cases")
        .select("display_order")
        .eq("medication_id", medicationId)
        .order("display_order", { ascending: false })
        .limit(1);

      if (maxOrderError) throw maxOrderError;

      const newDisplayOrder = maxOrderData && maxOrderData[0] 
        ? maxOrderData[0].display_order + 1 
        : 1;

      const { error } = await supabase
        .from("pedbook_medication_use_cases")
        .insert({
          medication_id: medicationId,
          name: newUseCase.name,
          description: newUseCase.description,
          display_order: newDisplayOrder,
        });

      if (error) throw error;

      toast({
        title: "Indicação adicionada com sucesso!",
        description: "A nova indicação de uso foi criada.",
      });

      setNewUseCase({ name: "", description: "" });
      refetch();
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao adicionar indicação",
        description: error.message,
      });
    }
  };

  const handleDeleteUseCase = async () => {
    if (!deletingUseCase) return;

    try {
      const { error: deleteError } = await supabase
        .from("pedbook_medication_use_cases")
        .delete()
        .eq("id", deletingUseCase);

      if (deleteError) throw deleteError;

      toast({
        title: "Indicação excluída com sucesso!",
        description: "A indicação de uso foi removida.",
      });

      setDeletingUseCase(null);
      refetch();
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao excluir indicação",
        description: error.message,
      });
    }
  };

  if (!useCases?.length) {
    return (
      <div className="text-center text-gray-500 py-8">
        Nenhuma indicação de uso cadastrada para este medicamento
      </div>
    );
  }

  return (
    <>
      <div className="space-y-4">
        {useCases.map((useCase, index) => (
          <div
            key={useCase.id}
            className="bg-white rounded-lg shadow-sm p-4 space-y-2 flex justify-between items-start"
          >
            <div>
              <h3 className="font-medium">{useCase.name}</h3>
              {useCase.description && (
                <p className="text-sm text-gray-600">{useCase.description}</p>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => handleMoveUseCase(useCase.id, 'up')}
                disabled={index === 0}
                className="hover:bg-primary/10"
              >
                <ArrowUp className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => handleMoveUseCase(useCase.id, 'down')}
                disabled={index === useCases.length - 1}
                className="hover:bg-primary/10"
              >
                <ArrowDown className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setDeletingUseCase(useCase.id)}
                className="hover:bg-destructive/10"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}
      </div>

      <div className="space-y-4 border-t pt-4">
        <h4 className="font-medium">Adicionar Nova Indicação</h4>
        <div className="space-y-2">
          <Input
            placeholder="Nome da indicação"
            value={newUseCase.name}
            onChange={(e) =>
              setNewUseCase({ ...newUseCase, name: e.target.value })
            }
          />
          <Textarea
            placeholder="Descrição (opcional)"
            value={newUseCase.description}
            onChange={(e) =>
              setNewUseCase({ ...newUseCase, description: e.target.value })
            }
          />
          <Button
            onClick={handleAddUseCase}
            disabled={!newUseCase.name}
            className="w-full"
          >
            <Plus className="h-4 w-4 mr-2" />
            Adicionar Indicação
          </Button>
        </div>
      </div>

      <AlertDialog open={!!deletingUseCase} onOpenChange={() => setDeletingUseCase(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir esta indicação de uso? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteUseCase}>
              Confirmar
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}