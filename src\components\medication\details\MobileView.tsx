
import { MobileMedicationSelector } from "../MobileMedicationSelector";
import { MedicationHeader } from "../MedicationHeader";
import { MedicationInfo } from "./MedicationInfo";
import { MedicationDashboard } from "../MedicationDashboard";
import { MedicationSkeleton } from "@/components/ui/MedicationSkeleton";
import { motion } from "framer-motion";

interface MobileViewProps {
  categories: any[];
  currentMedicationId: string;
  onMedicationSelect: (id: string) => void;
  isLoading: boolean;
  medication: any;
  weight: number;
  displayWeight: number;
  setTempWeight: (weight: number) => void;
  setWeight: (weight: number) => void;
  age: number;
  setAge: (age: number) => void;
  slug?: string; // Adicionar slug para controlar melhor a renderização
}

export const MobileView = ({
  categories,
  currentMedicationId,
  onMedicationSelect,
  isLoading,
  medication,
  weight,
  displayWeight,
  setTempWeight,
  setWeight,
  age,
  setAge,
  slug,
}: MobileViewProps) => {
  return (
    <div className="flex flex-col gap-4">
      <MobileMedicationSelector 
        categories={categories} 
        currentMedicationId={currentMedicationId}
        onMedicationSelect={onMedicationSelect}
      />

      {!slug ? (
        <MedicationDashboard />
      ) : isLoading ? (
        <MedicationSkeleton />
      ) : medication ? (
        <motion.div
          key={medication.id}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.4, ease: "easeOut" }}
          className="space-y-4"
        >
          <MedicationHeader
            name={medication.name}
            description={medication.description}
            brands={medication.brands}
            category={medication.pedbook_medication_categories?.name}
            slug={medication.slug}
            id={medication.id}
          />

          <MedicationInfo
            medication={medication}
            weight={weight}
            displayWeight={displayWeight}
            setTempWeight={setTempWeight}
            setWeight={setWeight}
            age={age}
            setAge={setAge}
          />
        </motion.div>
      ) : null}
    </div>
  );
};
