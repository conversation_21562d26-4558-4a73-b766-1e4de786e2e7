import { Card } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";

interface InsulinSelectionProps {
  weight: number;
  onSelect: (type: "regular" | "ultrafast") => void;
}

export const InsulinSelection = ({ weight, onSelect }: InsulinSelectionProps) => {
  const regularDose = (0.1 * weight).toFixed(2);
  const ultraFastDose = (0.15 * weight).toFixed(2);

  return (
    <Card className="p-6 space-y-4">
      <h3 className="text-lg font-semibold">Escolha do Tipo de Insulina para Insulinoterapia</h3>
      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <RadioGroup onValueChange={(value) => onSelect(value as "regular" | "ultrafast")}>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="regular" id="regular" />
              <Label htmlFor="regular" className="flex-1">
                <span className="font-medium">Insulina regular EV</span>
                <p className="text-sm text-blue-700">
                  {regularDose} UI/hora em bomba de infusão contínua
                </p>
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="ultrafast" id="ultrafast" />
              <Label htmlFor="ultrafast" className="flex-1">
                <span className="font-medium">Insulina ultrarrápida SC</span>
                <p className="text-sm text-blue-700">
                  {ultraFastDose} UI a cada 2 horas
                </p>
              </Label>
            </div>
          </div>
        </RadioGroup>
      </div>
    </Card>
  );
};