-- Função para buscar medicamentos mais acessados com paginação
CREATE OR REPLACE FUNCTION get_top_medications_paginated(
  start_date DATE,
  end_date DATE,
  page_size INTEGER DEFAULT 10,
  page_number INTEGER DEFAULT 1
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  offset_value INTEGER;
  total_count INTEGER;
  result JSON;
BEGIN
  -- Calcular offset
  offset_value := (page_number - 1) * page_size;
  
  -- Contar total de medicamentos únicos no período
  SELECT COUNT(DISTINCT medication_id) INTO total_count
  FROM site_analytics
  WHERE created_at AT TIME ZONE 'America/Sao_Paulo' >= start_date::timestamp
    AND created_at AT TIME ZONE 'America/Sao_Paulo' <= (end_date::timestamp + interval '1 day' - interval '1 second')
    AND action_type = 'medication_view'
    AND medication_id IS NOT NULL;

  -- Buscar dados paginados
  WITH medication_stats AS (
    SELECT
      sa.medication_id,
      pm.name as medication_name,
      pmc.name as category_name,
      COUNT(*) as view_count
    FROM site_analytics sa
    LEFT JOIN pedbook_medications pm ON sa.medication_id = pm.id
    LEFT JOIN pedbook_medication_categories pmc ON pm.category_id = pmc.id
    WHERE sa.created_at AT TIME ZONE 'America/Sao_Paulo' >= start_date::timestamp
      AND sa.created_at AT TIME ZONE 'America/Sao_Paulo' <= (end_date::timestamp + interval '1 day' - interval '1 second')
      AND sa.action_type = 'medication_view'
      AND sa.medication_id IS NOT NULL
    GROUP BY sa.medication_id, pm.name, pmc.name
    ORDER BY view_count DESC
    LIMIT page_size OFFSET offset_value
  )
  SELECT json_build_object(
    'items', json_agg(medication_stats),
    'total_count', total_count
  ) INTO result
  FROM medication_stats;
  
  RETURN result;
END;
$$;

-- Função para buscar categorias mais acessadas com paginação
CREATE OR REPLACE FUNCTION get_top_categories_paginated(
  start_date DATE,
  end_date DATE,
  page_size INTEGER DEFAULT 10,
  page_number INTEGER DEFAULT 1
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  offset_value INTEGER;
  total_count INTEGER;
  result JSON;
BEGIN
  -- Calcular offset
  offset_value := (page_number - 1) * page_size;
  
  -- Contar total de categorias únicas no período
  SELECT COUNT(DISTINCT pm.category_id) INTO total_count
  FROM site_analytics sa
  LEFT JOIN pedbook_medications pm ON sa.medication_id = pm.id
  WHERE sa.created_at AT TIME ZONE 'America/Sao_Paulo' >= start_date::timestamp
    AND sa.created_at AT TIME ZONE 'America/Sao_Paulo' <= (end_date::timestamp + interval '1 day' - interval '1 second')
    AND sa.action_type = 'medication_view'
    AND sa.medication_id IS NOT NULL
    AND pm.category_id IS NOT NULL;

  -- Buscar dados paginados
  WITH category_stats AS (
    SELECT
      pm.category_id,
      pmc.name as category_name,
      COUNT(*) as view_count
    FROM site_analytics sa
    LEFT JOIN pedbook_medications pm ON sa.medication_id = pm.id
    LEFT JOIN pedbook_medication_categories pmc ON pm.category_id = pmc.id
    WHERE sa.created_at AT TIME ZONE 'America/Sao_Paulo' >= start_date::timestamp
      AND sa.created_at AT TIME ZONE 'America/Sao_Paulo' <= (end_date::timestamp + interval '1 day' - interval '1 second')
      AND sa.action_type = 'medication_view'
      AND sa.medication_id IS NOT NULL
      AND pm.category_id IS NOT NULL
    GROUP BY pm.category_id, pmc.name
    ORDER BY view_count DESC
    LIMIT page_size OFFSET offset_value
  )
  SELECT json_build_object(
    'items', json_agg(category_stats),
    'total_count', total_count
  ) INTO result
  FROM category_stats;
  
  RETURN result;
END;
$$;

-- Função para buscar usuários mais ativos
CREATE OR REPLACE FUNCTION get_top_users_analytics(
  start_date DATE,
  end_date DATE,
  page_size INTEGER DEFAULT 10,
  page_number INTEGER DEFAULT 1
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  offset_value INTEGER;
  total_authenticated_users INTEGER;
  anonymous_views_count INTEGER;
  result JSON;
BEGIN
  -- Calcular offset
  offset_value := (page_number - 1) * page_size;
  
  -- Contar visualizações anônimas
  SELECT COUNT(*) INTO anonymous_views_count
  FROM site_analytics
  WHERE created_at AT TIME ZONE 'America/Sao_Paulo' >= start_date::timestamp
    AND created_at AT TIME ZONE 'America/Sao_Paulo' <= (end_date::timestamp + interval '1 day' - interval '1 second')
    AND action_type = 'medication_view'
    AND user_id IS NULL;

  -- Contar total de usuários autenticados únicos
  SELECT COUNT(DISTINCT user_id) INTO total_authenticated_users
  FROM site_analytics
  WHERE created_at AT TIME ZONE 'America/Sao_Paulo' >= start_date::timestamp
    AND created_at AT TIME ZONE 'America/Sao_Paulo' <= (end_date::timestamp + interval '1 day' - interval '1 second')
    AND action_type = 'medication_view'
    AND user_id IS NOT NULL;
  
  -- Buscar usuários autenticados paginados
  WITH user_stats AS (
    SELECT
      sa.user_id,
      p.full_name as user_name,
      p.email as user_email,
      COUNT(*) as view_count
    FROM site_analytics sa
    LEFT JOIN profiles p ON sa.user_id = p.id
    WHERE sa.created_at AT TIME ZONE 'America/Sao_Paulo' >= start_date::timestamp
      AND sa.created_at AT TIME ZONE 'America/Sao_Paulo' <= (end_date::timestamp + interval '1 day' - interval '1 second')
      AND sa.action_type = 'medication_view'
      AND sa.user_id IS NOT NULL
    GROUP BY sa.user_id, p.full_name, p.email
    ORDER BY view_count DESC
    LIMIT page_size OFFSET offset_value
  )
  SELECT json_build_object(
    'authenticated_users', COALESCE(json_agg(user_stats), '[]'::json),
    'anonymous_views', anonymous_views_count,
    'total_count', total_authenticated_users
  ) INTO result
  FROM user_stats;
  
  RETURN result;
END;
$$;
