import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { NewsItem, NewsCategory } from '@/types/newsletter';

// Nome correto da tabela no Supabase
const TABLE_NAME = 'medical_news';

export function useNewsletters(options?: {
  limit?: number;
  offset?: number;
  category?: string;
  searchTerm?: string;
  isPediDrop?: boolean;
}) {
  const { limit = 10, offset = 0, category, searchTerm, isPediDrop } = options || {};

  return useQuery({
    queryKey: ['newsletters', limit, offset, category, searchTerm, isPediDrop],
    queryFn: async () => {
      // Selecionar campos incluindo os novos campos do PediDrop
      const selectFields = isPediDrop
        ? 'id, title, summary, pub_date, category, source, link, image_url, main_topic, reading_time, practical_application, clinical_pearls, attention_points, scientific_references, content_sections, is_pedidrop'
        : 'id, title, summary, pub_date, category, source, link, image_url';

      let query = supabase
        .from(TABLE_NAME)
        .select(selectFields)
        .order('pub_date', { ascending: false })
        .range(offset, offset + limit - 1);

      // Filtrar por PediDrop se especificado
      if (isPediDrop !== undefined) {
        query = query.eq('is_pedidrop', isPediDrop);
      }

      if (category) {
        query = query.eq('category', category);
      }

      if (searchTerm) {
        const searchFields = isPediDrop
          ? `title.ilike.%${searchTerm}%,summary.ilike.%${searchTerm}%,main_topic.ilike.%${searchTerm}%,attention_points.ilike.%${searchTerm}%`
          : `title.ilike.%${searchTerm}%,summary.ilike.%${searchTerm}%`;
        query = query.or(searchFields);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(error.message);
      }

      return data as NewsItem[];
    },
    // Cache otimizado para notícias
    staleTime: 5 * 60 * 1000, // 5 minutos - notícias podem ser um pouco antigas
    gcTime: 15 * 60 * 1000, // 15 minutos
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    retry: 1,
  });
}

export function useNewslettersCount(options?: {
  category?: string;
  searchTerm?: string;
  isPediDrop?: boolean;
}) {
  const { category, searchTerm, isPediDrop } = options || {};

  return useQuery({
    queryKey: ['newsletters-count', category, searchTerm, isPediDrop],
    queryFn: async () => {
      // Otimizar count query - só buscar o count, não os dados
      let query = supabase
        .from(TABLE_NAME)
        .select('id', { count: 'exact', head: true });

      // Filtrar por PediDrop se especificado
      if (isPediDrop !== undefined) {
        query = query.eq('is_pedidrop', isPediDrop);
      }

      if (category) {
        query = query.eq('category', category);
      }

      if (searchTerm) {
        const searchFields = isPediDrop
          ? `title.ilike.%${searchTerm}%,summary.ilike.%${searchTerm}%,main_topic.ilike.%${searchTerm}%,attention_points.ilike.%${searchTerm}%`
          : `title.ilike.%${searchTerm}%,summary.ilike.%${searchTerm}%`;
        query = query.or(searchFields);
      }

      const { count, error } = await query;

      if (error) {
        throw new Error(error.message);
      }

      return count || 0;
    },
    // Cache mais longo para contagem
    staleTime: 10 * 60 * 1000, // 10 minutos
    gcTime: 30 * 60 * 1000, // 30 minutos
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    retry: 1,
  });
}

export function useNewsCategories() {
  return useQuery({
    queryKey: ['newsletter-categories'],
    queryFn: async () => {
      // OTIMIZAÇÃO MÁXIMA: usar função SQL otimizada
      const { data, error } = await supabase
        .rpc('get_news_categories');

      if (error) {
        throw new Error(error.message);
      }

      // Dados já vêm processados do banco
      return (data || []) as NewsCategory[];
    },
    // Cache MUITO longo para categorias - elas raramente mudam
    staleTime: 60 * 60 * 1000, // 1 hora
    gcTime: 24 * 60 * 60 * 1000, // 24 horas
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    retry: 1,
  });
}

export function useNewsItem(id: string) {
  return useQuery({
    queryKey: ['newsletter', id],
    queryFn: async () => {
      if (!id) return null;

      const { data, error } = await supabase
        .from(TABLE_NAME)
        .select('*')
        .eq('id', id)
        .maybeSingle();

      if (error) {
        if (error.code === 'PGRST116') {
          return null;
        }
        throw new Error(error.message);
      }

      return data as NewsItem;
    },
    enabled: !!id,
    // Cache longo para item individual
    staleTime: 30 * 60 * 1000, // 30 minutos
    gcTime: 60 * 60 * 1000, // 1 hora
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    retry: 1,
  });
}
