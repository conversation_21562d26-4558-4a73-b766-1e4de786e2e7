import { LEGEND_LABELS, MEASUREMENT_LABELS } from '../constants';

interface ChartLegendProps {
  payload?: any[];
  measurementType: 'weight' | 'height' | 'bmi' | 'head-circumference';
}

export function ChartLegend({ payload, measurementType }: ChartLegendProps) {
  if (!payload) return null;
  
  return (
    <div className="flex flex-col items-center gap-2 text-xs">
      <div className="flex flex-wrap justify-center items-center gap-4">
        {payload.filter(entry => entry.value !== 'age' && entry.value !== measurementType).map((entry, index) => (
          <div key={index} className="flex items-center gap-1">
            <div 
              className="w-4 h-0.5" 
              style={{ 
                backgroundColor: entry.color,
                borderStyle: entry.value === 'p3' || entry.value === 'p15' || entry.value === 'p85' || entry.value === 'p97' ? 'dashed' : 'solid'
              }}
            />
            <span>{LEGEND_LABELS[entry.value as keyof typeof LEGEND_LABELS]}</span>
          </div>
        ))}
      </div>
      <div className="flex flex-col items-center text-gray-600">
        <span>Eixo X: Idade (meses/anos)</span>
        <span>Eixo Y: {MEASUREMENT_LABELS[measurementType]}</span>
      </div>
    </div>
  );
}