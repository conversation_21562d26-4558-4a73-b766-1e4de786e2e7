export interface StatCategory {
  name: string;
  correct: number;
  total: number;
}

export interface SessionStats {
  time_spent: number;
  correct_answers: number;
  incorrect_answers: number;
  by_theme: Record<string, StatCategory>;
  by_specialty: Record<string, StatCategory>;
  by_focus: Record<string, StatCategory>;
}

export interface StudySession {
  id: string;
  user_id: string;
  stats: SessionStats;
  created_at: string;
  completed_at: string | null;
  questions: string[];
  total_questions: number;
  current_question_index: number;
}

export interface TransformedStats {
  bySpecialty: Record<string, StatCategory>;
  byTheme: Record<string, StatCategory>;
  byFocus: Record<string, StatCategory>;
}