
export interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  image_url?: string | string[];
  threadId?: string;
  isLoading?: boolean;
}

export interface Thread {
  id: string;
  title: string;
  lastMessage: string;
  createdAt: Date;
}

export interface ChatMetadata {
  thread?: {
    id: string;
    title: string;
    lastMessage: string;
    createdAt: string;
  };
  threadId?: string;
  image_url?: string | string[];
}
