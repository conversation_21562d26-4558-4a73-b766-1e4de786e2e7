/**
 * Utilitário para otimizar o logo do PedBook
 * Cria versões otimizadas em diferentes tamanhos
 */

import { compressImage } from './imageOptimization';

export interface LogoOptimizationResult {
  originalSize: number;
  optimizedSize: number;
  compressionRatio: number;
  optimizedBlob: Blob;
}

/**
 * Otimiza o logo para uso no header (48x48px)
 */
export const optimizeLogoForHeader = async (logoFile: File): Promise<LogoOptimizationResult> => {
  const originalSize = logoFile.size;
  
  const optimizedFile = await compressImage(logoFile, {
    maxWidth: 48,
    maxHeight: 48,
    quality: 0.9,
    format: 'webp',
    maintainAspectRatio: true
  });

  const optimizedSize = optimizedFile.size;
  const compressionRatio = Math.round(((originalSize - optimizedSize) / originalSize) * 100);

  return {
    originalSize,
    optimizedSize,
    compressionRatio,
    optimizedBlob: optimizedFile
  };
};

/**
 * Otimiza o logo para favicon (32x32px)
 */
export const optimizeLogoForFavicon = async (logoFile: File): Promise<LogoOptimizationResult> => {
  const originalSize = logoFile.size;
  
  const optimizedFile = await compressImage(logoFile, {
    maxWidth: 32,
    maxHeight: 32,
    quality: 0.95,
    format: 'webp',
    maintainAspectRatio: true
  });

  const optimizedSize = optimizedFile.size;
  const compressionRatio = Math.round(((originalSize - optimizedSize) / originalSize) * 100);

  return {
    originalSize,
    optimizedSize,
    compressionRatio,
    optimizedBlob: optimizedFile
  };
};

/**
 * Otimiza o logo para uso geral (mantém tamanho, reduz qualidade)
 */
export const optimizeLogoGeneral = async (logoFile: File): Promise<LogoOptimizationResult> => {
  const originalSize = logoFile.size;
  
  const optimizedFile = await compressImage(logoFile, {
    maxWidth: 200, // Tamanho máximo razoável para logo
    maxHeight: 200,
    quality: 0.85,
    format: 'webp',
    maintainAspectRatio: true
  });

  const optimizedSize = optimizedFile.size;
  const compressionRatio = Math.round(((originalSize - optimizedSize) / originalSize) * 100);

  return {
    originalSize,
    optimizedSize,
    compressionRatio,
    optimizedBlob: optimizedFile
  };
};

/**
 * Cria múltiplas versões otimizadas do logo
 */
export const createOptimizedLogoVersions = async (logoFile: File) => {
  const results = {
    favicon: await optimizeLogoForFavicon(logoFile),
    header: await optimizeLogoForHeader(logoFile),
    general: await optimizeLogoGeneral(logoFile)
  };

  const totalOriginalSize = logoFile.size * 3; // 3 usos diferentes
  const totalOptimizedSize = results.favicon.optimizedSize + results.header.optimizedSize + results.general.optimizedSize;
  const totalSavings = totalOriginalSize - totalOptimizedSize;
  const totalCompressionRatio = Math.round((totalSavings / totalOriginalSize) * 100);

  return {
    ...results,
    totalSavings,
    totalCompressionRatio,
    summary: {
      originalSize: logoFile.size,
      totalSavings: totalSavings,
      compressionRatio: totalCompressionRatio
    }
  };
};

/**
 * Download de blob como arquivo
 */
export const downloadBlob = (blob: Blob, filename: string) => {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};
