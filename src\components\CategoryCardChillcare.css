
.card-content {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

@media (max-width: 768px) {
  .card-content {
    padding: 1rem;
  }

  .card-content h3,
  .card-content p {
    font-size: 0.875rem;
  }
}

/* Enhanced card style */
.card-enhanced {
  transition: all 0.3s ease;
}

.card-enhanced:hover {
  transform: translateY(-3px);
}

/* Glass effect for cards */
.glass-card {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Pulsating animation for highlighted cards */
@keyframes soft-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.3);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* Apply pulsating animation to highlighted cards */
.isHighlighted {
  animation: soft-pulse 3s infinite;
}

/* Custom scrollbar styling */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Carousel indicator styling */
.carousel-indicator {
  transition: all 0.3s ease;
}

.carousel-indicator.active {
  width: 24px;
  background-color: #3b82f6;
}
