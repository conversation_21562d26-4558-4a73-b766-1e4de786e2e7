
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Pill, Calculator, ChevronDown, ChevronUp, AlertCircle, Clock } from "lucide-react";
import { calculateSupplementation } from "@/utils/supplementationCalculator";
import { SupplementationInput } from "@/types/supplementation";
import SupplementationFAQ from "@/components/supplementation/SupplementationFAQ";
import { formatAge } from "@/utils/formatAge";
import { useState } from "react";

interface SupplementationAnalysisProps {
  data: {
    age: number;
    weight?: number;
    birthWeight?: number;
    maturity: 'Term' | 'Pre-term';
    exclusiveBreastfeeding: boolean;
    riskFactors?: string[];
    gestationalAge?: number;
  };
  useCorrectedAge?: boolean;
}

// Medicamentos disponíveis para ferro
const ironMedications = [
  { name: "Sulfato ferroso gotas", concentration: 1.25, note: "Em geral disponível no Posto de Saúde" },
  { name: "Combiron gotas", concentration: 2.5, note: "glicinato" },
  { name: "Noripurum gotas", concentration: 2.5, note: "polimaltosado" },
  { name: "Neutrofer gotas", concentration: 2.5, note: "glicinato" },
  { name: "Endofer gotas", concentration: 2.5, note: "polimaltosado" }
];

export const SupplementationAnalysis = ({ data, useCorrectedAge = false }: SupplementationAnalysisProps) => {
  const [showIronPrescription, setShowIronPrescription] = useState(false);
  const [selectedIronMed, setSelectedIronMed] = useState(ironMedications[0]);
  const [isEndemicArea, setIsEndemicArea] = useState<boolean | null>(null);

  // Verificar se é prematuro
  const isPreterm = data.maturity === 'Pre-term';

  const supplementationInput: SupplementationInput = {
    ageInDays: data.age * 30,
    currentWeight: data.weight ? data.weight * 1000 : 0,
    birthWeight: data.birthWeight || 0,
    maturity: data.maturity,
    exclusiveBreastfeeding: data.exclusiveBreastfeeding,
    riskFactors: (data.riskFactors || []) as any[]
  };

  const recommendations = calculateSupplementation(supplementationInput);

  // Extrair dose de ferro em mg/dia da recomendação
  const extractIronDose = (ironText: string): number => {
    const match = ironText.match(/(\d+(?:\.\d+)?)\s*mg.*?ferro.*?dia/i);
    return match ? parseFloat(match[1]) : 0;
  };

  const ironDosePerDay = extractIronDose(recommendations.iron);

  // Calcular gotas necessárias
  const calculateDrops = (medication: typeof ironMedications[0]) => {
    if (ironDosePerDay === 0) return 0;
    return Math.round((ironDosePerDay / medication.concentration) * 10) / 10;
  };

  return (
    <div className="space-y-6">
      {/* Alerta de idade */}
      <Alert className="bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800/50">
        <Pill className="h-4 w-4 text-green-600 dark:text-green-400" />
        <AlertTitle className="text-green-800 dark:text-green-300">Suplementação Vitamínica</AlertTitle>
        <AlertDescription className="text-green-700 dark:text-green-200">
          Com base na idade do paciente ({formatAge(data.age)})
          {useCorrectedAge && isPreterm && (
            <span className="font-medium"> - usando idade corrigida</span>
          )}
          {!useCorrectedAge && isPreterm && (
            <span className="font-medium"> - usando idade cronológica</span>
          )}
        </AlertDescription>
      </Alert>

      {/* Cards de suplementação com design melhorado */}
      <div className="grid gap-4">
        {/* Vitamina D */}
        <Card className="overflow-hidden border-l-4 border-l-purple-400">
          <div className="p-6 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20">
            <div className="flex items-center gap-3 mb-3">
              <span className="text-2xl">☀️</span>
              <h3 className="text-lg font-semibold text-purple-700 dark:text-purple-300">Vitamina D</h3>
            </div>
            <p className="text-purple-600 dark:text-purple-200/90 leading-relaxed">{recommendations.vitaminD}</p>
          </div>
        </Card>

        {/* Ferro - Card expandido */}
        <Card className="overflow-hidden border-l-4 border-l-red-400">
          <div className="p-6 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-3">
                <span className="text-2xl">🩸</span>
                <h3 className="text-lg font-semibold text-red-700 dark:text-red-300">Ferro</h3>
              </div>
              {ironDosePerDay > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowIronPrescription(!showIronPrescription)}
                  className="text-red-600 border-red-200 hover:bg-red-100 dark:text-red-300 dark:border-red-700 dark:hover:bg-red-900/20"
                >
                  <Calculator className="h-4 w-4 mr-2" />
                  Ver Prescrição
                  {showIronPrescription ? <ChevronUp className="h-4 w-4 ml-2" /> : <ChevronDown className="h-4 w-4 ml-2" />}
                </Button>
              )}
            </div>
            <p className="text-red-600 dark:text-red-200/90 leading-relaxed mb-4">{recommendations.iron}</p>

            {/* Seção de prescrição expandível */}
            {showIronPrescription && ironDosePerDay > 0 && (
              <div className="mt-4 p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg border border-red-200 dark:border-red-700">
                <h4 className="font-semibold text-red-700 dark:text-red-300 mb-3 flex items-center gap-2">
                  <Calculator className="h-4 w-4" />
                  Cálculo de Prescrição ({ironDosePerDay} mg/dia)
                </h4>

                {/* Seletor de medicamento */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-red-600 dark:text-red-300 mb-2">
                    Selecione o medicamento:
                  </label>
                  <div className="grid gap-2">
                    {ironMedications.map((med, index) => (
                      <button
                        key={index}
                        onClick={() => setSelectedIronMed(med)}
                        className={`p-3 text-left rounded-lg border transition-all ${
                          selectedIronMed.name === med.name
                            ? 'border-red-400 bg-red-100 dark:bg-red-900/30 dark:border-red-600'
                            : 'border-gray-200 hover:border-red-300 hover:bg-red-50 dark:border-gray-600 dark:hover:border-red-700 dark:hover:bg-red-900/20'
                        }`}
                      >
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="font-medium text-red-700 dark:text-red-300">{med.name}</p>
                            <p className="text-sm text-red-600 dark:text-red-400">{med.concentration} mg/gota ({med.note})</p>
                          </div>
                          <Badge variant={selectedIronMed.name === med.name ? "default" : "secondary"}>
                            {calculateDrops(med)} gotas/dia
                          </Badge>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Prescrição final */}
                <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-lg border border-red-300 dark:border-red-600">
                  <h5 className="font-semibold text-red-800 dark:text-red-200 mb-2">📋 Prescrição:</h5>
                  <p className="text-red-700 dark:text-red-300">
                    <strong>{selectedIronMed.name}</strong><br/>
                    <strong>{calculateDrops(selectedIronMed)} gotas por dia</strong><br/>
                    <span className="text-sm">({selectedIronMed.concentration} mg/gota × {calculateDrops(selectedIronMed)} gotas = {ironDosePerDay} mg ferro elementar/dia)</span>
                  </p>
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* Vitamina A - Interativa baseada na idade */}
        {data.age < 6 ? (
          // Para crianças < 6 meses - Card simples
          <Card className="overflow-hidden border-l-4 border-l-amber-400">
            <div className="p-6 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20">
              <div className="flex items-center gap-3 mb-3">
                <span className="text-2xl">🥕</span>
                <h3 className="text-lg font-semibold text-amber-700 dark:text-amber-300">Vitamina A</h3>
              </div>
              <p className="text-amber-600 dark:text-amber-200/90 leading-relaxed">
                {recommendations.vitaminA}
              </p>
            </div>
          </Card>
        ) : (
          // Para crianças ≥ 6 meses - Card interativo
          <Card className="overflow-hidden border-l-4 border-l-amber-400">
            <div className="p-6 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">🥕</span>
                  <h3 className="text-lg font-semibold text-amber-700 dark:text-amber-300">Vitamina A</h3>
                </div>
                {isEndemicArea === null && (
                  <div className="flex items-center gap-2 text-amber-600 dark:text-amber-400">
                    <span className="text-sm font-medium">Avaliação necessária</span>
                    <span className="animate-pulse">⚠️</span>
                  </div>
                )}
              </div>

              {/* Pergunta sobre área endêmica */}
              {isEndemicArea === null && (
                <div className="mb-4 p-4 bg-gradient-to-r from-amber-100 to-orange-100 dark:from-amber-900/40 dark:to-orange-900/40 rounded-lg border border-amber-300 dark:border-amber-600">
                  <div className="flex items-start gap-3 mb-3">
                    <span className="text-xl">📍</span>
                    <div>
                      <p className="text-amber-800 dark:text-amber-200 font-semibold mb-2">
                        Avaliação de Área Endêmica
                      </p>
                      <p className="text-amber-700 dark:text-amber-300 text-sm mb-3">
                        A criança vive em área endêmica para deficiência de Vitamina A ou apresenta fatores de risco?
                      </p>
                      <div className="bg-amber-50 dark:bg-amber-900/20 p-3 rounded border-l-4 border-amber-400 mb-3">
                        <p className="text-xs text-amber-700 dark:text-amber-300">
                          <strong>Considere:</strong> Região com alta prevalência de desnutrição, diarreia crônica,
                          fibrose cística, ou outras condições que comprometem a absorção intestinal.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      size="sm"
                      onClick={() => setIsEndemicArea(true)}
                      className="bg-amber-600 hover:bg-amber-700 text-white flex-1 sm:flex-none"
                    >
                      <span className="mr-2">✅</span>
                      Sim, área endêmica/risco
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setIsEndemicArea(false)}
                      className="border-amber-600 text-amber-600 hover:bg-amber-50 dark:border-amber-400 dark:text-amber-400 dark:hover:bg-amber-900/20 flex-1 sm:flex-none"
                    >
                      <span className="mr-2">❌</span>
                      Não, área não endêmica
                    </Button>
                  </div>
                </div>
              )}

              {/* Recomendação baseada na resposta */}
              {isEndemicArea !== null && (
                <div className={`p-4 rounded-lg border-l-4 ${
                  isEndemicArea
                    ? 'bg-green-50 dark:bg-green-900/20 border-green-400 dark:border-green-600'
                    : 'bg-blue-50 dark:bg-blue-900/20 border-blue-400 dark:border-blue-600'
                }`}>
                  <div className="flex items-start gap-3">
                    <span className="text-xl">{isEndemicArea ? '💊' : 'ℹ️'}</span>
                    <div>
                      <h4 className={`font-semibold mb-2 ${
                        isEndemicArea
                          ? 'text-green-800 dark:text-green-200'
                          : 'text-blue-800 dark:text-blue-200'
                      }`}>
                        {isEndemicArea ? 'Suplementação Recomendada' : 'Suplementação Não Rotineira'}
                      </h4>
                      <p className={`text-sm leading-relaxed ${
                        isEndemicArea
                          ? 'text-green-700 dark:text-green-300'
                          : 'text-blue-700 dark:text-blue-300'
                      }`}>
                        {isEndemicArea ? (
                          <>
                            <strong>Protocolo de suplementação:</strong><br/>
                            • <strong>6 meses:</strong> 100.000 UI (dose única)<br/>
                            • <strong>12-59 meses:</strong> 200.000 UI a cada 6 meses<br/>
                            • Indicado para áreas endêmicas, desnutrição, diarreia crônica, fibrose cística ou outras condições que comprometem a absorção intestinal.
                          </>
                        ) : (
                          <>
                            A suplementação de vitamina A não é rotineiramente recomendada para esta criança.
                            Considere apenas em casos específicos como desnutrição, diarreia crônica, ou outras
                            condições que comprometem a absorção intestinal.
                          </>
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Botão para resetar resposta */}
              {isEndemicArea !== null && (
                <div className="mt-4 flex justify-end">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setIsEndemicArea(null)}
                    className="text-amber-600 hover:bg-amber-100 dark:text-amber-400 dark:hover:bg-amber-900/20"
                  >
                    🔄 Reavaliar
                  </Button>
                </div>
              )}
            </div>
          </Card>
        )}
      </div>

      {/* FAQ */}
      <div className="mt-8">
        <SupplementationFAQ />
      </div>
    </div>
  );
};
