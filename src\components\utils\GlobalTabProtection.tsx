import React, { useEffect } from 'react';
import { useTabFocusProtection } from '@/hooks/useTabFocusProtection';

/**
 * Componente que aplica a proteção contra perda de estado ao alternar tabs
 * de forma global para toda a aplicação.
 */
export const GlobalTabProtection: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Inicializar o hook de proteção global
  useTabFocusProtection();

  useEffect(() => {
    // Proteção global contra perda de estado inicializada
  }, []);

  return <>{children}</>;
};
