import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Baby, Calendar, User } from "lucide-react";
import { useState, useEffect } from "react";

interface BasicInfoInputsProps {
  defaultAgeMonths?: number;
  defaultAgeDays?: number;
  defaultGestationalAge?: number;
}

export function BasicInfoInputs({
  defaultAgeMonths = 0,
  defaultAgeDays = 0,
  defaultGestationalAge = 0
}: BasicInfoInputsProps) {
  const [ageMonths, setAgeMonths] = useState(defaultAgeMonths);
  const [ageDays, setAgeDays] = useState(defaultAgeDays);
  const [gestationalWeeks, setGestationalWeeks] = useState(Math.floor(defaultGestationalAge || 0));
  const [gestationalDays, setGestationalDays] = useState(Math.round(((defaultGestationalAge || 0) % 1) * 7));

  // Calcular idade gestacional total em semanas decimais
  const gestationalAge = gestationalWeeks + (gestationalDays / 7);

  // Calcular idade total em meses decimais
  const calculateTotalAge = (months: number, days: number) => {
    return months + (days / 30.44); // 30.44 é a média de dias por mês
  };

  // Classificação detalhada da idade gestacional
  const getGestationalAgeClassification = (weeks: number): string => {
    if (weeks < 28) return "🔴 Extremamente prematuro";
    if (weeks >= 28 && weeks < 32) return "🟠 Muito prematuro";
    if (weeks >= 32 && weeks < 34) return "🟡 Prematuro moderado";
    if (weeks >= 34 && weeks < 37) return "🟡 Prematuro tardio";
    if (weeks >= 37 && weeks < 39) return "🟢 Pré-termo limítrofe";
    if (weeks >= 39 && weeks < 41) return "🟢 Termo completo";
    if (weeks >= 41 && weeks < 42) return "🟢 Termo tardio";
    if (weeks >= 42) return "🔵 Pós-termo";
    return "A termo";
  };

  // Atualizar campo hidden com idade total
  useEffect(() => {
    const totalAge = calculateTotalAge(ageMonths, ageDays);
    const hiddenInput = document.querySelector('input[name="age"]') as HTMLInputElement;
    if (hiddenInput) {
      hiddenInput.value = totalAge.toString();
    }
  }, [ageMonths, ageDays]);

  return (
    <div className="space-y-4">
      {/* Primeira linha: Idade e Gênero */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Idade */}
        <div className="space-y-2">
          <Label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
            <Calendar className="h-4 w-4 text-blue-600" />
            Idade
          </Label>
          <div className="grid grid-cols-2 gap-2">
            <div className="relative">
              <Input
                id="ageMonths"
                type="number"
                min={0}
                max={240}
                value={ageMonths}
                onChange={(e) => setAgeMonths(Number(e.target.value) || 0)}
                className="bg-white dark:bg-slate-800 h-10 text-sm pr-8 border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500"
                placeholder="0"
              />
              <span className="absolute right-2 top-1/2 -translate-y-1/2 text-xs text-gray-500 font-medium">m</span>
            </div>
            <div className="relative">
              <Input
                id="ageDays"
                type="number"
                min={0}
                max={30}
                value={ageDays}
                onChange={(e) => setAgeDays(Number(e.target.value) || 0)}
                className="bg-white dark:bg-slate-800 h-10 text-sm pr-8 border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500"
                placeholder="0"
              />
              <span className="absolute right-2 top-1/2 -translate-y-1/2 text-xs text-gray-500 font-medium">d</span>
            </div>
          </div>
          <input type="hidden" name="age" value={calculateTotalAge(ageMonths, ageDays)} />
        </div>

        {/* Gênero */}
        <div className="space-y-2">
          <Label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
            <User className="h-4 w-4 text-blue-600" />
            Gênero
          </Label>
          <RadioGroup name="gender" className="flex gap-3 pt-1" defaultValue="male">
            <div className="flex items-center space-x-2 bg-white dark:bg-slate-800 px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:border-blue-400 transition-colors">
              <RadioGroupItem value="male" id="male" className="h-4 w-4" />
              <Label htmlFor="male" className="text-sm font-medium cursor-pointer">♂ Masculino</Label>
            </div>
            <div className="flex items-center space-x-2 bg-white dark:bg-slate-800 px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:border-blue-400 transition-colors">
              <RadioGroupItem value="female" id="female" className="h-4 w-4" />
              <Label htmlFor="female" className="text-sm font-medium cursor-pointer">♀ Feminino</Label>
            </div>
          </RadioGroup>
        </div>
      </div>

      {/* Segunda linha: Idade Gestacional */}
      <div className="space-y-2">
        <Label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
          <Baby className="h-4 w-4 text-blue-600" />
          Idade Gestacional
        </Label>
        <div className="grid grid-cols-2 gap-2">
          <div className="relative">
            <Input
              id="gestationalWeeks"
              type="number"
              min={22}
              max={42}
              value={gestationalWeeks || ''}
              onChange={(e) => setGestationalWeeks(Number(e.target.value) || 0)}
              className="bg-white dark:bg-slate-800 h-10 text-sm pr-12 border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500"
              placeholder="0"
            />
            <span className="absolute right-2 top-1/2 -translate-y-1/2 text-xs text-gray-500 font-medium">sem</span>
          </div>
          <div className="relative">
            <Input
              id="gestationalDays"
              type="number"
              min={0}
              max={6}
              value={gestationalDays || ''}
              onChange={(e) => setGestationalDays(Number(e.target.value) || 0)}
              className="bg-white dark:bg-slate-800 h-10 text-sm pr-8 border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500"
              placeholder="0"
            />
            <span className="absolute right-2 top-1/2 -translate-y-1/2 text-xs text-gray-500 font-medium">d</span>
          </div>
        </div>
        {gestationalAge > 0 && (
          <div className="text-xs text-gray-600 dark:text-gray-400 font-medium bg-gray-50 dark:bg-gray-800 px-2 py-1 rounded">
            {gestationalWeeks > 0 && gestationalDays > 0
              ? `${gestationalWeeks} semanas e ${gestationalDays} dias`
              : gestationalWeeks > 0 && gestationalDays === 0
              ? `${gestationalWeeks} semanas`
              : gestationalWeeks === 0 && gestationalDays > 0
              ? `${gestationalDays} dias`
              : `${gestationalWeeks} semanas`
            } • {getGestationalAgeClassification(gestationalAge)}
          </div>
        )}
        <input type="hidden" name="gestationalAge" value={gestationalAge} />
      </div>

      <input type="hidden" name="maturity" value={gestationalAge >= 37 ? "Term" : "Pre-term"} />
    </div>
  );
}