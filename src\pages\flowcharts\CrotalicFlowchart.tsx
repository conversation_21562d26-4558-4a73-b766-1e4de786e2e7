
import React from "react";
import { Link } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { FlowchartSEO } from "@/components/seo/FlowchartSEO";
import { FLOWCHART_SEO_DATA } from "@/data/flowchartSEOData";
import { ScrollArea } from "@/components/ui/scroll-area";
import { CrotalicQuestion } from "@/components/flowcharts/crotalic/CrotalicQuestion";
import { CrotalicResult } from "@/components/flowcharts/crotalic/CrotalicResult";
import { CrotalicSpecialConsiderations } from "@/components/flowcharts/crotalic/CrotalicSpecialConsiderations";
import { useCrotalicFlow } from "@/components/flowcharts/crotalic/useCrotalicFlow";

const CrotalicFlowchart = () => {
  const {
    currentStep,
    answers,
    handleAnswer,
    handleContinue,
    resetFlow,
    getCurrentQuestion,
    getCurrentResult,
  } = useCrotalicFlow();

  const seoData = FLOWCHART_SEO_DATA['crotalic'];

  const renderContent = () => {
    const question = getCurrentQuestion();
    const result = getCurrentResult();

    if (question) {
      if (currentStep === "severity") {
        return (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-center mb-6 text-gray-800 dark:text-gray-100">
              {question}
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div
                onClick={() => handleAnswer("mild")}
                className="p-6 rounded-lg bg-purple-50 dark:bg-purple-900/30 border border-purple-200 dark:border-purple-700/50 
                         hover:bg-purple-100 dark:hover:bg-purple-800/40 transition-all cursor-pointer space-y-2"
              >
                <h3 className="font-semibold text-purple-800 dark:text-purple-300">Leve</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Ptose palpebral discreta, turvação visual leve
                </p>
              </div>

              <div
                onClick={() => handleAnswer("moderate")}
                className="p-6 rounded-lg bg-orange-50 dark:bg-orange-900/30 border border-orange-200 dark:border-orange-700/50 
                         hover:bg-orange-100 dark:hover:bg-orange-800/40 transition-all cursor-pointer space-y-2"
              >
                <h3 className="font-semibold text-orange-800 dark:text-orange-300">Moderado</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Ptose palpebral com alterações visuais precoces
                </p>
              </div>

              <div
                onClick={() => handleAnswer("severe")}
                className="p-6 rounded-lg bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700/50 
                         hover:bg-red-100 dark:hover:bg-red-800/40 transition-all cursor-pointer space-y-2"
              >
                <h3 className="font-semibold text-red-800 dark:text-red-300">Grave</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Manifestações sistêmicas graves
                </p>
              </div>
            </div>
          </div>
        );
      }

      return (
        <CrotalicQuestion
          question={question}
          onAnswer={handleAnswer}
          selectedAnswer={answers[currentStep] as boolean}
        />
      );
    }

    if (result) {
      return (
        <CrotalicResult
          {...result}
          onReset={resetFlow}
          nextQuestion={currentStep === "observation" ? "initial" : undefined}
          onContinue={handleContinue}
        />
      );
    }

    return null;
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-purple-50 via-white to-purple-50 dark:from-purple-900/20 dark:via-slate-900 dark:to-purple-900/10">
      <FlowchartSEO {...seoData} />

      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto space-y-8">
          <div className="flex items-center gap-4">
            <Link
              to="/flowcharts/venomous"
              className="hidden sm:inline-flex items-center gap-2 text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Voltar para Animais Peçonhentos</span>
            </Link>
          </div>

          <div className="text-center space-y-4">
            <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-violet-600 dark:from-purple-400 dark:to-violet-400">
              Acidente Crotálico
            </h1>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Fluxograma para manejo de acidentes crotálicos em pediatria
            </p>
          </div>

          <ScrollArea className="h-[calc(100vh-300px)] pr-4">
            <div className="space-y-6">
              {renderContent()}
              <CrotalicSpecialConsiderations />
            </div>
          </ScrollArea>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default CrotalicFlowchart;
