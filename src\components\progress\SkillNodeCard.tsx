import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { ChevronDown, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

interface SkillNodeCardProps {
  name: string;
  progress: number;
  isExpanded: boolean;
  hasChildren: boolean;
  onToggle: () => void;
  level: number;
}

export const SkillNodeCard = ({
  name,
  progress,
  isExpanded,
  hasChildren,
  onToggle,
  level
}: SkillNodeCardProps) => {
  const getProgressColor = (progress: number) => {
    if (progress >= 70) return "bg-green-500";
    if (progress >= 40) return "bg-yellow-500";
    return "bg-red-500";
  };

  const progressColor = getProgressColor(progress);
  const formattedProgress = Math.round(progress);

  return (
    <Card className={`bg-gradient-to-r from-gray-50 to-white ${level > 0 ? 'ml-4' : ''}`}>
      <CardHeader className="py-3">
        <div className="text-sm font-medium flex items-center justify-between">
          <div className="flex items-center gap-2">
            {hasChildren && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggle}
                className="p-1 h-6 w-6"
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
            )}
            <span>{name}</span>
          </div>
          <span className="text-sm font-normal text-gray-500">
            {formattedProgress}%
          </span>
        </div>
      </CardHeader>
      <CardContent className="py-2">
        <Progress 
          value={formattedProgress} 
          className={`h-2 ${progressColor}`} 
        />
      </CardContent>
    </Card>
  );
};