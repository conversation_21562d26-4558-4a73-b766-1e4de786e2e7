
-- Create admin_user_permissions table if it doesn't exist
CREATE TABLE IF NOT EXISTS admin_user_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  resource TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(user_id, resource)
);

-- Create an index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS admin_user_permissions_user_id_idx ON admin_user_permissions(user_id);

-- Enable RLS
ALTER TABLE admin_user_permissions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Admins can read admin_user_permissions"
  ON admin_user_permissions FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND profiles.is_admin = true
    )
  );

CREATE POLICY "Only super admins can insert admin_user_permissions"
  ON admin_user_permissions FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM admin_user_roles
      WHERE admin_user_roles.user_id = auth.uid() 
      AND admin_user_roles.role_id = 'super_admin'
    )
  );

CREATE POLICY "Only super admins can update admin_user_permissions"
  ON admin_user_permissions FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM admin_user_roles
      WHERE admin_user_roles.user_id = auth.uid() 
      AND admin_user_roles.role_id = 'super_admin'
    )
  );

CREATE POLICY "Only super admins can delete admin_user_permissions"
  ON admin_user_permissions FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM admin_user_roles
      WHERE admin_user_roles.user_id = auth.uid() 
      AND admin_user_roles.role_id = 'super_admin'
    )
  );
