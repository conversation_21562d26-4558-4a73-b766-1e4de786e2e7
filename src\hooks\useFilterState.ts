import { useState, useCallback } from 'react';
import { SelectedFilters, FilterOption, filterTypeToKey } from '@/components/filters/types';

export const useFilterState = (initialFilters: SelectedFilters) => {
  const [selectedFilters, setSelectedFilters] = useState<SelectedFilters>(initialFilters);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const handleToggleFilter = useCallback((id: string, type: FilterOption['type']) => {
    const filterKey = filterTypeToKey(type);
    const currentFilters = selectedFilters[filterKey] || [];

    const newFilters = currentFilters.includes(id)
      ? currentFilters.filter(filterId => filterId !== id)
      : [...currentFilters, id];

    setSelectedFilters(prev => ({
      ...prev,
      [filterKey]: newFilters
    }));
  }, [selectedFilters]);

  const toggleExpand = useCallback((id: string) => {
    setExpandedItems(prev => 
      prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id]
    );
  }, []);

  return {
    selectedFilters,
    setSelectedFilters,
    expandedItems,
    handleToggleFilter,
    toggleExpand
  };
};
