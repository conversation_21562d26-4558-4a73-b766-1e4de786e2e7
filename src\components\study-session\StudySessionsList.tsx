
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { SessionCard } from "./SessionCard";
import { But<PERSON> } from "@/components/ui/button";
import { History, Book, BookOpenCheck } from "lucide-react";
import { 
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { motion } from "framer-motion";
import { useIsMobile } from "@/hooks/use-mobile";

const SESSIONS_PER_PAGE = 3;

interface Session {
  id: string;
  started_at: string;
  total_questions: number;
  current_question_index: number;
  questions: string[];
  stats: {
    correct_answers: number;
    total_questions: number;
  };
  status: 'in_progress' | 'completed';
  specialty_name?: string;
  title?: string;
}

export const StudySessionsList = () => {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalSessions, setTotalSessions] = useState(0);
  const navigate = useNavigate();
  const { toast } = useToast();
  const isMobile = useIsMobile();

  useEffect(() => {
    fetchSessions();
  }, [currentPage]);

  const fetchSessions = async () => {
    console.log('📋 [StudySessionsList] Buscando sessões de estudo...');
    try {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) return;

      // Primeiro, obter o total de sessões para paginação
      const { count } = await supabase
        .from("study_sessions")
        .select("*", { count: 'exact', head: true })
        .eq("user_id", userData.user.id);

      setTotalSessions(count || 0);
      console.log('📊 [StudySessionsList] Total de sessões:', count);

      // Depois, buscar as sessões da página atual
      const { data: sessionsData, error: sessionsError } = await supabase
        .from("study_sessions")
        .select(`
          *,
          specialty_id,
          title
        `)
        .eq("user_id", userData.user.id)
        .order("started_at", { ascending: false })
        .range((currentPage - 1) * SESSIONS_PER_PAGE, currentPage * SESSIONS_PER_PAGE - 1);

      if (sessionsError) throw sessionsError;

      const processedSessions = await Promise.all(
        (sessionsData || []).map(async (session) => {
          let specialtyName;
          if (session.specialty_id) {
            const { data: specialtyData } = await supabase
              .from("study_categories")
              .select("name")
              .eq("id", session.specialty_id)
              .single();
            specialtyName = specialtyData?.name;
          }

          return {
            id: session.id,
            started_at: session.started_at,
            total_questions: session.questions?.length || 0,
            current_question_index: session.current_question_index || 0,
            questions: session.questions || [],
            status: session.status as 'in_progress' | 'completed',
            stats: {
              correct_answers: session.total_correct || 0,
              total_questions: session.total_questions || session.questions?.length || 0
            },
            specialty_name: specialtyName,
            title: session.title
          };
        })
      );

      setSessions(processedSessions);
    } catch (error: any) {
      console.error("❌ [StudySessionsList] Erro ao carregar sessões:", error);
      toast({
        title: "Erro ao carregar sessões",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (sessionId: string) => {
    try {
      console.log('🗑️ [StudySessionsList] Deletando sessão:', sessionId);
      
      // Primeiro deletar os eventos da sessão
      const { error: eventsError } = await supabase
        .from("session_events")
        .delete()
        .eq("session_id", sessionId);

      if (eventsError) throw eventsError;

      // Depois deletar a sessão
      const { error } = await supabase
        .from("study_sessions")
        .delete()
        .eq("id", sessionId);

      if (error) throw error;

      setSessions(prev => prev.filter(s => s.id !== sessionId));
      toast({
        title: "Sessão removida",
        description: "A sessão foi removida com sucesso"
      });

      // Se a página atual ficar vazia, voltar para a página anterior
      if (sessions.length === 1 && currentPage > 1) {
        setCurrentPage(prev => prev - 1);
      } else {
        // Recarregar a página atual
        fetchSessions();
      }
    } catch (error: any) {
      console.error("❌ [StudySessionsList] Erro ao deletar sessão:", error);
      toast({
        title: "Erro ao deletar sessão",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const totalPages = Math.ceil(totalSessions / SESSIONS_PER_PAGE);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="animate-pulse text-primary">Carregando...</div>
      </div>
    );
  }

  if (sessions.length === 0 && currentPage === 1) {
    return (
      <div className="bg-gradient-to-br from-[#f7f9fa] to-[#e9f6ff] hover:shadow-lg transition-all rounded-xl p-4 sm:p-6 border-0">
        <div className="flex items-center justify-between mb-4 sm:mb-6">
          <div className="flex items-center gap-2 sm:gap-3">
            <div className="p-2 sm:p-3 bg-primary rounded-full">
              <History className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
            </div>
            <h2 className="text-lg sm:text-xl font-bold text-gray-800">Histórico de Sessões</h2>
          </div>
        </div>

        <div className="flex flex-col items-center justify-center py-6 sm:py-8 text-center">
          <div className="p-3 sm:p-4 rounded-full bg-gray-50 mb-3 sm:mb-4">
            <Book className="h-10 w-10 sm:h-12 sm:w-12 text-primary/60" />
          </div>
          <h3 className="text-lg sm:text-xl font-medium text-gray-700 mb-2">
            Nenhuma sessão de estudo encontrada
          </h3>
          <p className="text-sm sm:text-base text-gray-500 mb-4 sm:mb-6 max-w-md">
            Inicie sua jornada de estudos respondendo questões e acompanhe seu progresso.
          </p>
          <Button 
            onClick={() => navigate("/questions")} 
            className="bg-primary hover:bg-primary/90 text-white font-medium gap-2 text-sm"
          >
            <BookOpenCheck className="h-4 w-4" />
            Iniciar primeira sessão
          </Button>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
    >
      <div className="bg-gradient-to-br from-[#f7f9fa] to-[#e9f6ff] hover:shadow-lg transition-all rounded-xl p-4 sm:p-6 border-0">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-0 mb-4 sm:mb-6">
          <div className="flex items-center gap-2 sm:gap-3">
            <div className="p-2 sm:p-3 bg-primary rounded-full">
              <History className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
            </div>
            <h2 className="text-lg sm:text-xl font-bold text-gray-800">Histórico de Sessões</h2>
          </div>
          <Button 
            onClick={() => navigate("/questions")} 
            className="bg-primary hover:bg-primary/90 text-white text-xs sm:text-sm h-8 sm:h-10 px-2 sm:px-4"
          >
            <BookOpenCheck className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
            Nova Sessão
          </Button>
        </div>
        
        <div className="space-y-3 sm:space-y-4">
          {sessions.map((session, index) => (
            <motion.div 
              key={session.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * index }}
            >
              <SessionCard
                session={session}
                onDelete={handleDelete}
                onNavigate={(id) => navigate(`/questions/${id}`)}
              />
            </motion.div>
          ))}
        </div>
        
        {totalPages > 1 && (
          <div className="mt-4 sm:mt-6 flex justify-center">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious 
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    aria-disabled={currentPage === 1}
                    className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                  />
                </PaginationItem>
                <PaginationItem className="flex items-center px-2 sm:px-4">
                  <span className="text-xs sm:text-sm text-muted-foreground">
                    {isMobile ? `${currentPage}/${totalPages}` : `Página ${currentPage} de ${totalPages}`}
                  </span>
                </PaginationItem>
                <PaginationItem>
                  <PaginationNext 
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    aria-disabled={currentPage === totalPages}
                    className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </div>
    </motion.div>
  );
};
