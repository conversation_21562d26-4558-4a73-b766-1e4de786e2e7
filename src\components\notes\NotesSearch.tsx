import React from "react";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";

interface NotesSearchProps {
  onSearch: (term: string) => void;
}

export const NotesSearch: React.FC<NotesSearchProps> = ({ onSearch }) => {
  return (
    <div className="relative">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
      <Input
        type="search"
        placeholder="Buscar anotações..."
        className="pl-10"
        onChange={(e) => onSearch(e.target.value)}
      />
    </div>
  );
};