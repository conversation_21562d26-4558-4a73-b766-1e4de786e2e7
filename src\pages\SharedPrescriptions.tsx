
import { useNavigate } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { useSession } from "@supabase/auth-helpers-react";
import { SharedPrescriptionContent } from "@/components/prescriptions/shared/SharedPrescriptionContent";
import { getThemeClasses } from "@/components/ui/theme-utils";

export default function SharedPrescriptions() {
  const navigate = useNavigate();
  const session = useSession();

  return (
    <div className={getThemeClasses.pageBackground()}>
      <Header />
      <div className="flex-1 container mx-auto py-8 px-4">
        <Button
          variant="ghost"
          className="mb-6 text-primary hover:text-primary/80 transition-colors dark:text-blue-400 dark:hover:text-blue-300"
          onClick={() => navigate("/prescriptions")}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Voltar para prescrições
        </Button>

        <SharedPrescriptionContent userId={session?.user?.id} />
      </div>
      <Footer />
    </div>
  );
}
