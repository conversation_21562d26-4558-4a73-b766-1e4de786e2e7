import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { diagnosis, patientData } = await req.json();

    const prompt = `Atue como um médico especialista realizando uma análise detalhada do caso. Com base nas seguintes informações:

Paciente:
- Idade: ${patientData.age} anos
- Gênero: ${patientData.gender === 'male' ? 'Masculino' : 'Feminino'}
${patientData.gender === 'female' && patientData.isPregnant !== undefined ? `- Gestante: ${patientData.isPregnant ? 'Sim' : 'Não'}` : ''}
- Sintomas: ${patientData.symptoms.join(', ')}
${patientData.manualSymptoms ? `- Sintomas adicionais: ${patientData.manualSymptoms}` : ''}
${patientData.hasChronicDiseases ? `- Doenças crônicas: ${patientData.chronicDiseases}` : '- Sem doenças crônicas'}
- Intensidade dos sintomas: ${patientData.symptomsIntensity}/10
${patientData.hasRecentExams ? `- Exames recentes: ${patientData.examDetails}` : '- Sem exames recentes'}

Diagnóstico selecionado:
- Condição: ${diagnosis.condition}
- Probabilidade: ${diagnosis.probability}%
- Quadro clínico: ${diagnosis.clinicalPresentation}
- Exames solicitados: ${diagnosis.exams}
- Tratamento recomendado: ${diagnosis.treatment}

Por favor, forneça uma análise detalhada do caso incluindo:

1. Discussão aprofundada do diagnóstico
2. Justificativa para o tratamento escolhido
3. Prognóstico esperado
4. Possíveis complicações
5. Recomendações de acompanhamento
6. Critérios para reavaliação
7. Medidas preventivas recomendadas

Formate a resposta de maneira clara e organizada, usando subtítulos para cada seção.`;


    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { 
            role: 'system', 
            content: 'Você é um médico especialista realizando análises detalhadas de casos clínicos. Forneça análises aprofundadas e bem fundamentadas, baseadas em evidências científicas atuais.' 
          },
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    const analysis = data.choices[0].message.content;

    return new Response(JSON.stringify({ analysis }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error in generate-detailed-analysis function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});