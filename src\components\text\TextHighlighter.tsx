import React, { useState, useEffect, useCallback } from 'react';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface HighlightData {
  text: string;
  color: string;
  id: string;
}

interface TextHighlighterProps {
  children: React.ReactNode;
  className?: string;
}

const HIGHLIGHT_COLORS = [
  { name: '<PERSON><PERSON>', value: 'bg-yellow-200' },
  { name: 'Verde', value: 'bg-green-200' },
  { name: 'Azu<PERSON>', value: 'bg-blue-200' },
  { name: '<PERSON>', value: 'bg-pink-200' },
];

export const TextHighlighter: React.FC<TextHighlighterProps> = ({ children, className }) => {
  const [highlights, setHighlights] = useState<HighlightData[]>([]);
  const [selection, setSelection] = useState<{
    text: string;
    range: Range;
    top: number;
    left: number;
  } | null>(null);

  useEffect(() => {
    const savedHighlights = localStorage.getItem('textHighlights');
    if (savedHighlights) {
      setHighlights(JSON.parse(savedHighlights));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('textHighlights', JSON.stringify(highlights));
  }, [highlights]);

  const handleTextSelection = useCallback((e: MouseEvent) => {
    const target = e.target as HTMLElement;
    const statementContainer = target.closest('[data-highlighter="statement"]');

    // Só processa seleção se estiver dentro do container do enunciado
    if (!statementContainer || !target.closest('[data-highlighter="statement"]')) {
      return;
    }

    const windowSelection = window.getSelection();
    if (!windowSelection || windowSelection.isCollapsed) {
      return;
    }

    const range = windowSelection.getRangeAt(0);
    const text = windowSelection.toString().trim();

    if (!text) {
      return;
    }

    const rect = range.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

    setSelection({
      text,
      range,
      top: rect.top + scrollTop - 40,
      left: rect.left
    });
  }, []);

  const applyHighlight = useCallback((color: string) => {
    if (!selection) return;

    const range = selection.range;
    const highlightId = Math.random().toString(36).substring(7);
    
    const span = document.createElement('span');
    span.className = cn('px-1 rounded transition-colors inline-flex items-center group', color);
    span.dataset.highlightId = highlightId;
    
    const removeButton = document.createElement('button');
    removeButton.className = 'ml-0.5 text-gray-500 hover:text-gray-700 text-xs leading-none opacity-0 group-hover:opacity-100 transition-opacity';
    removeButton.innerHTML = '×';
    removeButton.onclick = (e) => {
      e.stopPropagation();
      removeHighlight(highlightId);
    };
    
    const fragment = range.extractContents();
    span.appendChild(fragment);
    span.appendChild(removeButton);
    
    range.insertNode(span);
    
    setHighlights(prev => [...prev, {
      text: selection.text,
      color,
      id: highlightId
    }]);

    window.getSelection()?.removeAllRanges();
    setSelection(null);
  }, [selection]);

  const removeHighlight = useCallback((id: string) => {
    const span = document.querySelector(`[data-highlight-id="${id}"]`);
    if (span) {
      const text = span.childNodes[0].textContent || '';
      const parent = span.parentNode;
      if (parent) {
        const textNode = document.createTextNode(text);
        parent.replaceChild(textNode, span);
        setHighlights(prev => prev.filter(h => h.id !== id));
      }
    }
  }, []);

  useEffect(() => {
    document.addEventListener('mouseup', handleTextSelection);
    return () => {
      document.removeEventListener('mouseup', handleTextSelection);
    };
  }, [handleTextSelection]);

  const handleClosePopup = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    window.getSelection()?.removeAllRanges();
    setSelection(null);
  }, []);

  return (
    <div className={cn("relative", className)}>
      {children}
      
      {selection && (
        <div 
          className="fixed z-50 bg-white rounded-lg shadow-lg border p-2 flex gap-2 items-center animate-in fade-in slide-in-from-top-1"
          style={{
            top: selection.top,
            left: selection.left,
          }}
        >
          <div className="flex gap-2">
            {HIGHLIGHT_COLORS.map((color) => (
              <div
                key={color.value}
                onClick={() => applyHighlight(color.value)}
                className={cn(
                  "w-6 h-6 rounded transition-transform hover:scale-110 cursor-pointer",
                  color.value
                )}
                title={color.name}
              />
            ))}
          </div>

          <button
            onClick={handleClosePopup}
            className="p-1 hover:bg-gray-100 rounded cursor-pointer"
            title="Fechar"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      )}
    </div>
  );
};