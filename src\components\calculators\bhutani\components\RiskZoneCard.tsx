import { Card } from "@/components/ui/card";

interface RiskZoneCardProps {
  zone: string;
  conduct: string;
  colorClass: string;
}

export function RiskZoneCard({ zone, conduct, colorClass }: RiskZoneCardProps) {
  return (
    <Card className={`p-4 ${colorClass} border-l-4 shadow-sm animate-in fade-in duration-300`}>
      <h4 className="font-semibold text-lg mb-2">{zone}</h4>
      <p className="text-sm text-gray-700">{conduct}</p>
    </Card>
  );
}