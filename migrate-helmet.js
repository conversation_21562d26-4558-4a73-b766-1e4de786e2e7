import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Diretórios a serem verificados
const directories = [
  'src/components',
  'src/pages'
];

// Função para verificar se um arquivo contém importações do react-helmet
function checkFileForHelmet(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return {
      hasHelmet: content.includes('import { Helmet } from "react-helmet"') ||
                 content.includes("import { Helmet } from 'react-helmet'") ||
                 content.includes('import Helmet from "react-helmet"') ||
                 content.includes("import Helmet from 'react-helmet'"),
      content
    };
  } catch (error) {
    console.error(`Erro ao ler o arquivo ${filePath}:`, error);
    return { hasHelmet: false, content: '' };
  }
}

// Função para percorrer diretórios recursivamente
function walkDir(dir, callback) {
  fs.readdirSync(dir).forEach(f => {
    const dirPath = path.join(dir, f);
    const isDirectory = fs.statSync(dirPath).isDirectory();
    isDirectory ? walkDir(dirPath, callback) : callback(path.join(dir, f));
  });
}

// Lista de arquivos que usam react-helmet
const filesWithHelmet = [];

// Percorrer diretórios e verificar arquivos
directories.forEach(dir => {
  walkDir(dir, (filePath) => {
    if (filePath.endsWith('.tsx') || filePath.endsWith('.jsx')) {
      const { hasHelmet, content } = checkFileForHelmet(filePath);
      if (hasHelmet) {
        filesWithHelmet.push({ filePath, content });
      }
    }
  });
});

// Função para migrar um arquivo
function migrateFile(file) {
  try {
    let content = file.content;

    // Substituir importação
    content = content.replace(
      /import\s+(?:{\s*)?Helmet(?:\s*})?\s+from\s+['"]react-helmet['"]/g,
      `import HelmetWrapper from "@/components/utils/HelmetWrapper"`
    );

    // Substituir tags
    content = content.replace(/<Helmet(\s|>)/g, '<HelmetWrapper$1');
    content = content.replace(/<\/Helmet>/g, '</HelmetWrapper>');

    // Salvar arquivo
    fs.writeFileSync(file.filePath, content, 'utf8');
    return true;
  } catch (error) {
    console.error(`Erro ao migrar o arquivo ${file.filePath}:`, error);
    return false;
  }
}

// Migrar arquivos
console.log('Migrando arquivos...');
let successCount = 0;
let failCount = 0;

filesWithHelmet.forEach(file => {
  const success = migrateFile(file);
  if (success) {
    console.log(`✅ Migrado: ${file.filePath}`);
    successCount++;
  } else {
    console.log(`❌ Falha: ${file.filePath}`);
    failCount++;
  }
});

console.log(`\nMigração concluída: ${successCount} arquivos migrados com sucesso, ${failCount} falhas.`);
console.log('\nLembre-se de verificar se o HelmetProvider está configurado no App.tsx');
