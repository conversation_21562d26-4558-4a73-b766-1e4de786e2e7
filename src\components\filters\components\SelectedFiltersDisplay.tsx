import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";
import type { SelectedFilters } from "@/types/question";

interface SelectedFiltersDisplayProps {
  selectedFilters: SelectedFilters;
  availableFilters: {
    specialties: any[];
    themes: any[];
    focuses: any[];
    locations: any[];
    years: number[];
  };
  onRemoveFilter: (id: string, type: keyof SelectedFilters) => void;
}

export function SelectedFiltersDisplay({
  selectedFilters,
  availableFilters,
  onRemoveFilter
}: SelectedFiltersDisplayProps) {
  const getItemName = (id: string, type: keyof SelectedFilters) => {
    if (!availableFilters || !id) return id;
    
    if (type === 'years') return id;
    
    const items = availableFilters[type] || [];
    const item = items.find((i: any) => i.id === id);
    
    return item?.name || id;
  };

  const renderFilterBadges = (type: keyof SelectedFilters) => {
    return (selectedFilters[type] || []).map((id: string) => (
      <Badge key={`${type}-${id}`} variant="secondary" className="px-3 py-1">
        {getItemName(id, type)}
        <X
          className="h-3 w-3 ml-2 cursor-pointer"
          onClick={() => onRemoveFilter(id, type)}
        />
      </Badge>
    ));
  };

  return (
    <div className="flex flex-wrap gap-2">
      {renderFilterBadges('specialties')}
      {renderFilterBadges('themes')}
      {renderFilterBadges('focuses')}
      {renderFilterBadges('locations')}
      {renderFilterBadges('years')}
    </div>
  );
}
