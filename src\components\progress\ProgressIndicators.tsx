
import { Card, CardContent } from "@/components/ui/card";
import { Target, Calendar, Clock, CalendarDays } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface ProgressIndicatorsProps {
  stats: {
    totalQuestions: number;
    correctAnswers: number;
    streakDays: number;
    averageTimePerQuestion: number;
  };
}

export const ProgressIndicators = ({ stats }: ProgressIndicatorsProps) => {
  const accuracy = stats.totalQuestions > 0 
    ? (stats.correctAnswers / stats.totalQuestions) * 100 
    : 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      {/* Accuracy Card */}
      <Card className="border-0 shadow-md bg-white overflow-hidden">
        <CardContent className="p-0">
          <div className="flex flex-col">
            <div className="p-4 bg-[#58CC02] flex items-center justify-center">
              <Target className="h-8 w-8 text-white" />
            </div>
            <div className="p-4 text-center">
              <p className="text-sm font-medium text-gray-500">Precisão Geral</p>
              <div className="flex items-end justify-center gap-1 mt-1">
                <p className="text-3xl font-bold text-gray-800">{accuracy.toFixed(1)}%</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Streak Card */}
      <Card className="border-0 shadow-md bg-white overflow-hidden">
        <CardContent className="p-0">
          <div className="flex flex-col">
            <div className="p-4 bg-[#FF9600] flex items-center justify-center">
              <Calendar className="h-8 w-8 text-white" />
            </div>
            <div className="p-4 text-center">
              <p className="text-sm font-medium text-gray-500">Sequência de Estudos</p>
              <div className="flex items-end justify-center gap-1 mt-1">
                <p className="text-3xl font-bold text-gray-800">{stats.streakDays}</p>
                <p className="text-lg text-gray-600 mb-1">dias</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Average Time Card */}
      <Card className="border-0 shadow-md bg-white overflow-hidden">
        <CardContent className="p-0">
          <div className="flex flex-col">
            <div className="p-4 bg-[#1CB0F6] flex items-center justify-center">
              <Clock className="h-8 w-8 text-white" />
            </div>
            <div className="p-4 text-center relative">
              <div className="flex items-center justify-center gap-2">
                <p className="text-sm font-medium text-gray-500">Tempo Médio por Questão</p>
                <Badge className="bg-blue-100 text-blue-600 absolute top-1 right-2">
                  <CalendarDays className="h-3 w-3 mr-1" />
                  Cronograma
                </Badge>
              </div>
              <div className="flex items-end justify-center gap-1 mt-2">
                <p className="text-3xl font-bold text-gray-800">{Math.round(stats.averageTimePerQuestion / 60)}</p>
                <p className="text-lg text-gray-600 mb-1">min</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
