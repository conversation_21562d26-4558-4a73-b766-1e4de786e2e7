import { useEffect } from 'react';

/**
 * Componente para desativar o zoom por toque duplo em dispositivos móveis
 * Este componente não renderiza nada, apenas adiciona event listeners
 * Modificado para permitir seleção de texto
 */
const DisableZoom = () => {
  useEffect(() => {
    // Função para prevenir o zoom apenas em elementos não-texto
    const handleTouchEvent = (e: TouchEvent) => {
      // Se for um gesto de múltiplos toques (pinça)
      if (e.touches.length > 1) {
        // Verificar se o elemento alvo é um elemento de texto
        const target = e.target as HTMLElement;
        const isTextElement =
          target.tagName === 'INPUT' ||
          target.tagName === 'TEXTAREA' ||
          target.isContentEditable ||
          target.classList.contains('selectable-text') ||
          target.closest('.selectable-text');

        // Apenas prevenir o comportamento padrão se não for um elemento de texto
        if (!isTextElement) {
          e.preventDefault();
        }
      }
    };

    // Adicionar event listeners para prevenir zoom
    document.addEventListener('touchstart', handleTouchEvent, { passive: false });
    document.addEventListener('touchmove', handleTouchEvent, { passive: false });

    // Limpar event listeners quando o componente for desmontado
    return () => {
      document.removeEventListener('touchstart', handleTouchEvent);
      document.removeEventListener('touchmove', handleTouchEvent);
    };
  }, []);

  // Este componente não renderiza nada
  return null;
};

export default DisableZoom;
