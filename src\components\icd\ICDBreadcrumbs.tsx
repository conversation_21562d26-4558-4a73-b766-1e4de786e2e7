
import { Button } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";

interface ICDBreadcrumbsProps {
  breadcrumbs: Array<{ id: string; name: string }>;
  onBreadcrumbClick: (index: number) => void;
  onHomeClick: () => void;
}

export const ICDBreadcrumbs = ({
  breadcrumbs,
  onBreadcrumbClick,
  onHomeClick,
}: ICDBreadcrumbsProps) => {
  return (
    <div className="flex items-center gap-2 overflow-x-auto max-w-full py-2 scrollbar-thin scrollbar-thumb-primary/10 dark:scrollbar-thumb-primary/20 scrollbar-track-transparent">
      <Button
        variant="ghost"
        className="hover:bg-primary/5 dark:hover:bg-primary/10 text-gray-800 dark:text-gray-200 shrink-0"
        onClick={onHomeClick}
      >
        Início
      </Button>
      {breadcrumbs.map((crumb, index) => (
        <div key={crumb.id} className="flex items-center gap-2">
          <ChevronRight className="h-4 w-4 text-muted-foreground dark:text-gray-400 shrink-0" />
          <Button
            variant="ghost"
            className="hover:bg-primary/5 dark:hover:bg-primary/10 text-gray-800 dark:text-gray-200 truncate max-w-[200px]"
            onClick={() => onBreadcrumbClick(index + 1)}
          >
            {crumb.name}
          </Button>
        </div>
      ))}
    </div>
  );
};
