
import React, { useMemo } from 'react';
import { cn } from '@/lib/utils';
import { processHtmlContent } from '@/utils/htmlProcessor';

interface HtmlRendererProps {
  htmlContent: string;
  className?: string;
}

export const HtmlRenderer: React.FC<HtmlRendererProps> = ({
  htmlContent,
  className
}) => {


  const cleanedHtml = useMemo(() => {
    const processed = processHtmlContent(htmlContent);
    return processed;
  }, [htmlContent]);

  // Função para formatar o HTML com quebras de linha
  const formatWithLineBreaks = (content: string): string => {
    return content.replace(/\n/g, '<br />');
  };

  // Usando dangerouslySetInnerHTML com quebras de linha convertidas para <br />
  return (
    <div
      className={cn(
        "prose max-w-none whitespace-pre-line font-sans leading-relaxed",
        className
      )}
      dangerouslySetInnerHTML={{ __html: formatWithLineBreaks(cleanedHtml) }}
    />
  );
};
