import React from 'react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { ExternalLink } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { NewsItem } from '@/types/newsletter';

interface NewsCardProps {
  news: NewsItem;
  compact?: boolean;
}

export const NewsCard: React.FC<NewsCardProps> = ({ news, compact = false }) => {
  const formatDate = (dateString?: string | null) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return format(date, "d 'de' MMMM, yyyy", { locale: ptBR });
    } catch (error) {
      return '';
    }
  };

  const handleOpenLink = () => {
    if (news.link) {
      window.open(news.link, '_blank', 'noopener,noreferrer');
    }
  };

  const renderCategories = () => {
    if (!news.category) return null;

    const categories = news.category.split(',').map(c => c.trim());

    // Limitar a 3 categorias para não sobrecarregar o layout
    const displayCategories = categories.slice(0, 3);

    return (
      <div className="flex flex-wrap gap-1.5 mt-2">
        {displayCategories.map((category, index) => (
          <Badge
            key={index}
            variant="outline"
            className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800/50 px-2 py-0.5 text-xs"
          >
            {category}
          </Badge>
        ))}
        {categories.length > 3 && (
          <Badge
            variant="outline"
            className="bg-gray-50 text-gray-600 border-gray-200 dark:bg-gray-800/30 dark:text-gray-400 dark:border-gray-700/50 px-2 py-0.5 text-xs"
          >
            +{categories.length - 3}
          </Badge>
        )}
      </div>
    );
  };

  if (compact) {
    return (
      <Card className="overflow-hidden hover:shadow-md transition-shadow">
        <CardContent className="p-4">
          <div className="flex flex-col space-y-2">
            <h3 className="font-semibold text-gray-900 dark:text-gray-100 line-clamp-2">{news.title}</h3>
            <div className="flex items-center justify-between">
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {news.source && <span>{news.source} • </span>}
                {formatDate(news.pub_date)}
              </div>
              {news.link && (
                <Button variant="ghost" size="sm" className="p-1 h-auto" onClick={handleOpenLink}>
                  <ExternalLink className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow h-full flex flex-col">
      <div className="flex flex-col flex-1">
        {news.image_url && (
          <div className="w-full h-48 overflow-hidden">
            <img
              src={news.image_url}
              alt={news.title}
              className="w-full h-full object-cover"
              onError={(e) => {
                (e.target as HTMLImageElement).style.display = 'none';
              }}
            />
          </div>
        )}
        <CardContent className="p-5 flex flex-col w-full flex-1">
          <div className="flex-1">
            <div className="flex items-center justify-between mb-2">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {news.source && <span className="font-medium">{news.source}</span>}
                {news.pub_date && news.source && <span className="mx-1">•</span>}
                {news.pub_date && <span>{formatDate(news.pub_date)}</span>}
              </div>
            </div>
            <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2">{news.title}</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4 leading-relaxed text-sm text-justify">{news.summary}</p>
            {renderCategories()}
          </div>

          {news.link && (
            <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-800">
              <Button
                variant="outline"
                size="sm"
                className="w-full text-blue-600 dark:text-blue-400 border-blue-200 dark:border-blue-800/50 hover:bg-blue-50 dark:hover:bg-blue-900/20 font-medium"
                onClick={handleOpenLink}
              >
                <ExternalLink className="h-3.5 w-3.5 mr-1.5" />
                Ler artigo
              </Button>
            </div>
          )}
        </CardContent>
      </div>
    </Card>
  );
};
