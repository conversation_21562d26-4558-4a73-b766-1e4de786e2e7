
import { MedicationSidebar } from "../MedicationSidebar";
import { MedicationHeader } from "../MedicationHeader";
import { MedicationInfo } from "./MedicationInfo";
import { MedicationDashboard } from "../MedicationDashboard";
import { MedicationSkeleton } from "@/components/ui/MedicationSkeleton";
import { motion, AnimatePresence } from "framer-motion";

interface DesktopViewProps {
  categories: any[];
  currentMedicationId: string;
  isLoading: boolean;
  medication: any;
  weight: number;
  displayWeight: number;
  setTempWeight: (weight: number) => void;
  setWeight: (weight: number) => void;
  age: number;
  setAge: (age: number) => void;
  slug?: string; // Adicionar slug para controlar melhor a renderização
}

export const DesktopView = ({
  categories,
  currentMedicationId,
  isLoading,
  medication,
  weight,
  displayWeight,
  setTempWeight,
  setWeight,
  age,
  setAge,
  slug,
}: DesktopViewProps) => {
  return (
    <div className="flex gap-6">
      <MedicationSidebar 
        categories={categories} 
        currentMedicationId={currentMedicationId}
      />

      {!slug ? (
        <MedicationDashboard />
      ) : isLoading ? (
        <MedicationSkeleton />
      ) : medication ? (
        <motion.div
          key={medication.id}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.4, ease: "easeOut" }}
          className="flex-1 space-y-6"
        >
          <MedicationHeader
            name={medication.name}
            description={medication.description}
            brands={medication.brands}
            category={medication.pedbook_medication_categories?.name}
            slug={medication.slug}
            id={medication.id}
          />

          <MedicationInfo
            medication={medication}
            weight={weight}
            displayWeight={displayWeight}
            setTempWeight={setTempWeight}
            setWeight={setWeight}
            age={age}
            setAge={setAge}
          />
        </motion.div>
      ) : null}
    </div>
  );
};
