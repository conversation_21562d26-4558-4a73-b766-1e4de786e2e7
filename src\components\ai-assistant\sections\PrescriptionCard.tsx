import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Copy } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

interface PrescriptionCardProps {
  prescription: string;
  editablePrescription: string;
  onPrescriptionChange: (value: string) => void;
}

export function PrescriptionCard({ 
  prescription, 
  editablePrescription, 
  onPrescriptionChange 
}: PrescriptionCardProps) {
  const { toast } = useToast();

  const handleCopyPrescription = async () => {
    try {
      await navigator.clipboard.writeText(editablePrescription);
      toast({
        title: "Copiado!",
        description: "Prescrição copiada para a área de transferência",
      });
    } catch (err) {
      toast({
        title: "Erro ao copiar",
        description: "Não foi possível copiar a prescrição",
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="p-6 bg-white/80 backdrop-blur-sm border border-primary/20 shadow-lg animate-fade-in">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold bg-gradient-to-r from-primary to-primary/70 text-transparent bg-clip-text">
          Prescrição Médica
        </h3>
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
          onClick={handleCopyPrescription}
        >
          <Copy className="h-4 w-4" />
          Copiar
        </Button>
      </div>
      <Textarea
        value={editablePrescription}
        onChange={(e) => onPrescriptionChange(e.target.value)}
        className="font-mono text-sm min-h-[200px] mb-4"
      />
    </Card>
  );
}