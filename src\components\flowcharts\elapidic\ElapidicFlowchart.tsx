
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ArrowRight, AlertCircle, Info } from "lucide-react";
import { useElapidicFlow } from "./useElapidicFlow";
import { useToast } from "@/components/ui/use-toast";
import { ClinicalPicturesGrid } from "./components/ClinicalPicturesGrid";
import { TreatmentCard } from "./components/TreatmentCard";

export const ElapidicFlowchart: React.FC = () => {
  const { currentStage, setStage } = useElapidicFlow();
  const { toast } = useToast();

  const handleSelectPicture = (severity: 'mild' | 'moderate' | 'severe') => {
    switch (severity) {
      case 'mild':
        setStage('mildTreatment');
        break;
      case 'moderate':
        setStage('moderateTreatment');
        break;
      case 'severe':
        setStage('severeTreatment');
        break;
    }
  };

  const renderStage = () => {
    switch (currentStage) {
      case 'initial':
        return (
          <Card className="p-8 max-w-2xl mx-auto bg-white shadow-md border-2 border-gray-100 dark:bg-slate-800 dark:border-gray-700">
            <h2 className="text-2xl font-bold mb-6 flex items-center gap-2 text-gray-800 dark:text-gray-100">
              <Info className="h-6 w-6 text-blue-500 dark:text-blue-400" />
              Início
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-8">
              Suspeita de acidente com coral verdadeira (Micrurus sp.)
            </p>
            <div className="space-y-6">
              <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200">A coral foi identificada?</h3>
              <div className="flex flex-col gap-4">
                <Button
                  variant="outline"
                  onClick={() => setStage('unidentifiedCoral')}
                  className="w-full flex justify-between items-center bg-[#D3E4FD] hover:bg-[#BED6F6] text-gray-700 border-2 border-[#BED6F6] dark:bg-blue-900/30 dark:hover:bg-blue-900/50 dark:border-blue-800/50 dark:text-gray-100"
                >
                  <span>Coral NÃO identificada</span>
                  <ArrowRight className="h-4 w-4 ml-2 flex-shrink-0" />
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setStage('identifiedCoral')}
                  className="w-full flex justify-between items-center bg-[#F2FCE2] hover:bg-[#E8F7D4] text-gray-700 border-2 border-[#E8F7D4] dark:bg-green-900/30 dark:hover:bg-green-900/50 dark:border-green-800/50 dark:text-gray-100"
                >
                  <span>Coral identificada</span>
                  <ArrowRight className="h-4 w-4 ml-2 flex-shrink-0" />
                </Button>
              </div>
            </div>
          </Card>
        );

      case 'unidentifiedCoral':
      case 'identifiedCoral':
        return (
          <Card className="p-8 max-w-2xl mx-auto bg-white shadow-md border-2 border-gray-100 dark:bg-slate-800 dark:border-gray-700">
            <h2 className="text-2xl font-bold mb-6 flex items-center gap-2 text-gray-800 dark:text-gray-100">
              <AlertCircle className="h-6 w-6 text-amber-500 dark:text-amber-400" />
              {currentStage === 'unidentifiedCoral' ? 'Coral NÃO Identificada' : 'Coral Identificada'}
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-8">
              {currentStage === 'unidentifiedCoral'
                ? 'Paciente com suspeita de acidente por Micrurus sp., mas sem confirmação visual do animal.'
                : 'Paciente com confirmação visual de acidente por Micrurus sp.'}
            </p>
            <div className="space-y-6">
              <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200">O paciente apresenta sintomas de envenenamento?</h3>
              <div className="grid gap-4">
                <Button
                  onClick={() => setStage('clinicalPictures')}
                  className="w-full justify-between bg-[#FEF7CD] hover:bg-[#FDF2B8] text-gray-700 border-2 border-[#FDF2B8] dark:bg-yellow-900/30 dark:hover:bg-yellow-900/50 dark:border-yellow-800/50 dark:text-gray-100"
                >
                  Sim
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setStage('discharge');
                    toast({
                      title: "Alta com orientações",
                      description: "Orientar retorno imediato em caso de sintomas",
                    });
                  }}
                  className="w-full justify-between bg-[#F1F0FB] hover:bg-gray-100 text-gray-700 border-2 border-gray-200 dark:bg-slate-800 dark:hover:bg-slate-700 dark:border-gray-700 dark:text-gray-200"
                >
                  Não
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          </Card>
        );

      case 'clinicalPictures':
        return <ClinicalPicturesGrid onSelectPicture={handleSelectPicture} />;

      case 'mildTreatment':
        return (
          <TreatmentCard
            title="Quadro Leve"
            description="Parestesia e dor de intensidade variável SEM clínica de miastenia"
            treatment="Manter sintomáticos e observação por 24 horas. Monitorar sinais precoces de miastenia."
            severity="mild"
            evolutionQuestion="Houve evolução com sinais/sintomas de miastenia?"
            onEvolution={() => setStage('moderateTreatment')}
            onNoEvolution={() => setStage('discharge')}
          />
        );

      case 'moderateTreatment':
        return (
          <TreatmentCard
            title="Quadro Moderado"
            description="Miastenia aguda SEM paralisia"
            treatment="Administrar SAEL IV (5 ampolas). Monitorar continuamente."
            severity="moderate"
            evolutionQuestion="Teve evolução com sinais/sintomas de paralisia?"
            onEvolution={() => setStage('severeTreatment')}
            onNoEvolution={() => setStage('discharge')}
          />
        );

      case 'severeTreatment':
        return (
          <TreatmentCard
            title="Quadro Grave"
            description="Miastenia aguda COM paralisia"
            treatment="Administrar SAEL IV (10 ampolas). Prover terapia de suporte (ventilação mecânica, neostigmina precedida de atropina)."
            severity="severe"
            evolutionQuestion=""
            onEvolution={() => setStage('discharge')}
            onNoEvolution={() => setStage('discharge')}
          />
        );

      case 'discharge':
        return (
          <Card className="p-8 max-w-2xl mx-auto bg-white shadow-md border-2 border-gray-100 dark:bg-slate-800 dark:border-gray-700">
            <h2 className="text-2xl font-bold mb-6 text-gray-800 dark:text-gray-100">Alta do Paciente</h2>
            <div className="space-y-6">
              <div className="bg-[#F2FCE2] p-6 rounded-lg border-2 border-[#E8F7D4] dark:bg-green-900/30 dark:border-green-800/50 dark:text-gray-200">
                <p className="text-gray-700 dark:text-gray-200">Paciente estável. Orientar retorno imediato caso surjam novos sintomas.</p>
              </div>
              <Button
                variant="outline"
                onClick={() => setStage('initial')}
                className="w-full justify-between bg-[#F1F0FB] hover:bg-gray-100 text-gray-700 border-2 border-gray-200 dark:bg-slate-800 dark:hover:bg-slate-700 dark:border-gray-700 dark:text-gray-200"
              >
                Concluir Atendimento
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-5xl mx-auto p-4">
      {renderStage()}
    </div>
  );
};
