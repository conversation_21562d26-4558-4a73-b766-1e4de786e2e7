
import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";

import { QuestionComments } from "@/components/question/QuestionComments";
import { MessageSquare } from "lucide-react";
import { QuestionAlternatives } from "./QuestionAlternatives";
import { DiscursiveAnswer } from "./DiscursiveAnswer";
import { QuestionFeedback } from "./QuestionFeedback";

import { QuestionMetadata } from "./QuestionMetadata";
import { QuestionLikeButtons } from "./QuestionLikeButtons";
import type { Question, Comment } from "@/types/question";
import { TextHighlighter } from "@/components/text/TextHighlighter";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { QuestionImages } from "./QuestionImages";
import { FormattedContent } from "./FormattedContent";
import { convertJsonToComments, convertCommentsToJson } from "@/utils/commentHelpers";
import { Json } from "@/integrations/supabase/types/json";

interface QuestionCardProps {
  question: Question;
  selectedAnswer: string | null;
  hasAnswered: boolean;
  onSelectAnswer: (answer: string) => void;
  onSubmitAnswer?: (timeSpent: number) => Promise<void>;
  onNext?: () => void;
  userId: string;
  sessionId: string;
  isAnswered?: boolean;
  timeSpent: number;
}

export const QuestionCard: React.FC<QuestionCardProps> = ({
  question,
  selectedAnswer,
  hasAnswered,
  onSelectAnswer,
  onSubmitAnswer,
  onNext,
  userId,
  sessionId,
  isAnswered = false,
  timeSpent
}) => {
  const { toast } = useToast();
  const [showComments, setShowComments] = useState(false);
  const [newComment, setNewComment] = useState("");
  const [questionData, setQuestionData] = useState<Question>(question);
  const [discursiveAnswer, setDiscursiveAnswer] = useState("");
  const [internalHasAnswered, setInternalHasAnswered] = useState(false);
  const [showFeedback, setShowFeedback] = useState(false);

  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (hasAnswered || isAnswered) {
      setInternalHasAnswered(true);
    }
  }, [hasAnswered, isAnswered]);

  useEffect(() => {
    setQuestionData(question);
    // Limpar estado interno ao mudar de questão
    setInternalHasAnswered(false);
    setDiscursiveAnswer("");
    setShowComments(false);
    setNewComment("");
    setShowFeedback(false);
  }, [question.id]);

  const handleSubmit = async () => {
    if (isSubmitting) {
      return;
    }

    const answerType = question.question_format || question.answer_type;

    if (answerType === 'DISCURSIVE' && !discursiveAnswer.trim()) {
      // Validação silenciosa - o usuário pode ver que o campo está vazio
      return;
    }

    setIsSubmitting(true);
    try {
      let success = false;

      if (answerType === 'DISCURSIVE') {
        // Verificar se specialty_id existe, pois é NOT NULL na tabela
        if (!question.specialty?.id) {
          console.error('❌ [QuestionCard] Erro: Especialidade não encontrada para a questão', question.id);
          throw new Error("Especialidade é obrigatória para salvar a resposta");
        }

        console.log('📝 [QuestionCard] Salvando resposta discursiva com specialty_id:', question.specialty.id);

        const { error } = await supabase
          .from('user_answers')
          .insert({
            user_id: userId,
            question_id: question.id,
            session_id: sessionId,
            text_answer: discursiveAnswer,
            is_correct: true,
            time_spent: timeSpent,
            specialty_id: question.specialty.id,
            theme_id: question.theme?.id,
            focus_id: question.focus?.id,
            year: question.exam_year || question.year || new Date().getFullYear()
          });

        if (error) {
          console.error('❌ [QuestionCard] Erro ao salvar resposta discursiva:', error);
          throw error;
        }

        console.log('✅ [QuestionCard] Resposta discursiva salva com sucesso');
        success = true;
        setInternalHasAnswered(true);
        setShowFeedback(true);
      } else {
        // Para questões de múltipla escolha, delegar para o QuestionSolver
        if (onSubmitAnswer) {
          await onSubmitAnswer(timeSpent);
          success = true;
          setInternalHasAnswered(true);
          setShowFeedback(true);
        }
      }
    } catch (error) {
      console.error('❌ [QuestionCard] Erro durante o processo de submissão:', error);
      // Erro silencioso - usuário pode tentar novamente
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNext = async () => {
    if (onNext) {
      onNext();
      setShowFeedback(false);
      setShowComments(false);
      setNewComment("");
    }
  };

  const handleAddComment = async (text: string) => {
    try {
      const { data, error } = await supabase
        .from('questions')
        .select('comments')
        .eq('id', question.id)
        .single();

      if (error) throw error;

      // Convert JSON to Comment array
      const commentsArray = data.comments ?
        (Array.isArray(data.comments) ? data.comments : []) : [];

      setQuestionData(prev => ({
        ...prev,
        comments: commentsArray as Comment[]
      }));

    } catch (error) {
      console.error('❌ [QuestionCard] Error fetching updated comments:', error);
    }
  };

  const handleReplyComment = async (commentId: string | number, replyText: string) => {
    try {
      const newReply: Comment = {
        id: crypto.randomUUID(),
        text: replyText,
        user: userId,
        timestamp: new Date().toISOString(),
        likes: 0,
        dislikes: 0,
        likedBy: [],
        dislikedBy: []
      };

      const commentsArray = [...(questionData.comments || [])];
      const updatedComments = commentsArray.map(comment => {
        if (comment.id === commentId) {
          return {
            ...comment,
            replies: [...(comment.replies || []), newReply]
          };
        }
        return comment;
      });

      // Convert the comments to JSON format for database update
      const commentsJson = convertCommentsToJson(updatedComments);

      const { error } = await supabase
        .from('questions')
        .update({ comments: commentsJson })
        .eq('id', question.id);

      if (error) throw error;

      setQuestionData(prev => ({
        ...prev,
        comments: updatedComments
      }));

    } catch (error) {
      console.error('❌ [QuestionCard] Error adding reply:', error);
    }
  };

  const handleCommentLike = async (commentId: string | number, isLike: boolean) => {
    try {
      const commentsArray = [...(questionData.comments || [])];
      const updatedComments = commentsArray.map(comment => {
        if (comment.id === commentId) {
          const alreadyLiked = comment.likedBy?.includes(userId);
          const alreadyDisliked = comment.dislikedBy?.includes(userId);

          let newLikedBy = comment.likedBy || [];
          let newDislikedBy = comment.dislikedBy || [];
          let newLikes = comment.likes || 0;
          let newDislikes = comment.dislikes || 0;

          if (alreadyLiked) {
            newLikedBy = newLikedBy.filter(id => id !== userId);
            newLikes--;
          }
          if (alreadyDisliked) {
            newDislikedBy = newDislikedBy.filter(id => id !== userId);
            newDislikes--;
          }

          if (isLike && !alreadyLiked) {
            newLikedBy.push(userId);
            newLikes++;
          } else if (!isLike && !alreadyDisliked) {
            newDislikedBy.push(userId);
            newDislikes++;
          }

          return {
            ...comment,
            likes: newLikes,
            dislikes: newDislikes,
            likedBy: newLikedBy,
            dislikedBy: newDislikedBy
          };
        }
        return comment;
      });

      // Convert the comments to JSON format for database update
      const commentsJson = convertCommentsToJson(updatedComments);

      const { error } = await supabase
        .from('questions')
        .update({ comments: commentsJson })
        .eq('id', question.id);

      if (error) throw error;

      setQuestionData(prev => ({
        ...prev,
        comments: updatedComments
      }));

      // Reação registrada - feedback visual através da mudança de cor do botão

    } catch (error) {
      console.error('❌ [QuestionCard] Error updating comment like:', error);
      // Erro silencioso - usuário pode tentar novamente
    }
  };

  const handleReplyLike = async (commentId: string | number, replyId: string | number, isLike: boolean) => {
    try {
      const commentsArray = [...(questionData.comments || [])];
      const updatedComments = commentsArray.map(comment => {
        if (comment.id === commentId) {
          const updatedReplies = (comment.replies || []).map(reply => {
            if (reply.id === replyId) {
              const alreadyLiked = reply.likedBy?.includes(userId);
              const alreadyDisliked = reply.dislikedBy?.includes(userId);

              let newLikedBy = reply.likedBy || [];
              let newDislikedBy = reply.dislikedBy || [];
              let newLikes = reply.likes || 0;
              let newDislikes = reply.dislikes || 0;

              if (alreadyLiked) {
                newLikedBy = newLikedBy.filter(id => id !== userId);
                newLikes--;
              }
              if (alreadyDisliked) {
                newDislikedBy = newDislikedBy.filter(id => id !== userId);
                newDislikes--;
              }

              if (isLike && !alreadyLiked) {
                newLikedBy.push(userId);
                newLikes++;
              } else if (!isLike && !alreadyDisliked) {
                newDislikedBy.push(userId);
                newDislikes++;
              }

              return {
                ...reply,
                likes: newLikes,
                dislikes: newDislikes,
                likedBy: newLikedBy,
                dislikedBy: newDislikedBy
              };
            }
            return reply;
          });

          return {
            ...comment,
            replies: updatedReplies
          };
        }
        return comment;
      });

      // Convert the comments to JSON format for database update
      const commentsJson = convertCommentsToJson(updatedComments);

      const { error } = await supabase
        .from('questions')
        .update({ comments: commentsJson })
        .eq('id', question.id);

      if (error) throw error;

      setQuestionData(prev => ({
        ...prev,
        comments: updatedComments
      }));

      // Reação registrada - feedback visual através da mudança de cor do botão

    } catch (error) {
      console.error('❌ [QuestionCard] Error updating reply like:', error);
      // Erro silencioso - usuário pode tentar novamente
    }
  };

  const currentQuestionAnswered = internalHasAnswered || hasAnswered || isAnswered;

  return (
    <Card className="bg-white rounded-xl shadow-lg overflow-hidden border-0">
      <div className="p-6 space-y-6">
        <div className="mb-4">
          <QuestionMetadata question={question} />
        </div>

        <div className="prose max-w-none mb-6 bg-gray-50 p-4 rounded-lg" data-highlighter="statement">
          <FormattedContent content={question.question_content || question.statement} />
        </div>

      {(question.media_attachments || question.images) && (question.media_attachments || question.images)?.length > 0 && (
        <QuestionImages images={question.media_attachments || question.images} />
      )}

      {(question.question_format || question.answer_type) === 'DISSERTATIVA' ? (
        <>
          <DiscursiveAnswer
            value={discursiveAnswer}
            onChange={setDiscursiveAnswer}
            onSubmit={handleSubmit}
            hasAnswered={currentQuestionAnswered}
            readOnly={currentQuestionAnswered}
          />

          {currentQuestionAnswered && (
            <QuestionFeedback
              question={{...question, discursiveAnswer}}
              selectedAnswer={null}
              onNext={onNext}
              isLastQuestion={false}
              sessionId={sessionId}
              discursiveAnswer={discursiveAnswer}
            />
          )}
        </>
      ) : (
        <>
          <QuestionAlternatives
            alternatives={
              (question.response_choices && question.response_choices.length > 0)
                ? question.response_choices
                : (question.alternatives && question.alternatives.length > 0)
                ? question.alternatives
                : []
            }
            selectedAnswer={selectedAnswer}
            setSelectedAnswer={!currentQuestionAnswered ? onSelectAnswer : undefined}
            hasAnswered={currentQuestionAnswered}
            correct_answer={parseInt((question.correct_choice || question.correct_answer).toString())}
            statistics={question.statistics}
            alternativeComments={question.alternativeComments}
            questionId={question.id}
          />

          {!currentQuestionAnswered && (
            <div className="flex justify-end gap-4">
              <Button
                onClick={handleSubmit}
                disabled={!selectedAnswer || isSubmitting}
                className="w-full md:w-auto"
              >
                {isSubmitting ? "Enviando..." : "Confirmar Resposta"}
              </Button>
            </div>
          )}

          {currentQuestionAnswered && (
            <QuestionFeedback
              question={question}
              selectedAnswer={selectedAnswer}
              onNext={onNext}
              isLastQuestion={false}
              sessionId={sessionId}
            />
          )}
        </>
      )}

      <div className="flex flex-col md:flex-row items-center justify-center md:justify-between gap-4">
        <QuestionLikeButtons
          questionId={question.id}
          userId={userId}
          initialLikes={question.likes || 0}
          initialDislikes={question.dislikes || 0}
          likedBy={question.liked_by || []}
          dislikedBy={question.disliked_by || []}
        />

        {currentQuestionAnswered && (
          <Button
            variant="outline"
            onClick={() => setShowComments(!showComments)}
            className="flex items-center gap-2"
          >
            <MessageSquare className="h-4 w-4" />
            {showComments ? "Ocultar Comentários" : "Ver Comentários"}
          </Button>
        )}
      </div>

      {showComments && currentQuestionAnswered && (
        <div className="animate-fade-in">
          <QuestionComments
            comments={questionData.comments || []}
            newComment={newComment}
            setNewComment={setNewComment}
            onAddComment={handleAddComment}
            onReplyComment={handleReplyComment}
            onLikeComment={handleCommentLike}
            onReplyLike={handleReplyLike}
            userId={userId}
            questionId={question.id}
          />
        </div>
      )}
      </div>
    </Card>
  );
};

export default QuestionCard;
