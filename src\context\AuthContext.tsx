import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useSupabaseClient, useSession } from '@supabase/auth-helpers-react';
import type { User } from '@supabase/supabase-js';

// Interface para o perfil do usuário
interface UserProfile {
  id: string;
  email?: string; // Adicionar email
  full_name?: string;
  avatar_url?: string;
  is_admin?: boolean;
  [key: string]: any;
}

// Interface para o contexto de autenticação
interface AuthContextType {
  user: User | null;
  profile: UserProfile | null;
  isAdmin: boolean;
  isLoading: boolean;
  signOut: () => Promise<void>;
}

// Criar o contexto
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Hook para usar o contexto
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const session = useSession();
  const supabase = useSupabaseClient();
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [profileCache, setProfileCache] = useState<Record<string, UserProfile>>({});
  const [isInitialized, setIsInitialized] = useState<boolean>(false);

  // Função para buscar o perfil do usuário com cache
  const fetchUserProfile = useCallback(async (userId: string, userEmail?: string) => {
    try {
      // Verificar se já temos o perfil em cache
      if (profileCache[userId]) {
        const cachedProfile = { ...profileCache[userId], email: userEmail };
        setProfile(cachedProfile);
        setIsAdmin(cachedProfile?.is_admin || false);
        return cachedProfile;
      }

      const { data, error } = await supabase
        .from("secure_profiles")
        .select("*")
        .eq("id", userId)
        .single();

      if (error) {
        return null;
      }

      // Adicionar email ao perfil
      const profileWithEmail = { ...data, email: userEmail };

      // Atualizar o cache
      setProfileCache(prev => ({
        ...prev,
        [userId]: profileWithEmail
      }));

      setProfile(profileWithEmail);
      setIsAdmin(profileWithEmail?.is_admin || false);
      return profileWithEmail;
    } catch (error) {
      return null;
    }
  }, [supabase, profileCache]);

  // Inicializar autenticação
  useEffect(() => {
    // Evitar inicialização múltipla
    if (isInitialized) return;

    const initAuth = async () => {
      // Iniciar processo de autenticação

      try {
        // Tentar carregar o perfil do cache local se disponível
        const cachedProfileData = localStorage.getItem('auth_profile');
        const cachedUserId = localStorage.getItem('auth_user_id');

        if (cachedProfileData && cachedUserId) {
          try {
            const parsedProfile = JSON.parse(cachedProfileData);
            // Usando perfil em cache local temporariamente
            setProfile(parsedProfile);
            setIsAdmin(parsedProfile?.is_admin || false);

            // Pré-popular o cache de perfil
            setProfileCache(prev => ({
              ...prev,
              [cachedUserId]: parsedProfile
            }));
          } catch (e) {
            // Erro ao parsear perfil em cache, remover dados inválidos
            localStorage.removeItem('auth_profile');
            localStorage.removeItem('auth_user_id');
          }
        }

        // Se já temos uma sessão do hook, usamos ela
        if (session !== undefined) {
          setUser(session?.user ?? null);

          if (session?.user) {
            const profile = await fetchUserProfile(session.user.id, session.user.email);

            // Salvar no localStorage para uso futuro
            if (profile) {
              localStorage.setItem('auth_profile', JSON.stringify(profile));
              localStorage.setItem('auth_user_id', session.user.id);
            }
          } else {
            setProfile(null);
            setIsAdmin(false);
            localStorage.removeItem('auth_profile');
            localStorage.removeItem('auth_user_id');
          }
        } else {
          // Se não temos sessão do hook, tentamos buscar do storage
          const { data } = await supabase.auth.getSession();

          if (data.session) {
            // Sessão encontrada no storage
            setUser(data.session.user);
            const profile = await fetchUserProfile(data.session.user.id, data.session.user.email);

            // Salvar no localStorage para uso futuro
            if (profile) {
              localStorage.setItem('auth_profile', JSON.stringify(profile));
              localStorage.setItem('auth_user_id', data.session.user.id);
            }
          } else {
            // Nenhuma sessão encontrada
            setUser(null);
            setProfile(null);
            setIsAdmin(false);
            localStorage.removeItem('auth_profile');
            localStorage.removeItem('auth_user_id');
          }
        }
      } catch (error) {
        // Erro ao inicializar autenticação
      } finally {
        setIsLoading(false);
        setIsInitialized(true);
      }
    };

    initAuth();
  }, [session, supabase, fetchUserProfile, isInitialized]);

  // Monitorar mudanças na sessão
  useEffect(() => {
    if (!isInitialized) return;

    const handleSessionChange = async () => {
      // Atualizar estado com base na nova sessão
      setUser(session?.user ?? null);

      if (session?.user) {
        const profile = await fetchUserProfile(session.user.id, session.user.email);

        // Salvar no localStorage para uso futuro
        if (profile) {
          localStorage.setItem('auth_profile', JSON.stringify(profile));
          localStorage.setItem('auth_user_id', session.user.id);
        }
      } else {
        setProfile(null);
        setIsAdmin(false);
        localStorage.removeItem('auth_profile');
        localStorage.removeItem('auth_user_id');
      }
    };

    handleSessionChange();
  }, [session, isInitialized, fetchUserProfile]);

  // Função para fazer logout
  const signOut = async () => {
    setIsLoading(true);
    await supabase.auth.signOut();
    setUser(null);
    setProfile(null);
    setIsAdmin(false);
    localStorage.removeItem('auth_profile');
    localStorage.removeItem('auth_user_id');
    setIsLoading(false);
  };

  const value = {
    user,
    profile,
    isAdmin,
    isLoading,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
