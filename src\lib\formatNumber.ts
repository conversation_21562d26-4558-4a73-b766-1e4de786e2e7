
export function formatNumberPtBR(value: number | string): string {
  // Special case for UI/IU units in strings
  if (typeof value === 'string' && /UI|IU/.test(value)) {
    // Extract the numeric part
    const numericPart = value.replace(/[^\d.,]/g, '');
    const numericValue = parseFloat(numericPart.replace(/\./g, '').replace(',', '.'));
    
    // Format large numbers with thousands separators
    const formattedNumber = new Intl.NumberFormat('pt-BR', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(numericValue);
    
    // Replace the numeric part in the original string
    return value.replace(numericPart, formattedNumber);
  }

  const numericValue = typeof value === 'string' ? 
    parseFloat(value.replace(/\./g, '').replace(',', '.')) : 
    value;

  if (Number.isInteger(numericValue) && Math.abs(numericValue) >= 1000) {
    return new Intl.NumberFormat('pt-BR', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(numericValue);
  }

  return new Intl.NumberFormat('pt-BR', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(numericValue);
}
