
import React, { useState } from "react";
import { Search, Pill, X, Loader2, PlusCircle } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface MedicationOption {
  id: string;
  value: string;
  label: string;
}

interface MedicationInputProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  isSearching: boolean;
  medicationOptions: MedicationOption[];
  handleAddMedication: (medication: string) => void;
  selectedMedications: string[];
  handleRemoveMedication: (index: number) => void;
}

export const MedicationInput: React.FC<MedicationInputProps> = ({
  searchTerm,
  setSearchTerm,
  isSearching,
  medicationOptions,
  handleAddMedication,
  selectedMedications,
  handleRemoveMedication,
}) => {
  // Estado para controlar a exibição da opção de adicionar manualmente
  const [showNoResults, setShowNoResults] = useState(false);

  // Função auxiliar para verificar se um medicamento já foi selecionado
  const isMedicationAlreadySelected = (medication: string) => {
    return selectedMedications.includes(medication);
  };

  // Função para adicionar medicamento manualmente
  const handleAddManually = () => {
    console.log("📝 Adicionando medicamento manualmente:", searchTerm);
    handleAddMedication(searchTerm);
    setShowNoResults(false);
  };

  // Atualizar o estado showNoResults quando mudar o searchTerm ou medicationOptions
  React.useEffect(() => {
    // Mostrar a opção "adicionar manualmente" apenas quando:
    // 1. Existe um termo de busca
    // 2. Nenhum resultado foi encontrado
    // 3. O termo de busca tem pelo menos 2 caracteres
    setShowNoResults(searchTerm.length >= 2 && medicationOptions.length === 0 && !isSearching);
  }, [searchTerm, medicationOptions, isSearching]);

  return (
    <div className="space-y-4">
      {/* Badge BETA sutil */}
      <div className="flex justify-end mb-1">
        <div className="bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 text-xs px-2 py-0.5 rounded-full font-medium border border-purple-200 dark:border-purple-800 inline-flex items-center gap-1">
          <span className="h-1.5 w-1.5 bg-purple-500 rounded-full animate-pulse"></span>
          BETA
        </div>
      </div>
      
      <div className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={18} />
          <Input
            type="text"
            placeholder="Digite o nome de um medicamento..."
            className="pl-10 pr-10 transition-all focus:ring-2 focus:ring-primary/30"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          {isSearching && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
            </div>
          )}
        </div>
        
        {/* Opções de medicamentos encontrados ou opção de adicionar manualmente */}
        {(medicationOptions.length > 0 || showNoResults) && (
          <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 max-h-60 overflow-auto">
            {medicationOptions.length > 0 ? (
              medicationOptions.map((option) => {
                const isAlreadySelected = isMedicationAlreadySelected(option.value);
                
                return (
                  <button
                    key={`${option.id}-${option.value}`}
                    className={cn(
                      "w-full text-left px-4 py-2.5 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-between gap-2",
                      isAlreadySelected && "opacity-50 bg-gray-50 dark:bg-gray-800"
                    )}
                    onClick={() => handleAddMedication(option.value)}
                    disabled={isAlreadySelected}
                  >
                    <div className="flex items-center gap-2">
                      <PlusCircle size={16} className="text-primary" />
                      <span>{option.label}</span>
                    </div>
                    {isAlreadySelected && (
                      <span className="text-xs text-gray-500 dark:text-gray-400">Já adicionado</span>
                    )}
                  </button>
                );
              })
            ) : (
              // Opção de adicionar manualmente quando não há resultados
              <button
                className="w-full text-left px-4 py-2.5 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-between"
                onClick={handleAddManually}
              >
                <div className="flex items-center gap-2">
                  <PlusCircle size={16} className="text-primary" />
                  <span>Adicionar <strong>"{searchTerm}"</strong> manualmente</span>
                </div>
              </button>
            )}
          </div>
        )}
      </div>

      {selectedMedications.length > 0 ? (
        <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Medicamentos selecionados ({selectedMedications.length}/6)
          </h3>
          <div className="flex flex-wrap gap-2">
            {selectedMedications.map((medication, index) => (
              <div
                key={`selected-${index}-${medication}`}
                className={cn(
                  "bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 rounded-full px-3 py-1.5 flex items-center gap-1.5 border border-gray-200 dark:border-gray-700 shadow-sm transition-all hover:shadow-md",
                  index % 4 === 0 && "border-l-4 border-l-primary",
                  index % 4 === 1 && "border-l-4 border-l-secondary",
                  index % 4 === 2 && "border-l-4 border-l-green-500",
                  index % 4 === 3 && "border-l-4 border-l-amber-500"
                )}
              >
                <Pill className="h-4 w-4" />
                <span className="text-sm">{medication}</span>
                <button
                  onClick={() => handleRemoveMedication(index)}
                  className="ml-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full p-1"
                  aria-label="Remover medicamento"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="bg-gray-50 dark:bg-gray-800/30 rounded-lg p-6 border border-dashed border-gray-300 dark:border-gray-700 flex flex-col items-center justify-center">
          <Pill className="h-10 w-10 text-gray-400 mb-2" />
          <p className="text-gray-500 dark:text-gray-400 text-center">
            Nenhum medicamento selecionado
          </p>
          <p className="text-xs text-gray-400 dark:text-gray-500 text-center mt-1">
            Pesquise e selecione de 2 a 6 medicamentos para análise
          </p>
        </div>
      )}
    </div>
  );
};
