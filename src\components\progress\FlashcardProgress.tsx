import { useState, useEffect } from 'react';
import { supabase } from "@/integrations/supabase/client";
import { FlashcardStats } from "./flashcards/FlashcardStats";
import { FlashcardHierarchy } from "./flashcards/FlashcardHierarchy";
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import type { CategoryNode } from "@/types/flashcard";

export const FlashcardProgress = () => {
  const [stats, setStats] = useState({ totalCards: 0, correctAnswers: 0, timeSpent: 0 });
  const [studiedHierarchy, setStudiedHierarchy] = useState<CategoryNode[]>([]);
  const [reviewHierarchy, setReviewHierarchy] = useState<CategoryNode[]>([]);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadFlashcardStats();
    loadStudiedHierarchy();
    loadReviewHierarchy();
  }, []);

  const loadFlashcardStats = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { data: sessions } = await supabase
      .from('flashcards_sessions')
      .select('*')
      .eq('user_id', user.id);

    if (sessions) {
      const totalCards = sessions.reduce((acc, session) => acc + session.total_cards, 0);
      const correctAnswers = sessions.reduce((acc, session) => acc + session.correct_cards, 0);
      setStats({
        totalCards,
        correctAnswers,
        timeSpent: sessions.length * 10
      });
    }
  };

  const loadStudiedHierarchy = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    // Carregar especialidades
    const { data: specialties } = await supabase
      .from('flashcards_specialty')
      .select('*')
      .eq('user_id', user.id);

    if (!specialties) return;

    const hierarchyData: CategoryNode[] = await Promise.all(
      specialties.map(async (specialty) => {
        // Contar cards da especialidade
        const { count: specialtyCount } = await supabase
          .from('flashcards_cards')
          .select('*', { count: 'exact', head: true })
          .eq('specialty_id', specialty.id)
          .eq('user_id', user.id);

        // Carregar temas da especialidade
        const { data: themes } = await supabase
          .from('flashcards_theme')
          .select('*')
          .eq('specialty_id', specialty.id);

        const themeNodes = await Promise.all((themes || []).map(async (theme) => {
          // Contar cards do tema
          const { count: themeCount } = await supabase
            .from('flashcards_cards')
            .select('*', { count: 'exact', head: true })
            .eq('theme_id', theme.id)
            .eq('user_id', user.id);

          // Carregar focos do tema
          const { data: focuses } = await supabase
            .from('flashcards_focus')
            .select('*')
            .eq('theme_id', theme.id);

          const focusNodes = await Promise.all((focuses || []).map(async (focus) => {
            // Contar cards do foco
            const { count: focusCount } = await supabase
              .from('flashcards_cards')
              .select('*', { count: 'exact', head: true })
              .eq('focus_id', focus.id)
              .eq('user_id', user.id);

            return {
              id: focus.id,
              name: focus.name,
              cardsCount: focusCount || 0
            };
          }));

          return {
            id: theme.id,
            name: theme.name,
            cardsCount: themeCount || 0,
            children: focusNodes
          };
        }));

        return {
          id: specialty.id,
          name: specialty.name,
          cardsCount: specialtyCount || 0,
          children: themeNodes
        };
      })
    );

    setStudiedHierarchy(hierarchyData);
  };

  const loadReviewHierarchy = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];

    // Carregar especialidades com cards para revisão
    const { data: reviews } = await supabase
      .from('flashcards_reviews')
      .select(`
        card_id,
        next_review_date,
        flashcards_cards (
          specialty_id,
          theme_id,
          focus_id
        )
      `)
      .eq('user_id', user.id)
      .eq('next_review_date', tomorrowStr);

    if (!reviews || reviews.length === 0) {
      setReviewHierarchy([]);
      return;
    }

    // Agrupar IDs por nível hierárquico
    const specialtyIds = new Set(reviews.map(r => r.flashcards_cards.specialty_id));
    const themeIds = new Set(reviews.map(r => r.flashcards_cards.theme_id).filter(Boolean));
    const focusIds = new Set(reviews.map(r => r.flashcards_cards.focus_id).filter(Boolean));

    // Carregar dados das categorias
    const { data: specialties } = await supabase
      .from('flashcards_specialty')
      .select('*')
      .in('id', Array.from(specialtyIds));

    const hierarchyData: CategoryNode[] = await Promise.all(
      (specialties || []).map(async (specialty) => {
        const { data: themes } = await supabase
          .from('flashcards_theme')
          .select('*')
          .eq('specialty_id', specialty.id)
          .in('id', Array.from(themeIds));

        const themeNodes = await Promise.all((themes || []).map(async (theme) => {
          const { data: focuses } = await supabase
            .from('flashcards_focus')
            .select('*')
            .eq('theme_id', theme.id)
            .in('id', Array.from(focusIds));

          const focusNodes = (focuses || []).map(focus => ({
            id: focus.id,
            name: focus.name,
            nextReview: tomorrow
          }));

          return {
            id: theme.id,
            name: theme.name,
            nextReview: tomorrow,
            children: focusNodes
          };
        }));

        return {
          id: specialty.id,
          name: specialty.name,
          nextReview: tomorrow,
          children: themeNodes
        };
      })
    );

    setReviewHierarchy(hierarchyData);
  };

  const toggleNode = (nodeId: string) => {
    setExpandedNodes(prev => {
      const next = new Set(prev);
      if (next.has(nodeId)) {
        next.delete(nodeId);
      } else {
        next.add(nodeId);
      }
      return next;
    });
  };

  return (
    <div className="space-y-6">
      <FlashcardStats {...stats} />
      
      <Tabs defaultValue="studied" className="space-y-4">
        <TabsList>
          <TabsTrigger value="studied">Cards Estudados</TabsTrigger>
          <TabsTrigger value="review">Próximas Revisões</TabsTrigger>
        </TabsList>

        <TabsContent value="studied">
          <FlashcardHierarchy 
            hierarchy={studiedHierarchy}
            expandedNodes={expandedNodes}
            onToggleNode={toggleNode}
          />
        </TabsContent>

        <TabsContent value="review">
          <FlashcardHierarchy 
            hierarchy={reviewHierarchy}
            expandedNodes={expandedNodes}
            onToggleNode={toggleNode}
            showReviewDate
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};