/**
 * 🔎 AUDITORIA AUTOMÁTICA – CHECKLIST LAUDO PEDIÁTRICO
 * 
 * Verifica automaticamente os pontos críticos do laudo pediátrico
 * baseado nas diretrizes médicas e consensos atuais.
 */

import { calculateBirthWeightClassification } from './pigAigGigClassification';
import { calculateIron } from './supplementationCalculator';
import { formatGestationalAge } from './formatGestationalAge';

export interface AuditoriaResult {
  status: 'APROVADO' | 'ALERTA' | 'ERRO';
  pontos: AuditoriaPonto[];
  resumo: string;
}

export interface AuditoriaPonto {
  id: string;
  titulo: string;
  status: 'OK' | 'ALERTA' | 'ERRO';
  descricao: string;
  recomendacao?: string;
  valor_encontrado?: string;
  valor_esperado?: string;
}

interface DadosPaciente {
  age: number; // idade cronológica em meses
  gestationalAge?: number; // idade gestacional em semanas
  birthWeight?: number; // peso ao nascer em gramas
  weight?: number; // peso atual em gramas
  maturity?: 'Term' | 'Pre-term';
  exclusiveBreastfeeding?: boolean;
  riskFactors?: string[];
  percentiles?: {
    weight?: number;
    height?: number;
    headCircumference?: number;
  };
}

/**
 * Executa auditoria completa do laudo pediátrico
 */
export function executarAuditoria(dados: DadosPaciente): AuditoriaResult {
  const pontos: AuditoriaPonto[] = [];

  // 1. Verificar uso de idade corrigida em prematuros
  pontos.push(verificarIdadeCorrigida(dados));

  // 2. Verificar classificação PIG/AIG/GIG
  pontos.push(verificarClassificacaoPesoNascer(dados));

  // 3. Verificar ativação de "AME prolongado sem ferro"
  pontos.push(verificarAMEProlongado(dados));

  // 4. Verificar interpretação de percentis > P97
  pontos.push(verificarPercentisPAltos(dados));

  // 5. Verificar prescrição de ferro para prematuros
  pontos.push(verificarPrescricaoFerro(dados));

  // Calcular status geral
  const erros = pontos.filter(p => p.status === 'ERRO').length;
  const alertas = pontos.filter(p => p.status === 'ALERTA').length;

  let status: 'APROVADO' | 'ALERTA' | 'ERRO';
  let resumo: string;

  if (erros > 0) {
    status = 'ERRO';
    resumo = `❌ LAUDO REPROVADO: ${erros} erro(s) crítico(s) encontrado(s)`;
  } else if (alertas > 0) {
    status = 'ALERTA';
    resumo = `⚠️ LAUDO COM ALERTAS: ${alertas} ponto(s) de atenção`;
  } else {
    status = 'APROVADO';
    resumo = `✅ LAUDO APROVADO: Todos os critérios atendidos`;
  }

  return { status, pontos, resumo };
}

/**
 * 1. Verificar se idade corrigida foi usada em prematuros < 37 semanas e < 24 meses
 */
function verificarIdadeCorrigida(dados: DadosPaciente): AuditoriaPonto {
  const isPreterm = dados.gestationalAge && dados.gestationalAge < 37;
  const isUnder24Months = dados.age < 24;

  if (isPreterm && isUnder24Months) {
    // Para prematuros < 24 meses, idade corrigida DEVE ser considerada
    return {
      id: 'idade_corrigida',
      titulo: '1. Uso de Idade Corrigida',
      status: 'ALERTA',
      descricao: `Prematuro (${formatGestationalAge(dados.gestationalAge!)}) com ${dados.age.toFixed(1)} meses`,
      recomendacao: 'OBRIGATÓRIO usar idade corrigida para avaliação antropométrica até 24 meses',
      valor_encontrado: `IG: ${formatGestationalAge(dados.gestationalAge!)}`,
      valor_esperado: 'Idade corrigida aplicada'
    };
  }

  return {
    id: 'idade_corrigida',
    titulo: '1. Uso de Idade Corrigida',
    status: 'OK',
    descricao: isPreterm ? 'Prematuro > 24 meses (idade cronológica adequada)' : 'RN a termo (idade cronológica adequada)',
  };
}

/**
 * 2. Verificar classificação PIG/AIG/GIG baseada na tabela INTERGROWTH-21st
 */
function verificarClassificacaoPesoNascer(dados: DadosPaciente): AuditoriaPonto {
  if (!dados.birthWeight || !dados.gestationalAge) {
    return {
      id: 'pig_aig_gig',
      titulo: '2. Classificação PIG/AIG/GIG',
      status: 'ERRO',
      descricao: 'Dados insuficientes para classificação',
      recomendacao: 'Informar peso ao nascer e idade gestacional',
    };
  }

  try {
    const classification = calculateBirthWeightClassification(dados.birthWeight, dados.gestationalAge);
    
    if (classification.classification === 'PIG' && classification.percentile >= 10) {
      return {
        id: 'pig_aig_gig',
        titulo: '2. Classificação PIG/AIG/GIG',
        status: 'ERRO',
        descricao: 'Classificação PIG incorreta',
        recomendacao: 'PIG deve ser < P10 pela tabela INTERGROWTH-21st',
        valor_encontrado: `${classification.classification} (P${classification.percentile})`,
        valor_esperado: 'P < 10 para PIG'
      };
    }

    if (classification.classification === 'GIG' && classification.percentile <= 90) {
      return {
        id: 'pig_aig_gig',
        titulo: '2. Classificação PIG/AIG/GIG',
        status: 'ERRO',
        descricao: 'Classificação GIG incorreta',
        recomendacao: 'GIG deve ser > P90 pela tabela INTERGROWTH-21st',
        valor_encontrado: `${classification.classification} (P${classification.percentile})`,
        valor_esperado: 'P > 90 para GIG'
      };
    }

    return {
      id: 'pig_aig_gig',
      titulo: '2. Classificação PIG/AIG/GIG',
      status: 'OK',
      descricao: `${classification.classification} (P${classification.percentile}) - Classificação correta`,
    };

  } catch (error) {
    return {
      id: 'pig_aig_gig',
      titulo: '2. Classificação PIG/AIG/GIG',
      status: 'ERRO',
      descricao: 'Erro no cálculo da classificação',
      recomendacao: 'Verificar dados de entrada (peso 300-6000g, IG 22-44 semanas)',
    };
  }
}

/**
 * 3. Verificar se "AME prolongado sem ferro" foi ativado apenas se idade corrigida ≥ 6 meses
 */
function verificarAMEProlongado(dados: DadosPaciente): AuditoriaPonto {
  const hasAMEFactor = dados.riskFactors?.includes('prolonged_breastfeeding') || 
                      dados.riskFactors?.includes('exclusive_breastfeeding_gt_6m_without_supplement');

  if (hasAMEFactor) {
    // Calcular idade corrigida se for prematuro
    let idadeParaAnalise = dados.age;
    if (dados.gestationalAge && dados.gestationalAge < 37) {
      const weeksPreterm = 40 - dados.gestationalAge;
      const monthsPreterm = (weeksPreterm * 7) / 30;
      idadeParaAnalise = dados.age - monthsPreterm;
    }

    if (idadeParaAnalise < 6) {
      return {
        id: 'ame_prolongado',
        titulo: '3. AME Prolongado sem Ferro',
        status: 'ERRO',
        descricao: 'Fator ativado incorretamente',
        recomendacao: 'AME prolongado só deve ser considerado ≥ 6 meses (idade corrigida)',
        valor_encontrado: `Idade: ${idadeParaAnalise.toFixed(1)} meses`,
        valor_esperado: '≥ 6 meses para ativar fator'
      };
    }
  }

  return {
    id: 'ame_prolongado',
    titulo: '3. AME Prolongado sem Ferro',
    status: 'OK',
    descricao: hasAMEFactor ? 'Fator aplicado corretamente (≥ 6 meses)' : 'Fator não aplicado',
  };
}

/**
 * 4. Verificar interpretação de percentis > P97 em prematuros
 */
function verificarPercentisPAltos(dados: DadosPaciente): AuditoriaPonto {
  const isPreterm = dados.gestationalAge && dados.gestationalAge < 37;
  const hasHighPercentiles = dados.percentiles && (
    (dados.percentiles.weight && dados.percentiles.weight > 97) ||
    (dados.percentiles.height && dados.percentiles.height > 97) ||
    (dados.percentiles.headCircumference && dados.percentiles.headCircumference > 97)
  );

  if (hasHighPercentiles && isPreterm) {
    return {
      id: 'percentis_altos',
      titulo: '4. Percentis > P97 em Prematuros',
      status: 'ALERTA',
      descricao: 'Prematuro com percentis > P97 detectado',
      recomendacao: 'Evitar termo "sobrepeso". Usar "acima do esperado, acompanhar catch-up growth"',
      valor_encontrado: `Prematuro (${formatGestationalAge(dados.gestationalAge!)}) com P > 97`,
      valor_esperado: 'Interpretação contextualizada para catch-up growth'
    };
  }

  return {
    id: 'percentis_altos',
    titulo: '4. Percentis > P97 em Prematuros',
    status: 'OK',
    descricao: hasHighPercentiles ? 'Percentis altos em RN termo (interpretação padrão)' : 'Percentis dentro da normalidade',
  };
}

/**
 * 5. Verificar prescrição de ferro para prematuros < 1500g
 */
function verificarPrescricaoFerro(dados: DadosPaciente): AuditoriaPonto {
  if (!dados.birthWeight || dados.maturity !== 'Pre-term') {
    return {
      id: 'ferro_prematuro',
      titulo: '5. Prescrição de Ferro (Prematuros)',
      status: 'OK',
      descricao: 'Não se aplica (RN termo ou dados insuficientes)',
    };
  }

  if (dados.birthWeight <= 1500) {
    const ageInDays = Math.round(dados.age * 30);
    const currentWeight = dados.weight || dados.birthWeight;
    
    try {
      const ironRecommendation = calculateIron(
        ageInDays,
        currentWeight,
        dados.birthWeight,
        'Pre-term',
        dados.exclusiveBreastfeeding || false,
        dados.riskFactors as any[] || []
      );

      // Verificar se a dose está correta para < 1500g
      if (dados.birthWeight <= 1000) {
        // Deve ser 4mg/kg/dia
        if (!ironRecommendation.includes('4mg/kg/dia') && ageInDays >= 30) {
          return {
            id: 'ferro_prematuro',
            titulo: '5. Prescrição de Ferro (Prematuros)',
            status: 'ERRO',
            descricao: 'Dose incorreta para prematuro ≤ 1000g',
            recomendacao: 'Prematuros ≤ 1000g devem receber 4mg/kg/dia aos 30 dias',
            valor_encontrado: ironRecommendation,
            valor_esperado: '4mg/kg/dia aos 30 dias'
          };
        }
      } else if (dados.birthWeight <= 1500) {
        // Deve ser 3mg/kg/dia
        if (!ironRecommendation.includes('3mg/kg/dia') && ageInDays >= 30) {
          return {
            id: 'ferro_prematuro',
            titulo: '5. Prescrição de Ferro (Prematuros)',
            status: 'ERRO',
            descricao: 'Dose incorreta para prematuro 1000-1500g',
            recomendacao: 'Prematuros 1000-1500g devem receber 3mg/kg/dia aos 30 dias',
            valor_encontrado: ironRecommendation,
            valor_esperado: '3mg/kg/dia aos 30 dias'
          };
        }
      }

      return {
        id: 'ferro_prematuro',
        titulo: '5. Prescrição de Ferro (Prematuros)',
        status: 'OK',
        descricao: 'Prescrição de ferro correta conforme Consenso SBP/SPSP 2018',
      };

    } catch (error) {
      return {
        id: 'ferro_prematuro',
        titulo: '5. Prescrição de Ferro (Prematuros)',
        status: 'ERRO',
        descricao: 'Erro no cálculo da suplementação de ferro',
        recomendacao: 'Verificar dados de entrada e lógica de cálculo',
      };
    }
  }

  return {
    id: 'ferro_prematuro',
    titulo: '5. Prescrição de Ferro (Prematuros)',
    status: 'OK',
    descricao: 'Prematuro > 1500g (doses padrão aplicáveis)',
  };
}

/**
 * Gera relatório de auditoria em formato legível
 */
export function gerarRelatorioAuditoria(resultado: AuditoriaResult): string {
  let relatorio = `🔎 RELATÓRIO DE AUDITORIA AUTOMÁTICA\n`;
  relatorio += `${resultado.resumo}\n\n`;

  resultado.pontos.forEach((ponto, index) => {
    const emoji = ponto.status === 'OK' ? '✅' : ponto.status === 'ALERTA' ? '⚠️' : '❌';
    relatorio += `${emoji} ${ponto.titulo}\n`;
    relatorio += `   Status: ${ponto.status}\n`;
    relatorio += `   ${ponto.descricao}\n`;
    
    if (ponto.recomendacao) {
      relatorio += `   💡 Recomendação: ${ponto.recomendacao}\n`;
    }
    
    if (ponto.valor_encontrado && ponto.valor_esperado) {
      relatorio += `   📊 Encontrado: ${ponto.valor_encontrado}\n`;
      relatorio += `   📋 Esperado: ${ponto.valor_esperado}\n`;
    }
    
    relatorio += '\n';
  });

  return relatorio;
}
