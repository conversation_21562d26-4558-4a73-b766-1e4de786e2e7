import { useState } from "react";
import { Progress } from "@/components/ui/progress";
import { DiagnosisActions } from "./DiagnosisActions";
import { Card } from "@/components/ui/card";
import { AnamnesisCard } from "./sections/AnamnesisCard";
import { PrescriptionCard } from "./sections/PrescriptionCard";
import { InstructionsCard } from "./sections/InstructionsCard";
import { ChevronDown } from "lucide-react";

interface Diagnosis {
  condition: string;
  probability: number;
  clinicalPresentation: string;
  exams: string;
  treatment: string;
}

interface DiagnosisResultProps {
  diagnoses: Diagnosis[];
  summary: string;
  formData: {
    age: number;
    weight: number;
    gender: "male" | "female";
    symptoms: string[];
    hasChronicDiseases: boolean;
    chronicDiseases?: string;
    symptomsIntensity: number;
    hasRecentExams: boolean;
    examDetails?: string;
    isPregnant?: boolean;
    isPediatric: boolean;
    manualSymptoms?: string;
  };
}

export function DiagnosisResult({ diagnoses, summary, formData }: DiagnosisResultProps) {
  const [selectedDiagnosis, setSelectedDiagnosis] = useState<Diagnosis | null>(null);
  const [prescription, setPrescription] = useState<string | null>(null);
  const [editablePrescription, setEditablePrescription] = useState<string>("");
  const [detailedAnalysis, setDetailedAnalysis] = useState<string | null>(null);
  const [isLoadingPrescription, setIsLoadingPrescription] = useState(false);
  const [isLoadingAnalysis, setIsLoadingAnalysis] = useState(false);

  const handlePrescriptionGenerated = (data: { prescription: string }) => {
    // Extrair a seção de medicamentos (entre **Medicamentos:** e **Observações:**)
    const medicationSection = data.prescription
      .split('**Medicamentos:**')[1]
      ?.split('**Observações:**')[0]
      ?.trim() || '';

    // Extrair observações e instruções (tudo após **Observações:**)
    const instructionsSection = data.prescription
      .split('**Observações:**')[1]
      ?.trim() || '';

    setPrescription(data.prescription);
    setEditablePrescription(medicationSection);
    setDetailedAnalysis(instructionsSection);
    setIsLoadingPrescription(false);
  };

  const handleDetailedAnalysis = (analysis: string) => {
    setDetailedAnalysis(analysis);
    setPrescription(null);
    setIsLoadingAnalysis(false);
  };

  return (
    <div className="space-y-8">
      <AnamnesisCard formData={formData} />

      {!selectedDiagnosis && (
        <div className="text-center space-y-2 mb-4">
          <h3 className="text-lg font-medium text-primary">
            Selecione um diagnóstico abaixo
          </h3>
          <ChevronDown className="w-6 h-6 text-primary mx-auto animate-bounce" />
        </div>
      )}

      <Card className="p-6 bg-white/80 backdrop-blur-sm border border-primary/20 shadow-lg animate-fade-in">
        <div className="space-y-3">
          {diagnoses.map((diagnosis, index) => (
            <div
              key={index}
              onClick={() => setSelectedDiagnosis(diagnosis)}
              className={`p-4 rounded-lg transition-all cursor-pointer group ${
                selectedDiagnosis?.condition === diagnosis.condition
                  ? "bg-primary/10 border-2 border-primary"
                  : "bg-white/50 border border-primary/20 hover:bg-primary/5"
              }`}
            >
              <div className="flex justify-between items-center mb-2">
                <h4 className="font-medium text-primary group-hover:text-primary/80 transition-colors">
                  {diagnosis.condition}
                </h4>
                <span className="text-sm font-medium text-primary/80">
                  {diagnosis.probability}%
                </span>
              </div>
              <Progress value={diagnosis.probability} className="h-2 bg-primary/10" />
            </div>
          ))}
        </div>
      </Card>

      {selectedDiagnosis && (
        <DiagnosisActions
          selectedDiagnosis={selectedDiagnosis}
          patientData={formData}
          onPrescriptionGenerated={handlePrescriptionGenerated}
          onDetailedAnalysis={handleDetailedAnalysis}
          setIsLoadingPrescription={setIsLoadingPrescription}
          setIsLoadingAnalysis={setIsLoadingAnalysis}
        />
      )}

      {isLoadingPrescription && (
        <Card className="p-6 bg-white/80 backdrop-blur-sm border border-primary/20 shadow-lg animate-pulse">
          <div className="flex items-center justify-center space-x-2">
            <div className="w-4 h-4 bg-primary/60 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
            <div className="w-4 h-4 bg-primary/60 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
            <div className="w-4 h-4 bg-primary/60 rounded-full animate-bounce"></div>
          </div>
          <p className="text-center text-primary/60 mt-4">Gerando prescrição...</p>
        </Card>
      )}

      {isLoadingAnalysis && (
        <Card className="p-6 bg-white/80 backdrop-blur-sm border border-primary/20 shadow-lg animate-pulse">
          <div className="flex items-center justify-center space-x-2">
            <div className="w-4 h-4 bg-primary/60 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
            <div className="w-4 h-4 bg-primary/60 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
            <div className="w-4 h-4 bg-primary/60 rounded-full animate-bounce"></div>
          </div>
          <p className="text-center text-primary/60 mt-4">Gerando análise detalhada...</p>
        </Card>
      )}

      {prescription && (
        <>
          <PrescriptionCard
            prescription={prescription}
            editablePrescription={editablePrescription}
            onPrescriptionChange={setEditablePrescription}
          />
          
          {detailedAnalysis && (
            <InstructionsCard content={detailedAnalysis} />
          )}
        </>
      )}

      {!prescription && detailedAnalysis && (
        <Card className="p-6 bg-white/80 backdrop-blur-sm border border-primary/20 shadow-lg animate-fade-in">
          <h3 className="text-xl font-semibold mb-4 bg-gradient-to-r from-primary to-primary/70 text-transparent bg-clip-text">
            Análise Detalhada
          </h3>
          <div 
            className="prose prose-blue max-w-none [&>*:first-child]:mt-0 [&>*:last-child]:mb-0"
            dangerouslySetInnerHTML={{ 
              __html: detailedAnalysis.replace(/\n/g, '<br>').replace(/- /g, '• ')
            }}
          />
        </Card>
      )}

      <Card className="p-6 bg-white/80 backdrop-blur-sm border border-primary/20 shadow-lg animate-fade-in">
        <h3 className="text-xl font-semibold mb-4 bg-gradient-to-r from-primary to-primary/70 text-transparent bg-clip-text">
          Resumo da Análise
        </h3>
        <p className="text-gray-600 text-base leading-relaxed whitespace-pre-line">
          {summary}
        </p>
      </Card>
    </div>
  );
}