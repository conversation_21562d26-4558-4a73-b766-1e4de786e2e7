
import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useTabFocusProtection } from "@/hooks/useTabFocusProtection";
import {
  ArrowLeft,
  Milk,
  Info,
  Search,
  Check,
  X,
  AlertTriangle,
  Loader2,
  RefreshCw,
  CheckCircle2,
  XCircle,
  Sparkles,
  ChevronLeft,
  ChevronRight,
  Database
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

// Interfaces
interface Medication {
  id: string;
  name: string;
  compatibility_level: string;
  usage_description: string;
  additional_info?: string | null;
  efeitos_no_lactente?: string | null;
  alternativas_seguras?: string[] | null;
  orientacoes_uso?: string | null;
  section_id: string;
  subsection_id?: string | null;
  section?: { name: string; id: string } | null;
  subsection?: { name: string } | null;
}

interface Section {
  id: string;
  name: string;
  count: number;
}

interface EnhancedMedication {
  id: string;
  name: string;
  original: {
    compatibility_level: string;
    usage_description: string;
  };
  enhanced: {
    uso_amamentacao: string;
    efeitos_no_lactente: string;
    alternativas_seguras: string[];
    orientacoes_uso: string;
  };
}

const BreastfeedingMedicationsEnhancement: React.FC = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedMedications, setSelectedMedications] = useState<string[]>([]);
  const [enhancedMedications, setEnhancedMedications] = useState<EnhancedMedication[]>([]);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const [currentPreview, setCurrentPreview] = useState<EnhancedMedication | null>(null);
  const [activeTab, setActiveTab] = useState("select");
  const [selectedSection, setSelectedSection] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const itemsPerPage = 10; // Alterado de 20 para 10
  const [isApplyingAll, setIsApplyingAll] = useState(false);

  // Usar o hook de proteção global
  useTabFocusProtection();

  // Monitorar montagem/desmontagem para debug
  useEffect(() => {
    console.log(`[BreastfeedingMedicationsEnhancement] Mounted at ${new Date().toISOString()}`);

    return () => {
      console.log(`[BreastfeedingMedicationsEnhancement] Unmounted at ${new Date().toISOString()}`);
    };
  }, []);

  // Buscar seções disponíveis
  const { data: sections, isLoading: isLoadingSections, refetch: refetchSections } = useQuery({
    queryKey: ['breastfeeding-sections-with-incomplete-medications'],
    queryFn: async () => {
      // Primeiro, buscar todas as seções
      const { data: sectionsData, error: sectionsError } = await supabase
        .from('pedbook_breastfeeding_sections')
        .select('id, name')
        .order('name');

      if (sectionsError) throw sectionsError;

      // Para cada seção, contar quantos medicamentos incompletos existem
      const sectionsWithCount: Section[] = [];

      for (const section of sectionsData) {
        const { count, error: countError } = await supabase
          .from('pedbook_breastfeeding_medications')
          .select('id', { count: 'exact', head: true })
          .eq('section_id', section.id)
          .is('efeitos_no_lactente', null);

        if (countError) throw countError;

        console.log(`[BreastfeedingMedicationsEnhancement] Seção ${section.name}: ${count} medicamentos incompletos`);

        if (count && count > 0) {
          sectionsWithCount.push({
            id: section.id,
            name: section.name,
            count: count
          });
        }
      }

      return sectionsWithCount;
    },
    // Desabilitar o cache para garantir que os dados sejam sempre atualizados
    staleTime: 0,
    gcTime: 0
  });

  // Buscar medicamentos da seção selecionada
  const { data: medications, isLoading, error, refetch } = useQuery({
    queryKey: ['breastfeeding-medications-by-section', selectedSection, searchTerm, page],
    queryFn: async () => {
      if (!selectedSection) return [];

      let query = supabase
        .from('pedbook_breastfeeding_medications')
        .select(`
          id,
          name,
          compatibility_level,
          usage_description,
          additional_info,
          efeitos_no_lactente,
          alternativas_seguras,
          orientacoes_uso,
          section_id,
          subsection_id,
          section:pedbook_breastfeeding_sections(id, name),
          subsection:pedbook_breastfeeding_subsections(name)
        `)
        .eq('section_id', selectedSection)
        .is('efeitos_no_lactente', null) // Apenas medicamentos que ainda não têm os novos campos preenchidos
        .order('name')
        .range((page - 1) * itemsPerPage, page * itemsPerPage - 1);

      if (searchTerm) {
        query = query.ilike('name', `%${searchTerm}%`);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data as Medication[];
    },
    enabled: !!selectedSection
  });

  // Obter contagem total para paginação
  const { data: totalCount } = useQuery({
    queryKey: ['breastfeeding-medications-count', selectedSection, searchTerm],
    queryFn: async () => {
      if (!selectedSection) return 0;

      let query = supabase
        .from('pedbook_breastfeeding_medications')
        .select('id', { count: 'exact', head: true })
        .eq('section_id', selectedSection)
        .is('efeitos_no_lactente', null);

      if (searchTerm) {
        query = query.ilike('name', `%${searchTerm}%`);
      }

      const { count, error } = await query;

      if (error) throw error;
      return count || 0;
    },
    enabled: !!selectedSection
  });

  // Modificar a mutação para atualização de medicamentos
  const updateMedicationMutation = useMutation({
    mutationFn: async (medication: {
      id: string;
      usage_description: string;
      efeitos_no_lactente: string;
      alternativas_seguras: string[];
      orientacoes_uso: string;
    }) => {
      console.log(`[BreastfeedingMedicationsEnhancement] Atualizando medicamento: ${medication.id}`);

      const { error } = await supabase
        .from('pedbook_breastfeeding_medications')
        .update({
          usage_description: medication.usage_description,
          efeitos_no_lactente: medication.efeitos_no_lactente,
          alternativas_seguras: medication.alternativas_seguras,
          orientacoes_uso: medication.orientacoes_uso,
          updated_at: new Date().toISOString()
        })
        .eq('id', medication.id);

      if (error) throw error;
      return medication.id;
    },
    onSuccess: () => {
      console.log('[BreastfeedingMedicationsEnhancement] Medicamento atualizado com sucesso, invalidando consultas');

      // Invalidar consultas de medicamentos
      queryClient.invalidateQueries({ queryKey: ['breastfeeding-medications-by-section'] });

      // Invalidar consultas de seções para atualizar contagens
      queryClient.invalidateQueries({ queryKey: ['breastfeeding-sections-with-incomplete-medications'] });

      // Forçar uma nova busca de seções
      setTimeout(() => {
        refetchSections();
      }, 500);
    }
  });

  // Função para aprimorar medicamentos selecionados
  const enhanceMedications = async () => {
    if (selectedMedications.length === 0) {
      toast({
        title: "Nenhum medicamento selecionado",
        description: "Selecione pelo menos um medicamento para aprimorar.",
        variant: "destructive"
      });
      return;
    }

    setIsEnhancing(true);
    try {
      const response = await supabase.functions.invoke('enhance-breastfeeding-medications', {
        body: { medicationIds: selectedMedications }
      });

      if (response.error) throw new Error(response.error.message);

      setEnhancedMedications(response.data.results);
      setActiveTab("review");

      toast({
        title: "Aprimoramento concluído",
        description: `${response.data.results.length} medicamentos foram aprimorados com sucesso.`,
      });
    } catch (error) {
      console.error("Erro ao aprimorar medicamentos:", error);
      toast({
        title: "Erro ao aprimorar medicamentos",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsEnhancing(false);
    }
  };

  // Função para aplicar todos os aprimoramentos de uma vez
  const applyAllEnhancements = async () => {
    if (enhancedMedications.length === 0) return;

    setIsApplyingAll(true);
    try {
      console.log('[BreastfeedingMedicationsEnhancement] Iniciando processamento em massa');

      // Criar uma cópia para não afetar a iteração
      const medicationsToProcess = [...enhancedMedications];
      let successCount = 0;

      for (const enhancedMed of medicationsToProcess) {
        const dataToUpdate = {
          id: enhancedMed.id,
          usage_description: enhancedMed.enhanced.uso_amamentacao,
          efeitos_no_lactente: enhancedMed.enhanced.efeitos_no_lactente,
          alternativas_seguras: enhancedMed.enhanced.alternativas_seguras,
          orientacoes_uso: enhancedMed.enhanced.orientacoes_uso
        };

        try {
          await updateMedicationMutation.mutateAsync(dataToUpdate);
          successCount++;
          // Remover o medicamento da lista conforme vai sendo processado
          setEnhancedMedications(prev => prev.filter(med => med.id !== enhancedMed.id));
        } catch (error) {
          console.error(`Erro ao processar ${enhancedMed.name}:`, error);
          // Continua para o próximo mesmo em caso de erro
        }
      }

      // Forçar atualização das seções após processamento em massa
      queryClient.invalidateQueries({ queryKey: ['breastfeeding-sections-with-incomplete-medications'] });

      // Forçar uma nova busca de seções com um pequeno atraso
      setTimeout(() => {
        refetchSections();
      }, 1000);

      toast({
        title: "Processamento em massa concluído",
        description: `${successCount} de ${medicationsToProcess.length} medicamentos foram atualizados com sucesso.`,
      });
    } catch (error) {
      console.error("Erro no processamento em massa:", error);
      toast({
        title: "Erro no processamento em massa",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsApplyingAll(false);
    }
  };

  // Adicionar estado para edição
  const [isEditing, setIsEditing] = useState(false);
  const [editedMedication, setEditedMedication] = useState<{
    uso_amamentacao: string,
    efeitos_no_lactente: string,
    alternativas_seguras: string[],
    orientacoes_uso: string
  } | null>(null);

  // Função para aplicar as alterações
  const applyEnhancements = async (medicationId: string) => {
    const enhancedMed = enhancedMedications.find(med => med.id === medicationId);
    if (!enhancedMed) return;

    // Se estamos editando, use os valores editados
    const dataToUpdate = isEditing && editedMedication ? {
      id: enhancedMed.id,
      usage_description: editedMedication.uso_amamentacao,
      efeitos_no_lactente: editedMedication.efeitos_no_lactente,
      alternativas_seguras: editedMedication.alternativas_seguras,
      orientacoes_uso: editedMedication.orientacoes_uso
    } : {
      id: enhancedMed.id,
      usage_description: enhancedMed.enhanced.uso_amamentacao,
      efeitos_no_lactente: enhancedMed.enhanced.efeitos_no_lactente,
      alternativas_seguras: enhancedMed.enhanced.alternativas_seguras,
      orientacoes_uso: enhancedMed.enhanced.orientacoes_uso
    };

    try {
      await updateMedicationMutation.mutateAsync(dataToUpdate);

      toast({
        title: "Alterações aplicadas",
        description: `As informações de "${enhancedMed.name}" foram atualizadas com sucesso.`,
      });

      // Remover o medicamento da lista de aprimorados
      setEnhancedMedications(prev => prev.filter(med => med.id !== medicationId));
      setShowPreviewDialog(false);
    } catch (error) {
      console.error("Erro ao aplicar alterações:", error);
      toast({
        title: "Erro ao aplicar alterações",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  // Função para visualizar detalhes
  const previewMedication = (medication: EnhancedMedication) => {
    setCurrentPreview(medication);
    // Inicializa o estado de edição com os valores atuais
    setEditedMedication({
      uso_amamentacao: medication.enhanced.uso_amamentacao,
      efeitos_no_lactente: medication.enhanced.efeitos_no_lactente,
      alternativas_seguras: Array.isArray(medication.enhanced.alternativas_seguras) ?
        medication.enhanced.alternativas_seguras :
        [medication.enhanced.alternativas_seguras],
      orientacoes_uso: medication.enhanced.orientacoes_uso
    });
    setIsEditing(false);
    setShowPreviewDialog(true);
  };

  // Renderizar badge de compatibilidade
  const renderCompatibilityBadge = (level: string) => {
    switch (level.toLowerCase()) {
      case 'verde':
        return <Badge className="bg-green-100 text-green-800">Compatível</Badge>;
      case 'amarelo':
        return <Badge className="bg-yellow-100 text-yellow-800">Cautela</Badge>;
      case 'vermelho':
        return <Badge className="bg-red-100 text-red-800">Incompatível</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">Desconhecido</Badge>;
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex items-center gap-2 mb-6">
        <Button variant="ghost" size="sm" className="gap-1" onClick={() => window.history.back()}>
          <ArrowLeft className="h-4 w-4" />
          Voltar
        </Button>
        <h1 className="text-3xl font-bold">Aprimoramento de Medicamentos na Amamentação</h1>
      </div>

      <Card className="mb-6 border-l-4 border-l-pink-400">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Milk className="h-6 w-6 text-pink-500" />
              <CardTitle>Aprimoramento de Medicamentos na Amamentação</CardTitle>
            </div>
          </div>
          <CardDescription>
            Utilize inteligência artificial para aprimorar informações sobre medicamentos durante a amamentação,
            incluindo efeitos no lactente, alternativas seguras e orientações de uso.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert className="bg-blue-50 border-blue-200 text-blue-800">
            <Info className="h-4 w-4" />
            <AlertTitle>Como funciona</AlertTitle>
            <AlertDescription>
              1. Selecione os medicamentos que deseja aprimorar<br />
              2. Clique em "Aprimorar com IA" para gerar sugestões<br />
              3. Revise as sugestões e aplique as que considerar adequadas
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="select">Selecionar Medicamentos</TabsTrigger>
          <TabsTrigger value="review" disabled={enhancedMedications.length === 0}>
            Revisar Sugestões {enhancedMedications.length > 0 && `(${enhancedMedications.length})`}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="select">
          <Card>
            <CardHeader>
              <CardTitle>Selecione os Medicamentos</CardTitle>
              <CardDescription>
                Escolha os medicamentos que deseja aprimorar com IA
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingSections ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : sections?.length === 0 ? (
                <Alert>
                  <AlertTitle>Nenhuma seção disponível</AlertTitle>
                  <AlertDescription>
                    Não há medicamentos incompletos para aprimorar.
                  </AlertDescription>
                </Alert>
              ) : (
                <>
                  <div className="mb-6">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="text-sm font-medium">Selecione uma classe de medicamentos:</h3>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          refetchSections();
                        }}
                        className="flex items-center gap-1"
                      >
                        <RefreshCw className="h-3 w-3" />
                        Atualizar
                      </Button>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                      {sections?.map((section) => (
                        <Button
                          key={section.id}
                          variant={selectedSection === section.id ? "default" : "outline"}
                          className="justify-start gap-2"
                          onClick={() => {
                            setSelectedSection(section.id);
                            setPage(1);
                            setSelectedMedications([]);
                          }}
                        >
                          <Database className="h-4 w-4" />
                          <span className="truncate">{section.name}</span>
                          <Badge className="ml-auto">{section.count}</Badge>
                        </Button>
                      ))}
                    </div>
                  </div>

                  {selectedSection && (
                    <>
                      <div className="flex items-center gap-2 mb-4">
                        <div className="relative flex-1">
                          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                          <Input
                            placeholder="Buscar medicamento..."
                            className="pl-9"
                            value={searchTerm}
                            onChange={(e) => {
                              setSearchTerm(e.target.value);
                              setPage(1);
                            }}
                          />
                        </div>
                        <Button variant="outline" size="icon" onClick={() => refetch()}>
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                      </div>

                      {isLoading ? (
                        <div className="flex justify-center items-center py-8">
                          <Loader2 className="h-8 w-8 animate-spin text-primary" />
                        </div>
                      ) : error ? (
                        <Alert variant="destructive">
                          <AlertTitle>Erro ao carregar medicamentos</AlertTitle>
                          <AlertDescription>
                            Ocorreu um erro ao buscar os medicamentos. Tente novamente mais tarde.
                          </AlertDescription>
                        </Alert>
                      ) : (
                        <>
                          <div className="border rounded-md overflow-hidden">
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead className="w-12">
                                    <Checkbox
                                      checked={medications?.length > 0 && selectedMedications.length === medications.length}
                                      onCheckedChange={(checked) => {
                                        if (checked) {
                                          setSelectedMedications(medications?.map(med => med.id) || []);
                                        } else {
                                          setSelectedMedications([]);
                                        }
                                      }}
                                    />
                                  </TableHead>
                                  <TableHead>Nome</TableHead>
                                  <TableHead>Compatibilidade</TableHead>
                                  <TableHead className="hidden lg:table-cell">Subclasse</TableHead>
                                  <TableHead className="hidden md:table-cell">Uso Atual</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {medications?.length === 0 ? (
                                  <TableRow>
                                    <TableCell colSpan={5} className="text-center py-4 text-gray-500">
                                      Nenhum medicamento encontrado
                                    </TableCell>
                                  </TableRow>
                                ) : (
                                  medications?.map((medication) => (
                                    <TableRow key={medication.id}>
                                      <TableCell>
                                        <Checkbox
                                          checked={selectedMedications.includes(medication.id)}
                                          onCheckedChange={(checked) => {
                                            if (checked) {
                                              setSelectedMedications(prev => [...prev, medication.id]);
                                            } else {
                                              setSelectedMedications(prev => prev.filter(id => id !== medication.id));
                                            }
                                          }}
                                        />
                                      </TableCell>
                                      <TableCell className="font-medium">{medication.name}</TableCell>
                                      <TableCell>{renderCompatibilityBadge(medication.compatibility_level)}</TableCell>
                                      <TableCell className="hidden lg:table-cell">{medication.subsection?.name || '-'}</TableCell>
                                      <TableCell className="hidden md:table-cell max-w-xs truncate">
                                        {medication.usage_description}
                                      </TableCell>
                                    </TableRow>
                                  ))
                                )}
                              </TableBody>
                            </Table>
                          </div>

                          {/* Paginação */}
                          {totalCount && totalCount > itemsPerPage && (
                            <div className="flex items-center justify-between mt-4">
                              <div className="text-sm text-gray-500">
                                Mostrando {(page - 1) * itemsPerPage + 1} a {Math.min(page * itemsPerPage, totalCount)} de {totalCount} medicamentos
                              </div>
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setPage(p => Math.max(1, p - 1))}
                                  disabled={page === 1}
                                >
                                  <ChevronLeft className="h-4 w-4" />
                                </Button>
                                <div className="text-sm">
                                  Página {page} de {Math.ceil(totalCount / itemsPerPage)}
                                </div>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setPage(p => Math.min(Math.ceil(totalCount / itemsPerPage), p + 1))}
                                  disabled={page >= Math.ceil(totalCount / itemsPerPage)}
                                >
                                  <ChevronRight className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          )}
                        </>
                      )}
                    </>
                  )}
                </>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-gray-500">
                {selectedMedications.length} medicamentos selecionados
              </div>
              <Button
                onClick={enhanceMedications}
                disabled={selectedMedications.length === 0 || isEnhancing}
                className="gap-2"
              >
                {isEnhancing ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Processando...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4" />
                    Aprimorar com IA
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="review">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Revisar Sugestões da IA</CardTitle>
                <CardDescription>
                  Revise e aplique as sugestões geradas pela IA para cada medicamento
                </CardDescription>
              </div>
              {enhancedMedications.length > 0 && (
                <Button
                  onClick={applyAllEnhancements}
                  disabled={isApplyingAll}
                  className="gap-2"
                >
                  {isApplyingAll ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Aplicando...
                    </>
                  ) : (
                    <>
                      <Check className="h-4 w-4" />
                      Aprovar Todos ({enhancedMedications.length})
                    </>
                  )}
                </Button>
              )}
            </CardHeader>
            <CardContent>
              {enhancedMedications.length === 0 ? (
                <div className="space-y-4 text-center py-6">
                  <Alert>
                    <AlertTitle>Nenhuma sugestão disponível</AlertTitle>
                    <AlertDescription>
                      Todas as sugestões foram processadas.
                    </AlertDescription>
                  </Alert>
                  <Button
                    onClick={() => setActiveTab("select")}
                    className="mt-4"
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Voltar para Seleção de Medicamentos
                  </Button>
                </div>
              ) : (
                <div className="border rounded-md overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Nome</TableHead>
                        <TableHead>Compatibilidade</TableHead>
                        <TableHead className="hidden md:table-cell">Uso Original</TableHead>
                        <TableHead className="w-32 text-right">Ações</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {enhancedMedications.map((medication) => (
                        <TableRow key={medication.id}>
                          <TableCell className="font-medium">{medication.name}</TableCell>
                          <TableCell>{renderCompatibilityBadge(medication.original.compatibility_level)}</TableCell>
                          <TableCell className="hidden md:table-cell max-w-xs truncate">
                            {medication.original.usage_description}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => previewMedication(medication)}
                              >
                                Visualizar
                              </Button>
                              <Button
                                variant="default"
                                size="sm"
                                onClick={() => applyEnhancements(medication.id)}
                                disabled={updateMedicationMutation.isPending}
                              >
                                {updateMedicationMutation.isPending ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Check className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Dialog de Visualização com capacidade de edição */}
      <Dialog open={showPreviewDialog} onOpenChange={setShowPreviewDialog}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Milk className="h-5 w-5 text-pink-500" />
              {currentPreview?.name}
              {currentPreview && renderCompatibilityBadge(currentPreview.original.compatibility_level)}
            </DialogTitle>
            <DialogDescription>
              {isEditing ?
                "Edite as informações conforme necessário" :
                "Comparação entre as informações originais e as sugestões da IA"}
            </DialogDescription>
          </DialogHeader>

          {currentPreview && editedMedication && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm flex items-center">
                      <span className="mr-2">Informações Originais</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium mb-1">Uso na Amamentação</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 p-2 rounded-md">
                        {currentPreview.original.usage_description}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm flex items-center">
                      <span className="mr-2">
                        {isEditing ? "Edição de Informações" : "Sugestões da IA"}
                      </span>
                      {!isEditing && <Sparkles className="h-4 w-4 text-yellow-500" />}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium mb-1">Uso na Amamentação</h4>
                      {isEditing ? (
                        <Input
                          value={editedMedication.uso_amamentacao}
                          onChange={(e) => setEditedMedication({
                            ...editedMedication,
                            uso_amamentacao: e.target.value
                          })}
                          className="text-sm mt-1"
                        />
                      ) : (
                        <p className="text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 p-2 rounded-md">
                          {currentPreview.enhanced.uso_amamentacao}
                        </p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-base font-medium">
                    {isEditing ? "Editar Campos Adicionais" : "Novos Campos Sugeridos"}
                  </h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(!isEditing)}
                  >
                    {isEditing ? "Cancelar Edição" : "Editar Campos"}
                  </Button>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-1">Efeitos no Lactente</h4>
                  {isEditing ? (
                    <Input
                      value={editedMedication.efeitos_no_lactente}
                      onChange={(e) => setEditedMedication({
                        ...editedMedication,
                        efeitos_no_lactente: e.target.value
                      })}
                      className="text-sm mt-1"
                    />
                  ) : (
                    <p className="text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 p-2 rounded-md">
                      {currentPreview.enhanced.efeitos_no_lactente}
                    </p>
                  )}
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-1">Alternativas Seguras</h4>
                  {isEditing ? (
                    <div className="space-y-2">
                      {editedMedication.alternativas_seguras.map((alt, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <Input
                            value={alt}
                            onChange={(e) => {
                              const newAlternativas = [...editedMedication.alternativas_seguras];
                              newAlternativas[index] = e.target.value;
                              setEditedMedication({
                                ...editedMedication,
                                alternativas_seguras: newAlternativas
                              });
                            }}
                            className="text-sm flex-1"
                          />
                          <Button
                            variant="outline"
                            size="icon"
                            type="button"
                            onClick={() => {
                              const newAlternativas = editedMedication.alternativas_seguras.filter((_, i) => i !== index);
                              setEditedMedication({
                                ...editedMedication,
                                alternativas_seguras: newAlternativas
                              });
                            }}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        type="button"
                        size="sm"
                        className="mt-2"
                        onClick={() => {
                          setEditedMedication({
                            ...editedMedication,
                            alternativas_seguras: [...editedMedication.alternativas_seguras, ""]
                          });
                        }}
                      >
                        Adicionar Alternativa
                      </Button>
                    </div>
                  ) : (
                    <div className="text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 p-2 rounded-md">
                      {Array.isArray(currentPreview.enhanced.alternativas_seguras) ? (
                        <ul className="list-disc pl-5 space-y-1">
                          {currentPreview.enhanced.alternativas_seguras.map((alt, idx) => (
                            <li key={idx}>{alt}</li>
                          ))}
                        </ul>
                      ) : (
                        currentPreview.enhanced.alternativas_seguras
                      )}
                    </div>
                  )}
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-1">Orientações de Uso</h4>
                  {isEditing ? (
                    <Input
                      value={editedMedication.orientacoes_uso}
                      onChange={(e) => setEditedMedication({
                        ...editedMedication,
                        orientacoes_uso: e.target.value
                      })}
                      className="text-sm mt-1"
                    />
                  ) : (
                    <p className="text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 p-2 rounded-md">
                      {currentPreview.enhanced.orientacoes_uso}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="flex justify-between items-center">
            <Button variant="outline" onClick={() => setShowPreviewDialog(false)}>
              Fechar
            </Button>
            <Button
              onClick={() => {
                if (currentPreview) {
                  applyEnhancements(currentPreview.id);
                }
              }}
              disabled={updateMedicationMutation.isPending || !currentPreview}
            >
              {updateMedicationMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Aplicando...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Aplicar Alterações
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default BreastfeedingMedicationsEnhancement;
