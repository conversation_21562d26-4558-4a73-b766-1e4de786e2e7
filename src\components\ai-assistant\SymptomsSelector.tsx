import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area";
import { calculateLevenshteinDistance } from "@/lib/utils";
import { SymptomCategory, symptomCategories } from "./symptomData";
import { SymptomList } from "./SymptomList";
import { SearchAndFilters } from "./SearchAndFilters";

interface SymptomsSelectorProps {
  selectedSymptoms: string[];
  onSymptomsChange: (symptoms: string[]) => void;
}

export function SymptomsSelector({ selectedSymptoms, onSymptomsChange }: SymptomsSelectorProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const handleSymptomToggle = (symptom: string) => {
    const newSymptoms = selectedSymptoms.includes(symptom)
      ? selectedSymptoms.filter(s => s !== symptom)
      : [...selectedSymptoms, symptom];
    onSymptomsChange(newSymptoms);
  };

  const filterSymptoms = (categories: SymptomCategory[]) => {
    return categories
      .filter(category => !selectedCategory || category.name === selectedCategory)
      .map(category => ({
        ...category,
        symptoms: category.symptoms.filter(symptom => {
          if (!searchTerm) return true;
          const distance = calculateLevenshteinDistance(
            symptom.toLowerCase(),
            searchTerm.toLowerCase()
          );
          return distance <= 3 || symptom.toLowerCase().includes(searchTerm.toLowerCase());
        })
      }))
      .filter(category => category.symptoms.length > 0);
  };

  const filteredCategories = filterSymptoms(symptomCategories);

  return (
    <div className="space-y-4">
      <SearchAndFilters
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        selectedCategory={selectedCategory}
        setSelectedCategory={setSelectedCategory}
      />

      <ScrollArea className="h-[400px] rounded-lg border p-4 bg-white/50 backdrop-blur-sm">
        <SymptomList
          categories={filteredCategories}
          selectedSymptoms={selectedSymptoms}
          onSymptomToggle={handleSymptomToggle}
        />
      </ScrollArea>

      <div className="flex items-center justify-between px-4">
        <span className="text-sm text-gray-500">
          {selectedSymptoms.length} sintoma(s) selecionado(s)
        </span>
        {selectedSymptoms.length > 0 && (
          <button
            onClick={() => onSymptomsChange([])}
            className="text-sm text-red-500 hover:text-red-600 transition-colors"
          >
            Limpar seleção
          </button>
        )}
      </div>
    </div>
  );
}