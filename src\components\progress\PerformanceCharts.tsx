import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import ProgressChart from "@/components/ProgressChart";

interface StatData {
  name: string;
  correct: number;
  total: number;
}

interface PerformanceChartsProps {
  stats: {
    bySpecialty: Record<string, StatData>;
    byTheme: Record<string, StatData>;
    byFocus: Record<string, StatData>;
  };
}

export const PerformanceCharts = ({ stats }: PerformanceChartsProps) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">Evolução Semanal</h2>
        <ProgressChart />
      </Card>

      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">Desempenho por Área</h2>
        <div className="space-y-4">
          {Object.entries(stats.bySpecialty || {}).map(([id, data]) => (
            <div key={id}>
              <div className="flex justify-between text-sm mb-2">
                <span className="font-medium">{data.name}</span>
                <span className="text-gray-600">
                  {data.correct} de {data.total} ({((data.correct / data.total) * 100).toFixed(1)}%)
                </span>
              </div>
              <Progress value={(data.correct / data.total) * 100} />
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};