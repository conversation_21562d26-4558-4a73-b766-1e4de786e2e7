
import React from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { useFeedbackDialog } from "./hooks/useFeedbackDialog";
import { FeedbackForm as FeedbackFormComponent } from "./components/FeedbackForm";

export function SiteFeedbackDialog() {
  const {
    open,
    setOpen,
    rating,
    setRating,
    comment,
    setComment,
    whatsapp,
    setWhatsapp,
    isSubmitting,
    handleSubmit,
    handleClose,
  } = useFeedbackDialog();


  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        console.log("Dialog onOpenChange called, new state:", newOpen);
        if (!newOpen) {
          handleClose();
        } else {
          setOpen(newOpen);
        }
      }}
    >
      <DialogContent
        className="fixed left-[50%] -translate-x-1/2 bg-white dark:bg-slate-800 p-0 overflow-hidden flex flex-col rounded-2xl shadow-xl border dark:border-slate-700"
        hideCloseButton={false}
        style={{
          top: window.innerWidth <= 640 ? '20%' : '50%',
          transform: window.innerWidth <= 640 ? 'translate(-50%, -20%)' : 'translate(-50%, -50%)',
          maxHeight: window.innerWidth <= 640 ? '75dvh' : '80vh',
          width: window.innerWidth <= 640 ? (window.innerHeight > window.innerWidth ? '98%' : '75%') : 'calc(100%-2rem)',
          maxWidth: window.innerWidth <= 640 ? (window.innerHeight > window.innerWidth ? '520px' : '350px') : '500px'
        }}
      >
        <div className="overflow-y-auto flex-1 p-5 sm:p-6">
          <FeedbackFormComponent
            rating={rating}
            setRating={setRating}
            comment={comment}
            setComment={setComment}
            whatsapp={whatsapp}
            setWhatsapp={setWhatsapp}
          />
        </div>

        <div className="flex gap-2 sm:gap-3 justify-end p-4 border-t border-gray-200 dark:border-slate-700 bg-gray-50/80 dark:bg-slate-800/90 backdrop-blur supports-[backdrop-filter]:bg-gray-50/60 dark:supports-[backdrop-filter]:bg-slate-800/80">
          <Button
            variant="outline"
            onClick={handleClose}
            className="hover:bg-gray-100 dark:hover:bg-slate-700 dark:bg-slate-800 text-sm sm:text-base rounded-xl dark:text-gray-300 dark:border-slate-600"
          >
            Deixar para depois
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || (rating <= 4 && (!comment.trim() || !whatsapp.trim()))}
            className="bg-primary hover:bg-primary/90 text-sm sm:text-base rounded-xl text-white"
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <span className="h-4 w-4 block rounded-full border-2 border-white border-t-transparent animate-spin"></span>
                <span>Enviando...</span>
              </div>
            ) : (
              "Enviar feedback"
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
