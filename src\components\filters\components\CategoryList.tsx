import { Checkbox } from "@/components/ui/checkbox";
import { ChevronRight } from "lucide-react";
import type { FilterOption } from "../types";
import { useMemo } from "react";
import type { SelectedFilters } from "@/types/question";

interface CategoryListProps {
  categories: FilterOption[];
  selectedFilters: string[];
  expandedCategories: string[];
  searchTerm: string;
  onToggleFilter: (id: string) => void;
  onToggleExpand: (id: string) => void;
  type: "specialty" | "theme" | "focus";
  parentFilters: SelectedFilters;
  questionCounts: {
    totalCounts: { [key: string]: number };
    filteredCounts: { [key: string]: number };
  };
}

export const CategoryList = ({
  categories,
  selectedFilters,
  expandedCategories,
  searchTerm,
  onToggleFilter,
  onToggleExpand,
  type,
  parentFilters,
  questionCounts
}: CategoryListProps) => {
  const filteredCategories = useMemo(() => {
    let items = categories.filter(cat => cat.type === type);
    
    if (type === "theme") {
      items = items.filter(item => 
        parentFilters.specialties.includes(item.parentId || "")
      );
    } else if (type === "focus") {
      items = items.filter(item => {
        const parentTheme = categories.find(c => c.id === item.parentId);
        return parentTheme && parentFilters.themes.includes(parentTheme.id);
      });
    }
    
    if (searchTerm) {
      items = items.filter(item => 
        item.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return items;
  }, [categories, type, searchTerm, parentFilters]);

  const getChildCategories = (parentId: string) => {
    const nextType = type === "specialty" ? "theme" : "focus";
    return categories.filter(cat => 
      cat.type === nextType && cat.parentId === parentId
    );
  };

  const renderCategory = (category: FilterOption, level = 0) => {
    const hasChildren = type !== "focus" && getChildCategories(category.id).length > 0;
    const isExpanded = expandedCategories.includes(category.id);
    const isSelected = selectedFilters.includes(category.id);
    const parentCategory = category.parentId ? 
      categories.find(c => c.id === category.parentId) : null;

    return (
      <div key={category.id} className="mb-2">
        <div 
          className={`
            flex items-center space-x-2 hover:bg-gray-100 rounded-lg p-2 transition-colors
            ${level > 0 ? 'ml-6' : ''}
          `}
        >
          {hasChildren && (
            <button
              onClick={() => onToggleExpand(category.id)}
              className="p-1 hover:bg-gray-200 rounded"
            >
              <ChevronRight
                className={`h-4 w-4 transition-transform ${
                  isExpanded ? 'transform rotate-90' : ''
                }`}
              />
            </button>
          )}
          <Checkbox
            checked={isSelected}
            onCheckedChange={() => onToggleFilter(category.id)}
          />
          <div className="flex flex-col flex-1">
            <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer">
              {category.name}
            </label>
            {parentCategory && (
              <span className="text-xs text-gray-500">
                ↳ {parentCategory.name}
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">
              {questionCounts.totalCounts[category.id] || 0}
            </span>
            {isSelected && (
              <span className="text-sm text-primary">
                {questionCounts.filteredCounts[category.id] || 0}
              </span>
            )}
          </div>
        </div>

        {isExpanded && hasChildren && (
          <div className="mt-1">
            {getChildCategories(category.id).map(child =>
              renderCategory(child, level + 1)
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-1">
      {filteredCategories.map(category => renderCategory(category))}
    </div>
  );
};