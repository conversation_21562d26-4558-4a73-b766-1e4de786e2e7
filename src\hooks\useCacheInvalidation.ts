/**
 * Hook para invalidação inteligente de cache
 * Facilita a invalidação de dados relacionados
 */

import { useQueryClient } from '@tanstack/react-query';
import { CACHE_INVALIDATION, CACHE_KEYS } from '@/utils/cacheConfig';
import { persistentCache } from '@/utils/persistentCache';

export const useCacheInvalidation = () => {
  const queryClient = useQueryClient();

  /**
   * Invalida cache relacionado ao usuário
   */
  const invalidateUserCache = (userId: string) => {
    const keys = CACHE_INVALIDATION.USER_RELATED(userId);
    keys.forEach(key => {
      queryClient.invalidateQueries({ queryKey: key });
      // Também remover do cache persistente
      persistentCache.remove(JSON.stringify(key));
    });
  };

  /**
   * Invalida cache de questões
   */
  const invalidateQuestionsCache = (domain: string) => {
    const keys = CACHE_INVALIDATION.QUESTIONS_RELATED(domain);
    keys.forEach(key => {
      queryClient.invalidateQueries({ queryKey: key });
    });
  };

  /**
   * Invalida cache de medicamentos
   */
  const invalidateMedicationsCache = () => {
    const keys = CACHE_INVALIDATION.MEDICATIONS_RELATED();
    keys.forEach(key => {
      queryClient.invalidateQueries({ queryKey: key });
      persistentCache.remove(JSON.stringify(key));
    });
  };

  /**
   * Invalida cache específico por chave
   */
  const invalidateSpecific = (queryKey: unknown[]) => {
    queryClient.invalidateQueries({ queryKey });
    persistentCache.remove(JSON.stringify(queryKey));
  };

  /**
   * Limpa todo o cache (usar com cuidado)
   */
  const clearAllCache = () => {
    queryClient.clear();
    persistentCache.clear();
  };

  /**
   * Pré-carrega dados importantes
   */
  const prefetchCriticalData = async (userId?: string) => {
    try {
      // Pré-carregar configurações do site
      await queryClient.prefetchQuery({
        queryKey: CACHE_KEYS.SITE_SETTINGS,
        queryFn: async () => {
          // Implementar busca de configurações
          return {};
        },
        staleTime: 24 * 60 * 60 * 1000, // 24 horas
      });

      // Pré-carregar categorias
      await queryClient.prefetchQuery({
        queryKey: CACHE_KEYS.CATEGORIES,
        queryFn: async () => {
          // Implementar busca de categorias
          return [];
        },
        staleTime: 60 * 60 * 1000, // 1 hora
      });

      // Se tiver usuário, pré-carregar dados do perfil
      if (userId) {
        await queryClient.prefetchQuery({
          queryKey: CACHE_KEYS.USER_PROFILE(userId),
          queryFn: async () => {
            // Implementar busca do perfil
            return null;
          },
          staleTime: 15 * 60 * 1000, // 15 minutos
        });
      }
    } catch (error) {
      console.warn('Failed to prefetch critical data:', error);
    }
  };

  /**
   * Atualiza dados específicos no cache
   */
  const updateCacheData = <T>(queryKey: unknown[], updater: (oldData: T | undefined) => T) => {
    queryClient.setQueryData(queryKey, updater);
    
    // Também atualizar no cache persistente se for uma chave persistida
    const keyString = JSON.stringify(queryKey);
    const newData = queryClient.getQueryData(queryKey);
    if (newData) {
      persistentCache.save(keyString, newData);
    }
  };

  /**
   * Obtém estatísticas do cache
   */
  const getCacheStats = () => {
    const queryCache = queryClient.getQueryCache();
    const queries = queryCache.getAll();
    const persistentStats = persistentCache.getStats();

    return {
      totalQueries: queries.length,
      activeQueries: queries.filter(q => q.state.status === 'success').length,
      errorQueries: queries.filter(q => q.state.status === 'error').length,
      loadingQueries: queries.filter(q => q.state.status === 'pending').length,
      persistent: persistentStats,
    };
  };

  /**
   * Força atualização de dados críticos
   */
  const refreshCriticalData = async () => {
    // Invalidar e refetch dados críticos
    await queryClient.invalidateQueries({ 
      queryKey: CACHE_KEYS.SITE_SETTINGS,
      refetchType: 'active'
    });
    
    await queryClient.invalidateQueries({ 
      queryKey: CACHE_KEYS.CATEGORIES,
      refetchType: 'active'
    });
  };

  return {
    // Invalidação
    invalidateUserCache,
    invalidateQuestionsCache,
    invalidateMedicationsCache,
    invalidateSpecific,
    clearAllCache,
    
    // Pré-carregamento
    prefetchCriticalData,
    refreshCriticalData,
    
    // Atualização
    updateCacheData,
    
    // Estatísticas
    getCacheStats,
  };
};
