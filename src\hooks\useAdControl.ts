import { useState, useEffect } from 'react';
import { useUser } from '@supabase/auth-helpers-react';

interface AdControlConfig {
  showAds: boolean;
  adFrequency: number; // A cada quantos itens mostrar anúncio
  respectUserPreferences: boolean;
  minTimeOnPage: number; // Tempo mínimo na página antes de mostrar anúncios (ms)
}

/**
 * Hook para controlar exibição de anúncios
 * Considera preferências do usuário, tempo na página, etc.
 */
export const useAdControl = (config: Partial<AdControlConfig> = {}) => {
  const defaultConfig: AdControlConfig = {
    showAds: true,
    adFrequency: 5, // A cada 5 itens
    respectUserPreferences: true,
    minTimeOnPage: 3000, // 3 segundos
    ...config
  };

  const [shouldShowAds, setShouldShowAds] = useState(false);
  const [timeOnPage, setTimeOnPage] = useState(0);
  const user = useUser();

  useEffect(() => {
    // Timer para contar tempo na página
    const startTime = Date.now();
    const timer = setInterval(() => {
      setTimeOnPage(Date.now() - startTime);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    const checkAdDisplay = () => {
      // Não mostrar anúncios se desabilitado globalmente
      if (!defaultConfig.showAds) {
        setShouldShowAds(false);
        return;
      }

      // Aguardar tempo mínimo na página
      if (timeOnPage < defaultConfig.minTimeOnPage) {
        setShouldShowAds(false);
        return;
      }

      // Verificar preferências do usuário (se implementado)
      if (defaultConfig.respectUserPreferences && user) {
        // Aqui você pode verificar se o usuário tem preferência de não ver anúncios
        // Por exemplo, usuários premium, configurações de conta, etc.
        const userPreferences = user.user_metadata?.ad_preferences;
        if (userPreferences?.hideAds) {
          setShouldShowAds(false);
          return;
        }
      }

      // Verificar se é um ambiente de desenvolvimento
      if (process.env.NODE_ENV === 'development') {
        // Mostrar anúncios em dev apenas se explicitamente habilitado
        const showInDev = localStorage.getItem('show-ads-in-dev') === 'true';
        setShouldShowAds(showInDev);
        return;
      }

      // Mostrar anúncios em produção
      setShouldShowAds(true);
    };

    checkAdDisplay();
  }, [timeOnPage, user, defaultConfig]);

  /**
   * Verifica se deve mostrar anúncio baseado no índice do item
   */
  const shouldShowAdAtIndex = (index: number): boolean => {
    if (!shouldShowAds) return false;
    return index > 0 && index % defaultConfig.adFrequency === 0;
  };

  /**
   * Verifica se deve mostrar anúncio baseado na posição na página
   */
  const shouldShowAdAtPosition = (position: 'top' | 'middle' | 'bottom'): boolean => {
    if (!shouldShowAds) return false;

    switch (position) {
      case 'top':
        return timeOnPage > 2000; // 2 segundos
      case 'middle':
        return timeOnPage > 5000; // 5 segundos
      case 'bottom':
        return timeOnPage > 1000; // 1 segundo
      default:
        return true;
    }
  };

  /**
   * Controle manual para desenvolvedores
   */
  const toggleAdsInDev = () => {
    if (process.env.NODE_ENV === 'development') {
      const current = localStorage.getItem('show-ads-in-dev') === 'true';
      localStorage.setItem('show-ads-in-dev', (!current).toString());
      setShouldShowAds(!current);
      console.log(`🎯 Anúncios em desenvolvimento: ${!current ? 'ATIVADOS' : 'DESATIVADOS'}`);
    }
  };

  return {
    shouldShowAds,
    shouldShowAdAtIndex,
    shouldShowAdAtPosition,
    timeOnPage: Math.floor(timeOnPage / 1000), // em segundos
    toggleAdsInDev,
    adFrequency: defaultConfig.adFrequency,
    isLoggedIn: !!user,
  };
};

/**
 * Hook específico para controle de anúncios em listas
 */
export const useListAdControl = (itemCount: number, frequency: number = 5) => {
  const { shouldShowAds, shouldShowAdAtIndex } = useAdControl({ adFrequency: frequency });

  // Calcular posições onde mostrar anúncios
  const adPositions = [];
  if (shouldShowAds) {
    for (let i = frequency; i < itemCount; i += frequency) {
      adPositions.push(i);
    }
  }

  return {
    shouldShowAds,
    shouldShowAdAtIndex,
    adPositions,
    totalAds: adPositions.length,
  };
};

/**
 * Hook para controle de anúncios em páginas específicas
 */
export const usePageAdControl = (pageName: string) => {
  const adControl = useAdControl();

  // Configurações específicas por página
  const pageConfigs = {
    home: { showHeader: true, showSidebar: true, showContent: true },
    medicamentos: { showHeader: true, showSidebar: false, showContent: true },
    calculadoras: { showHeader: false, showSidebar: true, showContent: true },
    puericultura: { showHeader: true, showSidebar: true, showContent: true },
    // Adicione mais páginas conforme necessário
  };

  const config = pageConfigs[pageName as keyof typeof pageConfigs] || {
    showHeader: true,
    showSidebar: true,
    showContent: true,
  };

  return {
    ...adControl,
    pageConfig: config,
    shouldShowHeaderAd: adControl.shouldShowAds && config.showHeader,
    shouldShowSidebarAd: adControl.shouldShowAds && config.showSidebar,
    shouldShowContentAd: adControl.shouldShowAds && config.showContent,
  };
};
