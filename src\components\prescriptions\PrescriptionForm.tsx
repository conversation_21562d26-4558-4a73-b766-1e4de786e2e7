import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { MedicationSelector } from "./MedicationSelector";
import { PrescriptionBasicInfo } from "./form/PrescriptionBasicInfo";
import { PrescriptionFormActions } from "./form/PrescriptionFormActions";
import type { PrescriptionWithMedications } from "./types";

interface PrescriptionFormProps {
  onSuccess?: () => void;
  session: any;
  prescription?: PrescriptionWithMedications;
  onClose?: () => void;
}

export function PrescriptionForm({ onSuccess, session, prescription, onClose }: PrescriptionFormProps) {
  const [name, setName] = useState(prescription?.name || "");
  const [description, setDescription] = useState(prescription?.description || "");
  const [notes, setNotes] = useState(prescription?.notes || "");
  const [selectedMedications, setSelectedMedications] = useState<Array<{
    medicationId: string;
    dosageId: string;
    sectionTitle?: string;
    quantity?: string;
  }>>(prescription?.pedbook_prescription_medications?.map(med => ({
    medicationId: med.medication_id,
    dosageId: med.dosage_id,
    sectionTitle: med.section_title || undefined,
    quantity: med.quantity || undefined,
  })) || []);
  
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (prescription) {
        const { error: prescriptionError } = await supabase
          .from("pedbook_prescriptions")
          .update({
            name,
            description,
            notes,
            category_id: null,
          })
          .eq('id', prescription.id);

        if (prescriptionError) throw prescriptionError;

        const { error: deleteError } = await supabase
          .from("pedbook_prescription_medications")
          .delete()
          .eq('prescription_id', prescription.id);

        if (deleteError) throw deleteError;
      } else {
        const { data: newPrescription, error: prescriptionError } = await supabase
          .from("pedbook_prescriptions")
          .insert({
            name,
            description,
            notes,
            category_id: null,
            user_id: session.user.id,
          })
          .select()
          .single();

        if (prescriptionError) throw prescriptionError;

        prescription = newPrescription;
      }

      if (selectedMedications.length > 0) {
        const prescriptionMedications = selectedMedications.map((selection, index) => ({
          prescription_id: prescription!.id,
          medication_id: selection.medicationId,
          dosage_id: selection.dosageId,
          section_title: selection.sectionTitle || null,
          quantity: selection.quantity || null,
          display_order: index + 1,
        }));

        const { error: medicationsError } = await supabase
          .from("pedbook_prescription_medications")
          .insert(prescriptionMedications);

        if (medicationsError) throw medicationsError;
      }

      toast({
        title: prescription ? "Prescrição atualizada com sucesso!" : "Prescrição criada com sucesso!",
        description: "Você será redirecionado para a página de prescrições.",
      });

      if (onSuccess) {
        onSuccess();
      } else {
        navigate("/prescriptions");
      }
    } catch (error: any) {
      console.error("Error creating/updating prescription:", error);
      toast({
        variant: "destructive",
        title: prescription ? "Erro ao atualizar prescrição" : "Erro ao criar prescrição",
        description: error.message,
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <PrescriptionBasicInfo
        name={name}
        description={description}
        notes={notes}
        onNameChange={setName}
        onDescriptionChange={setDescription}
        onNotesChange={setNotes}
      />

      <MedicationSelector
        selectedMedications={selectedMedications}
        onSelectMedication={setSelectedMedications}
        weight={prescription?.patient_weight || 0}
        age={prescription?.patient_age || 0}
      />

      <PrescriptionFormActions isEditing={!!prescription} onCancel={onClose} />
    </form>
  );
}