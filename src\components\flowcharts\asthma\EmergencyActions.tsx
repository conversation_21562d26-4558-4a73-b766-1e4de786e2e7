
import { Card } from "@/components/ui/card";

export const EmergencyActions = () => {
  return (
    <div className="space-y-4">
      <div className="bg-white/80 p-4 rounded-lg border border-orange-100 dark:bg-orange-900/20 dark:border-orange-800/30 dark:text-gray-100">
        <h4 className="font-semibold text-orange-700 dark:text-orange-300 mb-2">Ações Imediatas:</h4>
        <ul className="list-disc list-inside space-y-2 text-gray-700 dark:text-gray-200">
          <li>Salbutamol: 6–10 puffs com espaçador e máscara, a cada 20 minutos</li>
          <li>Oxigênio: Manter saturação entre 94–98%</li>
          <li>Prednisolona 2mg/kg (máx 20 mg para ≤ 2 anos; máx de 30 mg para 2–5 anos) OU hidrocortisona 2-4 mg/kg/dose de 6/6h (máximo 250 mg/dose);</li>
          <li>Avaliar inicio de sulfato de magnésio EV 50mg/kg;</li>
          <li>Avaliar uso de terbutalina 10 mcg/kg/dose (máx 25mcg/dose) SC 20/20 minutos 3 vezes;</li>
        </ul>
      </div>

      <div className="bg-red-50/80 p-4 rounded-lg border border-red-200 dark:bg-red-900/30 dark:border-red-800/30">
        <h4 className="font-semibold text-red-700 dark:text-red-300 mb-2">Sem resposta / deterioração clínica e risco de óbito por asma</h4>
        <p className="text-gray-700 dark:text-gray-300 mb-2 italic">
          (sonolento, confusão mental, tórax silencioso, sinais de insuficiência respiratória)
        </p>
        <p className="text-red-800 dark:text-red-300 font-medium">
          Proteção de via aérea – intubação por sequência rápida e solicitação de UTI.
        </p>
      </div>
    </div>
  );
};
