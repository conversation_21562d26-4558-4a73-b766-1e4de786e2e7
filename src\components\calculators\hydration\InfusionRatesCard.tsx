import { Card } from "@/components/ui/card";
import { Clock, Droplets } from "lucide-react";

interface InfusionRatesCardProps {
  aliquots: {
    volume: number;
    infusionRate: number;
    dropRate: number;
    infusionTime: number;
  }[];
}

export const InfusionRatesCard = ({ aliquots }: InfusionRatesCardProps) => {
  if (!aliquots.length) return null;

  return (
    <Card className="p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Taxas de Infusão (Total 24h)
      </h3>
      <div className="space-y-4">
        {aliquots.map((aliquot, index) => (
          <div key={index} className="p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-700 mb-2">
              Alíquota {index + 1} ({aliquot.volume} mL em {aliquot.infusionTime}h)
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-primary" />
                <div>
                  <p className="text-sm text-gray-600">Bomba de Infusão</p>
                  <p className="text-lg font-semibold text-primary">
                    {aliquot.infusionRate.toFixed(2)} mL/h
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Droplets className="h-5 w-5 text-primary" />
                <div>
                  <p className="text-sm text-gray-600">Gotejamento</p>
                  <p className="text-lg font-semibold text-primary">
                    {aliquot.dropRate} gotas/min
                  </p>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
};