
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.4';

const ZUKI_API_KEY = Deno.env.get("ZUKI_API_KEY");
const ZUKI_API_URL = "https://api.zukijourney.com/v1/chat/completions";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Cria um cliente Supabase usando as variáveis de ambiente
const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Prompt do sistema para a IA
const SYSTEM_PROMPT = `Você é uma IA médica especializada em saúde materno-infantil. Seu objetivo é analisar informações fornecidas sobre medicamentos e amamentação, melhorar o texto atual de "uso amamentação", e gerar automaticamente os campos adicionais necessários: "efeitos no lactente", "alternativas seguras" e "orientações de uso".

Todas as informações são baseadas nas diretrizes do Ministério da Saúde do Brasil.
O público-alvo são médicos e profissionais de saúde, portanto o texto deve ser técnico e claro.
O texto deve ser curto, objetivo e direto ao ponto.
Seja preciso e baseie-se em evidências científicas atuais.`;

// Interface para os dados do medicamento
interface MedicationData {
  id: string;
  name: string;
  compatibility_level: string;
  usage_description: string;
  additional_info?: string | null;
  section_name?: string;
  subsection_name?: string;
}

// Interface para a resposta da IA
interface EnhancedMedicationData {
  uso_amamentacao: string;
  efeitos_no_lactente: string;
  alternativas_seguras: string;
  orientacoes_uso: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  // Verificar se temos a chave da API Zuki
  if (!ZUKI_API_KEY) {
    return new Response(
      JSON.stringify({ error: "API Zuki não configurada no servidor" }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  }

  try {
    const { medicationIds } = await req.json();

    if (!medicationIds || !Array.isArray(medicationIds) || medicationIds.length === 0) {
      return new Response(
        JSON.stringify({ error: "É necessário fornecer pelo menos um ID de medicamento" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    // Limitar o número de medicamentos por requisição
    if (medicationIds.length > 10) {
      return new Response(
        JSON.stringify({ error: "Máximo de 10 medicamentos por requisição" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    // Buscar os medicamentos no banco de dados
    const { data: medications, error } = await supabase
      .from('pedbook_breastfeeding_medications')
      .select(`
        id,
        name,
        compatibility_level,
        usage_description,
        additional_info,
        section:pedbook_breastfeeding_sections(name),
        subsection:pedbook_breastfeeding_subsections(name)
      `)
      .in('id', medicationIds);

    if (error) {
      return new Response(
        JSON.stringify({ error: `Erro ao buscar medicamentos: ${error.message}` }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    if (!medications || medications.length === 0) {
      return new Response(
        JSON.stringify({ error: "Nenhum medicamento encontrado com os IDs fornecidos" }),
        {
          status: 404,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    // Processar cada medicamento com a IA
    const results = [];
    for (const medication of medications) {
      // Formatar os dados do medicamento
      const formattedMedication: MedicationData = {
        id: medication.id,
        name: medication.name,
        compatibility_level: medication.compatibility_level,
        usage_description: medication.usage_description,
        additional_info: medication.additional_info,
        section_name: medication.section?.name,
        subsection_name: medication.subsection?.name
      };

      // Criar o prompt para a IA
      const userPrompt = `
Receba as seguintes informações:

Classe: ${formattedMedication.section_name || 'Não especificada'}
Subclasse: ${formattedMedication.subsection_name || 'Não especificada'}
Nome: ${formattedMedication.name}
Compatibilidade Amamentação: ${formattedMedication.compatibility_level}
Uso Amamentação: ${formattedMedication.usage_description}
${formattedMedication.additional_info ? `Informações Adicionais: ${formattedMedication.additional_info}` : ''}

Com base nessas informações, você deve:

1. Melhorar o texto de "Uso Amamentação", tornando-o mais técnico e claro:
   - Explique o risco com mais precisão (ex.: "Pode causar sedação, irritabilidade ou efeitos anticolinérgicos no lactente.").
   - Inclua a orientação prática de monitorar sinais específicos se for o caso.
   - Mantenha o texto conciso, com no máximo 2-3 frases.
   - Seja coerente com o nível de compatibilidade (Verde, Amarelo ou Vermelho).

2. Gerar o campo "Efeitos no Lactente", listando os principais efeitos adversos conhecidos.
   - Ex.: "Sonolência, sedação, irritabilidade."
   - Se não houver efeitos conhecidos, indique "Não foram relatados efeitos adversos significativos no lactente."
   - Seja específico e objetivo, listando apenas os efeitos mais relevantes.

3. Gerar o campo "Alternativas Seguras", listando medicamentos de mesma classe que são considerados mais seguros para uso durante a amamentação.
   - Ex.: "Paracetamol (Verde), Ibuprofeno (Verde)".
   - Se não houver alternativas ou se o medicamento já for seguro, indique "Não são necessárias alternativas, pois este medicamento é considerado seguro."
   - Mencione apenas alternativas reais e comprovadamente mais seguras.

4. Gerar o campo "Orientações de Uso", fornecendo recomendações práticas de uso seguro:
   - Ex.: "Usar após a mamada. Evitar aplicação em grandes áreas ou uso prolongado."
   - Inclua orientações específicas sobre horários, doses ou monitoramento quando relevante.
   - Seja prático e direto, com orientações que o profissional de saúde possa transmitir facilmente.

Responda APENAS em formato JSON com os seguintes campos, sem texto adicional antes ou depois:
{
  "uso_amamentacao": "Texto melhorado",
  "efeitos_no_lactente": "Efeitos listados",
  "alternativas_seguras": "Alternativas listadas",
  "orientacoes_uso": "Orientações listadas"
}
`;

      // Preparar a requisição para a Zuki API
      const zukiPayload = {
        model: "chatgpt-4o-latest",
        messages: [
          { role: "system", content: SYSTEM_PROMPT },
          { role: "user", content: userPrompt }
        ],
        temperature: 0.5,
        max_tokens: 1000,
        stream: false,
      };

      // Enviar requisição para a API Zuki
      const response = await fetch(ZUKI_API_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${ZUKI_API_KEY}`,
        },
        body: JSON.stringify(zukiPayload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Erro na API da Zuki: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      const aiResponse = data.choices[0].message.content;

      // Extrair o JSON da resposta
      let enhancedData: EnhancedMedicationData;
      try {
        // Tentar extrair o JSON da resposta
        const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          enhancedData = JSON.parse(jsonMatch[0]);

          // Validar os campos obrigatórios
          if (!enhancedData.uso_amamentacao || !enhancedData.efeitos_no_lactente ||
              !enhancedData.alternativas_seguras || !enhancedData.orientacoes_uso) {
            throw new Error("Resposta incompleta da IA");
          }
        } else {
          throw new Error("Formato de resposta inválido");
        }
      } catch (jsonError) {
        console.error(`Erro ao processar resposta JSON para ${formattedMedication.name}:`, jsonError);
        console.error("Resposta da IA:", aiResponse);

        // Tentar extrair informações mesmo com erro de parsing
        let uso = formattedMedication.usage_description;
        let efeitos = "Não foi possível processar";
        let alternativas = "Não foi possível processar";
        let orientacoes = "Não foi possível processar";

        // Tentar extrair campos individuais da resposta
        try {
          if (aiResponse.includes('"uso_amamentacao"')) {
            const usoMatch = aiResponse.match(/"uso_amamentacao"\s*:\s*"([^"]*)"/);
            if (usoMatch && usoMatch[1]) uso = usoMatch[1];
          }

          if (aiResponse.includes('"efeitos_no_lactente"')) {
            const efeitosMatch = aiResponse.match(/"efeitos_no_lactente"\s*:\s*"([^"]*)"/);
            if (efeitosMatch && efeitosMatch[1]) efeitos = efeitosMatch[1];
          }

          if (aiResponse.includes('"alternativas_seguras"')) {
            const alternativasMatch = aiResponse.match(/"alternativas_seguras"\s*:\s*"([^"]*)"/);
            if (alternativasMatch && alternativasMatch[1]) alternativas = alternativasMatch[1];
          }

          if (aiResponse.includes('"orientacoes_uso"')) {
            const orientacoesMatch = aiResponse.match(/"orientacoes_uso"\s*:\s*"([^"]*)"/);
            if (orientacoesMatch && orientacoesMatch[1]) orientacoes = orientacoesMatch[1];
          }
        } catch (extractError) {
          console.error("Erro ao extrair campos individuais:", extractError);
        }

        // Criar um objeto com os dados extraídos ou valores padrão
        enhancedData = {
          uso_amamentacao: uso,
          efeitos_no_lactente: efeitos,
          alternativas_seguras: alternativas,
          orientacoes_uso: orientacoes
        };
      }

      // MODIFICAÇÃO: Converte alternativas_seguras para array para compatibilidade com o tipo no banco
      // Se alternativas_seguras for uma string, converta-a para um array com um único elemento
      const alternativasSeguras = enhancedData.alternativas_seguras 
        ? [enhancedData.alternativas_seguras] 
        : ["Não há alternativas registradas"];

      // Adicionar ao resultado
      results.push({
        id: formattedMedication.id,
        name: formattedMedication.name,
        original: {
          compatibility_level: formattedMedication.compatibility_level,
          usage_description: formattedMedication.usage_description
        },
        enhanced: {
          uso_amamentacao: enhancedData.uso_amamentacao,
          efeitos_no_lactente: enhancedData.efeitos_no_lactente,
          alternativas_seguras: alternativasSeguras, // Usar a versão convertida para array
          orientacoes_uso: enhancedData.orientacoes_uso
        }
      });
    }

    // Retornar os resultados
    return new Response(
      JSON.stringify({ results }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  } catch (error) {
    console.error("Erro na função enhance-breastfeeding-medications:", error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  }
});
