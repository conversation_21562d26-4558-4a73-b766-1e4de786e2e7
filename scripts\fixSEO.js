/**
 * Script de correção rápida de SEO
 * Corrige canonical URLs e meta tags sem SSR complexo
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { generateRoutes, generateSitemap } from './generateRoutes.js';

console.log('🔧 Iniciando correção rápida de SEO...');

async function fixSEOIssues() {
  try {
    // Passo 1: Gerar rotas dinamicamente
    console.log('\n📋 Passo 1: Gerando rotas...');
    const routes = await generateRoutes();
    console.log(`✅ Rotas geradas: ${routes.length}`);

    // Passo 2: Gerar sitemap
    console.log('\n🗺️ Passo 2: Gerando sitemap...');
    await generateSitemap(routes);
    console.log('✅ Sitemap gerado');

    // Passo 3: Build normal do Vite
    console.log('\n🔨 Passo 3: Executando build do Vite...');
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ Build do Vite concluído');

    // Passo 4: Corrigir SEO das páginas geradas
    console.log('\n🏷️ Passo 4: Corrigindo SEO das páginas...');
    await fixGeneratedPages();
    console.log('✅ SEO das páginas corrigido');

    console.log('\n🎉 Correção de SEO concluída!');

  } catch (error) {
    console.error('\n❌ Erro na correção de SEO:', error);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

/**
 * Corrige SEO das páginas já geradas
 */
async function fixGeneratedPages() {
  const distPath = path.join(process.cwd(), 'dist');

  // Carregar dados das rotas
  const routesData = JSON.parse(
    fs.readFileSync(path.join(process.cwd(), 'prerender-routes.json'), 'utf8')
  );

  console.log('🔧 Corrigindo páginas de medicamentos...');

  // Corrigir medicamentos
  routesData.medications.forEach(med => {
    const medDir = path.join(distPath, 'medicamentos', med.slug);
    if (!fs.existsSync(medDir)) {
      fs.mkdirSync(medDir, { recursive: true });
    }

    // Copiar e corrigir HTML base
    const baseHtml = fs.readFileSync(path.join(distPath, 'index.html'), 'utf8');
    let customHtml = baseHtml;

    // Gerar meta tags otimizadas
    const optimizedTitle = `${med.name} - Dose Pediátrica e Posologia | PedBook`;
    const optimizedDescription = generateMedicationDescription(med);
    const canonicalUrl = `https://pedb.com.br/medicamentos/${med.slug}`;
    const keywords = generateMedicationKeywords(med);

    // Substituir todas as meta tags
    customHtml = updateMetaTags(customHtml, {
      title: optimizedTitle,
      description: optimizedDescription,
      canonical: canonicalUrl,
      keywords: keywords,
      ogUrl: canonicalUrl,
      twitterUrl: canonicalUrl
    });

    // Adicionar structured data específico
    customHtml = addMedicationStructuredData(customHtml, med, canonicalUrl);

    // Adicionar conteúdo básico no body para SEO
    customHtml = addBasicContentToBody(customHtml, med, 'medication');

    fs.writeFileSync(path.join(medDir, 'index.html'), customHtml);
    console.log(`✅ Corrigido: /medicamentos/${med.slug}`);
  });

  console.log('🔧 Corrigindo páginas de calculadoras...');

  // Corrigir calculadoras
  routesData.calculators.forEach(calc => {
    const calcDir = path.join(distPath, 'calculadoras', calc.slug);
    if (!fs.existsSync(calcDir)) {
      fs.mkdirSync(calcDir, { recursive: true });
    }

    const baseHtml = fs.readFileSync(path.join(distPath, 'index.html'), 'utf8');
    let customHtml = baseHtml;

    const optimizedTitle = `${calc.name} Pediátrica - Calculadora Online | PedBook`;
    const optimizedDescription = generateCalculatorDescription(calc);
    const canonicalUrl = `https://pedb.com.br/calculadoras/${calc.slug}`;
    const keywords = generateCalculatorKeywords(calc);

    customHtml = updateMetaTags(customHtml, {
      title: optimizedTitle,
      description: optimizedDescription,
      canonical: canonicalUrl,
      keywords: keywords,
      ogUrl: canonicalUrl,
      twitterUrl: canonicalUrl
    });

    customHtml = addCalculatorStructuredData(customHtml, calc, canonicalUrl);
    customHtml = addBasicContentToBody(customHtml, calc, 'calculator');

    fs.writeFileSync(path.join(calcDir, 'index.html'), customHtml);
    console.log(`✅ Corrigido: /calculadoras/${calc.slug}`);
  });

  console.log('🎉 Todas as páginas foram corrigidas!');
}

/**
 * Atualiza todas as meta tags de uma vez
 */
function updateMetaTags(html, metaData) {
  let updatedHtml = html;

  // Title
  updatedHtml = updatedHtml.replace(
    /<title>.*?<\/title>/,
    `<title>${metaData.title}</title>`
  );

  // Description
  updatedHtml = updatedHtml.replace(
    /<meta name="description" content=".*?">/,
    `<meta name="description" content="${metaData.description}">`
  );

  // Keywords
  updatedHtml = updatedHtml.replace(
    /<meta name="keywords" content=".*?">/,
    `<meta name="keywords" content="${metaData.keywords}">`
  );

  // Canonical
  updatedHtml = updatedHtml.replace(
    /<link rel="canonical" href=".*?" \/>/,
    `<link rel="canonical" href="${metaData.canonical}" />`
  );

  // Open Graph
  updatedHtml = updatedHtml.replace(
    /<meta property="og:title" content=".*?" \/>/,
    `<meta property="og:title" content="${metaData.title}" />`
  );

  updatedHtml = updatedHtml.replace(
    /<meta property="og:description" content=".*?" \/>/,
    `<meta property="og:description" content="${metaData.description}" />`
  );

  updatedHtml = updatedHtml.replace(
    /<meta property="og:url" content=".*?" \/>/,
    `<meta property="og:url" content="${metaData.ogUrl}" />`
  );

  // Twitter Cards
  updatedHtml = updatedHtml.replace(
    /<meta name="twitter:title" content=".*?" \/>/,
    `<meta name="twitter:title" content="${metaData.title}" />`
  );

  updatedHtml = updatedHtml.replace(
    /<meta name="twitter:description" content=".*?" \/>/,
    `<meta name="twitter:description" content="${metaData.description}" />`
  );

  updatedHtml = updatedHtml.replace(
    /<meta name="twitter:url" content=".*?" \/>/,
    `<meta name="twitter:url" content="${metaData.twitterUrl}" />`
  );

  return updatedHtml;
}

/**
 * Gera descrição otimizada para medicamentos
 */
function generateMedicationDescription(med) {
  const baseDose = med.pediatric_dose || med.dose || '10-15mg/kg/dose';
  const indication = med.indication || med.category || 'analgésico e antipirético';

  return `Dose pediátrica do ${med.name}: ${baseDose}. ${indication}. Calculadora automática, posologia, indicações e contraindicações para pediatria.`.substring(0, 160);
}

/**
 * Gera descrição otimizada para calculadoras
 */
function generateCalculatorDescription(calc) {
  return `${calc.name} pediátrica online gratuita. Ferramenta precisa para profissionais de saúde com cálculos automáticos e confiáveis em pediatria.`.substring(0, 160);
}

/**
 * Gera keywords para medicamentos
 */
function generateMedicationKeywords(med) {
  const baseKeywords = [
    med.name.toLowerCase(),
    `dose ${med.name.toLowerCase()}`,
    `${med.name.toLowerCase()} pediatrico`,
    `posologia ${med.name.toLowerCase()}`,
    `${med.name.toLowerCase()} criança`,
    'dose pediátrica',
    'calculadora dose',
    'pediatria',
    'medicamento infantil'
  ];

  if (med.category) {
    baseKeywords.push(med.category.toLowerCase());
  }

  return baseKeywords.join(', ');
}

/**
 * Gera keywords para calculadoras
 */
function generateCalculatorKeywords(calc) {
  return [
    calc.name.toLowerCase(),
    `calculadora ${calc.name.toLowerCase()}`,
    `${calc.name.toLowerCase()} pediatrica`,
    'calculadora pediátrica',
    'ferramenta médica',
    'pediatria',
    'cálculo médico',
    'profissional saúde'
  ].join(', ');
}

/**
 * Adiciona structured data para medicamentos
 */
function addMedicationStructuredData(html, med, url) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "MedicalWebPage",
    "name": `${med.name} - Dosagem Pediátrica`,
    "description": generateMedicationDescription(med),
    "url": url,
    "medicalAudience": {
      "@type": "MedicalAudience",
      "audienceType": "Profissionais de Saúde Pediátrica"
    },
    "about": {
      "@type": "Drug",
      "name": med.name,
      "activeIngredient": med.active_ingredient || med.name,
      "dosageForm": "Suspensão oral, Comprimido, Solução"
    },
    "publisher": {
      "@type": "Organization",
      "name": "PedBook",
      "url": "https://pedb.com.br"
    },
    "lastReviewed": new Date().toISOString().split('T')[0]
  };

  const jsonLd = `<script type="application/ld+json">${JSON.stringify(structuredData, null, 2)}</script>`;

  return html.replace('</head>', `  ${jsonLd}\n</head>`);
}

/**
 * Adiciona structured data para calculadoras
 */
function addCalculatorStructuredData(html, calc, url) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": `${calc.name} Pediátrica`,
    "description": generateCalculatorDescription(calc),
    "url": url,
    "applicationCategory": "MedicalApplication",
    "operatingSystem": "Web Browser",
    "audience": {
      "@type": "MedicalAudience",
      "audienceType": "Profissionais de Saúde Pediátrica"
    },
    "publisher": {
      "@type": "Organization",
      "name": "PedBook",
      "url": "https://pedb.com.br"
    }
  };

  const jsonLd = `<script type="application/ld+json">${JSON.stringify(structuredData, null, 2)}</script>`;

  return html.replace('</head>', `  ${jsonLd}\n</head>`);
}

/**
 * Adiciona conteúdo básico no body para SEO
 */
function addBasicContentToBody(html, item, type) {
  const seoContent = generateSEOContent(item, type);

  // Adicionar conteúdo SEO após o div#root
  const seoDiv = `
    <div id="root"></div>
    <!-- SEO Content for Crawlers -->
    <div style="display: none;" data-seo-content>
      ${seoContent}
    </div>`;

  return html.replace('<div id="root"></div>', seoDiv);
}

/**
 * Gera conteúdo SEO básico
 */
function generateSEOContent(item, type) {
  if (type === 'medication') {
    return `
      <h1>${item.name} - Dose Pediátrica</h1>
      <p>Informações sobre ${item.name} em pediatria, incluindo dose, posologia e indicações.</p>
      <h2>Dose Pediátrica</h2>
      <p>Dose recomendada: ${item.pediatric_dose || item.dose || '10-15mg/kg/dose'}</p>
      <h2>Indicações</h2>
      <p>${item.indication || item.category || 'Medicamento para uso pediátrico'}</p>
      <h2>Calculadora de Dose</h2>
      <p>Use nossa calculadora automática para determinar a dose correta baseada no peso da criança.</p>
    `;
  } else if (type === 'calculator') {
    return `
      <h1>${item.name} Pediátrica</h1>
      <p>Calculadora online gratuita de ${item.name.toLowerCase()} para pediatria.</p>
      <h2>Como usar</h2>
      <p>Ferramenta precisa para profissionais de saúde realizarem cálculos pediátricos.</p>
      <h2>Benefícios</h2>
      <p>Cálculos automáticos, precisos e confiáveis para uso em pediatria.</p>
    `;
  }

  return `<h1>${item.name}</h1><p>Informações detalhadas sobre ${item.name} em pediatria.</p>`;
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  fixSEOIssues();
}

export { fixSEOIssues };
