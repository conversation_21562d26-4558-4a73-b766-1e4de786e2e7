import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";

interface PecarnQuestionProps {
  question: string | JSX.Element;
  onAnswer: (answer: boolean) => void;
}

export const PecarnQuestion = ({ question, onAnswer }: PecarnQuestionProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="p-6 rounded-xl bg-blue-50 border border-blue-200">
        {typeof question === "string" ? (
          <p className="text-lg text-gray-800 whitespace-pre-line">{question}</p>
        ) : (
          question
        )}
      </div>

      <div className="flex justify-center gap-4">
        <Button
          onClick={() => onAnswer(true)}
          variant="outline"
          className="w-32 border-blue-200 hover:bg-blue-50"
        >
          Sim
        </Button>
        <Button
          onClick={() => onAnswer(false)}
          variant="outline"
          className="w-32 border-blue-200 hover:bg-blue-50"
        >
          Não
        </Button>
      </div>
    </motion.div>
  );
};