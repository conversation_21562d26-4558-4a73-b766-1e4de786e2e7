
import { useState, useEffect, use<PERSON>em<PERSON> } from "react";
import { Card } from "./ui/card";
import { useToast } from "./ui/use-toast";
import { FilterHeader } from "./filters/components/FilterHeader";
import { FilterContent } from "./filters/components/FilterContent";
import { FilterSummary } from "./filters/components/FilterSummary";
import { FilterActions } from "./filters/components/FilterActions";
import { FilterAccordion } from "./filters/components/FilterAccordion";
import { useQuestionMetadata } from "@/hooks/useQuestionMetadata";
import { useFilteredQuestions } from "@/hooks/useFilteredQuestions";
import { useFilterState } from "@/hooks/useFilterState";
import { useOptimizedFilterSelection } from "@/hooks/useOptimizedFilterSelection";

import { useSessionCreation } from "@/components/filters/hooks/useSessionCreation";
import type { SelectedFilters, QuestionMetadata } from "@/types/question";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogDescription } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import RandomQuestionsDialog from "./RandomQuestionsDialog";
import { useDomain } from "@/hooks/useDomain";
import { useAnsweredQuestions } from "@/hooks/useAnsweredQuestions";
import { Skeleton } from "@/components/ui/skeleton";

import { QuestionFilterLoading } from "./filters/QuestionFilterLoading";
import { QuestionFilterTutorial } from "./filters/QuestionFilterTutorial";
import { useRef } from "react";
// Removido import desnecessário: import { useAnsweredQuestions } from "@/hooks/useAnsweredQuestions";

interface QuestionFilterProps {
  selectedFilters: SelectedFilters;
  setSelectedFilters: (filters: SelectedFilters) => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  setQuestions: (questions: any[]) => void;
  onSessionCreated: (sessionId: string) => void;
  onShowRandomDialog: () => void;
  domain?: string;
}

const QuestionFilter = ({
  selectedFilters: initialFilters,
  setSelectedFilters: parentSetSelectedFilters,
  searchTerm,
  setSearchTerm,
  setQuestions,
  onSessionCreated,
  onShowRandomDialog
}: QuestionFilterProps) => {
  const { toast } = useToast();
  const { domain, isResidencia, isReady } = useDomain();
  const [openSection, setOpenSection] = useState<"specialty" | "institution" | "year" | "question_type" | "question_format">("specialty");
  const [showTitleDialog, setShowTitleDialog] = useState(false);
  const [showRandomDialog, setShowRandomDialog] = useState(false);
  const [sessionTitle, setSessionTitle] = useState("");
  const [page, setPage] = useState(1);
  const [hasAppliedFilters, setHasAppliedFilters] = useState(false);
  const [excludeAnswered, setExcludeAnswered] = useState(false);
  const [answeredQuestionsCount, setAnsweredQuestionsCount] = useState(0);
  const { handleCreateSession } = useSessionCreation();
  const { data: answeredQuestions } = useAnsweredQuestions();
  const MAX_QUESTIONS_ALLOWED = 300;

  useEffect(() => {
    if (isReady && domain) {
      //console.log(`🏥 [QuestionFilter] Using domain: ${domain}, isResidencia: ${isResidencia}`);
    }
  }, [domain, isResidencia, isReady]);

  // Atualizar contador de questões acertadas
  useEffect(() => {
    if (answeredQuestions?.count) {
      setAnsweredQuestionsCount(answeredQuestions.count);
    } else {
      setAnsweredQuestionsCount(0);
    }
  }, [answeredQuestions]);

  const {
    selectedFilters,
    setSelectedFilters,
    expandedItems,
    handleToggleFilter,
    toggleExpand
  } = useFilterState(initialFilters || {
    specialties: [],
    themes: [],
    focuses: [],
    locations: [],
    years: [],
    question_types: [],
    question_formats: [],
    excludeAnswered: false
  });

  // Hook otimizado para seleção sem delay
  const {
    questionCount: optimizedQuestionCount,
    isUpdating,
    isFetching: isOptimizedFetching,
    handleFilterToggle: optimizedHandleToggle,
    setSelectedFilters: setOptimizedFilters
  } = useOptimizedFilterSelection({
    initialFilters: selectedFilters,
    onFiltersChange: (filters) => {
      setSelectedFilters(filters);
      parentSetSelectedFilters(filters);
    },
    debounceMs: 300
  });

  //console.log("🔄 selectedFilters:", selectedFilters);

  const { data: metadata, isLoading: isLoadingMetadata, error: metadataError } = useQuestionMetadata();

  // Só carregar questões completas quando realmente necessário (para criar sessão)
  // Nunca carregar automaticamente - só quando o usuário explicitamente quiser criar uma sessão
  const shouldLoadQuestions = false; // hasAppliedFilters && (showTitleDialog || showRandomDialog);

  const {
    data: filteredData,
    isLoading: isLoadingQuestions,
    isFetching: isFetchingQuestions,
    refetch
  } = useFilteredQuestions(selectedFilters, page, 50, shouldLoadQuestions);

  // Usar a contagem otimizada quando disponível, senão usar a contagem dos dados filtrados
  const totalQuestions = hasAppliedFilters ?
    (optimizedQuestionCount > 0 ? optimizedQuestionCount : (filteredData?.total_count || 0)) : 0;
  const questions = hasAppliedFilters ? (filteredData?.questions || []) : [];
  const filteredExcludedCount = filteredData?.excluded_questions_count || 0;

  // Separar loading inicial (metadata) do loading de questões filtradas
  const isInitialLoading = isLoadingMetadata;
  const isQuestionsLoading = isLoadingQuestions || isFetchingQuestions;

  useEffect(() => {
    if (questions && questions.length > 0) {
     // console.log(`📋 [QuestionFilter] Setting ${questions.length} questions for domain: ${domain}`);
      setQuestions(questions);
    } else if (hasAppliedFilters) {
      //console.log(`📋 [QuestionFilter] No questions found for the current filters in domain: ${domain}`);
     // setQuestions([]);
    }
  }, [questions, setQuestions, domain, hasAppliedFilters]);

  // Removido o refetch automático - só vamos carregar questões quando necessário
  // const lastFiltersHash = useRef("");

  // useEffect(() => {
  //   const hasAnyFilter = Object.values(selectedFilters).some(f =>
  //     Array.isArray(f) && f.length > 0
  //   );
  //   const currentHash = JSON.stringify(selectedFilters);

  //   if (hasAnyFilter && currentHash !== lastFiltersHash.current) {
  //     lastFiltersHash.current = currentHash;
  //     refetch();
  //   }
  // }, [selectedFilters, refetch]);

  useEffect(() => {
    if (!selectedFilters || typeof selectedFilters !== 'object') {
      return;
    }

    const hasAnyFilter = Object.values(selectedFilters).some(f =>
      Array.isArray(f) && f.length > 0
    );
    if (hasAppliedFilters !== hasAnyFilter) {
      setHasAppliedFilters(hasAnyFilter);
    }
  }, [selectedFilters, hasAppliedFilters]);

  // Build question counts for filtering
  const buildQuestionCounts = () => {
    const totalCounts = {};
    const filteredCounts = {};

    if (metadata) {
      metadata.specialties.forEach(specialty => {
        const count = specialty.question_count || specialty.count || 0;
        totalCounts[specialty.id] = count;
      });
      metadata.themes.forEach(theme => {
        const count = theme.question_count || theme.count || 0;
        totalCounts[theme.id] = count;
      });
      metadata.focuses.forEach(focus => {
        const count = focus.question_count || focus.count || 0;
        totalCounts[focus.id] = count;
      });
      metadata.locations.forEach(location => {
        const count = location.question_count || location.count || 0;
        totalCounts[location.id] = count;
      });
      metadata.years.forEach(yearObj => {
        const count = yearObj.question_count || yearObj.count || 0;
        totalCounts[yearObj.year.toString()] = count;
      });
    }

    // Adicionar contagens reais para question_formats baseadas nos dados de pediatria
    totalCounts['ALTERNATIVAS'] = 15165;
    totalCounts['VERDADEIRO_FALSO'] = 285;
    totalCounts['DISSERTATIVA'] = 202;

    if (questions) {
      const counts = {};

      questions.forEach(question => {
        if (question.specialty?.id) {
          counts[question.specialty.id] = (counts[question.specialty.id] || 0) + 1;
        }
        if (question.theme?.id) {
          counts[question.theme.id] = (counts[question.theme.id] || 0) + 1;
        }
        if (question.focus?.id) {
          counts[question.focus.id] = (counts[question.focus.id] || 0) + 1;
        }
        // Adicionar contagem para question_format
        if (question.question_format) {
          counts[question.question_format] = (counts[question.question_format] || 0) + 1;
        }
      });

      Object.assign(filteredCounts, counts);
    }



    return { totalCounts, filteredCounts };
  };

  const questionCounts = useMemo(() => buildQuestionCounts(), [metadata, questions]);

  const handleStartStudy = async () => {
    if (!sessionTitle.trim()) {
      toast({
        title: "Erro",
        description: "Por favor, insira um título para a sessão de estudos",
        variant: "destructive"
      });
      return;
    }

    if (totalQuestions === 0) {
      toast({
        title: "Erro",
        description: "Selecione pelo menos uma questão para iniciar os estudos",
        variant: "destructive"
      });
      return;
    }

    if (totalQuestions > MAX_QUESTIONS_ALLOWED) {
      toast({
        title: "Erro",
        description: `O limite máximo é de ${MAX_QUESTIONS_ALLOWED} questões por sessão de estudos`,
        variant: "destructive"
      });
      return;
    }

    try {
      const result = await handleCreateSession(selectedFilters, sessionTitle);

      if (result?.id) {
        setShowTitleDialog(false);
        setSessionTitle("");
        onSessionCreated(result.id);
      } else {
        throw new Error("Failed to create session");
      }
    } catch (error: any) {
      toast({
        title: "Erro ao criar sessão",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const handleShowRandomDialog = () => {
    setShowRandomDialog(true);
  };

  const handleClearAllFilters = () => {
    const emptyFilters = {
      specialties: [],
      themes: [],
      focuses: [],
      locations: [],
      years: [],
      question_types: [],
      question_formats: [],
      excludeAnswered: false
    };
    setSelectedFilters(emptyFilters);
    setOptimizedFilters(emptyFilters);
    setExcludeAnswered(false);
    parentSetSelectedFilters(emptyFilters);
  };

  // Mostrar loading completo apenas no carregamento inicial
  if (!isReady || isInitialLoading) {
    return <QuestionFilterLoading />;
  }

  const getSectionTitle = () => {
      // For PedBook, we always show themes directly
      return "Temas";
  };

  const isResidenciaOrRevalida = isResidencia || domain === "revalida";
  const shouldShowInstitution = domain !== "revalida";
  const shouldShowQuestionType = !isResidencia && domain !== "revalida";

  // Safely provide the metadata
  const safeMetadata: QuestionMetadata = metadata || {
    specialties: [],
    themes: [],
    focuses: [],
    locations: [],
    years: []
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header Section - Redesigned */}
      <div className="relative">
        <div className="bg-gradient-to-r from-[#FF6B00] to-[#FF8800] rounded-2xl p-4 sm:p-6 border-2 border-black shadow-lg">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-4">
            <div className="text-center lg:text-left">
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-white mb-2">
                🎯 Banco de Questões
              </h1>
              <p className="text-white/90 text-sm lg:text-base">
                Filtre e encontre as questões perfeitas para seu estudo
              </p>
            </div>
            <div className="bg-white/95 backdrop-blur-sm rounded-xl p-3 sm:p-4 border-2 border-black min-w-[140px] sm:min-w-[160px] text-center">
              <div className="text-xs text-gray-600 mb-1">Questões Encontradas</div>
              <div className="text-xl sm:text-2xl font-bold text-[#FF6B00]">
                {isUpdating || isOptimizedFetching ? '...' : totalQuestions.toLocaleString()}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filter Summary - Redesigned */}
      <FilterSummary
        selectedFilters={selectedFilters}
        availableFilters={safeMetadata}
        onRemoveFilter={optimizedHandleToggle}
        onClearAllFilters={handleClearAllFilters}
        totalQuestions={totalQuestions}
        isLoading={isQuestionsLoading}
      />

      {/* Filter Sections - Redesigned Grid Layout */}
      <div className="space-y-3">
        {isResidenciaOrRevalida && (
          <div className="bg-white/95 backdrop-blur-sm rounded-xl border-2 border-yellow-400/70 shadow-md overflow-hidden" data-tutorial="specialty-section">
            <FilterAccordion
              title={`🧠 ${getSectionTitle()}`}
              count={(selectedFilters.themes?.length || 0) + (selectedFilters.focuses?.length || 0)}
              isOpen={openSection === "specialty"}
              onToggle={() => setOpenSection(openSection === "specialty" ? null : "specialty")}
            >
              <FilterContent
                activeTab="specialty"
                filters={safeMetadata}
                selectedFilters={selectedFilters}
                expandedItems={expandedItems}
                onToggleExpand={toggleExpand}
                onToggleFilter={optimizedHandleToggle}
                questionCounts={questionCounts}
                isLoading={isLoadingMetadata}
              />
            </FilterAccordion>
          </div>
        )}

        {!isResidenciaOrRevalida && (
          <div className="bg-white/95 backdrop-blur-sm rounded-xl border-2 border-yellow-400/70 shadow-md overflow-hidden" data-tutorial="specialty-section">
            <FilterAccordion
              title="📚 Temas"
              count={selectedFilters.themes?.length || 0}
              isOpen={openSection === "specialty"}
              onToggle={() => setOpenSection(openSection === "specialty" ? null : "specialty")}
            >
              <FilterContent
                activeTab="specialty"
                filters={safeMetadata}
                selectedFilters={selectedFilters}
                expandedItems={expandedItems}
                onToggleExpand={toggleExpand}
                onToggleFilter={optimizedHandleToggle}
                questionCounts={questionCounts}
                isLoading={isLoadingMetadata}
              />
            </FilterAccordion>
          </div>
        )}

        {shouldShowInstitution && isResidenciaOrRevalida && (
          <div className="bg-white/95 backdrop-blur-sm rounded-xl border-2 border-blue-400/70 shadow-md overflow-hidden" data-tutorial="institution-section">
            <FilterAccordion
              title="🏥 Instituições"
              count={selectedFilters.locations?.length || 0}
              isOpen={openSection === "institution"}
              onToggle={() => setOpenSection(openSection === "institution" ? null : "institution")}
            >
              <FilterContent
                activeTab="location"
                filters={safeMetadata}
                selectedFilters={selectedFilters}
                expandedItems={expandedItems}
                onToggleExpand={toggleExpand}
                onToggleFilter={optimizedHandleToggle}
                questionCounts={questionCounts}
                isLoading={isLoadingMetadata}
              />
            </FilterAccordion>
          </div>
        )}

        <div className="bg-white/95 backdrop-blur-sm rounded-xl border-2 border-green-400/70 shadow-md overflow-hidden" data-tutorial="year-section">
          <FilterAccordion
            title="📅 Anos"
            count={selectedFilters.years?.length || 0}
            isOpen={openSection === "year"}
            onToggle={() => setOpenSection(openSection === "year" ? null : "year")}
          >
            <FilterContent
              activeTab="year"
              filters={safeMetadata}
              selectedFilters={selectedFilters}
              expandedItems={expandedItems}
              onToggleExpand={toggleExpand}
              onToggleFilter={optimizedHandleToggle}
              questionCounts={questionCounts}
              isLoading={isLoadingMetadata}
            />
          </FilterAccordion>
        </div>

        {shouldShowQuestionType && (
          <div className="bg-white/95 backdrop-blur-sm rounded-xl border-2 border-purple-400/70 shadow-md overflow-hidden" data-tutorial="question-type-section">
            <FilterAccordion
              title="📝 Tipo de Prova"
              count={selectedFilters.question_types?.length || 0}
              isOpen={openSection === "question_type"}
              onToggle={() => setOpenSection(openSection === "question_type" ? null : "question_type")}
            >
              <FilterContent
                activeTab="question_type"
                filters={safeMetadata}
                selectedFilters={selectedFilters}
                expandedItems={expandedItems}
                onToggleExpand={toggleExpand}
                onToggleFilter={optimizedHandleToggle}
                questionCounts={questionCounts}
                isLoading={isLoadingMetadata}
              />
            </FilterAccordion>
          </div>
        )}

        {/* Novo filtro de formato de questão */}
        <div className="bg-white/95 backdrop-blur-sm rounded-xl border-2 border-pink-400/70 shadow-md overflow-hidden" data-tutorial="question-format-section">
          <FilterAccordion
            title="🎯 Formato de Questão"
            count={selectedFilters.question_formats?.length || 0}
            isOpen={openSection === "question_format"}
            onToggle={() => setOpenSection(openSection === "question_format" ? null : "question_format")}
          >
            <FilterContent
              activeTab="question_format"
              filters={safeMetadata}
              selectedFilters={selectedFilters}
              expandedItems={expandedItems}
              onToggleExpand={toggleExpand}
              onToggleFilter={optimizedHandleToggle}
              questionCounts={questionCounts}
              isLoading={isLoadingMetadata}
            />
          </FilterAccordion>
        </div>
      </div>

      {/* Action Section - Redesigned */}
      <div className="bg-white/95 backdrop-blur-sm rounded-xl border-2 border-[#FF6B00]/70 shadow-lg p-6" data-tutorial="study-options">
        <FilterActions
          questionCount={totalQuestions}
          onShowRandomDialog={handleShowRandomDialog}
          onStartStudy={() => setShowTitleDialog(true)}
          excludeAnswered={excludeAnswered}
          onExcludeAnsweredChange={(value) => {
            setExcludeAnswered(value);
            const newFilters = {
              ...selectedFilters,
              excludeAnswered: value
            };
            setSelectedFilters(newFilters);
            setTimeout(() => {
              setOptimizedFilters(newFilters);
            }, 0);
          }}
          answeredQuestionsCount={answeredQuestionsCount}
          filteredExcludedCount={filteredExcludedCount}
        />
      </div>

      {/* Dialogs */}
      <Dialog open={showTitleDialog} onOpenChange={setShowTitleDialog}>
          <DialogContent className="w-[95vw] max-w-md max-h-[85dvh] rounded-2xl sm:rounded-xl overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Iniciar Sessão de Estudos</DialogTitle>
              <DialogDescription>
                Digite um título para identificar esta sessão de estudos
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6 py-4">
              <div className="space-y-2">
                <Label htmlFor="session-title" className="text-sm font-medium">
                  Título da Sessão
                </Label>
                <Input
                  id="session-title"
                  placeholder="Ex: Revisão Clínica Médica"
                  value={sessionTitle}
                  onChange={(e) => setSessionTitle(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="flex flex-col sm:flex-row justify-end gap-3 pt-2">
                <Button
                  variant="outline"
                  onClick={() => setShowTitleDialog(false)}
                  className="w-full sm:w-auto order-2 sm:order-1"
                >
                  Cancelar
                </Button>
                <Button
                  onClick={handleStartStudy}
                  className="w-full sm:w-auto order-1 sm:order-2"
                >
                  Começar
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

      <RandomQuestionsDialog
        open={showRandomDialog}
        onOpenChange={setShowRandomDialog}
        domain={domain}
        filteredQuestions={questions}
        totalQuestionCount={totalQuestions}
        filters={selectedFilters}
      />

      {/* Tutorial Component */}
      <QuestionFilterTutorial />
    </div>
  );
};

export default QuestionFilter;
