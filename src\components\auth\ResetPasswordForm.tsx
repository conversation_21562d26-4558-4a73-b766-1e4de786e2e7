
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { supabase } from "@/integrations/supabase/client";
import { KeyRound } from "lucide-react";
import { useNotification } from "@/context/NotificationContext";

const formSchema = z.object({
  email: z.string().email("Email inválido"),
});

interface ResetPasswordFormProps {
  onBackToLogin: () => void;
}

const ResetPasswordForm = ({ onBackToLogin }: ResetPasswordFormProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const { showNotification } = useNotification();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zod<PERSON><PERSON><PERSON>ver(formSchema),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsLoading(true);
      console.log("📧 [ResetPasswordForm] Solicitando redefinição para:", values.email);

      const { error } = await supabase.auth.resetPasswordForEmail(values.email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) {
        throw error;
      }

      showNotification({
        title: "Email enviado!",
        description: "Verifique sua caixa de entrada para redefinir sua senha.",
        type: "success",
        buttonText: "Continuar",
        onButtonClick: onBackToLogin
      });
    } catch (error: any) {

      showNotification({
        title: "Erro ao enviar email",
        description: "Verifique o endereço de email e tente novamente.",
        type: "error",
        buttonText: "Tentar novamente"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6 py-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 w-full max-w-sm mx-auto">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm sm:text-base font-medium">Email</FormLabel>
                <FormControl>
                  <Input
                    placeholder="<EMAIL>"
                    {...field}
                    type="email"
                    autoComplete="email"
                    className="w-full text-sm sm:text-base bg-white/5 border-primary/20 focus:border-primary transition-all"
                  />
                </FormControl>
                <FormMessage className="text-xs sm:text-sm" />
              </FormItem>
            )}
          />
          <div className="flex flex-col gap-4">
            <Button
              type="submit"
              className="w-full text-sm sm:text-base bg-primary hover:bg-primary/90 text-white transition-all"
              disabled={isLoading}
            >
              {isLoading ? "Enviando..." : "Enviar instruções"}
            </Button>

            <Button
              type="button"
              variant="outline"
              className="w-full flex items-center gap-2"
              onClick={onBackToLogin}
            >
              <KeyRound className="h-4 w-4" />
              Voltar para o login
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default ResetPasswordForm;
