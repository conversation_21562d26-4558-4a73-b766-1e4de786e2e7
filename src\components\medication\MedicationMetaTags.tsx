import HelmetWrapper from "@/components/utils/HelmetWrapper";

interface MedicationMetaTagsProps {
  name: string;
  description?: string | null;
  slug: string;
  brands?: string | null;
  category?: string | null;
}

export const MedicationMetaTags = ({
  name,
  description,
  slug,
  brands,
  category
}: MedicationMetaTagsProps) => {

  const generateKeywords = () => {
    const baseKeywords = new Set([
      `${name} pediátrico`,
      `dose de ${name}`,
      `cálculo de ${name}`,
      `prescrição de ${name}`,
      `medicamento pediátrico`,
      `pediatria ${name}`,
      `dosagem ${name}`,
      `posologia ${name}`
    ]);

    if (category) {
      baseKeywords.add(`${category} pediátrico`);
      baseKeywords.add(`medicamentos ${category.toLowerCase()}`);
    }

    return Array.from(baseKeywords).join(', ');
  };

  const metaDescription = `Informações detalhadas sobre ${name.toLowerCase()} em pediatria, incluindo dose, posologia e indicações.`;
  const metaKeywords = generateKeywords();
  const pageUrl = `https://pedb.com.br/medicamentos/${slug}`;
  const pageTitle = `PedBook | ${name} - Informações Pediátricas`;
  const ogImage = "https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/1496949.webp";

  return (
    <HelmetWrapper>
      {/* Título e Descrição */}
      <title>{pageTitle}</title>
      <meta name="description" content={metaDescription} />
      <meta name="keywords" content={metaKeywords} />

      {/* Configurações Gerais */}
      <meta name="robots" content="index, follow, max-image-preview:large" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta charSet="UTF-8" />
      <link rel="canonical" href={pageUrl} />

      {/* Open Graph para Redes Sociais */}
      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={metaDescription} />
      <meta property="og:type" content="article" />
      <meta property="og:url" content={pageUrl} />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />
      <meta property="og:image" content={ogImage} />
      <meta property="og:image:alt" content="Ícone de medicamento do PedBook" />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="article:publisher" content="https://pedb.com.br" />
      <meta property="article:section" content="Medicamentos" />
      <meta property="article:tag" content={category || 'Medicamentos'} />

      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={metaDescription} />
      <meta name="twitter:image" content={ogImage} />
      <meta name="twitter:site" content="@PedBook" />

      {/* JSON-LD Schema.org corrigido para conteúdo informativo */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "MedicalWebPage",
          "name": pageTitle,
          "description": metaDescription,
          "url": pageUrl,
          "keywords": metaKeywords.split(', '),
          "inLanguage": "pt-BR",
          "mainEntity": {
            "@type": "MedicalEntity",
            "name": name,
            "description": metaDescription
          },
          "specialty": "Pediatria",
          "audience": {
            "@type": "MedicalAudience",
            "audienceType": "Profissionais de Saúde"
          },
          "publisher": {
            "@type": "Organization",
            "name": "PedBook",
            "url": "https://pedb.com.br",
            "logo": {
              "@type": "ImageObject",
              "url": "https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/faviconx.png"
            }
          },
          "image": {
            "@type": "ImageObject",
            "url": ogImage,
            "width": 1200,
            "height": 630
          },
          "dateModified": new Date().toISOString()
        })}
      </script>
    </HelmetWrapper>
  );
};
