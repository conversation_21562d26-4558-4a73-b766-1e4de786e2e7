
import React from 'react';
import { cn } from '@/lib/utils';

interface FilterItemProps {
  id: string;
  name: string;
  type: string;
  isSelected: boolean;
  onToggle: () => void;
  count: number;
  hasActiveFilters: boolean;
}

export const FilterItem: React.FC<FilterItemProps> = ({
  id,
  name,
  type,
  isSelected,
  onToggle,
  count,
  hasActiveFilters
}) => {
  return (
    <div 
      className={cn(
        'flex items-center justify-between p-2 rounded-lg transition-all duration-200 cursor-pointer',
        'hover:bg-[#FEF7CD]/50',
        isSelected && 'bg-[#FEF7CD]'
      )}
      onClick={onToggle}
    >
      <div className="flex items-center gap-2 flex-1">
        <div 
          className={cn(
            "w-5 h-5 rounded border-2 transition-all duration-200 flex items-center justify-center",
            isSelected 
              ? "bg-[#FF6B00] border-black text-white" 
              : "border-black hover:border-[#FF6B00]",
          )}
        >
          {isSelected && (
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              className="w-3.5 h-3.5"
            >
              <polyline points="20 6 9 17 4 12"></polyline>
            </svg>
          )}
        </div>
        
        <span className={cn(
          "text-sm transition-colors duration-200",
          isSelected ? "text-[#FF6B00] font-medium" : "text-gray-700"
        )}>
          {name}
        </span>
      </div>
      
      <div 
        className={cn(
          "min-w-[3rem] text-center px-2 py-0.5 rounded-full text-xs font-medium transition-all duration-200",
          isSelected 
            ? "bg-[#FF6B00] text-white" 
            : "bg-gray-100 text-gray-600"
        )}
      >
        {count}
      </div>
    </div>
  );
};
