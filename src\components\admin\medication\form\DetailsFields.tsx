import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";

interface DetailsFieldsProps {
  formData: {
    contraindications?: string;
    guidelines?: string;
  };
  onChange: (field: string, value: string) => void;
}

export function DetailsFields({ formData, onChange }: DetailsFieldsProps) {
  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="contraindications">Contraindicações</Label>
        <Textarea
          id="contraindications"
          value={formData.contraindications || ""}
          onChange={(e) => onChange("contraindications", e.target.value)}
          className="min-h-[150px]"
        />
      </div>

      <div>
        <Label htmlFor="guidelines">Orientações</Label>
        <Textarea
          id="guidelines"
          value={formData.guidelines || ""}
          onChange={(e) => onChange("guidelines", e.target.value)}
          className="min-h-[150px]"
        />
      </div>
    </div>
  );
}