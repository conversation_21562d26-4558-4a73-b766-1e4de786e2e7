import { Maturity, SupplementationInput, SupplementationResult, RiskFactor } from "@/types/supplementation";

export const calculateVitaminA = (ageInDays: number): string => {
  if (ageInDays > 1825) {
    return 'Suplementação de Vitamina A não está mais indicada após 5 anos de idade.';
  }
  if (ageInDays >= 183 && ageInDays <= 365) {
    return 'Uma dose de 100.000 UI de vitamina A deve ser administrada a partir dos 6 meses de idade em crianças que vivem em áreas endêmicas para deficiência de vitamina A ou que apresentam risco aumentado de deficiência, como crianças desnutridas ou com condições que afetam a absorção de nutrientes.';
  }
  if (ageInDays >= 366 && ageInDays <= 1825) {
    if ((ageInDays - 366) % (6 * 30) == 0) {
      return '200.000 UI';
    }
    return 'Uma dose de 200.000 UI de vitamina A deve ser administrada a cada 6 meses, a partir dos 12 meses de idade, até os 5 anos, para crianças que vivem em áreas endêmicas para deficiência de vitamina A ou que apresentam risco aumentado de deficiência, como crianças desnutridas ou com condições que afetam a absorção de nutrientes.';
  }
  return 'Suplementação de Vitamina A deve ser iniciada aos 6 meses em crianças que vivem em áreas endêmicas para deficiência de vitamina A ou que apresentam risco aumentado de deficiência, como crianças desnutridas ou com doenças que afetam a absorção de nutrientes.';
};

export const calculateVitaminD = (ageInDays: number): string => {
  if (ageInDays >= 0 && ageInDays <= 365) {
    return '2 gotas (400 UI), todos os dias, até 1 ano de idade.';
  }
  if (ageInDays >= 366 && ageInDays <= 730) {
    return '3 gotas (600 UI), todos os dias, até 2 anos de idade.';
  }
  return 'Suplementação de Vitamina D não está mais indicada após 2 anos de idade.';
};

// Função para detectar fatores de risco automaticamente
const detectAutomaticRiskFactors = (
  birthWeight: number,
  maturity: Maturity,
  manualRiskFactors: RiskFactor[] | undefined
): RiskFactor[] => {
  // Garantir que manualRiskFactors seja sempre um array
  const safeManualRiskFactors = Array.isArray(manualRiskFactors) ? manualRiskFactors : [];
  const automaticFactors: RiskFactor[] = [...safeManualRiskFactors];

  // Detectar prematuridade automaticamente
  if (maturity === 'Pre-term' && !automaticFactors.includes("prematurity")) {
    automaticFactors.push("prematurity");
  }

  // Detectar baixo peso ao nascer automaticamente
  if (birthWeight < 2500 && !automaticFactors.includes("low_birth_weight")) {
    automaticFactors.push("low_birth_weight");
  }

  return automaticFactors;
};

// Função auxiliar para analisar fatores de risco específicos
const analyzeRiskFactors = (riskFactors: RiskFactor[]): {
  hasHighRisk: boolean;
  riskExplanation: string[];
} => {
  const highRiskFactors = [
    "prematurity",
    "low_birth_weight",
    "maternal_anemia",
    "multiple_gestation",
    "exclusive_breastfeeding_gt_6m_without_supplement"
  ];

  const explanations: string[] = [];
  const hasHighRisk = riskFactors.some(factor => {
    const isHighRisk = highRiskFactors.includes(factor);

    switch(factor) {
      case "prematurity":
        explanations.push("Prematuridade (estoques hepáticos reduzidos)");
        break;
      case "low_birth_weight":
        explanations.push("Baixo peso ao nascer (estoque de ferro reduzido)");
        break;
      case "maternal_anemia":
        explanations.push("Anemia materna (associação direta com anemia infantil)");
        break;
      case "multiple_gestation":
        explanations.push("Gestação múltipla (estoque dividido entre fetos)");
        break;
      case "exclusive_breastfeeding_gt_6m_without_supplement":
        explanations.push("AME prolongado sem suplementação (sem fonte externa de ferro)");
        break;
      case "poor_iron_diet":
        explanations.push("Alimentação pobre em ferro");
        break;
      case "early_cow_milk_exposure":
        explanations.push("Exposição precoce ao leite de vaca (micro-hemorragias intestinais)");
        break;
      case "frequent_infections":
        explanations.push("Infecções frequentes (maior consumo de ferro funcional)");
        break;
    }

    return isHighRisk;
  });

  return { hasHighRisk, riskExplanation: explanations };
};

export const calculateIron = (
  ageInDays: number,
  currentWeight: number,
  birthWeight: number,
  maturity: Maturity,
  _exclusiveBreastfeeding: boolean, // Prefixed with _ to indicate intentionally unused
  riskFactors: RiskFactor[] | undefined
): string => {
  // Suplementação até 24 meses (730 dias)
  if (ageInDays > 730) {
    return 'Suplementação de ferro profilática não está mais indicada após 24 meses de idade.';
  }

  const weightInKg = currentWeight / 1000;

  // Detectar fatores de risco automaticamente
  let allRiskFactors = detectAutomaticRiskFactors(birthWeight, maturity, riskFactors);

  // Filtrar AME prolongado baseado na idade corrigida para prematuros
  let idadeParaAnalise = ageInDays / 30; // Converter para meses

  // Para prematuros, calcular idade corrigida para validar AME prolongado
  if (maturity === 'Pre-term') {
    // Estimar prematuridade (assumindo IG média de 32 semanas para prematuros)
    const estimatedPrematureWeeks = 8; // 40 - 32 = 8 semanas de prematuridade
    const prematureDays = estimatedPrematureWeeks * 7;
    const correctedAgeInDays = ageInDays - prematureDays;
    idadeParaAnalise = Math.max(0, correctedAgeInDays / 30);
  }

  // Remover AME prolongado se idade (corrigida) < 6 meses
  if (idadeParaAnalise < 6) {
    allRiskFactors = allRiskFactors.filter(factor =>
      factor !== 'exclusive_breastfeeding_gt_6m_without_supplement'
    );
  }

  const { riskExplanation } = analyzeRiskFactors(allRiskFactors);

  // CONSENSO SBP/SPSP 2018 - Tabela de Recomendações Revisadas

  // 1. RN a termo, peso adequado (≥2500g) - INDEPENDENTE do aleitamento
  if (maturity === 'Term' && birthWeight >= 2500) {
    if (ageInDays < 90) {
      const explanation = riskExplanation.length > 0
        ? ` Fatores de risco identificados: ${riskExplanation.join(', ')}.`
        : '';
      return `Suplementação de ferro deve ser iniciada aos 3 meses de vida (Consenso SBP/SPSP 2018).${explanation}`;
    }
    const dose = 1 * weightInKg;
    const explanation = riskExplanation.length > 0
      ? ` Fatores de risco: ${riskExplanation.join(', ')}.`
      : '';
    return `${dose.toFixed(1)} mg de ferro elementar/dia (1mg/kg/dia) até 24 meses.${explanation}`;
  }

  // 2. RN a termo com peso < 2500g
  if (maturity === 'Term' && birthWeight < 2500) {
    if (ageInDays < 30) {
      return 'Suplementação de ferro deve ser iniciada aos 30 dias de vida devido ao baixo peso ao nascer.';
    }

    if (ageInDays <= 365) {
      const dose = 2 * weightInKg;
      return `${dose.toFixed(1)} mg de ferro elementar/dia (2mg/kg/dia) durante o primeiro ano. Após 12 meses: 1mg/kg/dia.`;
    } else {
      const dose = 1 * weightInKg;
      return `${dose.toFixed(1)} mg de ferro elementar/dia (1mg/kg/dia) até 24 meses.`;
    }
  }

  // 3. RN pré-termo 1500-2500g (EXCLUSIVE de 1500, INCLUSIVE de 2500)
  if (maturity === 'Pre-term' && birthWeight > 1500 && birthWeight <= 2500) {
    if (ageInDays < 30) {
      return 'Suplementação de ferro deve ser iniciada aos 30 dias de vida devido à prematuridade.';
    }

    if (ageInDays <= 365) {
      const dose = 2 * weightInKg;
      return `${dose.toFixed(1)} mg de ferro elementar/dia (2mg/kg/dia) durante o primeiro ano. Após 12 meses: 1mg/kg/dia.`;
    } else {
      const dose = 1 * weightInKg;
      return `${dose.toFixed(1)} mg de ferro elementar/dia (1mg/kg/dia) até 24 meses.`;
    }
  }

  // 4. RN pré-termo 1000-1500g (EXCLUSIVE de 1000, INCLUSIVE de 1500)
  if (maturity === 'Pre-term' && birthWeight > 1000 && birthWeight <= 1500) {
    if (ageInDays < 30) {
      return 'Suplementação de ferro deve ser iniciada aos 30 dias de vida devido à prematuridade extrema.';
    }

    if (ageInDays <= 365) {
      const dose = 3 * weightInKg;
      return `${dose.toFixed(1)} mg de ferro elementar/dia (3mg/kg/dia) durante o primeiro ano. Após 12 meses: 1mg/kg/dia.`;
    } else {
      const dose = 1 * weightInKg;
      return `${dose.toFixed(1)} mg de ferro elementar/dia (1mg/kg/dia) até 24 meses.`;
    }
  }

  // 5. RN pré-termo ≤ 1000g (INCLUSIVE)
  if (maturity === 'Pre-term' && birthWeight <= 1000) {
    if (ageInDays < 30) {
      return 'Suplementação de ferro deve ser iniciada aos 30 dias de vida devido à prematuridade extrema.';
    }

    if (ageInDays <= 365) {
      const dose = 4 * weightInKg;
      return `${dose.toFixed(1)} mg de ferro elementar/dia (4mg/kg/dia) durante o primeiro ano. Após 12 meses: 1mg/kg/dia.`;
    } else {
      const dose = 1 * weightInKg;
      return `${dose.toFixed(1)} mg de ferro elementar/dia (1mg/kg/dia) até 24 meses.`;
    }
  }

  // 6. RN pré-termo com peso > 2500g (lacuna do consenso - aplicar lógica de peso adequado)
  if (maturity === 'Pre-term' && birthWeight > 2500) {
    if (ageInDays < 90) {
      const explanation = riskExplanation.length > 0
        ? ` Fatores de risco identificados: ${riskExplanation.join(', ')}.`
        : '';
      return `Suplementação de ferro deve ser iniciada aos 3 meses de vida (Consenso SBP/SPSP 2018 - aplicando lógica de peso adequado para pré-termo).${explanation}`;
    }
    const dose = 1 * weightInKg;
    const explanation = riskExplanation.length > 0
      ? ` Fatores de risco: ${riskExplanation.join(', ')}.`
      : '';
    return `${dose.toFixed(1)} mg de ferro elementar/dia (1mg/kg/dia) até 24 meses (pré-termo com peso adequado).${explanation}`;
  }

  return 'Situação não contemplada nas recomendações do Consenso SBP/SPSP 2018.';
};

export const calculateSupplementation = (input: SupplementationInput): SupplementationResult => {
  return {
    vitaminA: calculateVitaminA(input.ageInDays),
    vitaminD: calculateVitaminD(input.ageInDays),
    iron: calculateIron(
      input.ageInDays,
      input.currentWeight,
      input.birthWeight,
      input.maturity,
      input.exclusiveBreastfeeding,
      input.riskFactors
    ),
  };
};