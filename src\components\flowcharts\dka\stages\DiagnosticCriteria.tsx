import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface DiagnosticCriteriaProps {
  onDiagnosisComplete: (isDKA: boolean) => void;
}

export const DiagnosticCriteria = ({ onDiagnosisComplete }: DiagnosticCriteriaProps) => {
  const [step, setStep] = useState<'glucose' | 'acidosis' | 'ketones'>('glucose');
  const [values, setValues] = useState({
    glucose: '',
    acidosisStatus: '',
    ketones: false
  });

  const handleGlucoseSubmit = () => {
    if (parseFloat(values.glucose) <= 200) {
      onDiagnosisComplete(false);
    } else {
      setStep('acidosis');
    }
  };

  const handleAcidosisSubmit = (status: string) => {
    setValues(prev => ({ ...prev, acidosisStatus: status }));
    if (status === "Nenhum alterado") {
      onDiagnosisComplete(false);
    } else {
      setStep('ketones');
    }
  };

  const handleKetonesSubmit = (hasKetones: boolean) => {
    setValues(prev => ({ ...prev, ketones: hasKetones }));
    onDiagnosisComplete(hasKetones);
  };

  if (step === 'glucose') {
    return (
      <Card className="p-6 space-y-4">
        <h3 className="text-lg font-semibold">Avaliação da Glicemia</h3>
        <div className="space-y-2">
          <label className="text-sm font-medium">Glicemia (mg/dL)</label>
          <Input
            type="number"
            value={values.glucose}
            onChange={(e) => setValues(prev => ({ ...prev, glucose: e.target.value }))}
            placeholder="Informe o valor da glicemia"
          />
        </div>
        <Button 
          onClick={handleGlucoseSubmit}
          disabled={!values.glucose}
          className="w-full"
        >
          Confirmar
        </Button>
      </Card>
    );
  }

  if (step === 'acidosis') {
    const acidosisOptions = [
      "pH < 7,3",
      "Bicarbonato < 15 mEq/L",
      "Ambos alterados",
      "Nenhum alterado"
    ];

    return (
      <Card className="p-6 space-y-4">
        <h3 className="text-lg font-semibold">Avaliação da Acidose</h3>
        <p className="text-gray-600">Qual o pH arterial ou bicarbonato sérico?</p>
        <div className="grid grid-cols-1 gap-2">
          {acidosisOptions.map((option) => (
            <Button
              key={option}
              variant="outline"
              className="justify-start"
              onClick={() => handleAcidosisSubmit(option)}
            >
              {option}
            </Button>
          ))}
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6 space-y-4">
      <h3 className="text-lg font-semibold">Avaliação de Cetonas</h3>
      <p className="text-gray-600">Paciente com cetonemia e/ou cetonúria?</p>
      <div className="grid grid-cols-2 gap-4">
        <Button onClick={() => handleKetonesSubmit(true)} variant="outline">
          Sim
        </Button>
        <Button onClick={() => handleKetonesSubmit(false)} variant="outline">
          Não
        </Button>
      </div>
    </Card>
  );
};