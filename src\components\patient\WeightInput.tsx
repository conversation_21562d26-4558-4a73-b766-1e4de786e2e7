
import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Scale } from "lucide-react";

interface WeightInputProps {
  value: number;
  onChange: (weight: number) => void;
  onCommit: (weight: number) => void;
}

export const WeightInput = ({ value, onChange, onCommit }: WeightInputProps) => {
  const [inputValue, setInputValue] = useState(value.toString());

  useEffect(() => {
    setInputValue(value.toString());
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    
    const numValue = parseFloat(newValue);
    if (!isNaN(numValue)) {
      const limitedValue = Math.min(numValue, 100);
      onChange(limitedValue);
      onCommit(limitedValue);
    }
  };

  const handleBlur = () => {
    const numValue = parseFloat(inputValue);
    if (isNaN(numValue) || numValue < 0) {
      setInputValue("0");
      onChange(0);
      onCommit(0);
    } else if (numValue > 100) {
      setInputValue("100");
      onChange(100);
      onCommit(100);
    } else {
      setInputValue(numValue.toString());
      onChange(numValue);
      onCommit(numValue);
    }
  };

  return (
    <div className="space-y-2 w-full">
      <Label htmlFor="weight" className="text-sm font-medium text-gray-700 dark:text-gray-200 flex items-center gap-2">
        <Scale className="h-4 w-4" />
        Peso do Paciente
      </Label>
      <div className="flex items-center gap-2">
        <Input
          id="weight"
          type="number"
          min={0}
          max={100}
          value={inputValue}
          onChange={handleChange}
          onBlur={handleBlur}
          className="flex-1 bg-white/50 border-primary/20 focus:border-primary/40 transition-colors text-center"
          placeholder="Digite o peso"
        />
        <span className="text-sm font-medium text-gray-600 dark:text-gray-300 min-w-[2rem]">kg</span>
      </div>
    </div>
  );
};
