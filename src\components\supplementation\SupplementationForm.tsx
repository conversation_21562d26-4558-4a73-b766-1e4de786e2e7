import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Calculator, Baby, Scale, AlertTriangle, ChevronDown, ChevronUp } from "lucide-react";
import { SupplementationInput, RiskFactor } from "@/types/supplementation";

interface SupplementationFormProps {
  onCalculate: (input: SupplementationInput) => void;
}

// Fatores de risco disponíveis
const availableRiskFactors = [
  { id: "prematurity", label: "Prematuridade", description: "Nascimento antes de 37 semanas" },
  { id: "low_birth_weight", label: "Baixo peso ao nascer", description: "Peso ao nascer < 2500g" },
  { id: "poor_iron_diet", label: "Alimentação pobre em ferro", description: "Introdução alimentar inadequada" },
  { id: "exclusive_breastfeeding_gt_6m_without_supplement", label: "AME prolongado sem suplementação", description: "Além de 6 meses sem ferro" },
  { id: "multiple_gestation", label: "Gestação múltipla", description: "Gêmeos, trigêmeos, etc." },
  { id: "maternal_anemia", label: "Anemia materna", description: "Durante gestação ou lactação" },
  { id: "frequent_infections", label: "Infecções frequentes", description: "Processos infecciosos recorrentes" },
  { id: "early_cow_milk_exposure", label: "Leite de vaca precoce", description: "Antes dos 12 meses" },
  { id: "low_socioeconomic_status", label: "Baixo nível socioeconômico", description: "Vulnerabilidade social" }
] as const;

export const SupplementationForm = ({ onCalculate }: SupplementationFormProps) => {
  const [formData, setFormData] = useState({
    ageInDays: "",
    currentWeight: "",
    birthWeight: "",
    maturity: "Term" as "Term" | "Pre-term",
    exclusiveBreastfeeding: false,
  });

  const [riskFactors, setRiskFactors] = useState<string[]>([]);
  const [showRiskFactors, setShowRiskFactors] = useState(false);

  // Função para lidar com mudanças nos fatores de risco
  const handleRiskFactorChange = (riskFactorId: string, checked: boolean) => {
    if (checked) {
      setRiskFactors([...riskFactors, riskFactorId]);
    } else {
      setRiskFactors(riskFactors.filter(id => id !== riskFactorId));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const input: SupplementationInput = {
      ageInDays: parseInt(formData.ageInDays),
      currentWeight: parseInt(formData.currentWeight),
      birthWeight: parseInt(formData.birthWeight),
      maturity: formData.maturity,
      exclusiveBreastfeeding: formData.exclusiveBreastfeeding,
      riskFactors: riskFactors as RiskFactor[]
    };

    onCalculate(input);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <Baby className="h-4 w-4" />
            Idade (dias)
          </Label>
          <Input
            type="number"
            placeholder="Em dias"
            value={formData.ageInDays}
            onChange={(e) => setFormData({ ...formData, ageInDays: e.target.value })}
            required
          />
        </div>

        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <Scale className="h-4 w-4" />
            Peso Atual (g)
          </Label>
          <Input
            type="number"
            placeholder="Em gramas"
            value={formData.currentWeight}
            onChange={(e) => setFormData({ ...formData, currentWeight: e.target.value })}
            required
          />
        </div>

        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <Scale className="h-4 w-4" />
            Peso ao Nascer (g)
          </Label>
          <Input
            type="number"
            placeholder="Em gramas"
            value={formData.birthWeight}
            onChange={(e) => setFormData({ ...formData, birthWeight: e.target.value })}
            required
          />
        </div>

        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <Baby className="h-4 w-4" />
            Maturidade
          </Label>
          <Select
            value={formData.maturity}
            onValueChange={(value: "Term" | "Pre-term") => 
              setFormData({ ...formData, maturity: value })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Selecione a maturidade" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Term">A termo</SelectItem>
              <SelectItem value="Pre-term">Prematuro</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Card className="p-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="exclusiveBreastfeeding" className="flex items-center gap-2">
                <Baby className="h-4 w-4 text-purple-500 dark:text-purple-400" />
                <span className="text-gray-800 dark:text-gray-200">Aleitamento Materno Exclusivo</span>
              </Label>
              <Switch
                id="exclusiveBreastfeeding"
                checked={formData.exclusiveBreastfeeding}
                onCheckedChange={(checked) => 
                  setFormData({ ...formData, exclusiveBreastfeeding: checked })
                }
              />
            </div>

            {/* Fatores de Risco */}
            <div className="space-y-3">
              <div
                className="flex items-center justify-between cursor-pointer"
                onClick={() => setShowRiskFactors(!showRiskFactors)}
              >
                <Label className="flex items-center gap-2 cursor-pointer">
                  <AlertTriangle className="h-4 w-4 text-yellow-500 dark:text-yellow-400" />
                  <span className="text-gray-800 dark:text-gray-200">
                    Fatores de Risco {riskFactors.length > 0 && `(${riskFactors.length} selecionados)`}
                  </span>
                </Label>
                {showRiskFactors ?
                  <ChevronUp className="h-4 w-4 text-gray-500" /> :
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                }
              </div>

              {showRiskFactors && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 p-3 bg-gray-50 dark:bg-slate-700/50 rounded-lg">
                  {availableRiskFactors.map((factor) => (
                    <div key={factor.id} className="flex items-start space-x-2">
                      <Checkbox
                        id={factor.id}
                        checked={riskFactors.includes(factor.id)}
                        onCheckedChange={(checked) => handleRiskFactorChange(factor.id, checked as boolean)}
                        className="mt-1"
                      />
                      <div className="grid gap-1.5 leading-none">
                        <Label
                          htmlFor={factor.id}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        >
                          {factor.label}
                        </Label>
                        {factor.description && (
                          <p className="text-xs text-muted-foreground">
                            {factor.description}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </Card>
      </div>

      <Button type="submit" className="w-full">
        <Calculator className="mr-2 h-4 w-4" />
        Calcular Suplementação
      </Button>
    </form>
  );
};