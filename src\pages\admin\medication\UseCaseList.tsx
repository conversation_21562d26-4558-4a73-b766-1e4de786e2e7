import { useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Trash2, Pencil } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { UseCaseForm } from "@/components/admin/UseCaseForm";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface UseCaseListProps {
  medicationId: string;
}

export function UseCaseList({ medicationId }: UseCaseListProps) {
  const [deletingUseCase, setDeletingUseCase] = useState<string | null>(null);
  const [editingUseCase, setEditingUseCase] = useState<any | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: useCases } = useQuery({
    queryKey: ["medication-use-cases", medicationId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_medication_use_cases")
        .select("*")
        .eq("medication_id", medicationId)
        .order("name");

      if (error) throw error;
      return data;
    },
  });

  const handleDeleteUseCase = async () => {
    if (!deletingUseCase) return;

    try {
      // First, update any dosages that reference this use case to remove the reference
      const { error: updateError } = await supabase
        .from("pedbook_medication_dosages")
        .update({ use_case_id: null })
        .eq("use_case_id", deletingUseCase);

      if (updateError) throw updateError;

      // Then delete the use case
      const { error: deleteError } = await supabase
        .from("pedbook_medication_use_cases")
        .delete()
        .eq("id", deletingUseCase);

      if (deleteError) throw deleteError;

      toast({
        title: "Indicação excluída com sucesso",
        description: "A indicação de uso foi removida.",
      });

      queryClient.invalidateQueries({ queryKey: ["medication-use-cases"] });
      queryClient.invalidateQueries({ queryKey: ["medication-dosages"] });
      setDeletingUseCase(null);
    } catch (error: any) {
      console.error("Error deleting use case:", error);
      toast({
        variant: "destructive",
        title: "Erro ao excluir indicação",
        description: error.message,
      });
    }
  };

  if (!useCases?.length) {
    return (
      <div className="text-center text-gray-500 py-8">
        Nenhuma indicação de uso cadastrada para este medicamento
      </div>
    );
  }

  return (
    <>
      <div className="space-y-4">
        {useCases.map((useCase) => (
          <div
            key={useCase.id}
            className="bg-white rounded-lg shadow-sm p-4 space-y-2 flex justify-between items-start"
          >
            <div>
              <h3 className="font-medium">{useCase.name}</h3>
              {useCase.description && (
                <p className="text-sm text-gray-600">{useCase.description}</p>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setEditingUseCase(useCase)}
                className="hover:bg-primary/10"
              >
                <Pencil className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setDeletingUseCase(useCase.id)}
                className="hover:bg-destructive/10"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}
      </div>

      <AlertDialog open={!!deletingUseCase} onOpenChange={() => setDeletingUseCase(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir esta indicação de uso? Esta ação não pode ser desfeita.
              As dosagens associadas a esta indicação serão mantidas, mas não estarão mais vinculadas a esta indicação.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteUseCase}>
              Confirmar
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Dialog open={!!editingUseCase} onOpenChange={() => setEditingUseCase(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Indicação de Uso</DialogTitle>
          </DialogHeader>
          {editingUseCase && (
            <UseCaseForm
              medicationId={medicationId}
              useCase={editingUseCase}
              onSuccess={() => setEditingUseCase(null)}
              onCancel={() => setEditingUseCase(null)}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}