import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";

interface TermsDialogProps {
  onAccept: () => void;
}

export function TermsDialog({ onAccept }: TermsDialogProps) {
  const [open, setOpen] = useState(true);
  const { toast } = useToast();

  const handleAccept = async () => {
    try {
      const { error } = await supabase
        .from('ai_terms_acceptance')
        .insert([{ user_id: (await supabase.auth.getUser()).data.user?.id }]);

      if (error) throw error;

      setOpen(false);
      onAccept();
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao aceitar os termos",
        description: error.message,
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Termos de Uso do Assistente IA</DialogTitle>
          <DialogDescription>
            Antes de prosseguir, é importante que você compreenda e aceite os seguintes termos:
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4 text-sm text-muted-foreground">
          <p>
            1. Este assistente utiliza inteligência artificial para gerar análises e prescrições médicas.
          </p>
          <p>
            2. As recomendações fornecidas são baseadas em padrões e dados de treinamento, mas não substituem o julgamento clínico profissional.
          </p>
          <p>
            3. Todas as análises e prescrições devem ser revisadas e validadas por um profissional de saúde qualificado antes de serem aplicadas.
          </p>
          <p>
            4. O usuário é responsável por verificar a precisão e adequação de todas as informações fornecidas.
          </p>
        </div>
        <DialogFooter>
          <Button onClick={handleAccept} className="w-full">
            Aceito os Termos
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}