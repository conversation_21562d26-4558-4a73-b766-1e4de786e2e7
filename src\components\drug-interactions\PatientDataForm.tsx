
import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface PatientData {
  age: string;
  gender: string;
  comorbidities: string;
  clinicalStatus: string;
}

interface PatientDataFormProps {
  patientData: PatientData;
  handlePatientDataChange: (field: keyof PatientData, value: string) => void;
}

export const PatientDataForm: React.FC<PatientDataFormProps> = ({
  patientData,
  handlePatientDataChange,
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
      <div className="space-y-2">
        <Label htmlFor="age">Idade</Label>
        <Input
          id="age"
          placeholder="Ex: 65"
          value={patientData.age}
          onChange={(e) => handlePatientDataChange("age", e.target.value)}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="gender">Sexo</Label>
        <Select
          value={patientData.gender}
          onValueChange={(value) => handlePatientDataChange("gender", value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Selecione..." />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="masculino">Masculino</SelectItem>
            <SelectItem value="feminino">Feminino</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-2">
        <Label htmlFor="comorbidities">Comorbidades</Label>
        <Input
          id="comorbidities"
          placeholder="Ex: insuficiência renal, hipertensão"
          value={patientData.comorbidities}
          onChange={(e) => handlePatientDataChange("comorbidities", e.target.value)}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="clinical-status">Estado clínico</Label>
        <Input
          id="clinical-status"
          placeholder="Ex: grávida, lactante, idoso"
          value={patientData.clinicalStatus}
          onChange={(e) => handlePatientDataChange("clinicalStatus", e.target.value)}
        />
      </div>
    </div>
  );
};
