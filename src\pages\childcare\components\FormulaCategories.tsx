import React, { memo, useMemo, useCallback } from "react";
import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Category, Formula } from "../types";
import { FormulaBrands } from "./FormulaBrands";
import { FormulaDetails } from "../FormulaDetails";

interface FormulaCategoriesProps {
  categories: Category[] | undefined;
  formulas: Formula[] | undefined;
  selectedCategory: string | null;
  setSelectedCategory: (id: string | null) => void;
  selectedFormula: Formula | null;
  setSelectedFormula: (formula: Formula | null) => void;
  isLoading?: boolean;
}

export const FormulaCategories = memo(({
  categories,
  formulas,
  selectedCategory,
  setSelectedCategory,
  selectedFormula,
  setSelectedFormula,
  isLoading = false,
}: FormulaCategoriesProps) => {

  // Memoizar fórmulas filtradas para evitar recálculos desnecessários
  const filteredFormulas = useMemo(() => {
    return formulas?.filter(f => f.category_id === selectedCategory) || [];
  }, [formulas, selectedCategory]);

  // Memoizar handlers para evitar re-renders
  const handleCategoryClick = useCallback((categoryId: string) => {
    if (selectedCategory === categoryId) {
      setSelectedCategory(null);
      setSelectedFormula(null);
    } else {
      setSelectedCategory(categoryId);
      setSelectedFormula(null);
    }
  }, [selectedCategory, setSelectedCategory, setSelectedFormula]);

  return (
    <div className="space-y-8">
      {/* Categories Row */}
      <div className="flex flex-wrap gap-4 justify-center">
        {categories?.map((category) => (
          <motion.div
            key={category.id}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="flex-shrink-0"
          >
            <Card
              className={cn(
                "p-4 cursor-pointer transition-all duration-300 hover:shadow-lg bg-white/80 backdrop-blur-sm border-2",
                selectedCategory === category.id
                  ? "border-primary shadow-lg transform -translate-y-1"
                  : "border-transparent hover:-translate-y-1"
              )}
              onClick={() => handleCategoryClick(category.id)}
            >
              <h3 className="text-lg font-medium text-gray-900 whitespace-nowrap">
                {category.name}
              </h3>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Brands Row */}
      {selectedCategory && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="pt-4"
        >
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
              <p className="text-gray-600 dark:text-gray-300 text-sm">Carregando fórmulas...</p>
            </div>
          ) : (
            <FormulaBrands
              formulas={filteredFormulas}
              selectedFormula={selectedFormula}
              setSelectedFormula={setSelectedFormula}
            />
          )}
        </motion.div>
      )}

      {/* Formula Details */}
      {selectedFormula && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="mt-8"
        >
          <FormulaDetails formula={selectedFormula} />
        </motion.div>
      )}
    </div>
  );
});