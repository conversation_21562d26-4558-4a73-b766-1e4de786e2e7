import { Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger, Tabs } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { AlertTriangle, TrendingUp, CheckCircle } from "lucide-react";

interface RecommendationsListProps {
  activeTab: string;
  setActiveTab: (value: string) => void;
  filterRecommendationsByPriority: (priority: string) => Array<{
    topic: string;
    reason: string;
    priority: string;
  }>;
}

export const RecommendationsList = ({
  activeTab,
  setActiveTab,
  filterRecommendationsByPriority
}: RecommendationsListProps) => {
  return (
    <Tabs defaultValue="high" className="space-y-4" onValueChange={setActiveTab}>
      <TabsList className="grid grid-cols-3 w-full">
        <TabsTrigger value="high" className="gap-2">
          <AlertTriangle className="h-4 w-4 text-red-500" />
          Alta Prioridade
        </TabsTrigger>
        <TabsTrigger value="medium" className="gap-2">
          <TrendingUp className="h-4 w-4 text-yellow-500" />
          Média Prioridade
        </TabsTrigger>
        <TabsTrigger value="low" className="gap-2">
          <CheckCircle className="h-4 w-4 text-green-500" />
          Baixa Prioridade
        </TabsTrigger>
      </TabsList>

      {["high", "medium", "low"].map((priority) => (
        <TabsContent key={priority} value={priority} className="space-y-4">
          {filterRecommendationsByPriority(priority).map((rec, index) => (
            <Card key={index}>
              <CardContent className="pt-6">
                <div className="space-y-2">
                  <h3 className="font-semibold text-lg">{rec.topic}</h3>
                  <p className="text-sm text-muted-foreground">{rec.reason}</p>
                </div>
              </CardContent>
            </Card>
          ))}
          {filterRecommendationsByPriority(priority).length === 0 && (
            <p className="text-center text-muted-foreground py-4">
              Nenhuma recomendação com esta prioridade no momento.
            </p>
          )}
        </TabsContent>
      ))}
    </Tabs>
  );
};