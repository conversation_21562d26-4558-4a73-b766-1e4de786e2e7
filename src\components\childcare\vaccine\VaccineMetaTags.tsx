import HelmetWrapper from "@/components/utils/HelmetWrapper";

export const VaccineMetaTags = () => {
  const pageTitle = "PedBook | Calendário Vacinal - Acompanhamento de Vacinação Infantil";
  const pageDescription = "Acesse o calendário vacinal completo com todas as vacinas recomendadas para cada idade. Acompanhe o esquema vacinal, doses e reforços necessários para a saúde do seu paciente.";
  const pageUrl = "https://pedb.com.br/puericultura/calendario-vacinal";
  const imageUrl = "https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/vacinas.webp";
  const keywords = [
    "calendário vacinal",
    "vacinação infantil",
    "imunização",
    "vacinas obrigatórias",
    "esquema vacinal",
    "doses vacina",
    "reforço vacinal",
    "prevenção doenças",
    "saúde infantil",
    "carteira vacinação",
    "programa nacional imunização",
    "vacinas pediatria"
  ].join(", ");

  return (
    <HelmetWrapper>
      <title>{pageTitle}</title>
      <meta name="description" content={pageDescription} />
      <meta name="keywords" content={keywords} />

      <meta name="robots" content="index, follow, max-image-preview:large" />
      <link rel="canonical" href={pageUrl} />

      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={pageDescription} />
      <meta property="og:type" content="article" />
      <meta property="og:url" content={pageUrl} />
      <meta property="og:image" content={imageUrl} />
      <meta property="og:image:alt" content="Ilustração sobre vacinação infantil e calendário vacinal" />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />

      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={pageDescription} />
      <meta name="twitter:image" content={imageUrl} />

      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "MedicalWebPage",
          "name": pageTitle,
          "description": pageDescription,
          "url": pageUrl,
          "image": imageUrl,
          "keywords": keywords.split(", "),
          "publisher": {
            "@type": "Organization",
            "name": "PedBook",
            "logo": {
              "@type": "ImageObject",
              "url": "https://pedb.com.br/logo.png"
            }
          },
          "specialty": "Pediatria",
          "audience": {
            "@type": "MedicalAudience",
            "audienceType": "Profissionais de Saúde"
          },
          "about": {
            "@type": "MedicalCondition",
            "name": "Imunização Infantil",
            "description": "Calendário vacinal e acompanhamento de vacinação para crianças"
          }
        })}
      </script>
    </HelmetWrapper>
  );
};
