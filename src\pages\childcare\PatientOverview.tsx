
import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, PillBottle, Clock, Info } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { PatientForm } from "@/components/patient-overview/PatientForm";
import { PatientDashboard } from "@/components/patient-overview/PatientDashboard";
import { Card } from "@/components/ui/card";
import { PatientOverviewBanner } from "@/components/patient-overview/PatientOverviewBanner";
import { ChildcareSEO } from "@/components/seo/ChildcareSEO";
import { CHILDCARE_SEO_DATA } from "@/data/childcareSEOData";

export default function PatientOverview() {
  const seoData = CHILDCARE_SEO_DATA['patient-overview'];

  const [showDashboard, setShowDashboard] = useState(false);
  const [patientData, setPatientData] = useState<any>(null);

  const handlePatientDataSubmit = (data: any) => {
    setPatientData(data);
    setShowDashboard(true);
  };

  const handlePatientDataUpdate = (updatedData: Partial<typeof patientData>) => {
    setPatientData(prev => prev ? { ...prev, ...updatedData } : null);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800">
      <ChildcareSEO {...seoData} />
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8 md:py-12">
        {/* Header com navegação */}
        <div className="flex items-center justify-between mb-8">
          <Link
            to="/puericultura"
            className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors dark:text-blue-400 dark:hover:text-blue-300"
          >
            <ArrowLeft className="h-5 w-5" />
            <span>Voltar para Puericultura</span>
          </Link>

          {/* Botão Editar Dados - só aparece no dashboard */}
          {showDashboard && (
            <button
              onClick={() => setShowDashboard(false)}
              className="inline-flex items-center gap-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-4 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 group"
            >
              <span className="hidden sm:inline">✏️</span>
              <span className="font-medium">
                <span className="hidden sm:inline">Editar Dados</span>
                <span className="sm:hidden">Editar</span>
              </span>
              <ArrowLeft className="h-4 w-4 rotate-180 group-hover:translate-x-[2px] transition-transform" />
            </button>
          )}
        </div>

        {!showDashboard ? (
          <>
            <PatientOverviewBanner />

            <Card className="max-w-2xl mx-auto p-6 mt-8 border border-primary/10 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm shadow-lg">
              <h2 className="text-xl font-bold mb-4 text-primary dark:text-blue-400">
                {patientData ? 'Editar dados do paciente' : 'Preencha os dados do paciente'}
              </h2>
              <PatientForm
                onSubmit={handlePatientDataSubmit}
                defaultValues={patientData}
              />
            </Card>
          </>
        ) : (
          <PatientDashboard
            data={patientData}
            onBack={() => setShowDashboard(false)}
            onPatientDataUpdate={handlePatientDataUpdate}
          />
        )}
      </main>

      <Footer />
    </div>
  );
}
