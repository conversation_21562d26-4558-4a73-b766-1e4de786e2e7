import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Tag } from "@/types/dosage";
import { Label } from "@/components/ui/label";

interface TagDisplayProps {
  tags: Tag[];
  onRemoveTag: (index: number) => void;
  onToggleRounding: (index: number, value: boolean) => void;
}

export function TagDisplay({ tags, onRemoveTag, onToggleRounding }: TagDisplayProps) {
  const formatTagValue = (tag: Tag) => {
    const formatDecimal = (value: number | undefined) => {
      if (!value) return "0";
      // For very small numbers, use fixed notation
      if (value > 0 && value < 0.001) {
        return value.toFixed(4);
      }
      // For other numbers, use locale string
      return value.toLocaleString('pt-BR', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 4
      });
    };

    if (tag.type === 'age' && tag.ageRanges) {
      return `(idade: ${tag.ageRanges.map(range => 
        `${range.startMonth}-${range.endMonth}m: ${formatDecimal(range.value)}`
      ).join(', ')})`;
    } else if (tag.type === 'fixed_by_weight' && tag.weightRanges) {
      return `(peso: ${tag.weightRanges.map(range => 
        `${range.startWeight}-${range.endWeight}kg: ${formatDecimal(range.value)}`
      ).join(', ')})`;
     }else if (tag.type === 'multiplier_by_fixed_age' && tag.ageRanges) {
      return `(Multiplicador em Idade: ${tag.ageRanges.map(range => 
        `${range.startMonth}-${range.endMonth}m: ${formatDecimal(range.value)}${tag.maxValue ? `, máximo: ${formatDecimal(tag.maxValue)}` : ""}`
      ).join(", ")})`;
    }
    else if (tag.type === 'fixed') {
      return `(valor fixo: ${formatDecimal(tag.multiplier)})`;
    } else {
      return `(multiplicador: ${formatDecimal(tag.multiplier)}${
        tag.maxValue ? `, máximo: ${formatDecimal(tag.maxValue)}` : ""
      })`;
    }
  };

  return (
    <div className="space-y-2">
      {tags.map((tag, index) => (
        <div key={index} className="flex items-center gap-2">
          <div className="flex-1 p-2 bg-gray-50 rounded-lg flex items-center justify-between">
            <span>
              <span className="font-medium">{tag.name}</span>
              <span className="text-gray-500 ml-2">{formatTagValue(tag)}</span>
            </span>
            <div className="flex items-center gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id={`round-${index}`}
                  checked={tag.roundResult || false}
                  onCheckedChange={(checked) => onToggleRounding(index, checked)}
                />
                <Label htmlFor={`round-${index}`} className="text-sm text-muted-foreground">
                  Aproximar
                </Label>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => onRemoveTag(index)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}