import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Plus } from "lucide-react";
import { MedicationList } from "./selector/MedicationList";
import { SelectedMedicationsList } from "./selector/SelectedMedicationsList";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useState } from "react";

interface MedicationSelectorProps {
  selectedMedications: Array<{
    medicationId: string;
    dosageId: string;
    sectionTitle?: string;
    quantity?: string;
  }>;
  onSelectMedication: (medications: Array<{
    medicationId: string;
    dosageId: string;
    sectionTitle?: string;
    quantity?: string;
  }>) => void;
  weight: number;
  age: number;
}

export function MedicationSelector({
  selectedMedications,
  onSelectMedication,
  weight,
  age,
}: MedicationSelectorProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const { data: medications } = useQuery({
    queryKey: ["medications"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_medications")
        .select(`
          *,
          pedbook_medication_dosages (
            id,
            name,
            dosage_template,
            summary
          )
        `);

      if (error) throw error;
      return data;
    },
  });

  const handleAddMedication = (medicationId: string, dosageId: string) => {
    onSelectMedication([
      ...selectedMedications,
      { medicationId, dosageId }
    ]);
    setIsDialogOpen(false);
  };

  const handleRemoveMedication = (index: number) => {
    const newMedications = [...selectedMedications];
    newMedications.splice(index, 1);
    onSelectMedication(newMedications);
  };

  const handleUpdateSectionTitle = (index: number, title: string) => {
    const newMedications = [...selectedMedications];
    newMedications[index] = {
      ...newMedications[index],
      sectionTitle: title || undefined
    };
    onSelectMedication(newMedications);
  };

  const handleUpdateQuantity = (index: number, quantity: string) => {
    const newMedications = [...selectedMedications];
    newMedications[index] = {
      ...newMedications[index],
      quantity: quantity || undefined
    };
    onSelectMedication(newMedications);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Label>Medicamentos</Label>
        <Button
          type="button"
          variant="default"
          onClick={() => setIsDialogOpen(true)}
          className="w-full max-w-[200px]"
        >
          <Plus className="h-4 w-4 mr-2" />
          Adicionar Medicamento
        </Button>
      </div>

      <SelectedMedicationsList
        selectedMedications={selectedMedications}
        medications={medications || []}
        onRemove={handleRemoveMedication}
        onUpdateSectionTitle={handleUpdateSectionTitle}
        onUpdateQuantity={handleUpdateQuantity}
        weight={weight}
        age={age}
      />

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <MedicationList
            onSelect={handleAddMedication}
            weight={weight}
            age={age}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}