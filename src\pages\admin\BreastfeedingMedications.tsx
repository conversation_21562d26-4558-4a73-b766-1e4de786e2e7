
import React, { useState, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { Progress } from "@/components/ui/progress";
import { Loader2, FileJson, AlertCircle, CheckCircle2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";

interface ImportStatus {
  total: number;
  processed: number;
  succeeded: number;
  failed: number;
}

interface BreastfeedingMedication {
  nome: string;
  compatibilidade_amamentacao: string;
  uso_amamentacao: string;
  informacao_adicional?: string;
}

interface BreastfeedingSubsection {
  name: string;
  description?: string;
  substancias?: BreastfeedingMedication[];
  nestingLevel?: number;
  parent?: string;
  [key: string]: any; // Allow for dynamic nested subsections
}

const BreastfeedingMedications: React.FC = () => {
  const { toast } = useToast();
  const [isImporting, setIsImporting] = useState(false);
  const [status, setStatus] = useState<ImportStatus>({ total: 0, processed: 0, succeeded: 0, failed: 0 });
  const [progress, setProgress] = useState(0);
  const [errors, setErrors] = useState<string[]>([]);
  const [currentOperation, setCurrentOperation] = useState("");
  const [rawJson, setRawJson] = useState("");

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (event) => {
      if (event.target?.result) {
        setRawJson(event.target.result.toString());
      }
    };
    reader.readAsText(file);
  };

  // Helper function to determine if an object is a subsection
  const isSubsection = (obj: any): boolean => {
    // Check if this is an object and not an array
    if (typeof obj !== 'object' || Array.isArray(obj) || obj === null) return false;

    // Either has 'substancias' array or has other nested objects (excluding 'descricao')
    return obj.substancias !== undefined ||
           Object.keys(obj).some(key =>
             key !== 'descricao' &&
             typeof obj[key] === 'object' &&
             !Array.isArray(obj[key]) &&
             obj[key] !== null
           );
  };

  const importJson = async () => {
    if (!rawJson) {
      toast({
        title: "Nenhum arquivo selecionado",
        description: "Por favor, selecione um arquivo JSON para importar.",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsImporting(true);
      setErrors([]);
      setProgress(0);

      // Parse JSON
      const importData = JSON.parse(rawJson);
      console.log("🔄 Dados JSON carregados:", Object.keys(importData).length, "seções");

      let sectionsToProcess = Object.keys(importData).length;
      let sectionCount = 0;
      let medicationsCount = 0;
      let subsectionsCount = 0;
      let successCount = 0;
      let errorCount = 0;

      // Process each section
      for (const [sectionName, sectionData] of Object.entries(importData)) {
        sectionCount++;
        try {
          setCurrentOperation(`Processando seção ${sectionCount}/${sectionsToProcess}: ${sectionName}`);
          console.log(`🔄 Processando seção: ${sectionName}`);

          // Create section
          const { data: sectionResult, error: sectionError } = await supabase
            .from('pedbook_breastfeeding_sections')
            .insert({
              name: sectionName,
              description: (sectionData as any).descricao || null,
              display_order: sectionCount
            })
            .select()
            .single();

          if (sectionError) {
            console.error(`❌ Erro ao criar seção ${sectionName}:`, sectionError);
            errorCount++;
            setErrors(prev => [...prev, `Erro ao criar seção ${sectionName}: ${sectionError.message}`]);
            continue;
          }

          const sectionId = sectionResult.id;

          // Process section content
          const sectionContent = sectionData as any;

          // Direct medications in a section
          if (sectionContent.substancias && Array.isArray(sectionContent.substancias)) {
            for (const medication of sectionContent.substancias) {
              try {
                // Create medication directly under section
                await createMedication(sectionId, null, medication);
                medicationsCount++;
                successCount++;
              } catch (error: any) {
                console.error(`❌ Erro ao criar medicamento ${medication.nome}:`, error);
                errorCount++;
                setErrors(prev => [...prev, `Erro ao criar medicamento ${medication.nome}: ${error.message}`]);
              }
            }
          }

          // Process nested content with recursive approach
          await processNestedContent(
            sectionContent,
            sectionId,
            null, // No parent subsection for top level
            1, // Start at nesting level 1
            sectionName, // Path starts with section name
            {
              incrementSubsectionCount: (count) => { subsectionsCount += count },
              incrementMedicationCount: (count) => { medicationsCount += count },
              incrementSuccessCount: (count) => { successCount += count },
              incrementErrorCount: (count) => { errorCount += count },
              addError: (error) => { setErrors(prev => [...prev, error]) }
            }
          );

          // Update progress
          setProgress((sectionCount / sectionsToProcess) * 100);
          setStatus({
            total: sectionsToProcess,
            processed: sectionCount,
            succeeded: successCount,
            failed: errorCount
          });

        } catch (error: any) {
          errorCount++;
          setErrors(prev => [...prev, `Erro ao processar seção ${sectionName}: ${error.message}`]);
        }
      }



      toast({
        title: "Importação concluída",
        description: `Processados: ${sectionCount} seções, ${subsectionsCount} subseções, ${medicationsCount} medicamentos. Sucesso: ${successCount}, Erros: ${errorCount}`,
        variant: errorCount > 0 ? "destructive" : "default"
      });

      // Complete progress
      setProgress(100);

    } catch (error: any) {
      toast({
        title: "Erro ao processar JSON",
        description: error.message,
        variant: "destructive"
      });
      setErrors(prev => [...prev, `Erro ao processar JSON: ${error.message}`]);
    } finally {
      setIsImporting(false);
    }
  };

  // Improved recursive processing function that handles any level of nesting
  const processNestedContent = async (
    content: any,
    sectionId: string,
    parentSubsectionId: string | null,
    nestingLevel: number,
    currentPath: string,
    counters: {
      incrementSubsectionCount: (count: number) => void,
      incrementMedicationCount: (count: number) => void,
      incrementSuccessCount: (count: number) => void,
      incrementErrorCount: (count: number) => void,
      addError: (error: string) => void
    }
  ) => {
    // Skip the description field as it's metadata
    if (content.descricao !== undefined) {
      delete content.descricao;
    }

    // Skip the substancias array as we'll process it separately
    if (content.substancias !== undefined) {
      delete content.substancias;
    }

    // Process each potential subsection
    for (const [key, value] of Object.entries(content)) {
      // Skip if key is special case or value isn't an object
      if (key === 'descricao' || key === 'substancias' || typeof value !== 'object' || value === null) continue;

      // Check if this is a subsection (has medications or nested subsections)
      if (isSubsection(value)) {
        try {
          const subsectionData = value as any;
          const subsectionName = key;
          const subsectionPath = `${currentPath} > ${subsectionName}`;

          console.log(`🔄 Processando subseção nível ${nestingLevel}: ${subsectionPath}`);

          // Create subsection
          const { data: subsectionResult, error: subsectionError } = await supabase
            .from('pedbook_breastfeeding_subsections')
            .insert({
              section_id: sectionId,
              parent_subsection_id: parentSubsectionId,
              name: subsectionName,
              description: subsectionData.descricao || null,
              display_order: 0, // Default order
              nesting_level: nestingLevel
            })
            .select()
            .single();

          if (subsectionError) {
            console.error(`❌ Erro ao criar subseção ${subsectionPath}:`, subsectionError);
            counters.incrementErrorCount(1);
            counters.addError(`Erro ao criar subseção ${subsectionPath}: ${subsectionError.message}`);
            continue;
          }

          counters.incrementSubsectionCount(1);
          const subsectionId = subsectionResult.id;

          // Process medications in this subsection
          if (subsectionData.substancias && Array.isArray(subsectionData.substancias)) {
            for (const medication of subsectionData.substancias) {
              try {
                await createMedication(sectionId, subsectionId, medication);
                counters.incrementMedicationCount(1);
                counters.incrementSuccessCount(1);
              } catch (error: any) {
                console.error(`❌ Erro ao criar medicamento ${medication.nome} em ${subsectionPath}:`, error);
                counters.incrementErrorCount(1);
                counters.addError(`Erro ao criar medicamento ${medication.nome} em ${subsectionPath}: ${error.message}`);
              }
            }
          }

          // Recursively process deeper nested content
          await processNestedContent(
            subsectionData,
            sectionId,
            subsectionId,
            nestingLevel + 1,
            subsectionPath,
            counters
          );
        } catch (error: any) {
          console.error(`❌ Erro ao processar subseção ${key}:`, error);
          counters.incrementErrorCount(1);
          counters.addError(`Erro ao processar subseção ${key}: ${error.message}`);
        }
      }
    }

    // Process direct medications (may be present at any level)
    if (content.substancias && Array.isArray(content.substancias)) {
      for (const medication of content.substancias) {
        try {
          await createMedication(sectionId, parentSubsectionId, medication);
          counters.incrementMedicationCount(1);
          counters.incrementSuccessCount(1);
        } catch (error: any) {
          console.error(`❌ Erro ao criar medicamento ${medication.nome}:`, error);
          counters.incrementErrorCount(1);
          counters.addError(`Erro ao criar medicamento ${medication.nome}: ${error.message}`);
        }
      }
    }
  };

  const createMedication = async (sectionId: string, subsectionId: string | null, medication: any) => {
    console.log(`🔄 Criando medicamento: ${medication.nome}`);

    const { error } = await supabase
      .from('pedbook_breastfeeding_medications')
      .insert({
        section_id: sectionId,
        subsection_id: subsectionId,
        name: medication.nome,
        compatibility_level: mapCompatibilityLevel(medication.compatibilidade_amamentacao),
        usage_description: medication.uso_amamentacao,
        additional_info: medication.informacao_adicional || null
      });

    if (error) throw error;
  };

  const mapCompatibilityLevel = (level: string): string => {
    // Normalize compatibility level to match our constraints
    const normalizedLevel = level.trim();

    if (normalizedLevel === "Verde") return "Verde";
    if (normalizedLevel === "Amarelo") return "Amarelo";
    if (normalizedLevel === "Vermelho") return "Vermelho";

    // Default to yellow if unknown
    console.warn(`⚠️ Nível de compatibilidade desconhecido: ${level}, usando Amarelo como padrão`);
    return "Amarelo";
  };

  const clearData = async () => {
    try {
      setIsImporting(true);
      setCurrentOperation("Limpando dados existentes...");

      // Delete all medications first (due to foreign key constraints)
      const { error: medError } = await supabase
        .from('pedbook_breastfeeding_medications')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all

      if (medError) throw medError;

      // Delete all subsections
      const { error: subsectionError } = await supabase
        .from('pedbook_breastfeeding_subsections')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all

      if (subsectionError) throw subsectionError;

      // Delete all sections
      const { error: sectionError } = await supabase
        .from('pedbook_breastfeeding_sections')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all

      if (sectionError) throw sectionError;

      toast({
        title: "Dados limpos com sucesso",
        description: "Todos os dados de medicamentos para amamentação foram removidos."
      });
    } catch (error: any) {
      toast({
        title: "Erro ao limpar dados",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsImporting(false);
    }
  };

  const handleRawJsonChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setRawJson(e.target.value);
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-3xl font-bold mb-6">Gerenciar Medicamentos e Amamentação</h1>

      <Tabs defaultValue="import">
        <TabsList className="mb-4">
          <TabsTrigger value="import">Importar JSON</TabsTrigger>
          <TabsTrigger value="clear">Limpar Dados</TabsTrigger>
        </TabsList>

        <TabsContent value="import">
          <Card>
            <CardHeader>
              <CardTitle>Importar Medicamentos para Amamentação</CardTitle>
              <CardDescription>
                Importe medicamentos e suas informações para o banco de dados a partir de um arquivo JSON.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Selecione um arquivo JSON
                  </label>
                  <Input
                    type="file"
                    accept=".json"
                    onChange={handleFileChange}
                    disabled={isImporting}
                    className="file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Ou cole o JSON diretamente
                  </label>
                  <textarea
                    className="min-h-[200px] w-full border rounded-md p-2 font-mono text-sm"
                    value={rawJson}
                    onChange={handleRawJsonChange}
                    disabled={isImporting}
                    placeholder='{ "SEÇÃO": { "descricao": null, "substancias": [{ "nome": "Medicamento", "compatibilidade_amamentacao": "Verde", "uso_amamentacao": "Uso compatível com a amamentação." }], "SUBSEÇÃO": { "descricao": null, "substancias": [] } } }'
                  ></textarea>
                </div>

                <Button
                  onClick={importJson}
                  disabled={isImporting || !rawJson.trim()}
                  className="w-full"
                >
                  {isImporting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Importando...
                    </>
                  ) : (
                    <>
                      <FileJson className="mr-2 h-4 w-4" />
                      Importar Dados
                    </>
                  )}
                </Button>

                {isImporting && (
                  <div className="space-y-2">
                    <p className="text-xs text-gray-500">{currentOperation}</p>
                    <Progress value={progress} />
                    <div className="text-xs text-gray-600">
                      {status.processed} de {status.total} seções processadas
                      ({status.succeeded} sucessos, {status.failed} falhas)
                    </div>
                  </div>
                )}

                {errors.length > 0 && (
                  <div className="mt-4">
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Erros encontrados ({errors.length})</AlertTitle>
                      <AlertDescription>
                        <div className="max-h-40 overflow-y-auto mt-2">
                          <ul className="list-disc pl-4 space-y-1">
                            {errors.map((error, i) => (
                              <li key={i} className="text-xs">{error}</li>
                            ))}
                          </ul>
                        </div>
                      </AlertDescription>
                    </Alert>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="clear">
          <Card>
            <CardHeader>
              <CardTitle>Limpar Dados Existentes</CardTitle>
              <CardDescription>
                Remova todos os dados de medicamentos e amamentação do banco de dados.
                <br />
                <span className="text-red-500 font-medium">Atenção: Esta ação não pode ser desfeita!</span>
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                variant="destructive"
                onClick={clearData}
                disabled={isImporting}
              >
                {isImporting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processando...
                  </>
                ) : (
                  <>
                    <AlertCircle className="mr-2 h-4 w-4" />
                    Limpar Todos os Dados
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Formato esperado para o JSON</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md text-xs overflow-x-auto">
{`{
  "NOME DA SEÇÃO": {
    "descricao": null,
    "substancias": [
      {
        "nome": "Nome do Medicamento",
        "compatibilidade_amamentacao": "Verde",
        "uso_amamentacao": "Uso compatível com a amamentação."
      }
    ],
    "NOME DA SUBSEÇÃO": {
      "descricao": null,
      "substancias": [
        {
          "nome": "Nome do Medicamento",
          "compatibilidade_amamentacao": "Amarelo",
          "uso_amamentacao": "Uso criterioso durante a amamentação."
        }
      ],
      "NOME DA SUB-SUBSEÇÃO": {
        "descricao": null,
        "substancias": [
          {
            "nome": "Nome do Medicamento",
            "compatibilidade_amamentacao": "Verde",
            "uso_amamentacao": "Uso compatível com a amamentação."
          }
        ]
      }
    }
  }
}`}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
};

export default BreastfeedingMedications;
