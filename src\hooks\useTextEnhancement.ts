import { useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export const useTextEnhancement = () => {
  const queryClient = useQueryClient();

  const { data: lastEnhancement } = useQuery({
    queryKey: ["text-enhancement"],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return null;

      const { data } = await supabase
        .from("pedbook_text_enhancements")
        .select("created_at")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false })
        .limit(1)
        .single();
      return data;
    },
  });

  const canEnhance = () => {
    if (!lastEnhancement) return true;
    
    const lastEnhancementTime = new Date(lastEnhancement.created_at);
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
    
    return lastEnhancementTime < thirtyMinutesAgo;
  };

  const recordEnhancement = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    await supabase
      .from("pedbook_text_enhancements")
      .insert({ user_id: user.id });

    // Invalidate the query to force a refresh
    queryClient.invalidateQueries({ queryKey: ["text-enhancement"] });
  };

  return {
    canEnhance: canEnhance(),
    lastEnhancement,
    recordEnhancement,
  };
};