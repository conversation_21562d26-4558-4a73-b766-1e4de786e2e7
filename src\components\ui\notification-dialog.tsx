import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, AlertCircle, Info } from "lucide-react";

export type NotificationType = "success" | "error" | "info";

interface NotificationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  type?: NotificationType;
  buttonText?: string;
  onButtonClick?: () => void;
}

export function NotificationDialog({
  open,
  onOpenChange,
  title,
  description,
  type = "info",
  buttonText = "Fechar",
  onButtonClick,
}: NotificationDialogProps) {
  const handleButtonClick = () => {
    if (onButtonClick) {
      onButtonClick();
    }
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader className="flex flex-col items-center text-center gap-2">
          <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
            type === "success" ? "bg-green-100" : 
            type === "error" ? "bg-red-100" : "bg-blue-100"
          }`}>
            {type === "success" && <CheckCircle className="h-6 w-6 text-green-600" />}
            {type === "error" && <AlertCircle className="h-6 w-6 text-red-600" />}
            {type === "info" && <Info className="h-6 w-6 text-blue-600" />}
          </div>
          <DialogTitle className="text-xl">{title}</DialogTitle>
        </DialogHeader>
        <div className="py-4 text-center">
          <p className="text-muted-foreground">{description}</p>
        </div>
        <DialogFooter>
          <Button 
            className="w-full" 
            onClick={handleButtonClick}
            variant={type === "success" ? "default" : type === "error" ? "destructive" : "outline"}
          >
            {buttonText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Hook to manage notification dialog state
export function useNotificationDialog() {
  const [open, setOpen] = React.useState(false);
  const [dialogProps, setDialogProps] = React.useState<Omit<NotificationDialogProps, 'open' | 'onOpenChange'>>({
    title: "",
    description: "",
    type: "info",
    buttonText: "Fechar",
  });

  const showNotification = (props: Omit<NotificationDialogProps, 'open' | 'onOpenChange'>) => {
    setDialogProps(props);
    setOpen(true);
  };

  const NotificationDialogComponent = () => (
    <NotificationDialog
      open={open}
      onOpenChange={setOpen}
      {...dialogProps}
    />
  );

  return {
    showNotification,
    NotificationDialogComponent,
  };
}
