import { supabase } from "@/integrations/supabase/client";
import { requestCache } from "./requestCache";

/**
 * Garante que temos um userId válido, usando cache global para evitar múltiplas chamadas
 * PedBook: Exige autenticação obrigatória
 */
export const ensureUserId = async (): Promise<string> => {
  return requestCache.executeOnce('user-id', async () => {
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error || !user) {
      throw new Error("Usuário não autenticado");
    }
    return user.id;
  });
};