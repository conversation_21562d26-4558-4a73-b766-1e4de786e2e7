import React from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Card } from "@/components/ui/card";
import { Plus } from "lucide-react";
import { Dialog, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "sonner";
import { FolderCard } from "./FolderCard";
import { CreateFolderDialog } from "./CreateFolderDialog";
import { EditFolderDialog } from "./EditFolderDialog";
import { DeleteFolderDialog } from "./DeleteFolderDialog";
import { useNotes } from "@/hooks/useNotes";

interface FolderListProps {
  onFolderSelect: (folderId: string) => void;
}

export const FolderList: React.FC<FolderListProps> = ({ onFolderSelect }) => {
  const [isCreating, setIsCreating] = React.useState(false);
  const [isEditing, setIsEditing] = React.useState(false);
  const [isDeleting, setIsDeleting] = React.useState(false);
  const [editingFolderId, setEditingFolderId] = React.useState<string | null>(null);
  const [deletingFolderId, setDeletingFolderId] = React.useState<string | null>(null);
  const [newFolderName, setNewFolderName] = React.useState("");
  const queryClient = useQueryClient();
  const { deleteAllNotesInFolder } = useNotes();

  const { data: folders, isLoading } = useQuery({
    queryKey: ['folders'],
    queryFn: async () => {
      const { data: userData, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      const { data, error } = await supabase
        .from('secure_notes_folders')
        .select('*')
        .eq('user_id', userData.user.id)
        .order('name');

      if (error) throw error;
      return data;
    },
  });

  const handleCreateFolder = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newFolderName.trim()) return;

    try {
      const { data: userData } = await supabase.auth.getUser();

      const { data, error } = await supabase
        .from('pedbook_notes_folders')
        .insert([{
          name: newFolderName.trim(),
          user_id: userData.user.id
        }])
        .select()
        .single();

      if (error) throw error;

      queryClient.setQueryData(['folders'], (oldData: any) => {
        return [...(oldData || []), data];
      });

      toast.success('Pasta criada com sucesso');
      setNewFolderName("");
      setIsCreating(false);
    } catch (error) {
      toast.error('Erro ao criar pasta');
    }
  };

  const handleEditFolder = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newFolderName.trim() || !editingFolderId) return;

    try {
      const { error } = await supabase
        .from('pedbook_notes_folders')
        .update({ name: newFolderName.trim() })
        .eq('id', editingFolderId);

      if (error) throw error;

      queryClient.setQueryData(['folders'], (oldData: any) => {
        return oldData.map((folder: any) =>
          folder.id === editingFolderId ? { ...folder, name: newFolderName.trim() } : folder
        );
      });

      toast.success('Nome da pasta atualizado');
      setNewFolderName("");
      setIsEditing(false);
      setEditingFolderId(null);
    } catch (error) {
      toast.error('Erro ao atualizar pasta');
    }
  };

  const handleDeleteFolder = async () => {
    if (!deletingFolderId) return;

    try {
      await deleteAllNotesInFolder(deletingFolderId);

      const { error } = await supabase
        .from('pedbook_notes_folders')
        .delete()
        .eq('id', deletingFolderId);

      if (error) throw error;

      queryClient.setQueryData(['folders'], (oldData: any) =>
        (oldData || []).filter((folder: any) => folder.id !== deletingFolderId)
      );

      toast.success('Pasta removida com sucesso');
      setIsDeleting(false);
      setDeletingFolderId(null);
    } catch (error) {
      toast.error('Erro ao remover pasta');
    }
  };

  if (isLoading) {
    return <div>Carregando...</div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {folders?.map((folder) => (
        <FolderCard
          key={folder.id}
          id={folder.id}
          name={folder.name}
          onSelect={onFolderSelect}
          onEdit={(folderId, currentName) => {
            setEditingFolderId(folderId);
            setNewFolderName(currentName);
            setIsEditing(true);
          }}
          onDelete={(folderId) => {
            setDeletingFolderId(folderId);
            setIsDeleting(true);
          }}
        />
      ))}

      <Dialog open={isCreating} onOpenChange={setIsCreating}>
        <DialogTrigger asChild>
          <Card className="p-6 flex flex-col items-center justify-center gap-4 border-2 border-dashed border-gray-200 hover:border-primary/50 cursor-pointer transition-colors">
            <Plus className="h-8 w-8 text-gray-400" />
            <span className="text-sm text-gray-600">Nova Pasta</span>
          </Card>
        </DialogTrigger>

        <CreateFolderDialog
          isOpen={isCreating}
          onOpenChange={setIsCreating}
          folderName={newFolderName}
          onFolderNameChange={setNewFolderName}
          onSubmit={handleCreateFolder}
        />
      </Dialog>

      <EditFolderDialog
        isOpen={isEditing}
        onOpenChange={setIsEditing}
        folderName={newFolderName}
        onFolderNameChange={setNewFolderName}
        onSubmit={handleEditFolder}
      />

      <DeleteFolderDialog
        isOpen={isDeleting}
        onOpenChange={setIsDeleting}
        onConfirm={handleDeleteFolder}
      />
    </div>
  );
};
