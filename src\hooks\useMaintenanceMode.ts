import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useNavigate, useLocation } from 'react-router-dom';

interface MaintenanceStatus {
  is_active: boolean;
  message: string;
  estimated_duration?: string;
  activated_at?: string;
}

export const useMaintenanceMode = () => {
  const { profile, isLoading: authLoading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [isChecking, setIsChecking] = useState(true);

  // Verificar se é super admin
  const superAdminEmails = ['<EMAIL>', '<EMAIL>'];
  const isSuperAdmin = profile?.is_admin && superAdminEmails.includes(profile?.email || '');



  // Query para verificar status de manutenção
  const { data: maintenanceStatus, isLoading, refetch } = useQuery({
    queryKey: ['maintenance-status'],
    queryFn: async (): Promise<MaintenanceStatus> => {
      const { data, error } = await supabase
        .from('site_maintenance')
        .select('is_active, message, estimated_duration, activated_at')
        .single();

      if (error) {
        return { is_active: false, message: 'Site em manutenção. Voltaremos em breve!' };
      }

      return data;
    },
    refetchInterval: false, // DESABILITADO: contribuía para consumo excessivo de bandwidth
    refetchOnWindowFocus: false, // Evitar refetch desnecessário
    refetchOnMount: false, // Não refetch ao montar
    refetchIntervalInBackground: false, // Não refetch em background
    staleTime: 5 * 60 * 1000, // 5 minutos - dados raramente mudam
    gcTime: 10 * 60 * 1000, // 10 minutos
    enabled: !authLoading, // Só executar quando auth estiver carregado
  });

  // Função para ativar/desativar manutenção (apenas super admin)
  const toggleMaintenance = async (
    isActive: boolean,
    message?: string,
    estimatedDuration?: string
  ) => {
    if (!isSuperAdmin) {
      throw new Error('Apenas super admin pode alterar o modo de manutenção');
    }

    // Primeiro, buscar o ID do registro
    const { data: maintenanceRecord } = await supabase
      .from('site_maintenance')
      .select('id')
      .single();

    if (!maintenanceRecord) {
      throw new Error('Registro de manutenção não encontrado');
    }

    const { error } = await supabase
      .from('site_maintenance')
      .update({
        is_active: isActive,
        message: message || 'Site em manutenção. Voltaremos em breve!',
        estimated_duration: estimatedDuration,
        activated_by: profile?.id,
        updated_at: new Date().toISOString(),
      })
      .eq('id', maintenanceRecord.id);

    if (error) {
      throw error;
    }

    // Refetch para atualizar o status
    await refetch();
  };

  // Efeito para redirecionar usuários quando manutenção é ativada
  useEffect(() => {
    if (authLoading || isLoading) {
      return;
    }

    setIsChecking(false);

    // Se manutenção está ativa e usuário não é super admin
    if (maintenanceStatus?.is_active && !isSuperAdmin) {
      // Não redirecionar se já está na página de manutenção
      if (location.pathname !== '/maintenance') {
        navigate('/maintenance', { replace: true });
      }
    } else if (!maintenanceStatus?.is_active && location.pathname === '/maintenance') {
      // Se manutenção foi desativada e usuário está na página de manutenção
      navigate('/', { replace: true });
    }
  }, [maintenanceStatus, isSuperAdmin, authLoading, isLoading, navigate, location.pathname]);

  return {
    maintenanceStatus,
    isMaintenanceActive: maintenanceStatus?.is_active || false,
    isSuperAdmin,
    isLoading: authLoading || isLoading || isChecking,
    toggleMaintenance,
    refetch,
  };
};
