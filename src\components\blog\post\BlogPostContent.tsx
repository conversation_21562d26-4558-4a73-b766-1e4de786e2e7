import { motion } from "framer-motion";
import { ThumbsUp, ThumbsDown, Tag } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

interface BlogPostContentProps {
  post: {
    content: string;
    likes_count?: number;
    dislikes_count?: number;
    id: string;
  };
  id: string;
  userReaction?: { reaction_type: string } | null;
  onReactionClick: (type: "like" | "dislike") => void;
}

export function BlogPostContent({
  post,
  id,
  userReaction,
  onReactionClick
}: BlogPostContentProps) {
  const { data: tags } = useQuery({
    queryKey: ["blog-post-tags", id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_blog_posts_tags')
        .select(`
          tag_id,
          pedbook_blog_tags (
            name,
            slug
          )
        `)
        .eq('post_id', id);

      if (error) throw error;
      return data;
    },
  });

  return (
    <>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
        className="prose prose-lg max-w-none mb-8 [&>p]:mb-4 [&>ul]:space-y-4 [&>ul>li>p]:mb-4 selectable-text"
        dangerouslySetInnerHTML={{ __html: post.content }}
      />

      {tags && tags.length > 0 && (
        <div className="flex items-center gap-2 mb-8">
          <Tag className="h-4 w-4 text-gray-500" />
          <div className="flex flex-wrap gap-2">
            {tags.map((tag) => (
              <Badge
                key={tag.tag_id}
                variant="secondary"
                className="text-sm"
              >
                {tag.pedbook_blog_tags.name}
              </Badge>
            ))}
          </div>
        </div>
      )}

      <div className="flex flex-col items-center gap-4 mb-8 p-6 bg-white/50 backdrop-blur-sm rounded-lg shadow-sm">
        <h3 className="text-lg font-medium text-gray-700">O que achou deste conteúdo?</h3>
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="lg"
            className={`flex items-center gap-2 min-w-[120px] ${
              userReaction?.reaction_type === "like"
                ? "bg-primary/10 text-primary border-primary"
                : "bg-white"
            }`}
            onClick={() => onReactionClick("like")}
          >
            <ThumbsUp className="h-5 w-5" />
            <span className="font-medium">{post.likes_count || 0}</span>
          </Button>
          <Button
            variant="outline"
            size="lg"
            className={`flex items-center gap-2 min-w-[120px] ${
              userReaction?.reaction_type === "dislike"
                ? "bg-red-50 text-red-500 border-red-500"
                : "bg-white"
            }`}
            onClick={() => onReactionClick("dislike")}
          >
            <ThumbsDown className="h-5 w-5" />
            <span className="font-medium">{post.dislikes_count || 0}</span>
          </Button>
        </div>
      </div>
    </>
  );
}