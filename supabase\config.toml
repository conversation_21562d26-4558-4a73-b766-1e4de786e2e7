
project_id = "bxedpdmgvgatjdfxgxij"

[api]
enabled = true
port = 54321
schemas = ["public", "storage", "graphql_public"]

[db]
port = 54322
shadow_port = 54320
major_version = 15

[studio]
enabled = true
port = 54323
api_url = "http://localhost"

[storage]
enabled = true
file_size_limit = "50MiB"

[auth]
enabled = true
site_url = "https://pedb.com.br"
additional_redirect_urls = ["https://pedb.com.br", "http://localhost:3000", "https://localhost:3000"]
jwt_expiry = 604800
enable_refresh_token_rotation = true
refresh_token_reuse_interval = 300

[realtime]
enabled = true

[analytics]
enabled = false
port = 54324

[functions]
verify_jwt = true

[functions.drug-interactions]
verify_jwt = true

[functions.dr-will-chat]
verify_jwt = true

[functions.medicine-chat]
verify_jwt = true

[functions.medicine-search]
verify_jwt = true

[functions.enhance-text]
verify_jwt = true

[functions.enhance-breastfeeding-medications]
verify_jwt = true
