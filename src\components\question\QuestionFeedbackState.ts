import { create } from 'zustand';

interface FeedbackState {
  feedbackByQuestion: Record<string, boolean>;
  setFeedback: (questionId: string, show: boolean) => void;
  resetFeedback: () => void;
}

export const useFeedbackState = create<FeedbackState>((set) => ({
  feedbackByQuestion: {},
  setFeedback: (questionId, show) => 
    set((state) => ({
      feedbackByQuestion: {
        ...state.feedbackByQuestion,
        [questionId]: show
      }
    })),
  resetFeedback: () => set({ feedbackByQuestion: {} })
}));