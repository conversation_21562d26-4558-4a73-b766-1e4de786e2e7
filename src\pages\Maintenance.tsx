import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON>, Clock, RefreshCw, Home, Brain, BookOpen, Award, Timer, Rocket, Hourglass } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useMaintenanceMode } from '@/hooks/useMaintenanceMode';
import { useNavigate } from 'react-router-dom';
import { Progress } from '@/components/ui/progress';

// Quiz educativo de pediatria
interface QuizQuestion {
  question: string;
  options: string[];
  correct: number;
  explanation: string;
}

interface GameState {
  score: number;
  currentQuestion: number;
  timeLeft: number;
  isPlaying: boolean;
  highScore: number;
  showExplanation: boolean;
  selectedAnswer: number | null;
}

// Perguntas educativas de pediatria
const quizQuestions: QuizQuestion[] = [
  {
    question: "Qual a dose de paracetamol para uma criança de 10kg?",
    options: ["100mg", "150mg", "200mg", "250mg"],
    correct: 1,
    explanation: "A dose de paracetamol é 15mg/kg/dose. Para 10kg: 15 × 10 = 150mg"
  },
  {
    question: "Em que idade geralmente ocorre o primeiro dente?",
    options: ["4-6 meses", "6-8 meses", "8-10 meses", "10-12 meses"],
    correct: 1,
    explanation: "O primeiro dente geralmente erupciona entre 6-8 meses de idade"
  },
  {
    question: "Qual o peso médio de um recém-nascido a termo?",
    options: ["2,5-3,0 kg", "3,0-3,5 kg", "3,5-4,0 kg", "4,0-4,5 kg"],
    correct: 1,
    explanation: "O peso médio de um RN a termo é entre 3,0-3,5 kg"
  },
  {
    question: "Até que idade é recomendado o aleitamento materno exclusivo?",
    options: ["4 meses", "6 meses", "8 meses", "12 meses"],
    correct: 1,
    explanation: "A OMS recomenda aleitamento materno exclusivo até os 6 meses"
  },
  {
    question: "Qual a frequência cardíaca normal de um lactente?",
    options: ["60-100 bpm", "80-120 bpm", "100-160 bpm", "120-180 bpm"],
    correct: 2,
    explanation: "A FC normal de um lactente é 100-160 bpm"
  },
  {
    question: "Com que idade geralmente a criança senta sem apoio?",
    options: ["2 meses", "6 meses", "9 meses", "12 meses"],
    correct: 1,
    explanation: "A criança geralmente senta sem apoio por volta dos 6 meses de idade"
  },
  {
    question: "A vacina BCG é indicada em qual faixa etária?",
    options: ["Ao nascer", "Até 5 anos se não vacinado e sem cicatriz", "Após 1 ano apenas", "Somente com 2 meses"],
    correct: 1,
    explanation: "A BCG é indicada ao nascer ou até 5 anos se não vacinado e sem cicatriz"
  },
  {
    question: "Qual a principal causa de febre sem sinais localizatórios em <3 anos?",
    options: ["Otite média", "Infecção viral autolimitada", "ITU", "Meningite"],
    correct: 1,
    explanation: "Infecções virais autolimitadas são a principal causa de febre sem sinais localizatórios em menores de 3 anos"
  },
  {
    question: "Qual o reflexo que desaparece por volta dos 6 meses?",
    options: ["Tônico cervical assimétrico", "Moro", "Preensão palmar", "Marcha automática"],
    correct: 1,
    explanation: "O reflexo de Moro desaparece por volta dos 6 meses de idade"
  },
  {
    question: "Em qual idade é esperado que a criança fale palavras com significado?",
    options: ["6 meses", "12 meses", "18 meses", "24 meses"],
    correct: 1,
    explanation: "Por volta dos 12 meses a criança já deve falar palavras com significado como 'mamã' e 'papá'"
  },
  {
    question: "Qual a frequência cardíaca normal de um recém-nascido?",
    options: ["60-100 bpm", "120-160 bpm", "80-110 bpm", "100-140 bpm"],
    correct: 1,
    explanation: "A FC normal de um recém-nascido é 120-160 bpm"
  },
  {
    question: "Quando é indicada a primeira dose da vacina pentavalente?",
    options: ["Ao nascer", "2 meses", "6 semanas", "4 meses"],
    correct: 1,
    explanation: "A primeira dose da pentavalente é indicada aos 2 meses de idade"
  },
  {
    question: "Qual o tratamento inicial da diarreia aguda em criança sem sinais de alarme?",
    options: ["Jejum", "Antibiótico", "Hidratação oral + zinco", "Soro intravenoso"],
    correct: 2,
    explanation: "O tratamento inicial é hidratação oral com SRO e suplementação de zinco"
  },
  {
    question: "Qual vitamina previne raquitismo na infância?",
    options: ["Vitamina A", "Vitamina D", "Vitamina E", "Vitamina B12"],
    correct: 1,
    explanation: "A vitamina D é essencial para absorção de cálcio e prevenção do raquitismo"
  },
  {
    question: "Quando ocorre o pico de cólica do lactente?",
    options: ["1ª semana", "6ª semana", "3 meses", "6 meses"],
    correct: 1,
    explanation: "O pico da cólica do lactente ocorre por volta da 6ª semana de vida"
  },
  {
    question: "Qual o limite máximo diário de paracetamol por kg em crianças?",
    options: ["30 mg/kg", "60 mg/kg", "75 mg/kg", "100 mg/kg"],
    correct: 1,
    explanation: "O limite máximo diário de paracetamol é 60 mg/kg/dia, dividido em 4-6 doses"
  },
  {
    question: "Em qual idade se inicia a introdução alimentar?",
    options: ["4 meses", "6 meses", "9 meses", "12 meses"],
    correct: 1,
    explanation: "A introdução alimentar deve iniciar aos 6 meses, mantendo o aleitamento materno"
  },
  {
    question: "Qual exame é padrão-ouro para ITU em criança?",
    options: ["Urina tipo I", "USG renal", "Urocultura", "Cintilografia DMSA"],
    correct: 2,
    explanation: "A urocultura é o padrão-ouro para diagnóstico de ITU em crianças"
  },
  {
    question: "Qual o principal agente da bronquiolite?",
    options: ["Adenovírus", "Vírus sincicial respiratório (VSR)", "Influenza", "Coronavírus"],
    correct: 1,
    explanation: "O VSR é o principal agente causador de bronquiolite em lactentes"
  },
  {
    question: "Quando é indicada a vacina contra HPV em meninas no SUS?",
    options: ["Aos 10 anos", "9 a 14 anos", "Aos 16 anos", "Após o início da vida sexual"],
    correct: 1,
    explanation: "A vacina HPV é indicada para meninas de 9 a 14 anos no calendário do SUS"
  }
];

const Maintenance: React.FC = () => {
  const { maintenanceStatus, isLoading, refetch } = useMaintenanceMode();
  const navigate = useNavigate();

  // Estado do quiz
  const [game, setGame] = useState<GameState>({
    score: 0,
    currentQuestion: 0,
    timeLeft: 60,
    isPlaying: false,
    highScore: parseInt(localStorage.getItem('pedbook_quiz_highscore') || '0'),
    showExplanation: false,
    selectedAnswer: null
  });

  // Estado da animação e countdown
  const [lastRefresh, setLastRefresh] = useState(Date.now());
  const [estimatedEndTime, setEstimatedEndTime] = useState<Date | null>(null);

  // Se não está em manutenção, redirecionar para home
  // NOTA: Este redirecionamento é controlado pelo hook useMaintenanceMode
  // Não precisamos fazer nada aqui, o hook já gerencia isso

  // Calcular tempo estimado de fim da manutenção
  useEffect(() => {
    if (maintenanceStatus?.estimated_duration && maintenanceStatus?.activated_at) {
      const startTime = new Date(maintenanceStatus.activated_at);
      const duration = maintenanceStatus.estimated_duration;

      // Parse melhorado da duração (suporta vários formatos)
      const match = duration.match(/(\d+)\s*(minuto|minutos|hora|horas|min|h)/i);
      if (match) {
        const value = parseInt(match[1]);
        const unit = match[2].toLowerCase();

        // Converter para minutos
        let minutes = value;
        if (unit.includes('hora') || unit.includes('h')) {
          minutes = value * 60;
        }

        const endTime = new Date(startTime.getTime() + minutes * 60 * 1000);
        setEstimatedEndTime(endTime);
      }
    }
  }, [maintenanceStatus]);

  // Timer do quiz
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (game.isPlaying && game.timeLeft > 0 && !game.showExplanation) {
      interval = setInterval(() => {
        setGame(prev => ({ ...prev, timeLeft: prev.timeLeft - 1 }));
      }, 1000);
    } else if (game.timeLeft === 0 && game.isPlaying) {
      // Fim do quiz
      setGame(prev => {
        const newHighScore = Math.max(prev.score, prev.highScore);
        localStorage.setItem('pedbook_quiz_highscore', newHighScore.toString());
        return { ...prev, isPlaying: false, highScore: newHighScore };
      });
    }
    return () => clearInterval(interval);
  }, [game.isPlaying, game.timeLeft, game.showExplanation]);

  // Funções do quiz
  const startQuiz = () => {
    setGame(prev => ({
      ...prev,
      score: 0,
      currentQuestion: 0,
      timeLeft: 60,
      isPlaying: true,
      showExplanation: false,
      selectedAnswer: null
    }));
  };

  const handleAnswerSelect = (answerIndex: number) => {
    if (game.showExplanation) return;

    setGame(prev => ({ ...prev, selectedAnswer: answerIndex, showExplanation: true }));

    // Verificar se está correto
    const currentQ = quizQuestions[game.currentQuestion];
    if (answerIndex === currentQ.correct) {
      setGame(prev => ({ ...prev, score: prev.score + 10 }));
    }

    // Próxima pergunta após 3 segundos
    setTimeout(() => {
      setGame(prev => {
        if (prev.currentQuestion < quizQuestions.length - 1) {
          return {
            ...prev,
            currentQuestion: prev.currentQuestion + 1,
            showExplanation: false,
            selectedAnswer: null
          };
        } else {
          // Fim do quiz
          const newHighScore = Math.max(prev.score, prev.highScore);
          localStorage.setItem('pedbook_quiz_highscore', newHighScore.toString());
          return { ...prev, isPlaying: false, highScore: newHighScore };
        }
      });
    }, 3000);
  };

  const handleRefresh = () => {
    setLastRefresh(Date.now());
    refetch();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex flex-col items-center justify-center p-4 relative overflow-hidden">
      {/* Animações de fundo */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-20 h-20 bg-blue-200 rounded-full opacity-20"
            animate={{
              x: [0, 100, 0],
              y: [0, -100, 0],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 8 + i * 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
            style={{
              left: `${10 + i * 15}%`,
              top: `${20 + i * 10}%`,
            }}
          />
        ))}
      </div>

      <div className="w-full max-w-4xl z-10">
        {/* Header com Logo */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center mb-4">
            <img
              src="/faviconx.webp"
              alt="PedBook Logo"
              className="w-16 h-16 mr-3"
            />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              PedBook
            </h1>
          </div>
          <p className="text-gray-600 text-lg">Calculadora Pediátrica</p>
        </motion.div>

        {/* Cards principais */}
        <div className="grid md:grid-cols-2 gap-6">
          {/* Card de Manutenção */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="p-6 shadow-xl border-0 bg-white/90 backdrop-blur-sm">
              {/* Ícone animado */}
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="mx-auto mb-4 w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center"
              >
                <Wrench className="h-8 w-8 text-white" />
              </motion.div>

              <h2 className="text-xl font-bold text-gray-800 mb-3 text-center">
                Site em Manutenção
              </h2>

              <p className="text-gray-600 mb-4 text-sm leading-relaxed text-center">
                {maintenanceStatus?.message || 'Estamos realizando melhorias no sistema. Voltaremos em breve!'}
              </p>

              {/* Countdown Visual */}
              {estimatedEndTime && (
                <div className="mb-4">
                  <CountdownTimer endTime={estimatedEndTime} />
                </div>
              )}

              {maintenanceStatus?.estimated_duration && (
                <div className="flex items-center justify-center gap-2 text-sm text-gray-500 mb-4">
                  <Clock className="h-4 w-4" />
                  <span>Tempo estimado: {maintenanceStatus.estimated_duration}</span>
                </div>
              )}

              <div className="space-y-2">
                <Button
                  onClick={handleRefresh}
                  variant="outline"
                  className="w-full"
                  disabled={isLoading}
                  size="sm"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                  Verificar Novamente
                </Button>

                <Button
                  onClick={() => window.location.href = '/'}
                  variant="ghost"
                  className="w-full text-gray-500"
                  size="sm"
                >
                  <Home className="h-4 w-4 mr-2" />
                  Tentar Acessar
                </Button>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200 text-center">
                <p className="text-xs text-gray-400">
                  Última verificação: {new Date(lastRefresh).toLocaleTimeString('pt-BR')}
                </p>
              </div>
            </Card>
          </motion.div>

          {/* Card do Jogo */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Card className="p-6 shadow-xl border-0 bg-white/90 backdrop-blur-sm">
              <div className="text-center mb-4">
                <motion.div
                  animate={{
                    scale: game.isPlaying ? [1, 1.05, 1] : 1,
                    rotate: game.isPlaying ? [0, 2, -2, 0] : 0
                  }}
                  transition={{ duration: 2, repeat: game.isPlaying ? Infinity : 0 }}
                  className="mx-auto mb-3 w-16 h-16 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center"
                >
                  <Brain className="h-8 w-8 text-white" />
                </motion.div>

                <h2 className="text-xl font-bold text-gray-800 mb-2">
                  Quiz de Pediatria
                </h2>
                <p className="text-sm text-gray-600 mb-4">
                  Teste seus conhecimentos enquanto aguarda!
                </p>
              </div>

              {!game.isPlaying ? (
                <div className="text-center space-y-3">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <Award className="h-5 w-5 text-blue-600 mx-auto mb-1" />
                      <p className="font-medium text-blue-800">Recorde</p>
                      <p className="text-blue-600">{game.highScore} pts</p>
                    </div>
                    <div className="bg-green-50 p-3 rounded-lg">
                      <BookOpen className="h-5 w-5 text-green-600 mx-auto mb-1" />
                      <p className="font-medium text-green-800">Último</p>
                      <p className="text-green-600">{game.score} pts</p>
                    </div>
                  </div>

                  <Button onClick={startQuiz} className="w-full bg-gradient-to-r from-green-500 to-blue-600">
                    <Brain className="h-4 w-4 mr-2" />
                    Iniciar Quiz (60s)
                  </Button>

                  <p className="text-xs text-gray-500">
                    {quizQuestions.length} perguntas • 10 pontos cada
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-lg font-bold text-green-600 mb-2">
                      Pergunta {game.currentQuestion + 1} de {quizQuestions.length}
                    </div>
                    <div className="text-sm text-gray-600 mb-3">
                      {game.score} pontos • {game.timeLeft}s restantes
                    </div>
                    <Progress value={(game.timeLeft / 60) * 100} className="mb-4" />
                  </div>

                  <div className="space-y-3">
                    <h3 className="font-medium text-gray-800 text-sm">
                      {quizQuestions[game.currentQuestion]?.question}
                    </h3>

                    <div className="space-y-2">
                      {quizQuestions[game.currentQuestion]?.options.map((option, index) => (
                        <motion.button
                          key={index}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => handleAnswerSelect(index)}
                          disabled={game.showExplanation}
                          className={`w-full p-3 text-left text-sm rounded-lg border transition-all ${
                            game.showExplanation
                              ? index === quizQuestions[game.currentQuestion].correct
                                ? 'bg-green-100 border-green-300 text-green-800'
                                : index === game.selectedAnswer
                                ? 'bg-red-100 border-red-300 text-red-800'
                                : 'bg-gray-50 border-gray-200 text-gray-600'
                              : 'bg-white border-gray-200 hover:border-blue-300 hover:bg-blue-50'
                          }`}
                        >
                          {option}
                        </motion.button>
                      ))}
                    </div>

                    {game.showExplanation && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="p-3 bg-blue-50 border border-blue-200 rounded-lg"
                      >
                        <p className="text-sm text-blue-800">
                          <strong>Explicação:</strong> {quizQuestions[game.currentQuestion]?.explanation}
                        </p>
                      </motion.div>
                    )}
                  </div>
                </div>
              )}
            </Card>
          </motion.div>
        </div>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
          className="text-center mt-8 text-gray-500 text-sm"
        >
          <p>PedBook - Calculadora Pediátrica • Aguarde enquanto melhoramos sua experiência</p>
        </motion.div>
      </div>
    </div>
  );
};

// Componente de Countdown Visual
const CountdownTimer: React.FC<{ endTime: Date }> = ({ endTime }) => {
  const [timeLeft, setTimeLeft] = useState<{
    hours: number;
    minutes: number;
    seconds: number;
    total: number;
  }>({ hours: 0, minutes: 0, seconds: 0, total: 0 });

  useEffect(() => {
    const updateTimer = () => {
      const now = new Date().getTime();
      const distance = endTime.getTime() - now;

      if (distance > 0) {
        const hours = Math.floor(distance / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        setTimeLeft({ hours, minutes, seconds, total: distance });
      } else {
        setTimeLeft({ hours: 0, minutes: 0, seconds: 0, total: 0 });
      }
    };

    updateTimer();
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [endTime]);

  const progress = Math.max(0, Math.min(100, (timeLeft.total / (60 * 60 * 1000)) * 100)); // Assumindo máximo de 1 hora

  return (
    <div className="text-center space-y-3">
      {/* Foguete animado */}
      <div className="relative">
        <motion.div
          animate={{
            y: [0, -10, 0],
            rotate: [0, 5, -5, 0]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="mx-auto w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center"
        >
          <Rocket className="h-6 w-6 text-white" />
        </motion.div>

        {/* Trilha do foguete */}
        <div className="absolute top-12 left-1/2 transform -translate-x-1/2 w-1 h-8 bg-gradient-to-b from-orange-300 to-transparent opacity-60"></div>
      </div>

      {/* Countdown */}
      <div className="flex justify-center space-x-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">{timeLeft.hours.toString().padStart(2, '0')}</div>
          <div className="text-xs text-gray-500">horas</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">{timeLeft.minutes.toString().padStart(2, '0')}</div>
          <div className="text-xs text-gray-500">min</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">{timeLeft.seconds.toString().padStart(2, '0')}</div>
          <div className="text-xs text-gray-500">seg</div>
        </div>
      </div>

      {/* Ampulheta com progresso */}
      <div className="flex items-center justify-center space-x-2">
        <Hourglass className="h-4 w-4 text-orange-500" />
        <div className="flex-1 max-w-32">
          <Progress value={100 - progress} className="h-2" />
        </div>
        <Timer className="h-4 w-4 text-orange-500" />
      </div>

      <p className="text-xs text-gray-500">
        Tempo restante estimado
      </p>
    </div>
  );
};

export default Maintenance;
