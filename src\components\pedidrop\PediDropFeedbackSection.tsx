import React from 'react';
import { motion } from 'framer-motion';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { 
  MessageSquare, 
  Droplets, 
  Heart, 
  Star,
  TrendingUp,
  Clock,
  Users
} from "lucide-react";
import { usePediDropFeedback } from "@/hooks/usePediDropFeedback";
import { PediDropFeedback } from "./PediDropFeedback";

interface PediDropFeedbackSectionProps {
  postId?: string;
  postTitle?: string;
}

export const PediDropFeedbackSection: React.FC<PediDropFeedbackSectionProps> = ({
  postId,
  postTitle
}) => {
  const { 
    showFeedback, 
    triggerFeedback, 
    closeFeedback, 
    isLoggedIn,
    timeOnPage 
  } = usePediDropFeedback();

  return (
    <>
      {/* Seção de Feedback */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
        className="mt-12 mb-8"
      >
        <Card className="bg-gradient-to-br from-blue-50 via-purple-50/30 to-indigo-50/20 dark:from-slate-800 dark:via-slate-700/50 dark:to-slate-800 border-blue-200/50 dark:border-blue-800/30 shadow-lg">
          <CardContent className="p-6 md:p-8">
            <div className="text-center space-y-6">
              {/* Header */}
              <div className="space-y-3">
                <div className="flex items-center justify-center gap-2">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
                    <Droplets className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-xl md:text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text">
                    Sua opinião importa!
                  </h3>
                </div>
                
                <p className="text-gray-700 dark:text-gray-300 text-base md:text-lg leading-relaxed max-w-2xl mx-auto">
                  O <strong>PediDrop</strong> é um formato experimental. Queremos saber se está sendo útil para você 
                  e se devemos continuar, melhorar ou repensar este modelo de atualização clínica.
                </p>
              </div>

              {/* Stats visuais */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-lg mx-auto">
                <div className="flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                  <Clock className="h-4 w-4 text-blue-500" />
                  <span>5 min de leitura</span>
                </div>
                <div className="flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                  <TrendingUp className="h-4 w-4 text-green-500" />
                  <span>Baseado em evidências</span>
                </div>
                <div className="flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                  <Users className="h-4 w-4 text-purple-500" />
                  <span>Para pediatras</span>
                </div>
              </div>

              {/* Call to Action */}
              <div className="space-y-4">
                {isLoggedIn ? (
                  <Button
                    onClick={triggerFeedback}
                    size="lg"
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                  >
                    <MessageSquare className="h-5 w-5 mr-2" />
                    Avaliar o PediDrop
                  </Button>
                ) : (
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Faça login para avaliar o PediDrop
                    </p>
                    <Button
                      variant="outline"
                      size="lg"
                      className="border-blue-300 text-blue-700 hover:bg-blue-50 dark:border-blue-700 dark:text-blue-300 dark:hover:bg-blue-900/30"
                      onClick={() => window.location.href = '/login'}
                    >
                      <Heart className="h-5 w-5 mr-2" />
                      Fazer Login para Avaliar
                    </Button>
                  </div>
                )}

                {/* Incentivo adicional */}
                <div className="bg-white/60 dark:bg-slate-700/30 rounded-lg p-4 border border-blue-200/30 dark:border-blue-800/30">
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span>
                      Seu feedback nos ajuda a decidir o futuro do PediDrop
                    </span>
                  </div>
                </div>
              </div>

              {/* Informação sobre tempo na página (apenas para debug em dev) */}
              {process.env.NODE_ENV === 'development' && (
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Tempo na página: {timeOnPage}s
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Dialog de Feedback */}
      <PediDropFeedback
        open={showFeedback}
        onOpenChange={closeFeedback}
        postId={postId}
        postTitle={postTitle}
      />
    </>
  );
};
