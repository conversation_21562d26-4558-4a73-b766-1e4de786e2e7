
import React, { useEffect } from 'react';
import { useTheme } from '@/context/ThemeContext';
import { Moon, Sun } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ThemeToggleProps {
  className?: string;
  size?: "sm" | "md" | "lg";
  variant?: "icon" | "default";
}

export const ThemeToggle = ({ 
  className, 
  size = "md", 
  variant = "default" 
}: ThemeToggleProps) => {
  const { theme, toggleTheme } = useTheme();
  
  useEffect(() => {
    // Tema atualizado
  }, [theme]);
  
  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-10 h-10",
    lg: "w-12 h-12"
  };
  
  const iconSizes = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6"
  };
  
  if (variant === "icon") {
    return (
      <button
        onClick={toggleTheme}
        className={cn(
          "rounded-full flex items-center justify-center transition-all duration-300 relative",
          "hover:bg-primary/10 dark:hover:bg-primary/20 focus:outline-none",
          theme === 'dark' 
            ? "text-yellow-300" 
            : "text-gray-500",
          className
        )}
        aria-label={theme === 'dark' ? 'Alternar para modo claro' : 'Alternar para modo escuro'}
        title={theme === 'dark' ? 'Alternar para modo claro' : 'Alternar para modo escuro'}
      >
        {theme === 'dark' ? (
          <Sun className={iconSizes[size]} />
        ) : (
          <Moon className={iconSizes[size]} />
        )}
      </button>
    );
  }
  
  return (
    <button
      onClick={toggleTheme}
      className={cn(
        sizeClasses[size],
        "rounded-full flex items-center justify-center transition-all duration-300 relative",
        "hover:bg-primary/10 dark:hover:bg-primary/20 focus:outline-none focus:ring-2 focus:ring-primary/20 dark:focus:ring-blue-500/30",
        theme === 'dark' 
          ? "text-yellow-300 bg-slate-800 hover:bg-slate-700 shadow-inner" 
          : "text-primary bg-white hover:bg-gray-100 shadow-sm",
        className
      )}
      aria-label={theme === 'dark' ? 'Alternar para modo claro' : 'Alternar para modo escuro'}
      title={theme === 'dark' ? 'Alternar para modo claro' : 'Alternar para modo escuro'}
    >
      {theme === 'dark' ? (
        <Sun className={iconSizes[size]} />
      ) : (
        <Moon className={iconSizes[size]} />
      )}
    </button>
  );
};
