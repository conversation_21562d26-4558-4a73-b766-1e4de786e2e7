import type { CategoryListProps } from "./types";

export const CategoryList = ({ categories, selectedType }: CategoryListProps) => {
  return (
    <div className="border rounded-lg p-4 space-y-2">
      {categories.length === 0 ? (
        <p className="text-gray-500 text-center py-4">
          Nenhuma categoria cadastrada
        </p>
      ) : (
        categories
          .filter(cat => cat.type === selectedType)
          .map(category => (
            <div
              key={category.id}
              className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg border hover:border-primary/50 transition-all"
            >
              <span className="font-medium">{category.name}</span>
              {category.parentId && (
                <span className="text-sm text-gray-500">
                  ↳ {categories.find(c => c.id === category.parentId)?.name}
                </span>
              )}
            </div>
          ))
      )}
    </div>
  );
};