
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { FileText, Stethoscope, Copy } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";

interface GeneratedTextProps {
  anamnese: string;
  exame: string;
}

export const GeneratedText: React.FC<GeneratedTextProps> = ({ anamnese, exame }) => {
  const { toast } = useToast();

  const copySection = (text: string, section: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: `${section} copiado!`,
      description: `O texto do ${section.toLowerCase()} foi copiado para a área de transferência.`,
    });
  };

  const renderFormattedText = (text: string) => {
    // Split text by markdown headings (## or **) to identify sections
    const sections = text.split(/(\*\*[^*]+\*\*|\n##[^#]+)/g).filter(Boolean);
    
    return (
      <div className="space-y-4">
        {sections.map((section, index) => {
          if (section.startsWith('**') && section.endsWith('**')) {
            // This is a heading with ** markup
            return (
              <h3 key={index} className="text-lg font-bold text-primary mt-6 first:mt-0">
                {section.replace(/\*\*/g, '')}
              </h3>
            );
          } else if (section.startsWith('\n##')) {
            // This is a heading with ## markup
            return (
              <h3 key={index} className="text-lg font-bold text-primary mt-6 first:mt-0">
                {section.replace(/\n##/, '').trim()}
              </h3>
            );
          } else {
            // Regular text, handle paragraph breaks
            const paragraphs = section.split('\n\n').filter(Boolean);
            
            return (
              <div key={index} className="space-y-2">
                {paragraphs.map((para, pIndex) => (
                  <p key={`${index}-${pIndex}`} className="text-gray-800">
                    {para.trim()}
                  </p>
                ))}
              </div>
            );
          }
        })}
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Resultado</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="completo" className="space-y-4">
          <TabsList className="grid grid-cols-3 w-full">
            <TabsTrigger value="completo">Texto Completo</TabsTrigger>
            <TabsTrigger value="anamnese">
              <FileText className="h-4 w-4 mr-2" /> Anamnese
            </TabsTrigger>
            <TabsTrigger value="exame">
              <Stethoscope className="h-4 w-4 mr-2" /> Exame Físico
            </TabsTrigger>
          </TabsList>

          <TabsContent value="completo">
            <div className="border rounded-md overflow-hidden">
              <div className="bg-gray-50 border-b p-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-bold text-primary">ANAMNESE</h3>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => copySection(anamnese, "Anamnese")}
                    className="flex gap-1 items-center"
                  >
                    <Copy className="h-4 w-4" /> Copiar
                  </Button>
                </div>
              </div>
              <ScrollArea className="h-48 p-4 bg-white">
                {renderFormattedText(anamnese)}
              </ScrollArea>
              
              <div className="bg-gray-50 border-y p-4 mt-2">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-bold text-primary">EXAME FÍSICO</h3>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => copySection(exame, "Exame Físico")}
                    className="flex gap-1 items-center"
                  >
                    <Copy className="h-4 w-4" /> Copiar
                  </Button>
                </div>
              </div>
              <ScrollArea className="h-48 p-4 bg-white">
                {renderFormattedText(exame)}
              </ScrollArea>
            </div>
          </TabsContent>

          <TabsContent value="anamnese">
            <div className="border rounded-md overflow-hidden">
              <div className="bg-gray-50 border-b p-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-bold text-primary">ANAMNESE</h3>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => copySection(anamnese, "Anamnese")}
                    className="flex gap-1 items-center"
                  >
                    <Copy className="h-4 w-4" /> Copiar
                  </Button>
                </div>
              </div>
              <ScrollArea className="h-96 p-4 bg-white">
                {renderFormattedText(anamnese)}
              </ScrollArea>
            </div>
          </TabsContent>

          <TabsContent value="exame">
            <div className="border rounded-md overflow-hidden">
              <div className="bg-gray-50 border-b p-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-bold text-primary">EXAME FÍSICO</h3>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => copySection(exame, "Exame Físico")}
                    className="flex gap-1 items-center"
                  >
                    <Copy className="h-4 w-4" /> Copiar
                  </Button>
                </div>
              </div>
              <ScrollArea className="h-96 p-4 bg-white">
                {renderFormattedText(exame)}
              </ScrollArea>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
