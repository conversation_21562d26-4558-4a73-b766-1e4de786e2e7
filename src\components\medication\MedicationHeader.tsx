
import { cn } from "@/lib/utils";
import { getDefaultCategoryConfig } from "@/config/medicationCategories";
import { MedicationMetaTags } from "./MedicationMetaTags";
import { MedicationInstructionsDialog } from "./MedicationInstructionsDialog";
import { ShoppingBag, FileText } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface MedicationHeaderProps {
  name: string;
  description?: string | null;
  brands?: string | null;
  category?: string | null;
  slug: string;
  id?: string;
}

export const MedicationHeader = ({
  name,
  description,
  brands,
  category,
  slug,
  id
}: MedicationHeaderProps) => {
  const categoryConfig = category ? getDefaultCategoryConfig(category) : null;


  return (
    <>
      <MedicationMetaTags
        name={name}
        description={description}
        brands={brands}
        category={category}
        slug={slug}
      />

      <div className="bg-white/90 dark:bg-slate-800/90 shadow-md rounded-2xl border border-primary/10 dark:border-primary/20 overflow-hidden">
        <div className="relative overflow-hidden">
          {/* Header gradient bar */}
          <div className="h-2 bg-gradient-to-r from-primary via-primary/80 to-primary/60 dark:from-blue-500 dark:via-blue-400 dark:to-blue-300" />

          <div className="p-3 sm:p-5">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-3 sm:gap-4">
              <div className="space-y-2 sm:space-y-3">
                <h1 className="title-gradient text-2xl sm:text-3xl md:text-4xl font-bold animate-fade-in leading-tight">
                  {name}
                </h1>

                {category && (
                  <div className="flex">
                    <span className="px-2.5 sm:px-3 py-1 sm:py-1.5 rounded-lg text-xs sm:text-sm bg-primary/10 dark:bg-primary/20 text-primary dark:text-blue-300 font-medium w-fit">
                      {category}
                    </span>
                  </div>
                )}
              </div>

              <div className="flex flex-wrap gap-1.5 sm:gap-2 md:gap-3">
                {brands && (
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="gap-1 sm:gap-2 border-primary/20 bg-white dark:bg-slate-700 dark:border-primary/30 shadow-sm hover:bg-primary/5 dark:hover:bg-primary/10 dark:text-white text-xs sm:text-sm px-2 sm:px-3 py-1.5 sm:py-2 h-auto flex-shrink-0"
                      >
                        <ShoppingBag className="h-3 w-3 sm:h-4 sm:w-4 text-primary dark:text-blue-400" />
                        <span className="whitespace-nowrap">Nomes Comerciais</span>
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md max-h-[80vh] dark:bg-slate-800">
                      <DialogHeader>
                        <DialogTitle className="text-lg sm:text-xl font-bold text-primary dark:text-blue-400">
                          Nomes Comerciais
                        </DialogTitle>
                      </DialogHeader>
                      <div className="space-y-3">
                        <p className="text-sm text-muted-foreground dark:text-gray-300">
                          Este medicamento pode ser encontrado com os seguintes nomes comerciais:
                        </p>
                        <div className="max-h-[50vh] overflow-y-auto p-3 bg-primary/5 dark:bg-primary/10 rounded-lg border">
                          <ul className="grid grid-cols-1 sm:grid-cols-2 gap-1 text-sm">
                            {brands.split(',').map((brand, index) => (
                              <li key={index} className="text-gray-700 dark:text-gray-200 py-1 px-2 rounded hover:bg-primary/10 dark:hover:bg-primary/20 transition-colors">
                                {brand.trim()}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                )}
                {id && (
                  <MedicationInstructionsDialog
                    medicationId={id}
                    medicationName={name}
                    slug={slug}
                  />
                )}
              </div>
            </div>

            {description && (
              <div className="mt-3 sm:mt-4 text-muted-foreground dark:text-gray-300 animate-fade-in delay-100 bg-gray-50 dark:bg-slate-700/50 p-3 sm:p-4 rounded-lg border border-gray-100 dark:border-gray-700">
                <p className="leading-relaxed text-sm sm:text-base">{description}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};
