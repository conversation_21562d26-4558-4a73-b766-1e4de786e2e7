import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";

export const Solution = () => {
  const features = [
    {
      icon: Calendar,
      title: "Planner Automático",
      description: "Um cronograma feito para você, ajustado ao seu ritmo",
    },
    {
      icon: Brain,
      title: "Revisões Inteligentes",
      description: "Nunca mais esqueça o que estudou",
    },
    {
      icon: Bar<PERSON><PERSON>,
      title: "Estatísticas de Progresso",
      description: "Veja onde está errando e melhore",
    },
    {
      icon: Rocket,
      title: "Conteúdo Atualizado",
      description: "Tudo o que você precisa em um só lugar",
    },
  ];

  return (
    <div className="py-20 px-4 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-5" />
      <div className="max-w-6xl mx-auto relative z-10">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-4 bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600 animate-fadeIn">
          A Plataforma que Organiza Seus Estudos Para Você!
        </h2>
        <p className="text-xl text-gray-600 text-center mb-12 max-w-3xl mx-auto animate-fadeIn">
          Nossa plataforma faz todo o planejamento automaticamente para que você só precise focar no que realmente importa: aprender!
        </p>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div 
              key={index}
              className="group bg-white/80 backdrop-blur-sm p-8 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 text-center transform hover:-translate-y-2 animate-fadeIn"
              style={{
                animationDelay: `${index * 150}ms`,
              }}
            >
              <div className="w-16 h-16 mx-auto mb-6 p-3 rounded-full bg-primary/10 group-hover:bg-primary/20 transition-colors">
                <feature.icon className="w-full h-full text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-800">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};