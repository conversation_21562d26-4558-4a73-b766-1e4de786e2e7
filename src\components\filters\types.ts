import { Json } from '@/integrations/supabase/types/json';

export interface FilterOption {
  id: string;
  name: string;
  type: "specialty" | "theme" | "focus" | "location" | "year";
  parentId?: string;
}

export interface SelectedFilters {
  specialties: string[];
  themes: string[];
  focuses: string[];
  locations: string[];
  years: string[];
}

export type FilterType = FilterOption['type'];
export type SelectedFilterKey = keyof SelectedFilters;

export const filterTypeToKey = (type: FilterType): SelectedFilterKey => {
  const mapping: Record<FilterType, SelectedFilterKey> = {
    specialty: 'specialties',
    theme: 'themes',
    focus: 'focuses',
    location: 'locations',
    year: 'years'
  };
  return mapping[type];
};

export const filterKeyToType = (key: SelectedFilterKey): FilterType => {
  const mapping: Record<SelectedFilterKey, FilterType> = {
    specialties: 'specialty',
    themes: 'theme',
    focuses: 'focus',
    locations: 'location',
    years: 'year'
  };
  return mapping[key];
};

// Helper to convert SelectedFilters to Json type
export const selectedFiltersToJson = (filters: SelectedFilters): Json => {
  return filters as unknown as Json;
};