import { ScrollArea } from "../ui/scroll-area";
import { FilterButton } from "./FilterButton";

interface FilterItem {
  id: string;
  name: string;
}

interface FilterSectionProps {
  items: FilterItem[];
  selectedIds: string[];
  onToggle: (id: string) => void;
}

export const FilterSection = ({ items, selectedIds, onToggle }: FilterSectionProps) => {
  return (
    <ScrollArea className="h-[400px] mt-4">
      <div className="space-y-2">
        {items.map(item => (
          <FilterButton
            key={item.id}
            id={item.id}
            name={item.name}
            isSelected={selectedIds.includes(item.id)}
            onToggle={onToggle}
          />
        ))}
      </div>
    </ScrollArea>
  );
};