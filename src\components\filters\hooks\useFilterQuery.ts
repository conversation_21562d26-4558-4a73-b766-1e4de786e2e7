import { supabase } from "@/integrations/supabase/client";
import type { SelectedFilters } from "@/types/question";

export const useFilterQuery = () => {
  // Função para buscar questões pediátricas usando RPC otimizada
  const fetchPediatricQuestions = async (
    filters: SelectedFilters,
    pageNumber: number = 1,
    itemsPerPage: number = 50,
    userId?: string
  ) => {
    const rpcFunction = userId
      ? 'get_filtered_pediatric_questions_excluding_answered'
      : 'get_filtered_pediatric_questions';

    const params = {
      specialty_ids: filters.specialties || [],
      theme_ids: filters.themes || [],
      focus_ids: filters.focuses || [],
      location_ids: filters.locations || [],
      years: filters.years?.map(Number) || [],
      question_types: filters.questionTypes || [],
      question_formats: filters.questionFormats || [],
      page_number: pageNumber,
      items_per_page: itemsPerPage,
      ...(userId && { user_id: userId })
    };

    return await supabase.rpc(rpcFunction, params);
  };

  // Função para contar questões pediátricas
  const countPediatricQuestions = async (
    filters: SelectedFilters,
    userId?: string
  ) => {
    const rpcFunction = userId
      ? 'get_filtered_pediatric_question_count_excluding_answered'
      : 'get_filtered_pediatric_question_count';

    const params = {
      specialty_ids: filters.specialties || [],
      theme_ids: filters.themes || [],
      focus_ids: filters.focuses || [],
      location_ids: filters.locations || [],
      years: filters.years?.map(Number) || [],
      question_types: filters.questionTypes || [],
      question_formats: filters.questionFormats || [],
      ...(userId && { user_id: userId })
    };

    return await supabase.rpc(rpcFunction, params);
  };

  // Manter função legacy para compatibilidade (se necessário)
  const buildQuery = (filters: SelectedFilters, domain?: string) => {
    let query = supabase
      .from('questions')
      .select('*')
      .eq('specialty_name', 'pediatria') // Filtro específico para pediatria
      .limit(1000);

    const categoryConditions = [];

    if (filters.specialties?.length > 0) {
      const specialtyFilter = `specialty_id.in.(${filters.specialties.join(',')})`;
      categoryConditions.push(specialtyFilter);
    }

    if (filters.themes?.length > 0) {
      const themeFilter = `theme_id.in.(${filters.themes.join(',')})`;
      categoryConditions.push(themeFilter);
    }

    if (filters.focuses?.length > 0) {
      const focusFilter = `focus_id.in.(${filters.focuses.join(',')})`;
      categoryConditions.push(focusFilter);
    }

    if (categoryConditions.length > 0) {
      const orCondition = categoryConditions.join(',');
      query = query.or(orCondition);
    }

    if (filters.locations?.length > 0) {
      query = query.in('exam_location', filters.locations);
    }

    if (filters.years?.length > 0) {
      query = query.in('exam_year', filters.years.map(Number));
    }

    if (filters.question_types?.length > 0) {
      query = query.in('assessment_type', filters.question_types);
    }

    if (filters.question_formats?.length > 0) {
      query = query.in('question_format', filters.question_formats);
    }

    return query;
  };

  return {
    buildQuery,
    fetchPediatricQuestions,
    countPediatricQuestions
  };
};
