
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Plus, Search, Pencil } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PrescriptionCategoryDialog } from "@/components/admin/PrescriptionCategoryDialog";
import { supabase } from "@/integrations/supabase/client";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { getThemeClasses } from "@/components/ui/theme-utils";

export default function PrescriptionCategories() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<any>(null);
  const [showDialog, setShowDialog] = useState(false);
  const [session, setSession] = useState<any>(null);
  const navigate = useNavigate();

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (!session) {
        navigate("/");
        return;
      }
      setSession(session);
    });
  }, [navigate]);

  const { data: categories } = useQuery({
    queryKey: ["prescription-categories"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_prescription_categories")
        .select("*")
        .eq("user_id", session?.user?.id)
        .order("name");
      
      if (error) throw error;
      return data;
    },
    enabled: !!session?.user?.id,
  });

  const filteredCategories = categories?.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className={getThemeClasses.pageBackground("container mx-auto py-8")}>
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-2xl font-bold dark:text-gray-100">Categorias de Prescrição</h1>
        
        <Button onClick={() => {
          setSelectedCategory(null);
          setShowDialog(true);
        }}>
          <Plus className="mr-2 h-4 w-4" />
          Nova Categoria
        </Button>
      </div>

      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={18} />
        <Input
          type="search"
          placeholder="Pesquisar categorias..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 dark:bg-slate-800 dark:border-slate-700 dark:text-gray-100"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCategories?.map((category) => (
          <div
            key={category.id}
            className={getThemeClasses.card("p-6 space-y-2")}
          >
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-semibold dark:text-gray-100">{category.name}</h3>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setSelectedCategory(category);
                  setShowDialog(true);
                }}
                className="dark:text-gray-300 dark:hover:text-white"
              >
                <Pencil className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}
      </div>

      {session && (
        <PrescriptionCategoryDialog
          category={selectedCategory}
          isOpen={showDialog}
          onClose={() => {
            setShowDialog(false);
            setSelectedCategory(null);
          }}
          session={session}
        />
      )}
    </div>
  );
}
