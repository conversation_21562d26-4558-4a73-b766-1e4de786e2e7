
import React from 'react';
import { motion } from "framer-motion";

export const TypingIndicator: React.FC = () => {
  return (
    <div className="flex items-center gap-3 p-4 text-sm text-gray-600 dark:text-gray-400 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border border-gray-200/50 dark:border-slate-700/50 shadow-md rounded-xl max-w-[280px] mx-2 my-2">
      <div className="relative">
        <div className="w-8 h-8 rounded-full overflow-hidden border-2 border-blue-500 shadow-lg">
          <img
            src="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/favicon.jpg"
            alt="Will"
            className="w-full h-full object-cover"
          />
        </div>
        <div className="absolute -top-1 -right-1 bg-green-500 text-white text-[8px] font-bold px-1 rounded-full shadow-sm">
          2.0
        </div>
      </div>
      <div className="flex flex-col">
        <span className="font-medium text-blue-600 dark:text-blue-400">Dr. Will</span>
        <div className="flex items-center gap-1 mt-1">
          <span className="text-gray-500 dark:text-gray-400">está digitando</span>
          <div className="flex space-x-1 ml-1">
            {[0, 1, 2].map((dot) => (
              <motion.div
                key={dot}
                className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full"
                animate={{
                  scale: [1, 1.3, 1],
                  opacity: [0.4, 1, 0.4]
                }}
                transition={{
                  duration: 1.2,
                  repeat: Infinity,
                  repeatType: "loop",
                  delay: dot * 0.2
                }}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
