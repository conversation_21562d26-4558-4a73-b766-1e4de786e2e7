import { useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { slugify } from "@/utils/slugify";

interface MedicationFormProps {
  categories: Array<{ id: string; name: string }>;
  onSuccess?: () => void;
}

export function MedicationForm({ categories, onSuccess }: MedicationFormProps) {
  const [name, setName] = useState("");
  const [categoryId, setCategoryId] = useState("");
  const [description, setDescription] = useState("");
  const [brands, setBrands] = useState("");
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const { error: medicationError } = await supabase
        .from("pedbook_medications")
        .insert({
          name,
          category_id: categoryId,
          description,
          brands,
          slug: slugify(name),
        });

      if (medicationError) throw medicationError;

      toast({
        title: "Medicamento criado com sucesso!",
        description: `O medicamento ${name} foi adicionado.`,
      });

      setName("");
      setCategoryId("");
      setDescription("");
      setBrands("");
      queryClient.invalidateQueries({ queryKey: ["medications"] });
      onSuccess?.();
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Erro ao criar medicamento",
        description: "Ocorreu um erro ao criar o medicamento. Tente novamente.",
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="name">Nome</Label>
        <Input
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          required
        />
      </div>
      <div>
        <Label htmlFor="category">Categoria</Label>
        <Select
          value={categoryId}
          onValueChange={setCategoryId}
          required
        >
          <SelectTrigger>
            <SelectValue placeholder="Selecione uma categoria" />
          </SelectTrigger>
          <SelectContent>
            {categories?.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div>
        <Label htmlFor="description">Descrição</Label>
        <Textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
        />
      </div>
      <div>
        <Label htmlFor="brands">Marcas Comerciais</Label>
        <Input
          id="brands"
          value={brands}
          onChange={(e) => setBrands(e.target.value)}
        />
      </div>
      <Button type="submit" className="w-full">
        Criar Medicamento
      </Button>
    </form>
  );
}