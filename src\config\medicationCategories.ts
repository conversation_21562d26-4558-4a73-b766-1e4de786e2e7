
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  Pill,
  Thermometer,
  Bug,
  Heart,
  Shield,
  Wind,
  Coffee,
  Syringe,
  Ear,
  Bandage,
  Brain,
  LucideIcon,
  AlertCircle,
  Tablets,
  Apple
} from "lucide-react";

interface CategoryConfig {
  icon: LucideIcon;
  gradient: string;
  color: string;
}

// Configurações por ID
export const ID_CONFIGS: Record<string, CategoryConfig> = {
  // Analgésicos / Antitérmicos
  "a41889e8-b942-4ae6-b328-38f27a5b68b2": {
    icon: Thermometer,
    gradient: "from-red-100 to-red-50",
    color: "bg-red-100"
  },
  // Antiácidos
  "695e11e3-f436-4be3-9fb1-8fff3f7c1957": {
    icon: Pill,
    gradient: "from-orange-100 to-orange-50",
    color: "bg-orange-100"
  },
  // Antidiarreico
  "bae61da7-0bb8-4d3b-a281-f5e063135ea8": {
    icon: Tablets,
    gradient: "from-amber-100 to-amber-50",
    color: "bg-amber-100"
  },
  // Antieméticos
  "35e151d7-e65e-4724-9ede-30367feb6c83": {
    icon: Pill,
    gradient: "from-emerald-100 to-emerald-50",
    color: "bg-emerald-100"
  },
  // Anti-histamínicos
  "3bbc2536-3cb1-4e7c-84a8-8c23bd8bb15d": {
    icon: Bug,
    gradient: "from-purple-100 to-purple-50",
    color: "bg-purple-100"
  },
  // Anti inflamatórios
  "8decca82-8540-4c0b-a49a-b979ce7831cd": {
    icon: Heart,
    gradient: "from-rose-100 to-rose-50",
    color: "bg-rose-100"
  },
  // Antimicrobianos
  "0d2ff8a9-80ba-4be1-866d-2f0f74073e65": {
    icon: Shield,
    gradient: "from-blue-100 to-blue-50",
    color: "bg-blue-100"
  },
  // Anti-Parasitários
  "eca9210a-3be0-4590-afbd-991606bc118b": {
    icon: Bug,
    gradient: "from-teal-100 to-teal-50",
    color: "bg-teal-100"
  },
  // Antitussígenos
  "fbd60aff-b67e-4a94-9a39-1a05018c41df": {
    icon: Coffee,
    gradient: "from-pink-100 to-pink-50",
    color: "bg-pink-100"
  },
  // Broncodilatadores
  "21e87b46-255e-403d-be01-bd052c9cf35a": {
    icon: Wind,
    gradient: "from-cyan-100 to-cyan-50",
    color: "bg-cyan-100"
  },
  // Corticoides
  "8b6028b4-3b03-46bc-93a9-aa63984020b1": {
    icon: Syringe,
    gradient: "from-red-100 to-red-50",
    color: "bg-red-100"
  },
  // Drogas de Atendimento Emergencial
  "c8462677-99e1-44a0-87a8-64e75f855f06": {
    icon: AlertCircle,
    gradient: "from-violet-100 to-violet-50",
    color: "bg-violet-100"
  },
  // Laxantes
  "db6e60ae-1716-4db1-9d4b-c03fc03c0f62": {
    icon: Pill,
    gradient: "from-yellow-100 to-yellow-50",
    color: "bg-yellow-100"
  },
  // Otológicos
  "7c28c48d-09a9-40b9-b644-9176de941992": {
    icon: Ear,
    gradient: "from-indigo-100 to-indigo-50",
    color: "bg-indigo-100"
  },
  // Pomadas e Cremes
  "3626fb0e-605d-40e8-9dfa-6e9d9b520781": {
    icon: Bandage,
    gradient: "from-sky-100 to-sky-50",
    color: "bg-sky-100"
  },
  // Psicotrópicos
  "8c9dc9ea-62e3-40a9-ac57-1cc16fb35dc1": {
    icon: Brain,
    gradient: "from-fuchsia-100 to-fuchsia-50",
    color: "bg-fuchsia-100"
  },
  // Vitaminas e Suplementos
  "e9e704a4-4a90-4fcb-ae6f-578e2782bf05": {
    icon: Apple,
    gradient: "from-lime-100 to-lime-50",
    color: "bg-lime-100"
  }
};

// Função que tenta encontrar uma configuração por ID
export const getCategoryConfig = (category: any): CategoryConfig => {
  if (!category) {
    return {
      icon: Stethoscope,
      gradient: "from-gray-100 to-gray-50",
      color: "bg-gray-100"
    };
  }

  // Tenta encontrar por ID
  if (category.id && ID_CONFIGS[category.id]) {
    return ID_CONFIGS[category.id];
  }

  // Configuração padrão
  return {
    icon: Stethoscope,
    gradient: "from-gray-100 to-gray-50",
    color: "bg-gray-100"
  };
};

// Mantemos a função original para compatibilidade
export const getDefaultCategoryConfig = (categoryName: string): CategoryConfig => {
  return {
    icon: Stethoscope,
    gradient: "from-gray-100 to-gray-50",
    color: "bg-gray-100"
  };
};
