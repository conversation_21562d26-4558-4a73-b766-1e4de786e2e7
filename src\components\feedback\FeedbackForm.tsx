import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";

interface FeedbackFormProps {
  onSuccess?: () => void;
}

interface FeedbackType {
  id: string;
  name: string;
  description: string | null;
}

export function FeedbackForm({ onSuccess }: FeedbackFormProps) {
  const [types, setTypes] = useState<FeedbackType[]>([]);
  const [selectedTypeId, setSelectedTypeId] = useState<string>("");
  const [message, setMessage] = useState("");
  const [whatsapp, setWhatsapp] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    const fetchFeedbackTypes = async () => {
      try {
        const { data, error } = await supabase
          .from("pedbook_feedback_types")
          .select("*");

        if (error) throw error;
        setTypes(data);
      } catch (error: any) {
        toast({
          title: "Erro ao carregar tipos de feedback",
          description: error.message,
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchFeedbackTypes();
  }, [toast]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setIsSubmitting(true);
    try {
      const { error } = await supabase
        .from("pedbook_feedbacks")
        .insert([{
          user_id: user.id,
          type_id: selectedTypeId,
          message,
          whatsapp,
          title: types.find(t => t.id === selectedTypeId)?.name || 'Feedback'
        }]);

      if (error) throw error;

      toast({
        title: "Feedback enviado",
        description: "Obrigado por compartilhar sua opinião!",
      });

      setSelectedTypeId("");
      setMessage("");
      setWhatsapp("");
      onSuccess?.();
    } catch (error: any) {
      toast({
        title: "Erro ao enviar feedback",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return <div className="p-4 text-center bg-white dark:bg-slate-800 rounded-lg shadow animate-pulse">Carregando tipos de feedback...</div>;
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6 p-6 bg-white dark:bg-slate-800/90 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
      <div className="space-y-1.5">
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Tipo de Feedback
        </label>
        <Select value={selectedTypeId} onValueChange={setSelectedTypeId} required>
          <SelectTrigger className="bg-white dark:bg-slate-700 border-gray-200 dark:border-slate-600 rounded-lg">
            <SelectValue placeholder="Selecione o tipo de feedback" />
          </SelectTrigger>
          <SelectContent className="bg-white dark:bg-slate-700 border-gray-200 dark:border-slate-600">
            {types.map((type) => (
              <SelectItem key={type.id} value={type.id}>
                {type.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-1.5">
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          WhatsApp (opcional)
        </label>
        <input
          type="text"
          value={whatsapp}
          onChange={(e) => setWhatsapp(e.target.value)}
          placeholder="Digite seu WhatsApp..."
          className="w-full px-4 py-2 border border-gray-200 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 dark:text-gray-200 focus:ring-2 focus:ring-primary/20 focus:border-primary/40 transition duration-200 outline-none"
        />
      </div>

      <div className="space-y-1.5">
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Mensagem
        </label>
        <Textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Digite sua mensagem..."
          required
          className="min-h-[150px] bg-white dark:bg-slate-700 border-gray-200 dark:border-slate-600 rounded-lg resize-none dark:text-gray-200 focus:ring-2 focus:ring-primary/20 focus:border-primary/40"
        />
      </div>

      <Button 
        type="submit" 
        disabled={isSubmitting} 
        className="w-full bg-primary hover:bg-primary/90 text-white rounded-lg py-2.5 px-4 font-medium transition-all duration-200 shadow-sm hover:shadow"
      >
        {isSubmitting ? (
          <div className="flex items-center justify-center gap-2">
            <span className="h-4 w-4 block rounded-full border-2 border-white border-t-transparent animate-spin"></span>
            <span>Enviando...</span>
          </div>
        ) : (
          "Enviar Feedback"
        )}
      </Button>
    </form>
  );
}
