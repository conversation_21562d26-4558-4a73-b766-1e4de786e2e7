import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { ScorpionClinicalSigns } from "./ScorpionClinicalSigns";
import { ArrowLeft } from "lucide-react";

interface ScorpionQuestionProps {
  question: string;
  onAnswer: (answer: boolean) => void;
  selectedAnswer: boolean | null;
  onReset: () => void;
}

export const ScorpionQuestion = ({
  question,
  onAnswer,
  selectedAnswer,
  onReset,
}: ScorpionQuestionProps) => {
  const isInitialQuestion = question === "O paciente apresenta sinais clínicos de envenenamento?";
  const isObservationQuestion = question === "Após observação, o paciente desenvolveu sinais de envenenamento?";

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-8"
    >
      <div className="p-6 rounded-xl bg-yellow-50 border border-yellow-200 glass-card relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent pointer-events-none" />
        
        <div className="flex justify-end mb-4 relative z-10">
          <Button
            onClick={onReset}
            variant="ghost"
            size="sm"
            className="text-yellow-600 hover:text-yellow-700 hover:bg-yellow-100 transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Recomeçar
          </Button>
        </div>

        <h2 className="text-xl font-semibold text-gray-800 mb-6 text-center relative z-10">
          {question}
        </h2>
        
        <div className="flex flex-col sm:flex-row gap-4 relative z-10 mb-8">
          <Button
            onClick={() => onAnswer(true)}
            variant={selectedAnswer === true ? "default" : "outline"}
            className={`flex-1 transition-all duration-300 ${
              selectedAnswer === true
                ? "bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600"
                : "hover:bg-yellow-50 border-yellow-200"
            }`}
          >
            Sim
          </Button>
          <Button
            onClick={() => onAnswer(false)}
            variant={selectedAnswer === false ? "default" : "outline"}
            className={`flex-1 transition-all duration-300 ${
              selectedAnswer === false
                ? "bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600"
                : "hover:bg-yellow-50 border-yellow-200"
            }`}
          >
            Não
          </Button>
        </div>

        {isInitialQuestion && <ScorpionClinicalSigns />}

        {!isInitialQuestion && isObservationQuestion && (
          <div className="bg-white/50 rounded-lg p-4 backdrop-blur-sm border border-yellow-100">
            <p className="text-yellow-800 font-medium mb-4">
              Mantenha o paciente em observação por no mínimo 4 horas.
            </p>
            <p className="text-gray-700">
              Durante este período, monitore atentamente o desenvolvimento de possíveis sinais de envenenamento.
            </p>
          </div>
        )}
      </div>
    </motion.div>
  );
};