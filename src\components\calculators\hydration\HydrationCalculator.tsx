
import { useState } from "react";
import { Scale } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { calculateHydration } from "./utils/hydrationCalculations";
import { InfusionRatesCard } from "./InfusionRatesCard";
import { ReferencesCard } from "./ReferencesCard";
import { getThemeClasses } from "@/components/ui/theme-utils";

export const HydrationCalculator = () => {
  const [weight, setWeight] = useState<string>("");
  const [results, setResults] = useState<ReturnType<typeof calculateHydration> | null>(null);
  const { toast } = useToast();

  const handleWeightChange = (value: string) => {
    setWeight(value);
  };

  const handleCalculate = () => {
    const weightNum = parseFloat(weight);
    if (!weightNum || weightNum < 3.5) {
      toast({
        title: "Erro",
        description: "Por favor, insira um peso válido (mínimo 3,5 kg).",
        variant: "destructive"
      });
      return;
    }
    
    setResults(calculateHydration(weightNum));
  };

  return (
    <div className="space-y-6">
      <Card className={getThemeClasses.card("p-6")}>
        <div className="space-y-4">
          <label className="flex items-center gap-2 text-lg font-medium text-gray-700 dark:text-gray-300">
            <Scale className="h-5 w-5 text-primary dark:text-blue-400" />
            Peso do Paciente (kg)
          </label>
          <div className="flex gap-4">
            <Input
              type="number"
              value={weight}
              onChange={(e) => handleWeightChange(e.target.value)}
              placeholder="Digite o peso em kg..."
              min="3.5"
              step="0.1"
              className={getThemeClasses.input("text-lg")}
            />
            <Button 
              onClick={handleCalculate}
              className="bg-primary hover:bg-primary/90 dark:bg-blue-600 dark:hover:bg-blue-700"
            >
              Calcular
            </Button>
          </div>
        </div>
      </Card>

      {results && (
        <>
          <div className="grid gap-4 md:grid-cols-3">
            <Card className={getThemeClasses.gradientCard("blue", "p-4")}>
              <h3 className="font-semibold text-gray-700 dark:text-gray-200 mb-2">Volume Diário</h3>
              <p className="text-2xl font-bold text-primary dark:text-blue-400">
                {results.dailyVolume.toFixed(2)} mL
              </p>
            </Card>

            <Card className={getThemeClasses.gradientCard("purple", "p-4")}>
              <h3 className="font-semibold text-gray-700 dark:text-gray-200 mb-2">Taxa por Hora</h3>
              <p className="text-2xl font-bold text-primary dark:text-blue-400">
                {results.hourlyRate.toFixed(2)} mL/h
              </p>
            </Card>

            <Card className={getThemeClasses.gradientCard("green", "p-4")}>
              <h3 className="font-semibold text-gray-700 dark:text-gray-200 mb-2">Taxa por Minuto</h3>
              <p className="text-2xl font-bold text-primary dark:text-blue-400">
                {results.minuteRate.toFixed(2)} mL/min
              </p>
            </Card>
          </div>

          <InfusionRatesCard aliquots={results.aliquots} />

          <Card className={getThemeClasses.card("p-6")}>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Composição da Solução (para cada 500 mL)
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-100 dark:border-blue-800/50">
                <h4 className="font-medium text-gray-700 dark:text-gray-300">Soro Glicosado 5%</h4>
                <p className="text-xl font-semibold text-primary dark:text-blue-400">
                  {results.components.glucose} mL
                </p>
              </div>
              <div className="p-4 bg-green-50 dark:bg-green-900/30 rounded-lg border border-green-100 dark:border-green-800/50">
                <h4 className="font-medium text-gray-700 dark:text-gray-300">NaCl 20%</h4>
                <p className="text-xl font-semibold text-primary dark:text-blue-400">
                  {results.components.sodium} mL
                </p>
              </div>
              <div className="p-4 bg-purple-50 dark:bg-purple-900/30 rounded-lg border border-purple-100 dark:border-purple-800/50">
                <h4 className="font-medium text-gray-700 dark:text-gray-300">KCl 19,1%</h4>
                <p className="text-xl font-semibold text-primary dark:text-blue-400">
                  {results.components.potassium} mL
                </p>
              </div>
            </div>
          </Card>
        </>
      )}

      <ReferencesCard />
    </div>
  );
};
