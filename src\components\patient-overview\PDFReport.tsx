import React, { useRef, useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Printer, Download, FileText, ChevronDown, ChevronUp, Eye, EyeOff } from "lucide-react";
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { calculateSupplementation } from "@/utils/supplementationCalculator";
import { calculateExactPercentileSync } from "@/utils/exactPercentileCalculation";
import { calculateBirthWeightClassification } from "@/utils/pigAigGigClassification";
import { supabase } from "@/integrations/supabase/client";
import { SupplementationInput } from "@/types/supplementation";
import { formatAge, formatAgeMedical } from "@/utils/formatAge";
import { getPercentileColors, formatPercentileInterpretation, getPercentileIcon } from "@/utils/percentileColors";
import { formatGestationalAge } from "@/utils/formatGestationalAge";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { VaccineDose } from "@/components/admin/vaccine/types";

import '../../styles/pdf-report.css';

interface PatientData {
  name?: string;
  age: number;
  gender: 'male' | 'female';
  weight?: number;
  height?: number;
  headCircumference?: number;
  birthWeight?: number;
  gestationalAge?: number;
  maturity?: 'Term' | 'Pre-term';
  exclusiveBreastfeeding?: boolean;
  riskFactors?: string[];
}

interface Milestone {
  id: string;
  age_type: string;
  age_years: number | null;
  age_months: number;
  social_emotional: string | null;
  language_communication: string | null;
  cognition: string | null;
  motor_physical: string | null;
  image_url: string | null;
}

interface GroupedDoses {
  [key: string]: VaccineDose[];
}

interface PDFReportProps {
  patientData: PatientData;
  useCorrectedAge?: boolean;
}

export function PDFReport({ patientData, useCorrectedAge = false }: PDFReportProps) {
  const reportRef = useRef<HTMLDivElement>(null);
  const [isReportVisible, setIsReportVisible] = useState(false);
  const [percentiles, setPercentiles] = useState<any>(null);

  // Calcular percentis quando os dados do paciente mudarem
  useEffect(() => {
    const calculatePercentiles = async () => {
      const result = await getPercentiles();
      setPercentiles(result);
    };

    if (patientData.weight && patientData.height && patientData.headCircumference) {
      calculatePercentiles();
    }
  }, [patientData.weight, patientData.height, patientData.headCircumference, patientData.age, patientData.gender]);

  // Query para obter dados de vacinas
  const { data: vaccineData } = useQuery({
    queryKey: ['vaccine-doses-pdf'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_vaccine_doses')
        .select(`
          id,
          dose_number,
          age_recommendation,
          type,
          dose_type,
          vaccine:pedbook_vaccines (
            id,
            name,
            description
          )
        `)
        .order('age_recommendation');

      if (error) throw error;
      return data as VaccineDose[];
    },
  });

  // Query para obter dados de DNPM
  const { data: dnpmData } = useQuery({
    queryKey: ["dnpm-milestones-pdf"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_dnpm_milestones")
        .select("*")
        .order("age_months");

      if (error) throw error;
      return data as Milestone[];
    },
  });

  // Calcular percentis usando EXATAMENTE o mesmo método do cabeçalho
  const getPercentiles = async () => {
    if (!patientData.weight || !patientData.height || !patientData.headCircumference) {
      return null;
    }

    try {
      // Buscar dados WHO do Supabase (mesmo método do cabeçalho)
      const { data: weightData } = await supabase
        .from('pedbook_growth_curve_metadata')
        .select('data')
        .eq('gender', patientData.gender)
        .eq('type', 'weight')
        .single();

      const { data: heightData } = await supabase
        .from('pedbook_growth_curve_metadata')
        .select('data')
        .eq('gender', patientData.gender)
        .eq('type', 'height')
        .single();

      const { data: headData } = await supabase
        .from('pedbook_growth_curve_metadata')
        .select('data')
        .eq('gender', patientData.gender)
        .eq('type', 'head-circumference')
        .single();

      // Função para interpolar e calcular percentil (mesmo método do cabeçalho)
      const calculatePercentileFromData = (value: number, data: any[], ageInMonths: number) => {
        if (!data || data.length === 0) return null;

        const sortedData = data.sort((a: any, b: any) => a.age_months - b.age_months);

        let lowerPoint = sortedData[0];
        let upperPoint = sortedData[sortedData.length - 1];

        for (let i = 0; i < sortedData.length - 1; i++) {
          if (ageInMonths >= sortedData[i].age_months && ageInMonths <= sortedData[i + 1].age_months) {
            lowerPoint = sortedData[i];
            upperPoint = sortedData[i + 1];
            break;
          }
        }

        const ratio = (ageInMonths - lowerPoint.age_months) / (upperPoint.age_months - lowerPoint.age_months);
        const p3 = lowerPoint.percentiles["3rd"] + (upperPoint.percentiles["3rd"] - lowerPoint.percentiles["3rd"]) * ratio;
        const p15 = lowerPoint.percentiles["15th"] + (upperPoint.percentiles["15th"] - lowerPoint.percentiles["15th"]) * ratio;
        const p50 = lowerPoint.percentiles["50th"] + (upperPoint.percentiles["50th"] - lowerPoint.percentiles["50th"]) * ratio;
        const p85 = lowerPoint.percentiles["85th"] + (upperPoint.percentiles["85th"] - lowerPoint.percentiles["85th"]) * ratio;
        const p97 = lowerPoint.percentiles["97th"] + (upperPoint.percentiles["97th"] - lowerPoint.percentiles["97th"]) * ratio;

        let percentile = 50;
        let interpretation = 'normal';

        if (value <= p3) {
          percentile = 3;
          interpretation = 'muito_baixo';
        } else if (value <= p15) {
          percentile = Math.round(3 + ((value - p3) / (p15 - p3)) * 12);
          interpretation = 'baixo';
        } else if (value <= p50) {
          percentile = Math.round(15 + ((value - p15) / (p50 - p15)) * 35);
          interpretation = 'normal';
        } else if (value <= p85) {
          percentile = Math.round(50 + ((value - p50) / (p85 - p50)) * 35);
          interpretation = 'normal';
        } else if (value <= p97) {
          percentile = Math.round(85 + ((value - p85) / (p97 - p85)) * 12);
          interpretation = 'alto';
        } else {
          percentile = 97;
          interpretation = 'muito_alto';
        }

        return {
          percentile,
          interpretation,
          zScore: 0,
          description: interpretation,
          color: 'normal'
        };
      };

      const results: any = {};

      // Calcular peso
      if (weightData?.data) {
        const result = calculatePercentileFromData(patientData.weight, weightData.data, patientData.age);
        if (result) results.weight = result;
      }

      // Calcular altura
      if (heightData?.data) {
        const result = calculatePercentileFromData(patientData.height, heightData.data, patientData.age);
        if (result) results.height = result;
      }

      // Calcular PC
      if (headData?.data) {
        const result = calculatePercentileFromData(patientData.headCircumference, headData.data, patientData.age);
        if (result) results.head = result;
      }

      return results;
    } catch (error) {
      console.error('Erro ao calcular percentis no PDF:', error);
      return null;
    }
  };

  // Calcular classificação de peso ao nascer
  const getBirthWeightClassification = () => {
    if (!patientData.birthWeight || !patientData.gestationalAge) {
      return null;
    }

    try {
      return calculateBirthWeightClassification(
        patientData.birthWeight,
        patientData.gestationalAge
      );
    } catch (error) {
      return null;
    }
  };

  // Calcular suplementação
  const getSupplementation = () => {
    const input: SupplementationInput = {
      ageInDays: Math.round(patientData.age * 30),
      currentWeight: patientData.weight ? patientData.weight * 1000 : 0,
      birthWeight: patientData.birthWeight || 0,
      maturity: patientData.maturity || 'Term',
      exclusiveBreastfeeding: patientData.exclusiveBreastfeeding || false,
      riskFactors: (patientData.riskFactors || []) as any[]
    };

    return calculateSupplementation(input);
  };

  // Processar dados de vacinas
  const getVaccineAnalysis = () => {
    if (!vaccineData) return null;

    const groupDosesByAge = (doses: VaccineDose[]): GroupedDoses => {
      return doses.reduce((groups: GroupedDoses, dose) => {
        const ageKey = dose.age_recommendation === "0" ? "Ao nascer" : `${dose.age_recommendation} meses`;
        if (!groups[ageKey]) {
          groups[ageKey] = [];
        }
        groups[ageKey].push(dose);
        return groups;
      }, {});
    };

    const groupedDoses = groupDosesByAge(vaccineData);
    const currentGroups: GroupedDoses = {};
    const upcomingGroups: GroupedDoses = {};

    Object.entries(groupedDoses).forEach(([ageKey, doseGroup]) => {
      const age = ageKey === "Ao nascer" ? 0 : parseInt(ageKey);
      if (age <= patientData.age) {
        currentGroups[ageKey] = doseGroup.filter(dose => dose.type === 'SUS'); // Apenas SUS para o laudo
      } else {
        // Próxima faixa etária
        const nextAge = Object.keys(groupedDoses)
          .map(key => key === "Ao nascer" ? 0 : parseInt(key))
          .filter(age => age > patientData.age)
          .sort((a, b) => a - b)[0];

        if (age === nextAge) {
          upcomingGroups[ageKey] = doseGroup.filter(dose => dose.type === 'SUS');
        }
      }
    });

    return { currentGroups, upcomingGroups };
  };

  // Processar dados de DNPM
  const getDNPMAnalysis = () => {
    if (!dnpmData) return null;

    const currentMilestone = dnpmData
      .filter(m => m.age_months <= patientData.age)
      .slice(-1)[0];

    const nextMilestone = dnpmData
      .find(m => m.age_months > patientData.age);

    return { currentMilestone, nextMilestone };
  };

  const percentiles = getPercentiles();
  const birthClassification = getBirthWeightClassification();
  const supplementation = getSupplementation();
  const vaccineAnalysis = getVaccineAnalysis();
  const dnpmAnalysis = getDNPMAnalysis();

  // Função para verificar orientações especiais sobre alimentação
  const getAlimentationGuidance = () => {
    let correctedAge = patientData.age;

    // Calcular idade corrigida para prematuros
    if (patientData.maturity === 'Pre-term' && patientData.gestationalAge && patientData.gestationalAge < 37) {
      const weeksPreterm = 40 - patientData.gestationalAge;
      const monthsPreterm = (weeksPreterm * 7) / 30;
      correctedAge = patientData.age - monthsPreterm;
    }

    const correctedAgeInMonths = Math.max(0, correctedAge);

    if (correctedAgeInMonths >= 5.5 && correctedAgeInMonths <= 7) {
      return {
        show: true,
        title: correctedAgeInMonths >= 6 ? "🍎 INTRODUÇÃO ALIMENTAR" : "🍎 PREPARAÇÃO PARA INTRODUÇÃO ALIMENTAR",
        message: correctedAgeInMonths >= 6
          ? "Iniciar alimentação complementar a partir desta idade. Manter AME até 6 meses é benéfico, mas agora deve ser acompanhado por introdução alimentar balanceada. Oferecer alimentos ricos em ferro para prevenir anemia."
          : `Criança próxima dos 6 meses (idade corrigida: ${correctedAgeInMonths.toFixed(1)} meses). Preparar para introdução alimentar em breve. Manter AME até completar 6 meses de idade corrigida.`
      };
    }

    return { show: false };
  };

  // Função para traduzir fatores de risco
  const translateRiskFactor = (factor: string): string => {
    const translations: Record<string, string> = {
      "prematurity": "Prematuridade",
      "low_birth_weight": "Baixo peso ao nascer",
      "poor_iron_diet": "Alimentação pobre em ferro",
      "exclusive_breastfeeding_gt_6m_without_supplement": "AME prolongado sem suplementação",
      "multiple_pregnancy": "Gestação múltipla",
      "maternal_anemia": "Anemia materna",
      "frequent_infections": "Infecções frequentes",
      "multiple_gestation": "Gestação múltipla",
      "early_cow_milk_exposure": "Leite de vaca precoce",
      "low_socioeconomic_status": "Baixo nível socioeconômico",
      "vegetarian_diet_without_supplement": "Dieta vegetariana sem suplemento",
      "gestational_diabetes": "Diabetes gestacional",
      "gestational_hypertension": "Hipertensão gestacional",
      "preeclampsia": "Pré-eclâmpsia",
      "maternal_infections": "Infecções maternas",
      "thyroid_disorders": "Distúrbios da tireoide",
      "autoimmune_diseases": "Doenças autoimunes",
      "substance_use": "Uso de substâncias"
    };
    return translations[factor] || factor;
  };



  // Função desabilitada temporariamente
  const generatePDF = async () => {
    alert('Funcionalidade temporariamente desabilitada para manutenção.');
  };

  return (
    <div className="space-y-4">
      {/* Cabeçalho da Seção */}
      <div className="print:hidden">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-4">
          <div className="flex-1">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              Laudo Pediátrico Completo
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Laudo completo com análise antropométrica, recomendações nutricionais, calendário vacinal e marcos de desenvolvimento
            </p>
          </div>

          {/* Botões de Ação */}
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
            <Button
              onClick={() => setIsReportVisible(!isReportVisible)}
              variant="outline"
              className="border-blue-600 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 shadow-md hover:shadow-lg transition-all duration-200"
              size="lg"
            >
              {isReportVisible ? (
                <>
                  <EyeOff className="h-4 w-4 mr-2" />
                  Ocultar Laudo
                </>
              ) : (
                <>
                  <Eye className="h-4 w-4 mr-2" />
                  Visualizar Laudo
                </>
              )}
            </Button>
            <Button
              onClick={() => alert('Funcionalidade temporariamente desabilitada')}
              disabled
              className="bg-gray-400 cursor-not-allowed text-white shadow-lg transition-all duration-200"
              size="lg"
            >
              <Printer className="h-4 w-4 mr-2" />
              Imprimir Laudo (Em manutenção)
            </Button>
            <Button
              onClick={() => alert('Funcionalidade temporariamente desabilitada')}
              disabled
              variant="outline"
              className="border-gray-400 text-gray-400 cursor-not-allowed shadow-md transition-all duration-200"
              size="lg"
            >
              <Download className="h-4 w-4 mr-2" />
              Baixar PDF (Em manutenção)
            </Button>
          </div>
        </div>
      </div>

      {/* Conteúdo do Laudo */}
      {isReportVisible && (
        <div
          ref={reportRef}
          className="pdf-report bg-white p-3 max-w-3xl mx-auto border border-gray-200 rounded-lg overflow-hidden"
        >
        {/* Cabeçalho COMPACTO */}
        <div className="text-center mb-6 border-b-2 border-blue-600 pb-2">
          <h1 className="text-xl font-bold text-blue-800 mb-1">
            LAUDO PEDIÁTRICO COMPLETO
          </h1>
          <p className="text-sm text-gray-600">
            PedBook - Análise Antropométrica, Nutricional, Vacinal e de Desenvolvimento
          </p>
          <p className="text-xs text-gray-500 mt-1">
            {new Date().toLocaleDateString('pt-BR')} | {new Date().toLocaleTimeString('pt-BR')}
          </p>
        </div>

        {/* Dados do Paciente COMPACTO */}
        <div className="mb-4">
          <h2 className="text-base font-bold text-blue-700 mb-2 border-b border-gray-300 pb-1">
            📋 DADOS DO PACIENTE
          </h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p><strong>Nome:</strong> {patientData.name || 'Não informado'}</p>
              <p><strong>Idade:</strong> {formatAgeMedical(patientData.age, useCorrectedAge)}</p>
              {useCorrectedAge && patientData.gestationalAge && patientData.gestationalAge < 37 && (
                <p className="text-sm text-gray-600 ml-4">
                  <em>Cronológica: {formatAgeMedical(patientData.age + ((40 - patientData.gestationalAge) * 7 / 30), false)}</em>
                </p>
              )}
              <p><strong>Gênero:</strong> {patientData.gender === 'male' ? 'Masculino' : 'Feminino'}</p>
              <p><strong>Tipo de Idade:</strong> {useCorrectedAge ? 'Idade Corrigida' : 'Idade Cronológica'}</p>
              {useCorrectedAge && patientData.gestationalAge && patientData.gestationalAge < 37 && (
                <p className="text-xs text-blue-600 mt-1">
                  ℹ️ Usando idade corrigida para análise antropométrica
                </p>
              )}
            </div>
            <div>
              <p><strong>Peso Atual:</strong> {patientData.weight ? `${patientData.weight} kg` : 'Não informado'}</p>
              <p><strong>Altura:</strong> {patientData.height ? `${patientData.height} cm` : 'Não informado'}</p>
              <p><strong>PC:</strong> {patientData.headCircumference ? `${patientData.headCircumference} cm` : 'Não informado'}</p>
              <p><strong>Maturidade:</strong> {patientData.maturity === 'Pre-term' ? 'Pré-termo' : 'A termo'}</p>
            </div>
          </div>
        </div>

        {/* Dados Perinatais */}
        <div className="mb-6">
          <h2 className="text-lg font-bold text-blue-700 mb-3 border-b border-gray-300 pb-1">
            🍼 DADOS PERINATAIS
          </h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p><strong>Peso ao Nascer:</strong> {patientData.birthWeight ? `${patientData.birthWeight}g` : 'Não informado'}</p>
              <p><strong>Idade Gestacional:</strong> {patientData.gestationalAge ? formatGestationalAge(patientData.gestationalAge) : 'Não informado'}</p>
            </div>
            <div>
              <p><strong>AME:</strong> {patientData.exclusiveBreastfeeding ? 'Sim' : 'Não'}</p>
              {birthClassification && (
                <p><strong>Classificação:</strong> {birthClassification.classification} (P{birthClassification.percentile})</p>
              )}
            </div>
          </div>
        </div>

        {/* Análise Antropométrica */}
        {percentiles && (
          <div className="mb-6">
            <h2 className="text-lg font-bold text-blue-700 mb-3 border-b border-gray-300 pb-1">
              📊 ANÁLISE ANTROPOMÉTRICA
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Peso */}
              {(() => {
                const colors = getPercentileColors(percentiles.weight.percentile);
                return (
                  <div className={`section-card ${colors.bgColor} ${colors.borderColor} border`}>
                    <h3 className={`font-semibold ${colors.textColor} mb-2 flex items-center gap-2`}>
                      {getPercentileIcon(colors.status)} Peso
                    </h3>
                    <div className="space-y-1 text-sm">
                      <p><strong>Valor:</strong> {patientData.weight} kg</p>
                      <p><strong>Percentil:</strong> P{percentiles.weight.percentile}</p>
                      <p className={`${colors.textColor} font-medium mt-2`}>
                        {formatPercentileInterpretation(percentiles.weight.interpretation)}
                      </p>
                    </div>
                  </div>
                );
              })()}

              {/* Altura */}
              {(() => {
                const colors = getPercentileColors(percentiles.height.percentile);
                return (
                  <div className={`section-card ${colors.bgColor} ${colors.borderColor} border`}>
                    <h3 className={`font-semibold ${colors.textColor} mb-2 flex items-center gap-2`}>
                      {getPercentileIcon(colors.status)} Altura
                    </h3>
                    <div className="space-y-1 text-sm">
                      <p><strong>Valor:</strong> {patientData.height} cm</p>
                      <p><strong>Percentil:</strong> P{percentiles.height.percentile}</p>
                      <p className={`${colors.textColor} font-medium mt-2`}>
                        {formatPercentileInterpretation(percentiles.height.interpretation)}
                      </p>
                    </div>
                  </div>
                );
              })()}

              {/* Perímetro Cefálico */}
              {(() => {
                const colors = getPercentileColors(percentiles.head.percentile);
                return (
                  <div className={`section-card ${colors.bgColor} ${colors.borderColor} border`}>
                    <h3 className={`font-semibold ${colors.textColor} mb-2 flex items-center gap-2`}>
                      {getPercentileIcon(colors.status)} Perímetro Cefálico
                    </h3>
                    <div className="space-y-1 text-sm">
                      <p><strong>Valor:</strong> {patientData.headCircumference} cm</p>
                      <p><strong>Percentil:</strong> P{percentiles.head.percentile}</p>
                      <p className={`${colors.textColor} font-medium mt-2`}>
                        {formatPercentileInterpretation(percentiles.head.interpretation)}
                      </p>
                    </div>
                  </div>
                );
              })()}
            </div>
          </div>
        )}

        {/* Fatores de Risco */}
        {patientData.riskFactors && patientData.riskFactors.length > 0 && (
          <div className="mb-6">
            <h2 className="text-lg font-bold text-blue-700 mb-3 border-b border-gray-300 pb-1">
              ⚠️ FATORES DE RISCO IDENTIFICADOS
            </h2>
            <div className="bg-yellow-50 p-4 rounded border border-yellow-200">
              <ul className="list-disc list-inside space-y-1">
                {patientData.riskFactors.map((factor, index) => (
                  <li key={index} className="text-yellow-800">
                    {translateRiskFactor(factor)}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {/* Recomendações de Suplementação */}
        <div className="mb-6">
          <h2 className="text-lg font-bold text-blue-700 mb-3 border-b border-gray-300 pb-1">
            💊 RECOMENDAÇÕES DE SUPLEMENTAÇÃO
          </h2>
          <div className="space-y-4">
            <div className="section-card bg-amber-50">
              <h3 className="font-semibold text-amber-700 mb-2">Vitamina A</h3>
              <p className="text-amber-700">{supplementation.vitaminA}</p>
            </div>
            <div className="section-card bg-purple-50">
              <h3 className="font-semibold text-purple-700 mb-2">Vitamina D</h3>
              <p className="text-purple-700">{supplementation.vitaminD}</p>
            </div>
            <div className="section-card bg-red-50">
              <h3 className="font-semibold text-red-700 mb-2">Ferro</h3>
              <p className="text-red-700">{supplementation.iron}</p>
            </div>
          </div>
        </div>

        {/* Calendário Vacinal */}
        {vaccineAnalysis && (
          <div className="mb-6">
            <h2 className="text-lg font-bold text-blue-700 mb-3 border-b border-gray-300 pb-1">
              💉 CALENDÁRIO VACINAL (SUS)
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Vacinas Aplicadas */}
              {Object.keys(vaccineAnalysis.currentGroups).length > 0 && (
                <div className="section-card bg-green-50">
                  <h3 className="font-semibold text-green-700 mb-3">✅ Vacinas que já deveriam ter sido aplicadas</h3>
                  <div className="space-y-2">
                    {Object.entries(vaccineAnalysis.currentGroups).map(([ageKey, doses]) => (
                      <div key={ageKey}>
                        <h4 className="font-medium text-green-800 text-sm border-b border-green-200 pb-1 mb-1">
                          {ageKey}
                        </h4>
                        {doses.map((dose) => (
                          <div key={dose.id} className="text-sm text-green-700 ml-2">
                            • {dose.vaccine.name}
                            {dose.dose_type === 'reforço' ? ' (Reforço)' : ` (${dose.dose_number}ª dose)`}
                          </div>
                        ))}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Próximas Vacinas */}
              {Object.keys(vaccineAnalysis.upcomingGroups).length > 0 && (
                <div className="section-card bg-blue-50">
                  <h3 className="font-semibold text-blue-700 mb-3">📅 Próximas vacinas</h3>
                  <div className="space-y-2">
                    {Object.entries(vaccineAnalysis.upcomingGroups).map(([ageKey, doses]) => (
                      <div key={ageKey}>
                        <h4 className="font-medium text-blue-800 text-sm border-b border-blue-200 pb-1 mb-1">
                          {ageKey}
                        </h4>
                        {doses.map((dose) => (
                          <div key={dose.id} className="text-sm text-blue-700 ml-2">
                            • {dose.vaccine.name}
                            {dose.dose_type === 'reforço' ? ' (Reforço)' : ` (${dose.dose_number}ª dose)`}
                          </div>
                        ))}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Desenvolvimento Neuropsicomotor */}
        {dnpmAnalysis && (
          <div className="mb-6">
            <h2 className="text-lg font-bold text-blue-700 mb-3 border-b border-gray-300 pb-1">
              🧠 DESENVOLVIMENTO NEUROPSICOMOTOR (DNPM)
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Marco Atual */}
              {dnpmAnalysis.currentMilestone && (
                <div className="section-card bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200">
                  <h3 className="font-semibold text-green-800 mb-4 flex items-center gap-2">
                    <span className="bg-green-100 p-2 rounded-full">🎯</span>
                    Marco Atual ({dnpmAnalysis.currentMilestone.age_months} meses)
                  </h3>
                  <div className="space-y-3">
                    {dnpmAnalysis.currentMilestone.social_emotional && (
                      <div className="bg-pink-50 p-3 rounded-lg border-l-4 border-pink-400">
                        <p className="font-semibold text-pink-700 flex items-center gap-2 mb-2">
                          <span>👥</span> Social/Emocional
                        </p>
                        <p className="text-pink-800 text-sm leading-relaxed">{dnpmAnalysis.currentMilestone.social_emotional}</p>
                      </div>
                    )}
                    {dnpmAnalysis.currentMilestone.language_communication && (
                      <div className="bg-blue-50 p-3 rounded-lg border-l-4 border-blue-400">
                        <p className="font-semibold text-blue-700 flex items-center gap-2 mb-2">
                          <span>💬</span> Linguagem/Comunicação
                        </p>
                        <p className="text-blue-800 text-sm leading-relaxed">{dnpmAnalysis.currentMilestone.language_communication}</p>
                      </div>
                    )}
                    {dnpmAnalysis.currentMilestone.cognition && (
                      <div className="bg-purple-50 p-3 rounded-lg border-l-4 border-purple-400">
                        <p className="font-semibold text-purple-700 flex items-center gap-2 mb-2">
                          <span>🧩</span> Cognição
                        </p>
                        <p className="text-purple-800 text-sm leading-relaxed">{dnpmAnalysis.currentMilestone.cognition}</p>
                      </div>
                    )}
                    {dnpmAnalysis.currentMilestone.motor_physical && (
                      <div className="bg-orange-50 p-3 rounded-lg border-l-4 border-orange-400">
                        <p className="font-semibold text-orange-700 flex items-center gap-2 mb-2">
                          <span>🏃</span> Motora/Física
                        </p>
                        <p className="text-orange-800 text-sm leading-relaxed">{dnpmAnalysis.currentMilestone.motor_physical}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Próximo Marco */}
              {dnpmAnalysis.nextMilestone && (
                <div className="section-card bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200">
                  <h3 className="font-semibold text-blue-800 mb-4 flex items-center gap-2">
                    <span className="bg-blue-100 p-2 rounded-full">➡️</span>
                    Próximo Marco ({dnpmAnalysis.nextMilestone.age_months} meses)
                  </h3>
                  <div className="space-y-3">
                    {dnpmAnalysis.nextMilestone.social_emotional && (
                      <div className="bg-pink-50 p-3 rounded-lg border-l-4 border-pink-400">
                        <p className="font-semibold text-pink-700 flex items-center gap-2 mb-2">
                          <span>👥</span> Social/Emocional
                        </p>
                        <p className="text-pink-800 text-sm leading-relaxed">{dnpmAnalysis.nextMilestone.social_emotional}</p>
                      </div>
                    )}
                    {dnpmAnalysis.nextMilestone.language_communication && (
                      <div className="bg-blue-50 p-3 rounded-lg border-l-4 border-blue-400">
                        <p className="font-semibold text-blue-700 flex items-center gap-2 mb-2">
                          <span>💬</span> Linguagem/Comunicação
                        </p>
                        <p className="text-blue-800 text-sm leading-relaxed">{dnpmAnalysis.nextMilestone.language_communication}</p>
                      </div>
                    )}
                    {dnpmAnalysis.nextMilestone.cognition && (
                      <div className="bg-purple-50 p-3 rounded-lg border-l-4 border-purple-400">
                        <p className="font-semibold text-purple-700 flex items-center gap-2 mb-2">
                          <span>🧩</span> Cognição
                        </p>
                        <p className="text-purple-800 text-sm leading-relaxed">{dnpmAnalysis.nextMilestone.cognition}</p>
                      </div>
                    )}
                    {dnpmAnalysis.nextMilestone.motor_physical && (
                      <div className="bg-orange-50 p-3 rounded-lg border-l-4 border-orange-400">
                        <p className="font-semibold text-orange-700 flex items-center gap-2 mb-2">
                          <span>🏃</span> Motora/Física
                        </p>
                        <p className="text-orange-800 text-sm leading-relaxed">{dnpmAnalysis.nextMilestone.motor_physical}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Orientações Alimentares Especiais */}
        {(() => {
          const guidance = getAlimentationGuidance();
          if (guidance.show) {
            return (
              <div className="mb-6">
                <h2 className="text-lg font-bold text-blue-700 mb-3 border-b border-gray-300 pb-1">
                  {guidance.title}
                </h2>
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg border border-green-200">
                  <div className="flex items-start gap-3">
                    <span className="text-2xl">🍎</span>
                    <div>
                      <p className="text-green-800 font-medium mb-2">Orientações Nutricionais Importantes:</p>
                      <p className="text-green-700 leading-relaxed">{guidance.message}</p>
                      {patientData.age >= 6 && (
                        <div className="mt-3 p-3 bg-green-100 rounded border-l-4 border-green-400">
                          <p className="text-green-800 text-sm">
                            <strong>💡 Dica:</strong> Priorizar alimentos ricos em ferro (carnes, feijão, vegetais verde-escuros)
                            e vitamina C (frutas cítricas) para melhor absorção. Evitar leite de vaca antes de 12 meses.
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          }
          return null;
        })()}

        {/* Observações Clínicas */}
        <div className="mb-6">
          <h2 className="text-lg font-bold text-blue-700 mb-3 border-b border-gray-300 pb-1">
            🩺 OBSERVAÇÕES CLÍNICAS
          </h2>
          <div className="bg-blue-50 p-4 rounded border border-blue-200">
            <div className="space-y-2">
              {patientData.maturity === 'Pre-term' && (
                <p className="text-blue-800">
                  • <strong>Prematuridade:</strong> Paciente nascido pré-termo.
                  {useCorrectedAge ? ' Análise realizada com idade corrigida.' : ' Considerar uso de idade corrigida para avaliação do crescimento.'}
                </p>
              )}
              {birthClassification && birthClassification.classification === 'PIG' && (
                <p className="text-blue-800">
                  • <strong>PIG:</strong> Peso inadequado para idade gestacional. Monitoramento rigoroso do crescimento e risco de hipoglicemia.
                </p>
              )}
              {birthClassification && birthClassification.classification === 'GIG' && (
                <p className="text-blue-800">
                  • <strong>GIG:</strong> Peso excessivo para idade gestacional. Atenção para risco de trauma ao nascimento e diabetes neonatal.
                </p>
              )}
              {percentiles && (
                <>
                  {percentiles.weight.percentile < 3 && (
                    <p className="text-blue-800">
                      • <strong>Baixo peso:</strong> Peso abaixo do P3. Investigar causas e considerar suporte nutricional.
                    </p>
                  )}
                  {percentiles.weight.percentile > 97 && (
                    <p className="text-blue-800">
                      • <strong>{patientData.maturity === 'Pre-term' ? 'Peso acima do esperado' : 'Sobrepeso'}:</strong> Peso acima do P97.
                      {patientData.maturity === 'Pre-term'
                        ? ' Acompanhar crescimento catch-up. Evitar restrições em prematuros.'
                        : ' Orientações nutricionais e acompanhamento do crescimento.'}
                    </p>
                  )}
                  {percentiles.height.percentile < 3 && (
                    <p className="text-blue-800">
                      • <strong>Baixa estatura:</strong> Altura abaixo do P3. Investigar causas e considerar avaliação endocrinológica.
                    </p>
                  )}
                </>
              )}
            </div>
          </div>
        </div>

        {/* Rodapé */}
        <div className="mt-8 pt-4 border-t-2 border-gray-300 text-center">
          <p className="text-sm text-gray-600 mb-2">
            <strong>Referências:</strong> Consenso SBP/SPSP 2018, Curvas OMS 2006/2007, CDC 2000
          </p>
          <p className="text-xs text-gray-500">
            Este laudo foi gerado automaticamente pelo PedBook e deve ser interpretado por profissional médico qualificado.
          </p>
          <p className="text-xs text-gray-500 mt-2">
            <FileText className="inline h-3 w-3 mr-1" />
            Documento gerado em {new Date().toLocaleString('pt-BR')}
          </p>
        </div>
        </div>
      )}
    </div>
  );
}
