import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";

interface FeedbackStatusBadgeProps {
  status: string;
}

export function FeedbackStatusBadge({ status }: FeedbackStatusBadgeProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'aguardando':
        return {
          label: 'Aguardando',
          className: 'bg-yellow-100 text-yellow-800 border-yellow-200'
        };
      case 'em_andamento':
        return {
          label: 'Em Andamento',
          className: 'bg-blue-100 text-blue-800 border-blue-200'
        };
      case 'resolvido':
        return {
          label: 'Resolvido',
          className: 'bg-green-100 text-green-800 border-green-200'
        };
      default:
        return {
          label: status,
          className: 'bg-gray-100 text-gray-800 border-gray-200'
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      whileHover={{ scale: 1.05 }}
      className="transform-gpu"
    >
      <Badge 
        variant="outline" 
        className={`${config.className} px-3 py-1 text-sm font-medium border shadow-sm`}
      >
        {config.label}
      </Badge>
    </motion.div>
  );
}