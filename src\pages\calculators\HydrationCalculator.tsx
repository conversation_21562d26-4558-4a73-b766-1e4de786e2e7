
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { HydrationCalculator } from "@/components/calculators/hydration/HydrationCalculator";
import { HydrationInfoCard } from "@/components/calculators/hydration/HydrationInfoCard";
import { FlowchartSEO } from "@/components/seo/FlowchartSEO";
import { FLOWCHART_SEO_DATA } from "@/data/flowchartSEOData";
import { getThemeClasses } from "@/components/ui/theme-utils";

const HydrationCalculatorPage = () => {
  const navigate = useNavigate();
  const seoData = FLOWCHART_SEO_DATA['hidratacao'];

  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen flex flex-col")}>
      <FlowchartSEO {...seoData} />
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto space-y-8">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate('/calculadoras')}
              className="hover:bg-primary/10 dark:hover:bg-primary/20"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            
            <div className="flex-1">
              <h1 className={getThemeClasses.gradientHeading("text-3xl text-center")}>
                Calculadora de Hidratação Pediátrica
              </h1>
            </div>
          </div>

          <HydrationInfoCard />
          <HydrationCalculator />
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default HydrationCalculatorPage;
