import HelmetWrapper from "@/components/utils/HelmetWrapper";

interface ProfessionalInstructionsSEOProps {
  medicationsCount?: number;
  categories?: string[];
  popularMedications?: Array<{
    name: string;
    slug: string;
    category: string;
  }>;
}

export const ProfessionalInstructionsSEO = ({
  medicationsCount = 0,
  categories = [],
  popularMedications = []
}: ProfessionalInstructionsSEOProps) => {
  
  // Título dinâmico
  const dynamicTitle = medicationsCount > 0 
    ? `Bulas Profissionais - ${medicationsCount} Medicamentos Pediátricos | PedBook`
    : "Bulas Profissionais de Medicamentos Pediátricos | PedBook";

  // Descrição dinâmica
  const generateDescription = () => {
    let desc = "Acesse bulas profissionais completas de medicamentos pediátricos. ";
    
    if (medicationsCount > 0) {
      desc += `${medicationsCount} medicamentos com informações detalhadas sobre posologia, indicações, contraindicações e cuidados especiais em pediatria. `;
    }
    
    if (popularMedications.length > 0) {
      const medNames = popularMedications.slice(0, 5).map(med => med.name).join(', ');
      desc += `Inclui medicamentos como ${medNames} e outros essenciais para a prática pediátrica.`;
    }

    return desc.substring(0, 160);
  };

  // Keywords dinâmicas
  const generateKeywords = () => {
    const baseKeywords = [
      "bulas profissionais",
      "medicamentos pediátricos",
      "bulas pediatria",
      "posologia pediátrica",
      "indicações pediátricas",
      "contraindicações pediatria",
      "medicamentos infantis",
      "farmacologia pediátrica",
      "prescrição pediátrica"
    ];

    // Adicionar categorias
    if (categories.length > 0) {
      categories.slice(0, 8).forEach(category => {
        baseKeywords.push(`bula ${category.toLowerCase()}`);
        baseKeywords.push(`${category.toLowerCase()} pediátrico`);
      });
    }

    // Adicionar medicamentos populares
    if (popularMedications.length > 0) {
      popularMedications.slice(0, 10).forEach(med => {
        baseKeywords.push(`bula ${med.name.toLowerCase()}`);
        baseKeywords.push(`${med.name.toLowerCase()} pediátrico`);
      });
    }

    return baseKeywords.join(", ");
  };

  // Schema.org para página de listagem médica
  const medicalListingSchema = {
    "@context": "https://schema.org",
    "@type": "MedicalWebPage",
    "name": dynamicTitle,
    "description": generateDescription(),
    "url": "https://pedb.com.br/bulas-profissionais",
    "mainContentOfPage": {
      "@type": "WebPageElement",
      "cssSelector": "main"
    },
    "specialty": "Pediatria",
    "audience": {
      "@type": "MedicalAudience",
      "audienceType": "Médicos pediatras e profissionais da saúde"
    },
    "about": {
      "@type": "Thing",
      "name": "Bulas de Medicamentos Pediátricos"
    },
    "lastReviewed": new Date().toISOString().split('T')[0],
    "reviewedBy": {
      "@type": "Organization",
      "name": "PedBook",
      "url": "https://pedb.com.br"
    }
  };

  // Schema.org para coleção de medicamentos
  const medicationCollectionSchema = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": "Bulas Profissionais de Medicamentos Pediátricos",
    "description": generateDescription(),
    "url": "https://pedb.com.br/bulas-profissionais",
    "numberOfItems": medicationsCount,
    "itemListElement": popularMedications.slice(0, 10).map((med, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "item": {
        "@type": "Drug",
        "name": med.name,
        "url": `https://pedb.com.br/bulas-profissionais/${med.slug}`,
        "category": med.category,
        "targetPopulation": "Pacientes pediátricos"
      }
    }))
  };

  // FAQ Schema para bulas
  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "O que são bulas profissionais?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Bulas profissionais são documentos técnicos dirigidos aos profissionais da saúde, contendo informações detalhadas sobre medicamentos, incluindo posologia, indicações, contraindicações, interações medicamentosas e cuidados especiais."
        }
      },
      {
        "@type": "Question",
        "name": "Como usar as bulas pediátricas?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "As bulas pediátricas devem ser consultadas por profissionais da saúde para prescrição adequada. Sempre considere peso, idade, condições clínicas do paciente e siga as diretrizes médicas atualizadas."
        }
      },
      {
        "@type": "Question",
        "name": "Quantos medicamentos estão disponíveis?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": medicationsCount > 0 
            ? `Atualmente temos ${medicationsCount} medicamentos com bulas profissionais disponíveis, organizados por categorias para facilitar a consulta.`
            : "Temos uma ampla coleção de medicamentos pediátricos com bulas profissionais organizadas por categorias."
        }
      },
      {
        "@type": "Question",
        "name": "As informações são confiáveis?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Sim, todas as informações são baseadas em bulas oficiais e literatura médica atualizada. Sempre consulte as diretrizes locais e use seu julgamento clínico profissional."
        }
      }
    ]
  };

  // Breadcrumb Schema
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "PedBook",
        "item": "https://pedb.com.br"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Bulas Profissionais",
        "item": "https://pedb.com.br/bulas-profissionais"
      }
    ]
  };

  return (
    <HelmetWrapper>
      {/* Título e Descrição Dinâmicos */}
      <title>{dynamicTitle}</title>
      <meta name="description" content={generateDescription()} />
      <meta name="keywords" content={generateKeywords()} />

      {/* Meta tags médicas específicas */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1" />
      <meta name="googlebot" content="index, follow" />
      <meta name="medical-content" content="professional" />
      <meta name="target-audience" content="healthcare-professionals" />
      <meta name="content-type" content="drug-information-listing" />
      
      {/* Geo targeting */}
      <meta name="geo.region" content="BR" />
      <meta name="geo.country" content="Brazil" />
      <meta name="language" content="Portuguese" />

      {/* Open Graph */}
      <meta property="og:title" content={dynamicTitle} />
      <meta property="og:description" content={generateDescription()} />
      <meta property="og:type" content="website" />
      <meta property="og:url" content="https://pedb.com.br/bulas-profissionais" />
      <meta property="og:image" content="https://pedb.com.br/faviconx.webp" />
      <meta property="og:image:alt" content="Bulas Profissionais - PedBook" />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />
      <meta property="og:updated_time" content={new Date().toISOString()} />

      {/* Twitter Cards */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={dynamicTitle} />
      <meta name="twitter:description" content={generateDescription()} />
      <meta name="twitter:image" content="https://pedb.com.br/faviconx.webp" />
      <meta name="twitter:site" content="@pedbook" />

      {/* Canonical */}
      <link rel="canonical" href="https://pedb.com.br/bulas-profissionais" />

      {/* Schema.org - Página Médica */}
      <script type="application/ld+json">
        {JSON.stringify(medicalListingSchema)}
      </script>

      {/* Schema.org - Coleção de Medicamentos */}
      {medicationsCount > 0 && (
        <script type="application/ld+json">
          {JSON.stringify(medicationCollectionSchema)}
        </script>
      )}

      {/* Schema.org - FAQ */}
      <script type="application/ld+json">
        {JSON.stringify(faqSchema)}
      </script>

      {/* Schema.org - Breadcrumb */}
      <script type="application/ld+json">
        {JSON.stringify(breadcrumbSchema)}
      </script>

      {/* Preconnect para performance */}
      <link rel="preconnect" href="https://bxedpdmgvgatjdfxgxij.supabase.co" />
      <link rel="dns-prefetch" href="https://bxedpdmgvgatjdfxgxij.supabase.co" />
    </HelmetWrapper>
  );
};
