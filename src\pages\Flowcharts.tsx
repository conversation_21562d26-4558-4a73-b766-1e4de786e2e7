
import React from "react";
import { Link } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { getThemeClasses } from "@/components/ui/theme-utils";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

const Flowcharts = () => {
  const flowcharts = [
    {
      id: "hydration",
      title: "Hidratação Venosa",
      description: "Fluxograma para cálculo da hidratação venosa de manutenção (Holliday-Segar)",
      path: "/flowcharts/hidratacao",
      colorName: "blue",
      icon: "💧"
    },
    {
      id: "dengue",
      title: "Dengue",
      description: "Fluxograma para manejo de casos suspeitos de dengue",
      path: "/flowcharts/dengue",
      colorName: "orange",
      icon: "🦟"
    },
    {
      id: "dka",
      title: "Cetoacidose Diabética",
      description: "Fluxograma para manejo de cetoacidose diabética em pediatria",
      path: "/flowcharts/dka",
      colorName: "purple",
      icon: "💉"
    },
    {
      id: "anaphylaxis",
      title: "Anafilaxia",
      description: "Fluxograma para manejo de anafilaxia em pediatria",
      path: "/flowcharts/anaphylaxis",
      colorName: "red",
      icon: "💊"
    },
    {
      id: "asthma",
      title: "Crise Asmática",
      description: "Fluxograma para manejo de crise asmática em pediatria",
      path: "/flowcharts/asthma",
      colorName: "blue",
      icon: "💨"
    },
    {
      id: "seizure",
      title: "Crise Convulsiva",
      description: "Fluxograma para manejo de crise convulsiva em pediatria",
      path: "/flowcharts/seizure",
      colorName: "yellow",
      icon: "🧠"
    },
    {
      id: "pecarn",
      title: "PECARN - Trauma Craniano",
      description: "Fluxograma para avaliação de trauma craniano em pediatria",
      path: "/flowcharts/pecarn",
      colorName: "indigo",
      icon: "🤕"
    },
    {
      id: "venomous",
      title: "Animais Peçonhentos",
      description: "Fluxograma para manejo de acidentes com animais peçonhentos",
      path: "/flowcharts/venomous",
      colorName: "green",
      icon: "🐍"
    }
  ];

  // Helper function to get border color based on colorName
  const getBorderColor = (colorName: string) => {
    switch(colorName) {
      case 'blue': return 'border-blue-500 dark:border-blue-600';
      case 'orange': return 'border-orange-500 dark:border-orange-600';
      case 'purple': return 'border-purple-500 dark:border-purple-600';
      case 'red': return 'border-red-500 dark:border-red-600';
      case 'yellow': return 'border-yellow-500 dark:border-yellow-600';
      case 'indigo': return 'border-indigo-500 dark:border-indigo-600';
      case 'green': return 'border-green-500 dark:border-green-600';
      default: return 'border-gray-300 dark:border-gray-600';
    }
  };

  // Helper function to get icon background color based on colorName
  const getIconBgColor = (colorName: string) => {
    switch(colorName) {
      case 'blue': return 'bg-blue-50 dark:bg-blue-900/30';
      case 'orange': return 'bg-orange-50 dark:bg-orange-900/30';
      case 'purple': return 'bg-purple-50 dark:bg-purple-900/30';
      case 'red': return 'bg-red-50 dark:bg-red-900/30';
      case 'yellow': return 'bg-yellow-50 dark:bg-yellow-900/30';
      case 'indigo': return 'bg-indigo-50 dark:bg-indigo-900/30';
      case 'green': return 'bg-green-50 dark:bg-green-900/30';
      default: return 'bg-gray-50 dark:bg-gray-800/30';
    }
  };

  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen flex flex-col from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800")}>
      <HelmetWrapper>
        <title>PedBook | Fluxogramas</title>
        <meta name="description" content="Fluxogramas interativos para manejo de urgências e emergências pediátricas" />
      </HelmetWrapper>

      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto space-y-8">
          <div className="flex items-center gap-4">
            <Link to="/">
              <Button
                variant="ghost"
                size="icon"
                className="hidden sm:inline-flex hover:bg-primary/10 dark:hover:bg-primary/20"
              >
                <ArrowLeft className="h-5 w-5 text-primary dark:text-blue-400" />
              </Button>
            </Link>
            <div className="flex-1">
              <h1 className={getThemeClasses.gradientHeading("text-3xl text-center")}>
                Fluxogramas de Urgência
              </h1>
              <p className="text-gray-600 dark:text-gray-300 text-center max-w-2xl mx-auto">
                Guias práticos e interativos para manejo de urgências e emergências pediátricas
              </p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2 sm:gap-4">
            {flowcharts.map((flowchart, index) => (
              <Link
                key={flowchart.id}
                to={flowchart.path}
                className="block group transform transition-all duration-500 hover:scale-[1.02]"
                style={{
                  animationDelay: `${index * 100}ms`,
                  animation: 'fade-in-up 0.5s ease-out forwards',
                  opacity: 0
                }}
              >
                <div
                  className={cn(
                    "relative h-full p-3 sm:p-5 rounded-xl transition-all duration-300 hover:shadow-lg hover:-translate-y-1",
                    "bg-white/90 dark:bg-slate-800/90 backdrop-blur-md shadow-md",
                    "border border-gray-100 dark:border-gray-700/50"
                  )}
                >
                  {/* Barra de cor na parte superior para aparência de app */}
                  <div className={cn(
                    "absolute top-0 left-0 right-0 h-1.5 rounded-t-xl",
                    flowchart.colorName === 'blue' ? "bg-blue-500" :
                    flowchart.colorName === 'orange' ? "bg-orange-500" :
                    flowchart.colorName === 'purple' ? "bg-purple-500" :
                    flowchart.colorName === 'red' ? "bg-red-500" :
                    flowchart.colorName === 'yellow' ? "bg-yellow-500" :
                    flowchart.colorName === 'indigo' ? "bg-indigo-500" :
                    flowchart.colorName === 'green' ? "bg-green-500" :
                    "bg-primary"
                  )} />
                  <div className="flex flex-col items-center text-center h-full">
                    <div className={cn(
                      "w-10 h-10 sm:w-14 sm:h-14 rounded-xl flex items-center justify-center mb-2 sm:mb-3 shadow-sm transition-transform group-hover:scale-110",
                      "border-2",
                      getBorderColor(flowchart.colorName),
                      getIconBgColor(flowchart.colorName)
                    )}>
                      <span className="text-xl sm:text-2xl">{flowchart.icon}</span>
                    </div>
                    <div className="space-y-2 flex-1">
                      <h3 className="text-sm sm:text-base font-semibold text-gray-800 dark:text-gray-100 line-clamp-2">
                        {flowchart.title}
                      </h3>
                      <p className="text-[10px] sm:text-xs text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                        {flowchart.description}
                      </p>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Flowcharts;
