import HelmetWrapper from "@/components/utils/HelmetWrapper";

interface PoisoningSEOProps {
  title: string;
  description: string;
  slug: string;
  category: 'main' | 'sedative' | 'narcotic' | 'anticholinergic' | 'sympathomimetic' | 'cholinergic' | 'methemoglobinemic' | 'hepatotoxic' | 'cardiotoxic' | 'cardiodepressant';
  keywords: string[];
  antidote: string;
  toxidromeType: string;
  clinicalUse: string;
  precautions: string[];
  examples: string[];
  management: string[];
  clinicalSignificance: string;
  relatedTopics: string[];
}

export const PoisoningSEO = ({
  title,
  description,
  slug,
  category,
  keywords,
  antidote,
  toxidromeType,
  clinicalUse,
  precautions,
  examples,
  management,
  clinicalSignificance,
  relatedTopics
}: PoisoningSEOProps) => {

  // Gerar título dinâmico otimizado
  const generateTitle = () => {
    let seoTitle = `${title}`;
    
    if (category === 'main') {
      seoTitle += ` - Gerenciador de Intoxicações`;
    } else if (category === 'sedative') {
      seoTitle += ` - Intoxicação Sedativa`;
    } else if (category === 'narcotic') {
      seoTitle += ` - Intoxicação Narcótica`;
    } else if (category === 'anticholinergic') {
      seoTitle += ` - Intoxicação Anticolinérgica`;
    } else if (category === 'sympathomimetic') {
      seoTitle += ` - Intoxicação Simpatomimética`;
    } else if (category === 'cholinergic') {
      seoTitle += ` - Intoxicação Colinérgica`;
    } else if (category === 'methemoglobinemic') {
      seoTitle += ` - Metemoglobinemia`;
    } else if (category === 'hepatotoxic') {
      seoTitle += ` - Intoxicação Hepatotóxica`;
    } else if (category === 'cardiotoxic') {
      seoTitle += ` - Intoxicação Cardiotóxica`;
    } else if (category === 'cardiodepressant') {
      seoTitle += ` - Intoxicação Cardiodepressora`;
    }
    
    seoTitle += ` | PedBook`;
    
    return seoTitle.substring(0, 60);
  };

  // Gerar descrição dinâmica
  const generateDescription = () => {
    let desc = `${title}: ${description}`;
    
    if (antidote && antidote !== "Não há antídoto específico") {
      desc += ` Antídoto: ${antidote}.`;
    }
    
    desc += ` ${clinicalUse}`;
    
    if (examples.length > 0) {
      desc += ` Exemplos: ${examples.slice(0, 3).join(', ')}.`;
    }
    
    if (precautions.length > 0) {
      desc += ` Precauções importantes incluídas.`;
    }
    
    desc += ` Ferramenta essencial para emergências pediátricas.`;
    
    return desc.substring(0, 160);
  };

  // Gerar keywords dinâmicas
  const generateKeywords = () => {
    const baseKeywords = [
      `${title.toLowerCase()}`,
      `intoxicação ${title.toLowerCase()}`,
      `${title.toLowerCase()} pediatria`,
      `${title.toLowerCase()} emergência`,
      `${title.toLowerCase()} antídoto`,
      `toxíndrome ${title.toLowerCase()}`
    ];

    // Adicionar antídoto como keyword
    if (antidote && antidote !== "Não há antídoto específico") {
      baseKeywords.push(
        `${antidote.toLowerCase()}`,
        `antídoto ${antidote.toLowerCase()}`,
        `${antidote.toLowerCase()} pediatria`
      );
    }

    // Adicionar keywords por categoria
    if (category === 'main') {
      baseKeywords.push(
        'intoxicações pediátricas',
        'gerenciador intoxicações',
        'toxíndromes',
        'antídotos pediatria',
        'emergências toxicológicas'
      );
    }

    if (category === 'sedative') {
      baseKeywords.push(
        'benzodiazepínicos',
        'flumazenil',
        'sedação excessiva',
        'depressão snc',
        'reversão sedação'
      );
    }

    if (category === 'narcotic') {
      baseKeywords.push(
        'opioides',
        'naloxona',
        'depressão respiratória',
        'overdose opioides',
        'narcóticos'
      );
    }

    if (category === 'anticholinergic') {
      baseKeywords.push(
        'anticolinérgicos',
        'fisostigmina',
        'delírio anticolinérgico',
        'atropina',
        'escopolamina'
      );
    }

    if (category === 'sympathomimetic') {
      baseKeywords.push(
        'simpatomiméticos',
        'cocaína',
        'anfetaminas',
        'hipertermia',
        'agitação psicomotora'
      );
    }

    if (category === 'cholinergic') {
      baseKeywords.push(
        'colinérgicos',
        'organofosforados',
        'carbamatos',
        'atropina',
        'pralidoxima'
      );
    }

    if (category === 'methemoglobinemic') {
      baseKeywords.push(
        'metemoglobinemia',
        'azul de metileno',
        'cianose',
        'nitratos',
        'dapsona'
      );
    }

    if (category === 'hepatotoxic') {
      baseKeywords.push(
        'paracetamol',
        'acetaminofeno',
        'n-acetilcisteína',
        'hepatotoxicidade',
        'lesão hepática'
      );
    }

    if (category === 'cardiotoxic') {
      baseKeywords.push(
        'antidepressivos tricíclicos',
        'bicarbonato sódio',
        'cardiotoxicidade',
        'arritmias',
        'bloqueio condução'
      );
    }

    if (category === 'cardiodepressant') {
      baseKeywords.push(
        'betabloqueadores',
        'glucagon',
        'bradicardia',
        'hipotensão',
        'cardiodepressão'
      );
    }

    // Adicionar keywords específicas passadas
    baseKeywords.push(...keywords);

    // Adicionar exemplos como keywords
    examples.forEach(example => {
      baseKeywords.push(`${example.toLowerCase()}`);
    });

    // Adicionar tópicos relacionados
    relatedTopics.forEach(topic => {
      baseKeywords.push(`${topic.toLowerCase()}`);
    });

    return baseKeywords.join(", ");
  };

  // URL canônica
  const canonicalUrl = slug === 'main' 
    ? `https://pedb.com.br/poisonings`
    : `https://pedb.com.br/poisonings/${slug}`;

  // Schema.org para emergência médica
  const medicalEmergencySchema = {
    "@context": "https://schema.org",
    "@type": "MedicalWebPage",
    "name": generateTitle(),
    "description": generateDescription(),
    "url": canonicalUrl,
    "mainContentOfPage": {
      "@type": "WebPageElement",
      "cssSelector": "main"
    },
    "specialty": "Toxicologia Pediátrica",
    "audience": {
      "@type": "MedicalAudience",
      "audienceType": "Médicos emergencistas e pediatras"
    },
    "about": {
      "@type": "MedicalCondition",
      "name": title,
      "description": clinicalUse,
      "possibleTreatment": antidote !== "Não há antídoto específico" ? {
        "@type": "Drug",
        "name": antidote,
        "description": clinicalUse
      } : undefined
    },
    "lastReviewed": new Date().toISOString().split('T')[0],
    "reviewedBy": {
      "@type": "Organization",
      "name": "PedBook",
      "url": "https://pedb.com.br"
    }
  };

  // Schema.org para organização médica
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "MedicalOrganization",
    "name": "PedBook",
    "url": "https://pedb.com.br",
    "logo": {
      "@type": "ImageObject",
      "url": "https://pedb.com.br/faviconx.webp"
    },
    "medicalSpecialty": "Toxicologia Pediátrica",
    "serviceType": "Emergências Toxicológicas",
    "areaServed": "Brasil",
    "availableService": {
      "@type": "MedicalTherapy",
      "name": title,
      "description": clinicalUse
    }
  };

  // Breadcrumb Schema
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "PedBook",
        "item": "https://pedb.com.br"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Intoxicações",
        "item": "https://pedb.com.br/poisonings"
      }
    ]
  };

  // Adicionar item específico se não for a página principal
  if (slug !== 'main') {
    breadcrumbSchema.itemListElement.push({
      "@type": "ListItem",
      "position": 3,
      "name": title,
      "item": canonicalUrl
    });
  }

  return (
    <HelmetWrapper>
      {/* Título e Descrição Dinâmicos */}
      <title>{generateTitle()}</title>
      <meta name="description" content={generateDescription()} />
      <meta name="keywords" content={generateKeywords()} />

      {/* Meta tags médicas específicas */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1" />
      <meta name="googlebot" content="index, follow" />
      <meta name="medical-content" content="toxicology" />
      <meta name="target-audience" content="healthcare-professionals" />
      <meta name="content-type" content="medical-emergency" />
      <meta name="clinical-specialty" content="pediatric-toxicology" />
      <meta name="emergency-type" content={category} />
      
      {/* Geo targeting */}
      <meta name="geo.region" content="BR" />
      <meta name="geo.country" content="Brazil" />
      <meta name="language" content="Portuguese" />

      {/* Open Graph */}
      <meta property="og:title" content={generateTitle()} />
      <meta property="og:description" content={generateDescription()} />
      <meta property="og:type" content="website" />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:image" content="https://pedb.com.br/faviconx.webp" />
      <meta property="og:image:alt" content={`${title} - PedBook`} />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />
      <meta property="article:section" content="Medicina" />
      <meta property="article:tag" content="Toxicologia" />
      <meta property="article:tag" content="Emergência" />
      <meta property="article:tag" content={category} />
      {relatedTopics.map((topic, index) => (
        <meta key={index} property="article:tag" content={topic} />
      ))}

      {/* Twitter Cards */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={generateTitle()} />
      <meta name="twitter:description" content={generateDescription()} />
      <meta name="twitter:image" content="https://pedb.com.br/faviconx.webp" />
      <meta name="twitter:site" content="@pedbook" />

      {/* Canonical */}
      <link rel="canonical" href={canonicalUrl} />

      {/* Schema.org - Emergência Médica */}
      <script type="application/ld+json">
        {JSON.stringify(medicalEmergencySchema)}
      </script>

      {/* Schema.org - Organização Médica */}
      <script type="application/ld+json">
        {JSON.stringify(organizationSchema)}
      </script>

      {/* Schema.org - Breadcrumb */}
      <script type="application/ld+json">
        {JSON.stringify(breadcrumbSchema)}
      </script>
    </HelmetWrapper>
  );
};
