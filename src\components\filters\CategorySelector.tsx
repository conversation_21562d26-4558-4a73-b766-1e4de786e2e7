import { But<PERSON> } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";
import type { Category } from "@/types/question";

interface CategorySelectorProps {
  selectedFilters: {
    specialty?: Category;
    theme?: Category;
    focus?: Category;
  };
  onSelectType: (type: "specialty" | "theme" | "focus") => void;
  categories: Category[];
  onSelectCategory?: (category: Category) => void;
}

const CategorySelector = ({ 
  selectedFilters, 
  onSelectType, 
  categories,
  onSelectCategory 
}: CategorySelectorProps) => {
  return (
    <div className="flex gap-2">
      <Button
        variant="outline"
        className="flex-1 justify-between"
        onClick={() => onSelectType("specialty")}
      >
        {selectedFilters.specialty?.name || "Especialidade *"}
        <ChevronRight className="h-4 w-4" />
      </Button>

      <Button
        variant="outline"
        className="flex-1 justify-between"
        onClick={() => onSelectType("theme")}
        disabled={!selectedFilters.specialty}
      >
        {selectedFilters.theme?.name || "Tema"}
        <ChevronRight className="h-4 w-4" />
      </Button>

      <Button
        variant="outline"
        className="flex-1 justify-between"
        onClick={() => onSelectType("focus")}
        disabled={!selectedFilters.theme}
      >
        {selectedFilters.focus?.name || "Foco"}
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  );
};

export default CategorySelector;