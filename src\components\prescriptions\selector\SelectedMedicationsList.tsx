import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { DosageDisplay } from "@/components/DosageDisplay";

interface SelectedMedicationsListProps {
  selectedMedications: Array<{
    medicationId: string;
    dosageId: string;
    sectionTitle?: string;
    quantity?: string;
  }>;
  medications: Array<any>;
  onRemove: (index: number) => void;
  onUpdateSectionTitle: (index: number, title: string) => void;
  onUpdateQuantity: (index: number, quantity: string) => void;
  weight: number;
  age: number;
}

export const SelectedMedicationsList = ({
  selectedMedications,
  medications,
  onRemove,
  onUpdateSectionTitle,
  onUpdateQuantity,
  weight,
  age,
}: SelectedMedicationsListProps) => {
  return (
    <ScrollArea className="h-[300px] rounded-md border p-4">
      <div className="space-y-4">
        {selectedMedications.map((selection, index) => {
          const medication = medications?.find(
            (med) => med.id === selection.medicationId
          );
          const dosage = medication?.pedbook_medication_dosages?.find(
            (dos) => dos.id === selection.dosageId
          );

          if (!medication || !dosage) return null;

          return (
            <div key={index} className="relative border rounded-lg p-4 space-y-3">
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2"
                onClick={() => onRemove(index)}
              >
                <X className="h-4 w-4" />
              </Button>

              <Input
                placeholder="Título da seção (opcional)"
                value={selection.sectionTitle || ""}
                onChange={(e) => onUpdateSectionTitle(index, e.target.value)}
                className="max-w-sm"
              />

              <Input
                placeholder="Quantidade (ex: 1 FR, 20 Comprimidos)"
                value={selection.quantity || ""}
                onChange={(e) => onUpdateQuantity(index, e.target.value)}
                className="max-w-sm"
              />

              <div className="pt-2">
                <h4 className="font-semibold mb-2">{medication.name}</h4>
                <DosageDisplay
                  dosage={{
                    ...dosage,
                    medication_id: medication.id
                  }}
                  weight={weight}
                  age={age}
                />
              </div>
            </div>
          );
        })}
      </div>
    </ScrollArea>
  );
};