import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>hart, Line, XAxis, <PERSON>Axis, CartesianGrid, <PERSON>lt<PERSON>, Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import { useEffect, useState } from "react";

const ProgressChart = () => {
  const [weeklyData, setWeeklyData] = useState([]);

  useEffect(() => {
    // Get study sessions from localStorage
    const sessions = JSON.parse(localStorage.getItem("studySessions") || "[]");
    
    // Process sessions to get weekly data
    const weekDays = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sab'];
    const today = new Date();
    const lastWeekData = Array(7).fill(null).map((_, index) => {
      const date = new Date(today);
      date.setDate(date.getDate() - (6 - index));
      
      const daysSessions = sessions.filter(session => {
        const sessionDate = new Date(session.date);
        return sessionDate.toDateString() === date.toDateString();
      });

      return {
        day: weekDays[date.getDay()],
        questoes: daysSessions.length,
        acertos: daysSessions.filter(s => s.isCorrect).length
      };
    });

    setWeeklyData(lastWeekData);
  }, []);

  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={weeklyData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="day" 
            stroke="#64748b"
            fontSize={12}
          />
          <YAxis 
            stroke="#64748b"
            fontSize={12}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: "white",
              border: "1px solid #e2e8f0",
              borderRadius: "6px",
              padding: "8px",
            }}
          />
          <Legend />
          <Line
            type="monotone"
            dataKey="questoes"
            name="Questões"
            stroke="#2563eb"
            strokeWidth={2}
            dot={{ fill: "#2563eb" }}
          />
          <Line
            type="monotone"
            dataKey="acertos"
            name="Acertos"
            stroke="#0d9488"
            strokeWidth={2}
            dot={{ fill: "#0d9488" }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default ProgressChart;