import { useEffect, useState } from "react";
import { useSession } from "@supabase/auth-helpers-react";
import { supabase } from "@/integrations/supabase/client";

interface UserAvatarProps {
  userId: string;
  className?: string;
}

export const UserAvatar = ({ userId, className = "" }: UserAvatarProps) => {
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
  const session = useSession();

  useEffect(() => {
    const fetchAvatar = async () => {
      try {
        // Primeiro tenta buscar o avatar dos metadados do usuário
        const avatarUrlFromMetadata = session?.user?.user_metadata?.avatar_url;
        if (avatarUrlFromMetadata) {
          setAvatarUrl(avatarUrlFromMetadata);
          return;
        }

        // Se não encontrou nos metadados, busca do bucket 'avatars'
        const { data: profileData } = await supabase
          .from('secure_profiles')
          .select('avatar_url')
          .eq('id', userId)
          .single();

        if (profileData?.avatar_url) {
          setAvatarUrl(profileData.avatar_url);
          return;
        }

        // Se não encontrou em nenhum lugar, usa o avatar padrão
        setAvatarUrl("https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/avatars/user.png");
      } catch (error) {
        setAvatarUrl("https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/avatars/user.png");
      }
    };

    if (userId && !avatarUrl) {
      fetchAvatar();
    }
  }, [userId, session?.user?.user_metadata?.avatar_url]);

  return (
    <div className={`w-8 h-8 rounded-full overflow-hidden flex-shrink-0 border-2 border-primary shadow-lg ${className}`}>
      <img
        src={avatarUrl || "https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/avatars/user.png"}
        alt="User Avatar"
        className="w-full h-full object-cover"
        onError={(e) => {
          (e.target as HTMLImageElement).src = "https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/avatars/user.png";
        }}
      />
    </div>
  );
};
