
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Content-Type': 'application/json'
}

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

// Format number following PT-BR locale standards with special handling for values with UI/IU units
function formatNumberPtBR(value: number | string): string {
  // Special case for UI/IU units in strings
  if (typeof value === 'string' && /UI|IU/.test(value)) {
    const numericPart = value.replace(/[^\d.,]/g, '');
    const numericValue = parseFloat(numericPart.replace(/\./g, '').replace(',', '.'));

    const formattedNumber = new Intl.NumberFormat('pt-BR', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(numericValue);

    return value.replace(numericPart, formattedNumber);
  }

  const numericValue = typeof value === 'string' ?
    parseFloat(value.replace(/\./g, '').replace(',', '.')) :
    value;

  if (Number.isInteger(numericValue) && Math.abs(numericValue) >= 1000) {
    return new Intl.NumberFormat('pt-BR', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(numericValue);
  }

  return new Intl.NumberFormat('pt-BR', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(numericValue);
}

// Helper function to normalize text for sorting (remove accents and special characters)
function normalizeText(text: string): string {
  return text
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "")
    .toLowerCase();
}

// Process dosage template with proper number formatting
function processDosageTemplate(template: string, tags: any[], weight: number, age: number): string {
  let result = template;

  // Create a Map to store calculated values by tag name
  const tagValues: Map<string, number> = new Map();

  // First pass: process all tags and calculate their values
  for (const tag of tags) {
    // Calculate the value based on tag type
    let calculatedValue: number = 0; // Initialize as 0 to ensure it always has a value

    if (tag.type === 'fixed_by_weight') {
      // For 'fixed_by_weight' type, find the fixed value based on weight range
      const matchingTag = tags.find(t =>
        t.name === tag.name &&
        t.type === 'fixed_by_weight' &&
        weight >= (t.start_weight || 0) &&
        weight <= (t.end_weight || Infinity)
      );

      if (matchingTag) {
        calculatedValue = matchingTag.multiplier || 0;

        if (matchingTag.round_result) {
          calculatedValue = Math.round(calculatedValue);
        }
      }
    }
    else if (tag.type === 'age') {
      // For 'age' type, check if age is within range
      const matchingTag = tags.find(t =>
        t.name === tag.name &&
        t.type === 'age' &&
        age >= (t.start_month || 0) &&
        age <= (t.end_month || Infinity)
      );

      if (matchingTag) {
        calculatedValue = matchingTag.multiplier || 0;

        if (matchingTag.round_result) {
          calculatedValue = Math.round(calculatedValue);
        }
      }
    }
    else if (tag.type === 'multiplier_by_fixed_age') {
      // For 'multiplier_by_fixed_age', multiply by weight if age is in range
      const matchingTag = tags.find(t =>
        t.name === tag.name &&
        t.type === 'multiplier_by_fixed_age' &&
        age >= (t.start_month || 0) &&
        age <= (t.end_month || Infinity)
      );

      if (matchingTag) {
        calculatedValue = weight * (matchingTag.multiplier || 0);

        // Apply max value constraint if defined
        if (matchingTag.max_value && calculatedValue > matchingTag.max_value) {
          calculatedValue = matchingTag.max_value;
        }

        if (matchingTag.round_result) {
          calculatedValue = Math.round(calculatedValue);
        }
      }
    }
    else if (tag.type === 'fixed') {
      // For 'fixed' type, simply use the multiplier value
      calculatedValue = tag.multiplier || 0;

      if (tag.round_result) {
        calculatedValue = Math.round(calculatedValue);
      }
    }
    else {
      // Default 'multiplier' type
      const multiplier = tag.multiplier || 0;
      calculatedValue = weight * multiplier;

      if (tag.max_value && calculatedValue > tag.max_value) {
        calculatedValue = tag.max_value;
      }

      if (tag.round_result) {
        calculatedValue = Math.round(calculatedValue);
      }
    }

    // Store the calculated value for this tag
    const existingValue = tagValues.get(tag.name);

    // Prioritize non-zero values when there's a tag conflict
    if (existingValue === undefined ||
        (calculatedValue !== 0 && existingValue === 0) ||
        (existingValue === 0 && calculatedValue === 0)) {
      tagValues.set(tag.name, calculatedValue);
    }
  }

  // Second pass: replace all tags in the template with calculated values
  for (const [tagName, value] of tagValues.entries()) {
    const tagPattern = new RegExp(`\\(\\(${tagName}\\)\\)`, 'g');

    let formattedValue;
    if (value === 0) {
      formattedValue = "0";
    } else if (value > 0 && value < 0.001) {
      formattedValue = value.toFixed(4).replace('.', ',');
    } else if (value > 0 && value < 0.01) {
      formattedValue = value.toFixed(3).replace('.', ',');
    } else {
      formattedValue = formatNumberPtBR(value);
    }

    result = result.replace(tagPattern, formattedValue);
  }

  // Ensure all remaining tags are replaced, even if they don't have calculated values
  const remainingTagsPattern = /\(\(([^)]+)\)\)/g;
  result = result.replace(remainingTagsPattern, "0");

  // Process weight and age placeholders
  result = result.replace('{weight}', formatNumberPtBR(weight))
           .replace('{age}', age.toString());

  // Final check for any remaining UI/IU patterns that might need formatting
  result = result.replace(/(\d+(?:\.\d+)?)\s*(UI|IU)/g, (match, num, unit) => {
    return `${formatNumberPtBR(num)} ${unit}`;
  });

  return result;
}

// Check for dosage restrictions based on age and weight
function checkDosageRestrictions(tags: any[], weight: number, age: number, dosageTemplate: string): {
  hasRestriction: boolean;
  restrictionMessage?: string;
  restrictionType?: "age" | "weight" | "both";
  minAge?: number;
  minWeight?: number;
} {
  // CRITICAL FIX: If no dosage template is provided, return no restriction
  if (!dosageTemplate) {
    console.log("No dosage template provided, returning no restriction");
    return { hasRestriction: false };
  }

  // CRITICAL FIX: Filter tags to ONLY include those EXPLICITLY referenced in the dosage template
  // This ensures we only check restrictions for tags used in this specific dosage
  const relevantTags = [];
  for (const tag of tags) {
    const tagPattern = `((${tag.name}))`;
    if (dosageTemplate.includes(tagPattern)) {
      relevantTags.push(tag);
      console.log(`✅ Tag "${tag.name}" IS relevant for this dosage template`);
    } else {
      console.log(`❌ Tag "${tag.name}" is NOT relevant for this dosage template`);
    }
  }

  console.log(`Found ${relevantTags.length} relevant tags out of ${tags.length} total tags for dosage template`);
  console.log(`Dosage template preview: ${dosageTemplate.substring(0, 100)}...`);

  // If no relevant tags found, return no restriction
  if (relevantTags.length === 0) {
    console.log("No relevant tags found for this dosage template, returning no restriction");
    return { hasRestriction: false };
  }

  // Find age-based restrictions from relevant tags only
  const ageBasedTags = relevantTags.filter(tag =>
    (tag.type === 'age' || tag.type === 'multiplier_by_fixed_age') &&
    tag.start_month !== null &&
    tag.start_month > 0
  );

  // Find weight-based restrictions from relevant tags only
  const weightBasedTags = relevantTags.filter(tag =>
    tag.type === 'fixed_by_weight' &&
    tag.start_weight !== null &&
    tag.start_weight > 0
  );

  console.log(`Found ${ageBasedTags.length} age-based tags and ${weightBasedTags.length} weight-based tags`);
  if (ageBasedTags.length > 0) {
    console.log(`Age-based tags: ${JSON.stringify(ageBasedTags.map(t => ({ name: t.name, start_month: t.start_month })))}`);
  }
  if (weightBasedTags.length > 0) {
    console.log(`Weight-based tags: ${JSON.stringify(weightBasedTags.map(t => ({ name: t.name, start_weight: t.start_weight })))}`);
  }

  // CRITICAL FIX: If no age or weight based tags found, return no restriction
  if (ageBasedTags.length === 0 && weightBasedTags.length === 0) {
    console.log("No age or weight based tags found, returning no restriction");
    return { hasRestriction: false };
  }

  let minAgeRestriction: number | undefined;
  let minWeightRestriction: number | undefined;

  // Find the lowest start_month (minimum age)
  if (ageBasedTags.length > 0) {
    minAgeRestriction = Math.min(...ageBasedTags.map(tag => tag.start_month || Infinity));

    // Check if the current age is less than the minimum required
    if (age < minAgeRestriction) {
      if (weightBasedTags.length > 0) {
        minWeightRestriction = Math.min(...weightBasedTags.map(tag => tag.start_weight || Infinity));

        if (weight < minWeightRestriction) {
          return {
            hasRestriction: true,
            restrictionType: "both",
            restrictionMessage: formatRestrictionMessage("both", minAgeRestriction, minWeightRestriction),
            minAge: minAgeRestriction,
            minWeight: minWeightRestriction
          };
        }
      }

      return {
        hasRestriction: true,
        restrictionType: "age",
        restrictionMessage: formatRestrictionMessage("age", minAgeRestriction),
        minAge: minAgeRestriction
      };
    }
  }

  // Find the lowest start_weight (minimum weight)
  if (weightBasedTags.length > 0) {
    minWeightRestriction = Math.min(...weightBasedTags.map(tag => tag.start_weight || Infinity));

    // Check if the current weight is less than the minimum required
    if (weight < minWeightRestriction) {
      return {
        hasRestriction: true,
        restrictionType: "weight",
        restrictionMessage: formatRestrictionMessage("weight", undefined, minWeightRestriction),
        minWeight: minWeightRestriction
      };
    }
  }

  return { hasRestriction: false };
}

// Format restriction message based on type and values
function formatRestrictionMessage(type: "age" | "weight" | "both", minAge?: number, minWeight?: number): string {
  if (type === "age" && minAge) {
    return `Esta apresentação é indicada para pacientes a partir de ${formatAgeRestriction(minAge)} de idade.`;
  } else if (type === "weight" && minWeight) {
    return `Esta apresentação é indicada para pacientes com peso mínimo de ${minWeight}kg.`;
  } else if (type === "both" && minAge && minWeight) {
    return `Esta apresentação é indicada para pacientes a partir de ${formatAgeRestriction(minAge)} de idade e com peso mínimo de ${minWeight}kg.`;
  }

  return "";
}

// Convert months to a readable age format
function formatAgeRestriction(months: number): string {
  if (months < 12) {
    return `${months} ${months === 1 ? 'mês' : 'meses'}`;
  } else {
    const years = Math.floor(months / 12);
    const remainingMonths = months % 12;

    if (remainingMonths === 0) {
      return `${years} ${years === 1 ? 'ano' : 'anos'}`;
    } else {
      return `${years} ${years === 1 ? 'ano' : 'anos'} e ${remainingMonths} ${remainingMonths === 1 ? 'mês' : 'meses'}`;
    }
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const url = new URL(req.url)
    const path = url.pathname
    const params = url.searchParams

    // GET /api/v1/category
    if (path === '/api/v1/category') {
      const { data: categories, error } = await supabase
        .from('pedbook_medication_categories')
        .select(`
          id,
          name,
          description,
          color,
          icon_url,
          medications:pedbook_medications(count)
        `)
        .order('name', { ascending: true })

      if (error) throw error

      const formattedCategories = categories.map(category => ({
        ...category,
        medication_count: category.medications?.[0]?.count || 0
      }))

      return new Response(
        JSON.stringify(formattedCategories),
        { headers: corsHeaders }
      )
    }

    // GET /api/v1/medicine?category_id=uuid
    if (path === '/api/v1/medicine') {
      const categoryId = params.get('category_id')

      if (!categoryId) {
        return new Response(
          JSON.stringify({ error: 'category_id is required' }),
          { status: 400, headers: corsHeaders }
        )
      }

      const { data, error } = await supabase
        .from('pedbook_medications')
        .select(`
          id,
          name,
          description,
          brands,
          slug,
          measure_types,
          category:pedbook_medication_categories(
            id,
            name
          )
        `)
        .eq('category_id', categoryId)
        .order('name', { ascending: true })

      if (error) throw error

      return new Response(
        JSON.stringify(data),
        { headers: corsHeaders }
      )
    }

    // GET /api/v1/medicine/dosage?id=uuid&weight=10&age=24
    if (path === '/api/v1/medicine/dosage') {
      const id = params.get('id')
      const weight = parseFloat(params.get('weight') || '0')
      const age = parseInt(params.get('age') || '0')

      if (!id) {
        return new Response(
          JSON.stringify({ error: 'id is required' }),
          { status: 400, headers: corsHeaders }
        )
      }

      if (weight === 0 && age === 0) {
        return new Response(
          JSON.stringify({ error: 'weight or age is required' }),
          { status: 400, headers: corsHeaders }
        )
      }

      // Fetch medication data
      const { data: medication, error: medError } = await supabase
        .from('pedbook_medications')
        .select(`
          id,
          name,
          description,
          brands,
          contraindications,
          guidelines,
          pedbook_medication_dosages (
            id,
            name,
            type,
            summary,
            description,
            dosage_template,
            use_case:pedbook_medication_use_cases(
              name
            )
          )
        `)
        .eq('id', id)
        .single()

      if (medError) throw medError

      // Fetch tags for dosage calculations
      const { data: tags, error: tagsError } = await supabase
        .from('pedbook_medication_tags')
        .select('*')
        .eq('medication_id', id)

      if (tagsError) throw tagsError

      // Group dosages by use case
      const dosagesMap: Record<string, any[]> = {}

      // Calculate dosages with improved formatting
      await Promise.all(medication.pedbook_medication_dosages.map(async (dosage) => {
        console.log(`Processing dosage: ${dosage.name}`);
        console.log(`Dosage template: ${dosage.dosage_template.substring(0, 100)}...`);

        // Check for restrictions first, passing the dosage template to filter relevant tags
        const restrictions = checkDosageRestrictions(tags || [], weight, age, dosage.dosage_template);
        console.log(`Restrictions result for ${dosage.name}:`, JSON.stringify(restrictions));

        // Process the dosage template
        const calculatedDosage = processDosageTemplate(
          dosage.dosage_template,
          tags || [],
          weight,
          age
        );
        console.log(`Calculated dosage for ${dosage.name}: ${calculatedDosage}`);

        const useCase = dosage.use_case?.name || 'Outros'

        if (!dosagesMap[useCase]) {
          dosagesMap[useCase] = []
        }

        dosagesMap[useCase].push({
          name: dosage.name,
          calculated_dosage: calculatedDosage,
          summary: dosage.summary,
          description: dosage.description,
          restriction_message: restrictions.hasRestriction ? restrictions.restrictionMessage : null
        })
      }))

      return new Response(
        JSON.stringify({
          id: medication.id,
          name: medication.name,
          brands: medication.brands,
          description: medication.description,
          dosages: dosagesMap,
          contraindications: medication.contraindications,
          guidelines: medication.guidelines
        }),
        { headers: corsHeaders }
      )
    }

    return new Response(
      JSON.stringify({ error: 'Not Found' }),
      { status: 404, headers: corsHeaders }
    )

  } catch (error) {
    console.error('API error:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: corsHeaders }
    )
  }
})
