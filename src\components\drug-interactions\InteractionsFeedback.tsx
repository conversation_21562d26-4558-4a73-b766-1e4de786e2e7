
import React, { useEffect, useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { ThumbsUp, ThumbsDown, <PERSON>h, Heart } from "lucide-react";
import { Card } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";

interface InteractionsFeedbackProps {
  interactionId: string;
  medicationList: string[];
  analysisResult?: string; // Adicionar resultado completo da análise
}

const RATING_OPTIONS = [
  {
    value: 'excellent',
    label: 'Excelente',
    icon: Heart,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 hover:bg-blue-100',
  },
  {
    value: 'good',
    label: 'Bom',
    icon: ThumbsUp,
    color: 'text-green-500',
    bgColor: 'bg-green-50 hover:bg-green-100',
  },
  {
    value: 'regular',
    label: 'Regular',
    icon: Meh,
    color: 'text-yellow-500',
    bgColor: 'bg-yellow-50 hover:bg-yellow-100',
  },
  {
    value: 'poor',
    label: 'Ruim',
    icon: ThumbsDown,
    color: 'text-red-500',
    bgColor: 'bg-red-50 hover:bg-red-100',
  },
] as const;

type RatingOption = typeof RATING_OPTIONS[number]['value'];

export function InteractionsFeedback({ interactionId, medicationList, analysisResult }: InteractionsFeedbackProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [existingFeedback, setExistingFeedback] = useState<string | null>(null);
  const [feedbackStatus, setFeedbackStatus] = useState<string | null>(null);
  const [showCommentDialog, setShowCommentDialog] = useState(false);
  const [comment, setComment] = useState("");
  const [pendingRating, setPendingRating] = useState<RatingOption | null>(null);



  // Verificar se o usuário já forneceu feedback para esta análise
  useEffect(() => {
    const checkExistingFeedback = async () => {
      if (!user) return;

      try {

        const { data, error } = await supabase
          .from('pedbook_conducts_feedback')
          .select('rating')
          .eq('summary_id', interactionId)
          .eq('user_id', user.id)
          .eq('tipo_feedback', 'interactions')
          .maybeSingle();

        if (error) {

          return;
        }

        if (data) {
          setExistingFeedback(data.rating);
        }
      } catch (error) {

      }
    };

    checkExistingFeedback();
  }, [user, interactionId]);

  const askForComment = (rating: RatingOption) => {
    // Mostrar diálogo de comentários para classificações não excelentes
    if (rating !== 'excellent') {
      setPendingRating(rating);
      setShowCommentDialog(true);
    } else {
      handleFeedback(rating);
    }
  };

  const handleFeedback = async (rating: RatingOption, userComment?: string) => {
    if (!user) {
      setFeedbackStatus("É necessário estar logado para enviar feedback");
      return;
    }

    if (existingFeedback) {
      setFeedbackStatus("Você já avaliou esta análise anteriormente");
      return;
    }

    try {

      setFeedbackStatus("Enviando feedback...");

      // Salvamos com o resultado completo da análise, não apenas os nomes dos medicamentos
      const { error } = await supabase
        .from('pedbook_conducts_feedback')
        .insert({
          summary_id: interactionId,
          summary_title: analysisResult || medicationList.join(', '), // Usar o resultado completo ou fallback para a lista
          user_id: user.id,
          rating: rating,
          comment: userComment || null,
          tipo_feedback: 'interactions', // Identificando que é um feedback de interações
        });

      if (error) {

        if (error.code === '23505') {
          setFeedbackStatus("Você já avaliou esta análise anteriormente");
          return;
        }
        throw error;
      }

      setExistingFeedback(rating);
      setFeedbackStatus("Feedback enviado com sucesso!");

      // Limpar o status após 3 segundos
      setTimeout(() => {
        setFeedbackStatus(null);
      }, 3000);

    } catch (error: any) {

      setFeedbackStatus(`Erro ao enviar feedback: ${error.message}`);
    }
  };

  const handleCommentSubmit = () => {
    if (pendingRating) {
      handleFeedback(pendingRating, comment);
      setShowCommentDialog(false);
      setComment("");
      setPendingRating(null);
    }
  };

  // Se não houver ID de interação, não mostrar o componente
  if (!interactionId) {
    return null;
  }

  return (
    <div className="mt-6 border-t pt-6">
      <div className="text-center space-y-2">
        <h3 className="text-base font-medium text-gray-900 dark:text-gray-100">
          {existingFeedback ? 'Sua avaliação' : 'Esta análise foi útil para você?'}
        </h3>

        {feedbackStatus && (
          <div className={`text-sm px-4 py-2 rounded-md ${
            feedbackStatus.includes('Erro')
              ? 'bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-400'
              : 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'
          }`}>
            {feedbackStatus}
          </div>
        )}
      </div>

      <div className="mt-4 flex justify-center gap-3">
        {RATING_OPTIONS.map((option) => {
          const isSelected = existingFeedback === option.value;
          return (
            <Card
              key={option.value}
              className={`flex flex-col items-center justify-center p-2 cursor-${existingFeedback ? 'default' : 'pointer'} transition-all
                ${isSelected ? `${option.bgColor} ring-2 ring-${option.color}` : 'bg-gray-50'}
                ${!existingFeedback ? option.bgColor : ''}
                w-16 h-16`}
              onClick={() => !existingFeedback && askForComment(option.value)}
            >
              <option.icon className={`w-6 h-6 ${isSelected ? option.color : 'text-gray-400'}`} />
              <span className={`mt-1 text-xs font-medium ${isSelected ? 'text-gray-900' : 'text-gray-500'}`}>
                {option.label}
              </span>
            </Card>
          );
        })}
      </div>

      <Dialog open={showCommentDialog} onOpenChange={setShowCommentDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Deixe seu comentário</DialogTitle>
            <DialogDescription>
              Sua opinião é valiosa para melhorarmos nossas análises de interações medicamentosas.
              Por favor, compartilhe qualquer observação ou sugestão.
            </DialogDescription>
          </DialogHeader>
          <div className="my-4">
            <Textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Digite seu comentário aqui..."
              className="min-h-[100px]"
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setShowCommentDialog(false);
              if (pendingRating) {
                handleFeedback(pendingRating);
              }
            }}>
              Enviar sem comentário
            </Button>
            <Button onClick={handleCommentSubmit}>
              Enviar comentário
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
