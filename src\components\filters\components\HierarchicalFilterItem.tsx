import { ChevronRight, ChevronDown } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import type { FilterOption } from '../types';

interface HierarchicalFilterItemProps {
  item: FilterOption;
  level: number;
  isExpanded: boolean;
  isSelected: boolean;
  questionCount: number;
  hasChildren: boolean;
  onToggleExpand: (id: string) => void;
  onToggleSelect: (id: string, filterType: FilterOption['type']) => void;
}

export const HierarchicalFilterItem = ({
  item,
  level,
  isExpanded,
  isSelected,
  questionCount,
  hasChildren,
  onToggleExpand,
  onToggleSelect
}: HierarchicalFilterItemProps) => {
  const handleSelect = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleSelect(item.id, item.type);
  };

  return (
    <div 
      className={`
        flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors
        ${level > 0 ? 'ml-6' : ''}
        ${isSelected ? 'bg-gray-100' : ''}
      `}
    >
      {hasChildren && (
        <Button
          variant="ghost"
          size="sm"
          className="p-0 h-6 w-6"
          onClick={() => onToggleExpand(item.id)}
        >
          {isExpanded ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
        </Button>
      )}
      
      <Checkbox
        checked={isSelected}
        onCheckedChange={() => onToggleSelect(item.id, item.type)}
        className="data-[state=checked]:bg-primary"
      />
      
      <span className="flex-1 text-sm cursor-pointer" onClick={handleSelect}>
        {item.name}
      </span>
      
      <Badge variant="secondary" className="ml-2">
        {questionCount}
      </Badge>
    </div>
  );
};