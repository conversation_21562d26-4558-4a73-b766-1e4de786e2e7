
import { 
  Bar<PERSON>hart<PERSON>, 
  <PERSON>, 
  <PERSON>, 
  <PERSON>, 
  <PERSON>, 
  PillBottle, 
  Info, 
  TrendingUp
} from "lucide-react";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

export function PatientOverviewBanner() {
  
  const features = [
    { 
      icon: BarChart3, 
      title: "Crescimento", 
      description: "Avaliação das curvas de crescimento com percentis e status nutricional",
      color: "text-orange-500 dark:text-orange-400",
      bg: "bg-orange-100 dark:bg-orange-900/30"
    },
    { 
      icon: Shield, 
      title: "Vacinas", 
      description: "Verificação do status vacinal com calendário e recomendações",
      color: "text-purple-500 dark:text-purple-400",
      bg: "bg-purple-100 dark:bg-purple-900/30"
    },
    { 
      icon: Brain, 
      title: "DNPM", 
      description: "Avaliação dos marcos do desenvolvimento neuropsicomotor",
      color: "text-blue-500 dark:text-blue-400",
      bg: "bg-blue-100 dark:bg-blue-900/30"
    },
    { 
      icon: PillBottle, 
      title: "Suplementação", 
      description: "Recomendações personalizadas de suplementação vitamínica",
      color: "text-green-500 dark:text-green-400",
      bg: "bg-green-100 dark:bg-green-900/30"
    }
  ];

  return (
    <div className="mb-6">
      <div className="text-center mb-4">
        <h1 className="text-2xl md:text-4xl font-bold mb-2 text-primary dark:text-blue-400">
          Visão Geral do Paciente
        </h1>
        <p className="text-sm md:text-base text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Análise completa e personalizada do desenvolvimento infantil
        </p>
      </div>

      {/* Mobile: Carousel com design elegante */}
      <div className="md:hidden mx-auto max-w-md">
        <Carousel
          opts={{
            align: "start",
            loop: true,
          }}
          className="w-full"
        >
          <CarouselContent className="-ml-2">
            {features.map((feature, index) => (
              <CarouselItem key={index} className="pl-2 basis-4/5">
                <div className="bg-white dark:bg-slate-800 rounded-xl p-3 shadow-md border border-primary/10 transition-all hover:shadow-lg h-full">
                  <div className="flex items-start gap-3">
                    <div className={`${feature.bg} w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0`}>
                      <feature.icon className={`h-5 w-5 ${feature.color}`} />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-800 dark:text-white text-sm">{feature.title}</h3>
                      <p className="text-xs text-gray-600 dark:text-gray-300 mt-1 line-clamp-2">{feature.description}</p>
                    </div>
                  </div>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <div className="flex justify-center mt-4">
            <div className="flex gap-1">
              {features.map((_, index) => (
                <div 
                  key={index} 
                  className="w-2 h-2 rounded-full bg-gray-300 dark:bg-gray-600"
                />
              ))}
            </div>
          </div>
        </Carousel>
      </div>

      {/* Desktop: Grid */}
      <div className="hidden md:grid md:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4 max-w-5xl mx-auto">
        {features.map((feature, index) => (
          <div 
            key={index} 
            className="bg-white dark:bg-slate-800 rounded-xl p-4 shadow-md border border-primary/10 transition-all hover:shadow-lg"
          >
            <div className="flex flex-col">
              <div className={`${feature.bg} w-12 h-12 rounded-full flex items-center justify-center mb-3`}>
                <feature.icon className={`h-6 w-6 ${feature.color}`} />
              </div>
              <div>
                <h3 className="font-semibold text-gray-800 dark:text-white text-base">{feature.title}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{feature.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 mb-2 md:mt-6 bg-gradient-to-r from-primary/10 to-primary/5 dark:from-primary/20 dark:to-primary/10 p-3 md:p-4 rounded-lg border border-primary/20 max-w-5xl mx-auto">
        <div className="flex items-start gap-3">
          <div className="bg-primary/10 dark:bg-primary/20 p-1.5 md:p-2 rounded-full flex-shrink-0 mt-0.5">
            <Info className="h-4 w-4 md:h-5 md:w-5 text-primary dark:text-blue-400" />
          </div>
          <div>
            <h3 className="text-sm md:text-base font-medium text-primary dark:text-blue-400 mb-1">Como funciona</h3>
            <p className="text-xs md:text-sm text-gray-700 dark:text-gray-300">
              Preencha os dados do paciente abaixo para receber uma análise personalizada
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
