
import React from "react";

interface DiscursiveAIAnalysisProps {
  aiAnswer: string;
  feedback: string;
}

export const DiscursiveAIAnalysis: React.FC<DiscursiveAIAnalysisProps> = ({
  aiAnswer,
  feedback,
}) => {
  if (!aiAnswer && !feedback) return null;

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 mt-4 space-y-3">
      {aiAnswer && (
        <div>
          <h4 className="text-sm font-bold text-blue-900">Resposta do Especialista</h4>
          <p className="text-blue-900 whitespace-pre-line">{aiAnswer}</p>
        </div>
      )}
      {feedback && (
        <div>
          <h4 className="text-sm font-bold text-blue-800 mt-2">Análise sobre sua resposta</h4>
          <p className="text-blue-800 whitespace-pre-line">{feedback}</p>
        </div>
      )}
    </div>
  );
};
