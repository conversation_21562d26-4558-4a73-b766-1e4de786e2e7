import { useState, useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface UseCaseFormProps {
  medicationId: string;
  useCase?: {
    id: string;
    name: string;
    description?: string;
    display_order?: number;
  };
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function UseCaseForm({ medicationId, useCase, onSuccess, onCancel }: UseCaseFormProps) {
  const [name, setName] = useState(useCase?.name || "");
  const [description, setDescription] = useState(useCase?.description || "");
  const [displayOrder, setDisplayOrder] = useState(useCase?.display_order || 0);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (useCase) {
      setName(useCase.name);
      setDescription(useCase.description || "");
      setDisplayOrder(useCase.display_order || 0);
    }
  }, [useCase]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (useCase) {
        // Update existing use case
        const { error } = await supabase
          .from("pedbook_medication_use_cases")
          .update({
            name,
            description,
            display_order: displayOrder,
          })
          .eq("id", useCase.id);

        if (error) throw error;

        toast({
          title: "Indicação atualizada com sucesso!",
          description: "As alterações foram salvas.",
        });
      } else {
        // Get max display order for this medication
        const { data: maxOrderData, error: maxOrderError } = await supabase
          .from("pedbook_medication_use_cases")
          .select("display_order")
          .eq("medication_id", medicationId)
          .order("display_order", { ascending: false })
          .limit(1);

        if (maxOrderError) throw maxOrderError;

        const newDisplayOrder = maxOrderData && maxOrderData[0] 
          ? maxOrderData[0].display_order + 1 
          : 1;

        // Create new use case
        const { error } = await supabase
          .from("pedbook_medication_use_cases")
          .insert({
            medication_id: medicationId,
            name,
            description,
            display_order: newDisplayOrder,
          });

        if (error) throw error;

        toast({
          title: "Indicação criada com sucesso!",
          description: "A nova indicação de uso foi adicionada.",
        });
      }

      queryClient.invalidateQueries({ queryKey: ["medication-use-cases"] });
      onSuccess?.();
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: useCase ? "Erro ao atualizar indicação" : "Erro ao criar indicação",
        description: error.message,
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="name">Nome da Indicação</Label>
        <Input
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Ex: Escabiose"
          required
        />
      </div>

      <div>
        <Label htmlFor="description">Descrição</Label>
        <Textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Descreva a indicação de uso..."
        />
      </div>

      <div>
        <Label htmlFor="displayOrder">Ordem de Exibição</Label>
        <Input
          id="displayOrder"
          type="number"
          min="0"
          value={displayOrder}
          onChange={(e) => setDisplayOrder(parseInt(e.target.value))}
          placeholder="Ordem de exibição"
        />
      </div>

      <div className="flex justify-end gap-2">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancelar
          </Button>
        )}
        <Button type="submit">
          {useCase ? "Salvar Alterações" : "Criar Indicação"}
        </Button>
      </div>
    </form>
  );
}