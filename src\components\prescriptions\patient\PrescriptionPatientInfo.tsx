import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PrescriptionStatusIndicator } from "../PrescriptionStatusIndicator";
import { useAgeInput } from "@/hooks/useAgeInput";
import { Check } from "lucide-react";

interface PrescriptionPatientInfoProps {
  weight: string;
  age: string;
  isUpdating: boolean;
  onWeightChange: (weight: string) => void;
  onAgeChange: (age: string) => void;
}

export const PrescriptionPatientInfo = ({
  weight,
  age,
  isUpdating,
  onWeightChange,
  onAgeChange,
}: PrescriptionPatientInfoProps) => {
  const [showWeightConfirmation, setShowWeightConfirmation] = useState(false);
  const [showAgeConfirmation, setShowAgeConfirmation] = useState(false);
  const [displayAge, setDisplayAge] = useState("");

  const {
    unit,
    inputValue,
    handleUnitChange,
    handleChange,
    handleBlur
  } = useAgeInput({
    ageInMonths: parseInt(age) || 0,
    onChange: (value) => onAgeChange(value.toString()),
    onCommit: (value) => onAgeChange(value.toString())
  });

  useEffect(() => {
    const ageNum = parseInt(age) || 0;
    if (unit === "years") {
      setDisplayAge(`${Math.floor(ageNum / 12)} anos`);
    } else {
      setDisplayAge(`${ageNum} meses`);
    }
  }, [age, unit]);

  const handleWeightChange = (value: string) => {
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      const limitedValue = Math.min(numValue, 100);
      onWeightChange(limitedValue.toString());
      setShowWeightConfirmation(true);
      setTimeout(() => setShowWeightConfirmation(false), 2000);
    } else {
      onWeightChange(value);
    }
  };

  const handleAgeInputChange = (value: string) => {
    handleChange(value);
    setShowAgeConfirmation(true);
    setTimeout(() => setShowAgeConfirmation(false), 2000);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 bg-gradient-to-br from-primary/5 via-primary/10 to-transparent p-4 rounded-xl backdrop-blur-sm border border-primary/10">
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Label htmlFor="weight" className="text-sm font-medium text-primary/80">
            Peso (kg)
          </Label>
          <div className="h-4 w-4">
            {isUpdating ? (
              <PrescriptionStatusIndicator isPublic={false} />
            ) : showWeightConfirmation && (
              <Check className="h-4 w-4 text-green-500 animate-in fade-in" />
            )}
          </div>
          {weight && !isUpdating && (
            <span className="text-xs text-primary/60 ml-auto">
              {weight}kg aplicado
            </span>
          )}
        </div>
        <Input
          id="weight"
          type="number"
          value={weight}
          onChange={(e) => handleWeightChange(e.target.value)}
          min={0}
          max={100}
          step={0.1}
          className="bg-white/50 border-primary/20 focus:border-primary/40 transition-colors"
        />
      </div>
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Label htmlFor="age" className="text-sm font-medium text-primary/80">
            Idade
          </Label>
          <div className="h-4 w-4">
            {isUpdating ? (
              <PrescriptionStatusIndicator isPublic={false} />
            ) : showAgeConfirmation && (
              <Check className="h-4 w-4 text-green-500 animate-in fade-in" />
            )}
          </div>
          {age && !isUpdating && (
            <span className="text-xs text-primary/60 ml-auto">
              {displayAge} aplicado
            </span>
          )}
        </div>
        <div className="flex gap-2">
          <Input
            id="age"
            type="number"
            value={inputValue}
            onChange={(e) => handleAgeInputChange(e.target.value)}
            onBlur={handleBlur}
            min={0}
            max={unit === "years" ? 100 : 1200}
            className="flex-1 bg-white/50 border-primary/20 focus:border-primary/40 transition-colors"
          />
          <Select value={unit} onValueChange={(value: "months" | "years") => handleUnitChange(value)}>
            <SelectTrigger className="w-[110px] bg-white/50 border-primary/20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="months">meses</SelectItem>
              <SelectItem value="years">anos</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};