
import { Card } from "@/components/ui/card";
import { Info } from "lucide-react";
import { getThemeClasses } from "@/components/ui/theme-utils";

export const HydrationInfoCard = () => {
  return (
    <Card className={getThemeClasses.gradientCard("blue", "p-6")}>
      <div className="flex items-start gap-4">
        <Info className="h-6 w-6 text-primary dark:text-blue-400 mt-1 flex-shrink-0" />
        <div className="space-y-2">
          <h3 className="font-semibold text-gray-900 dark:text-gray-100">Hidratação de Manutenção</h3>
          <p className="text-sm text-gray-600 dark:text-gray-300">
          O manejo da hidratação venosa de manutenção consiste em administrar líquidos intravenosos de forma calculada para suprir as necessidades básicas de água e eletrólitos, ajustando volume e composição de acordo com o quadro clínico do paciente.
          </p>
        </div>
      </div>
    </Card>
  );
};
