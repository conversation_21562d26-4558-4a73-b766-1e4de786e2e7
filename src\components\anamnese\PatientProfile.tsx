
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { PatientData, TemplateType } from "./AnamneseForm";
import { User } from "lucide-react";

interface PatientProfileProps {
  data: PatientData;
  onChange: (data: Partial<PatientData>) => void;
  templateType: TemplateType;
}

export const PatientProfile: React.FC<PatientProfileProps> = ({ data, onChange, templateType }) => {
  const isPediatric = templateType === "pediatric_1_2m";

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5 text-primary" />
          Perfil do Paciente
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {isPediatric && (
            <div className="space-y-2">
              <Label htmlFor="name">Nome</Label>
              <Input
                id="name"
                placeholder="Nome do paciente"
                value={data.name || ""}
                onChange={(e) => onChange({ name: e.target.value })}
              />
            </div>
          )}
          
          <div className="space-y-2">
            <Label htmlFor="gender">Gênero</Label>
            <Select
              value={data.gender}
              onValueChange={(value: "male" | "female" | "other") => onChange({ gender: value })}
            >
              <SelectTrigger id="gender">
                <SelectValue placeholder="Selecione o gênero" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="male">Masculino</SelectItem>
                <SelectItem value="female">Feminino</SelectItem>
                <SelectItem value="other">Outro</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="age">Idade</Label>
            <div className="flex gap-2">
              <Input
                id="age"
                type="number"
                min={0}
                step={1}
                value={data.age || ""}
                onChange={(e) => onChange({ age: parseInt(e.target.value) || 0 })}
                className="flex-1"
              />
              <Select
                value={data.ageUnit}
                onValueChange={(value: "days" | "months" | "years") => onChange({ ageUnit: value })}
              >
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Unidade" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="days">Dias</SelectItem>
                  <SelectItem value="months">Meses</SelectItem>
                  <SelectItem value="years">Anos</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {isPediatric && (
            <>
              <div className="space-y-2">
                <Label htmlFor="birthDate">Data de Nascimento</Label>
                <Input
                  id="birthDate"
                  type="date"
                  value={data.birthDate || ""}
                  onChange={(e) => onChange({ birthDate: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="informant">Informante</Label>
                <Input
                  id="informant"
                  placeholder="Nome do informante e relação com o paciente"
                  value={data.informant || ""}
                  onChange={(e) => onChange({ informant: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="previousDiagnosis">Diagnósticos Prévios</Label>
                <Input
                  id="previousDiagnosis"
                  placeholder="Diagnósticos prévios, se houver"
                  value={data.previousDiagnosis || ""}
                  onChange={(e) => onChange({ previousDiagnosis: e.target.value })}
                />
              </div>

              <div className="space-y-2 flex items-center justify-between">
                <Label htmlFor="premature" className="cursor-pointer">Prematuro?</Label>
                <Switch
                  id="premature"
                  checked={data.premature || false}
                  onCheckedChange={(checked) => onChange({ premature: checked })}
                />
              </div>

              {data.premature && (
                <div className="space-y-2">
                  <Label htmlFor="correctedAge">Idade Corrigida</Label>
                  <Input
                    id="correctedAge"
                    placeholder="Idade corrigida para prematuridade"
                    value={data.correctedAge || ""}
                    onChange={(e) => onChange({ correctedAge: e.target.value })}
                  />
                </div>
              )}
            </>
          )}

          {!isPediatric && (
            <div className="space-y-2">
              <Label htmlFor="appointmentType">Tipo de Atendimento</Label>
              <Input
                id="appointmentType"
                placeholder="Ex: Consulta inicial, retorno, urgência"
                value={data.appointmentType}
                onChange={(e) => onChange({ appointmentType: e.target.value })}
              />
            </div>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="mainComplaint">
            {isPediatric ? "Queixas" : "Queixa Principal"}
          </Label>
          <Textarea
            id="mainComplaint"
            placeholder={isPediatric 
              ? "Descreva as queixas atuais"
              : "Descreva a queixa principal nas palavras do paciente"
            }
            value={data.mainComplaint}
            onChange={(e) => onChange({ mainComplaint: e.target.value })}
            className="min-h-[80px]"
          />
        </div>
      </CardContent>
    </Card>
  );
};
