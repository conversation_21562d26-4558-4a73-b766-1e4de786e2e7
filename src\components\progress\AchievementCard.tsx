import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Trophy, Star, Target, Brain, Clock } from "lucide-react";

interface AchievementCardProps {
  title: string;
  description: string;
  progress: number;
  total: number;
  type: 'streak' | 'accuracy' | 'speed' | 'mastery';
  isUnlocked: boolean;
}

const iconMap = {
  streak: Trophy,
  accuracy: Target,
  speed: Clock,
  mastery: Brain,
};

export const AchievementCard = ({
  title,
  description,
  progress,
  total,
  type,
  isUnlocked,
}: AchievementCardProps) => {
  const Icon = iconMap[type];
  const percentage = Math.round((progress / total) * 100);

  return (
    <Card className={`relative ${isUnlocked ? 'bg-gradient-to-br from-purple-50 to-white' : 'opacity-75'}`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          {title}
        </CardTitle>
        <Icon className={`h-4 w-4 ${isUnlocked ? 'text-purple-600' : 'text-gray-400'}`} />
      </CardHeader>
      <CardContent>
        <div className="text-xs text-muted-foreground mb-2">{description}</div>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="text-2xl font-bold">{progress}</div>
            <div className="text-xs text-muted-foreground">/ {total}</div>
          </div>
          <Badge variant={isUnlocked ? "default" : "secondary"}>
            {percentage}%
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
};