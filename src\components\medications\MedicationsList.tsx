
import React from "react";
import { MedicationCard } from "@/components/MedicationCard";

interface MedicationsListProps {
  medications: any[];
}

export const MedicationsList: React.FC<MedicationsListProps> = ({ medications }) => {
  
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {medications.map((medication, index) => (
        <div key={medication.id || index}>
          <MedicationCard
            id={medication.id}
            name={medication.name}
            category={medication.pedbook_medication_categories?.name || "Sem categoria"}
            slug={medication.slug}
          />
        </div>
      ))}
    </div>
  );
};
