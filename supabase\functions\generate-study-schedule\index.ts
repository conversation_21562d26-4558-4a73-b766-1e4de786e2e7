
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Helper function to format time properly
const formatTime = (hour: number): string => {
  const hours = Math.floor(hour);
  const minutes = Math.round((hour - hours) * 60);
  return `${hours}:${minutes.toString().padStart(2, '0')}`;
};

// Helper function to format duration properly
const formatDuration = (durationInHours: number): string => {
  const hours = Math.floor(durationInHours);
  const minutes = Math.round((durationInHours - hours) * 60);
  return `${hours}:${minutes.toString().padStart(2, '0')}`;
};

interface StudyTopic {
  specialty: string;
  theme: string;
  focus: string;
  difficulty: 'Fácil' | 'Médio' | 'Difícil';
  activity: string;
  startTime: string;
  duration: string;
  isExtraLearning?: boolean;
}

interface DaySchedule {
  day: string;
  totalHours: number;
  topics: StudyTopic[];
}

interface RequestData {
  performanceData: {
    availability: {
      hoursPerDay: number;
      availableDays: string[];
    };
    specialtyAnalysis: Array<{
      name: string;
      accuracy: number;
      total: number;
    }>;
    themeAnalysis: Array<{
      name: string;
      accuracy: number;
      total: number;
    }>;
    focusAnalysis: Array<{
      name: string;
      accuracy: number;
      total: number;
    }>;
  };
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log('📊 [generate-study-schedule] Starting schedule generation');
    
    const requestData: RequestData = await req.json();
    console.log('📊 [generate-study-schedule] Received request data:', requestData);

    if (!requestData?.performanceData?.availability) {
      console.error('❌ [generate-study-schedule] Missing availability data in request');
      throw new Error('Missing availability data in request');
    }

    const { availability } = requestData.performanceData;
    console.log('📊 [generate-study-schedule] Processing availability:', availability);

    if (!availability.availableDays || !Array.isArray(availability.availableDays)) {
      console.error('❌ [generate-study-schedule] Invalid availableDays format:', availability.availableDays);
      throw new Error('Invalid availableDays format');
    }

    if (!availability.hoursPerDay || typeof availability.hoursPerDay !== 'number') {
      console.error('❌ [generate-study-schedule] Invalid hoursPerDay format:', availability.hoursPerDay);
      throw new Error('Invalid hoursPerDay format');
    }

    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Buscar métricas de performance do usuário
    const { data: metrics, error: metricsError } = await supabase
      .from('study_performance_metrics')
      .select(`
        *,
        specialty:study_categories!study_performance_metrics_specialty_id_fkey(name),
        theme:study_categories!study_performance_metrics_theme_id_fkey(name),
        focus:study_categories!study_performance_metrics_focus_id_fkey(name)
      `)
      .order('last_study_date', { ascending: true });

    if (metricsError) {
      console.error('❌ [generate-study-schedule] Error fetching metrics:', metricsError);
      throw metricsError;
    }

    // Buscar temas não estudados para recomendações extras
    const { data: newTopics, error: newTopicsError } = await supabase
      .from('study_categories')
      .select(`
        id,
        name,
        type,
        parent_id
      `)
      .order('id', { ascending: false }) // Changed from random() to a deterministic order
      .limit(50); // Limit the results and we'll randomize in memory

    if (newTopicsError) {
      console.error('❌ [generate-study-schedule] Error fetching new topics:', newTopicsError);
      throw newTopicsError;
    }

    // Randomize the topics in memory instead of in the query
    const shuffleArray = <T>(array: T[]): T[] => {
      for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
      }
      return array;
    };

    // Organizar e randomizar temas não estudados por tipo
    const shuffledTopics = shuffleArray(newTopics);
    const newSpecialties = shuffledTopics.filter(t => t.type === 'specialty');
    const newThemes = shuffledTopics.filter(t => t.type === 'theme');
    const newFocuses = shuffledTopics.filter(t => t.type === 'focus');

    console.log('📈 [generate-study-schedule] Retrieved performance metrics:', metrics);

    // Classificar tópicos por prioridade
    const prioritizedTopics = metrics.map(metric => {
      const accuracy = metric.correct_answers / metric.total_questions * 100;
      const daysSinceLastStudy = metric.last_study_date
        ? Math.floor((Date.now() - new Date(metric.last_study_date).getTime()) / (1000 * 60 * 60 * 24))
        : 30;

      const priorityScore = (100 - accuracy) * 0.6 + 
                           (daysSinceLastStudy) * 0.4;

      return {
        specialty: metric.specialty?.name || '',
        theme: metric.theme?.name || '',
        focus: metric.focus?.name || '',
        accuracy,
        daysSinceLastStudy,
        priorityScore,
        avgResponseTime: metric.avg_response_time
      };
    }).sort((a, b) => b.priorityScore - a.priorityScore);

    console.log('🎯 [generate-study-schedule] Prioritized topics:', prioritizedTopics);

    // Função para gerar um tópico extra de aprendizado
    const generateExtraTopic = (startTime: string): StudyTopic | null => {
      if (newSpecialties.length === 0 || newThemes.length === 0 || newFocuses.length === 0) return null;

      const randomSpecialty = newSpecialties[Math.floor(Math.random() * newSpecialties.length)];
      const randomTheme = newThemes[Math.floor(Math.random() * newThemes.length)];
      const randomFocus = newFocuses[Math.floor(Math.random() * newFocuses.length)];

      return {
        specialty: randomSpecialty.name,
        theme: randomTheme.name,
        focus: randomFocus.name,
        difficulty: 'Médio',
        activity: 'Exploração de Novo Conteúdo 🌟',
        startTime,
        duration: "0:30", // Formato padronizado
        isExtraLearning: true
      };
    };

    // Gerar cronograma baseado na disponibilidade
    const schedule: DaySchedule[] = availability.availableDays.map((day: string) => {
      const dayTopics: StudyTopic[] = [];
      let currentTime = 9; // Começar às 9h
      let remainingHours = availability.hoursPerDay;
      let topicIndex = 0;
      let dayTotalHours = 0;

      while (remainingHours > 0 && (prioritizedTopics.length > topicIndex || remainingHours >= 0.5)) {
        // Alternar entre tópicos prioritários e extras
        let topic: StudyTopic;

        if (topicIndex % 3 === 2) { // A cada 3 tópicos, inserir um tópico extra
          const extraTopic = generateExtraTopic(formatTime(currentTime));
          
          if (extraTopic) {
            dayTopics.push(extraTopic);
            const durationInHours = 0.5;  // 30 minutos
            currentTime += durationInHours;
            remainingHours -= durationInHours;
            dayTotalHours += durationInHours;
            continue;
          }
        }

        if (topicIndex < prioritizedTopics.length) {
          const priorityTopic = prioritizedTopics[topicIndex];
          const durationMinutes = priorityTopic.accuracy < 50 ? 30 : 15;
          const durationInHours = durationMinutes / 60;
          
          topic = {
            specialty: priorityTopic.specialty,
            theme: priorityTopic.theme,
            focus: priorityTopic.focus,
            difficulty: priorityTopic.accuracy < 50 ? 'Difícil' : 
                  priorityTopic.accuracy < 80 ? 'Médio' : 'Fácil',
            activity: priorityTopic.accuracy < 50 ? 
                'Estudo Teórico + Resolver Questões' : 
                'Revisão com Questões',
            startTime: formatTime(currentTime),
            duration: formatDuration(durationInHours) // Formato padronizado
          };

          dayTopics.push(topic);
          currentTime += durationInHours;
          remainingHours -= durationInHours;
          dayTotalHours += durationInHours;
        }

        topicIndex++;
      }

      return {
        day,
        totalHours: dayTotalHours,
        topics: dayTopics
      };
    });

    console.log('📅 [generate-study-schedule] Generated schedule:', schedule);

    return new Response(
      JSON.stringify({ recommendations: schedule }),
      { 
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );

  } catch (error) {
    console.error('❌ [generate-study-schedule] Error:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );
  }
});
