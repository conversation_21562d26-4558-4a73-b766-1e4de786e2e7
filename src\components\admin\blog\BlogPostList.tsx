import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { Pencil, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

export const BlogPostList = ({ 
  onEdit, 
  onDelete 
}: { 
  onEdit: (post: any) => void;
  onDelete: (post: any) => void;
}) => {
  const navigate = useNavigate();
  const { data: posts, isLoading } = useQuery({
    queryKey: ['admin-blog-posts'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_blog_posts')
        .select(`
          *,
          author:profiles(full_name),
          pedbook_blog_categories(name)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
  });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="grid gap-4">
      {posts?.map((post) => (
        <div
          key={post.id}
          className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow space-y-2"
        >
          <div className="flex justify-between items-start">
            <div className="space-y-1">
              <h3 className="font-semibold text-lg">{post.title}</h3>
              <p className="text-sm text-gray-500">
                por {post.author?.full_name} em{" "}
                {new Date(post.created_at).toLocaleDateString()}
              </p>
              {post.pedbook_blog_categories?.name && (
                <span className="inline-block bg-primary/10 text-primary text-xs px-2 py-1 rounded">
                  {post.pedbook_blog_categories.name}
                </span>
              )}
            </div>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => onEdit(post)}
              >
                <Pencil className="h-4 w-4 mr-2" />
                Editar
              </Button>
              <Button 
                variant="destructive" 
                size="sm"
                onClick={() => onDelete(post)}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Excluir
              </Button>
            </div>
          </div>
          <div className="flex justify-between items-center mt-4">
            <span className={`text-sm ${post.published ? 'text-green-600' : 'text-yellow-600'}`}>
              {post.published ? 'Publicado' : 'Rascunho'}
            </span>
            <Button
              variant="link"
              onClick={() => navigate(`/blog/post/${post.id}`)}
              className="text-primary hover:text-primary/80"
            >
              Visualizar post
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
};