
import { Shield, MessageSquare, Settings, LogOut, HeadphonesIcon, Phone, MessageCircle } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { useNavigate } from "react-router-dom";
import { Badge } from "../ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import md5 from "md5";
import type { User } from "@supabase/supabase-js";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "../ui/dialog";
import { useState } from "react";
import { FeedbackPage } from "@/pages/feedback/FeedbackPage";
import { useNotification } from "@/context/NotificationContext";
import { cn } from "@/lib/utils";

interface HeaderActionsProps {
  user: User | null;
  profile: any;
  isAdmin: boolean;
  onFeedbackClick: () => void;
  onLogout: () => void;
}

export const HeaderActions = ({
  user,
  profile,
  isAdmin,
  onFeedbackClick,
  onLogout
}: HeaderActionsProps) => {
  const navigate = useNavigate();
  const [showFeedbackDialog, setShowFeedbackDialog] = useState(false);
  const [showSupportDialog, setShowSupportDialog] = useState(false);
  const { showNotification } = useNotification();

  const getGravatarUrl = (email: string) => {
    const hash = md5(email.toLowerCase().trim());
    return `https://www.gravatar.com/avatar/${hash}?d=mp`;
  };

  const handleLogout = async () => {
    onLogout();
    showNotification({
      title: "Logout realizado com sucesso",
      description: "Você foi desconectado da sua conta.",
      type: "success",
      buttonText: "Continuar",
      onButtonClick: () => navigate("/")
    });
  };

  const handleFeedbackClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setShowFeedbackDialog(true);
    // Still call the prop callback in case parent components need to know
    onFeedbackClick();
  };

  const handleSupportClick = () => {
    setShowSupportDialog(true);
  };

  const handleWhatsAppContact = () => {
    const phoneNumber = "5564993198433"; // Número com código do país
    const message = encodeURIComponent("Olá! Preciso de suporte com o PedBook.");
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${message}`;
    window.open(whatsappUrl, '_blank');
    setShowSupportDialog(false);
  };

  return (
    <div className="flex items-center gap-2 shrink-0">
      {isAdmin && (
        <Button
          variant="outline"
          className="flex items-center gap-2 bg-gradient-to-r from-primary/10 to-transparent hover:from-primary/20"
          onClick={() => navigate("/admin/dashboard")}
        >
          <Shield className="h-4 w-4" />
          <span className="hidden sm:inline">Painel Admin</span>
          <Badge variant="secondary" className="ml-2 bg-primary/20">
            Admin
          </Badge>
        </Button>
      )}

      {user && (
        <DropdownMenu>
          <DropdownMenuTrigger className="focus:outline-none">
            <div className="flex items-center gap-2 p-1.5 rounded-full bg-gradient-to-r from-primary/10 to-transparent hover:from-primary/20 transition-colors">
              <Avatar>
                <AvatarImage
                  src={profile?.avatar_url || getGravatarUrl(user.email || '')}
                  alt={profile?.full_name || 'User'}
                />
                <AvatarFallback>
                  {(profile?.full_name || 'U').charAt(0)}
                </AvatarFallback>
              </Avatar>
              <span className="hidden sm:block text-sm font-medium">
                {profile?.full_name}
              </span>
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            className={cn(
              "w-64 p-2 bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl",
              "border border-gray-200/50 dark:border-slate-700/50 shadow-xl",
              "rounded-xl animate-in fade-in-0 zoom-in-95 duration-200"
            )}
          >
            {/* Header do Menu */}
            <div className="px-3 py-2 mb-2">
              <div className="flex items-center gap-3">
                <Avatar className="h-8 w-8 ring-2 ring-primary/20">
                  <AvatarImage
                    src={profile?.avatar_url || getGravatarUrl(user.email || '')}
                    alt={profile?.full_name || 'User'}
                  />
                  <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                    {(profile?.full_name || 'U').charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-semibold text-gray-900 dark:text-gray-100 truncate">
                    {profile?.full_name || 'Usuário'}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                    {user.email}
                  </p>
                </div>
              </div>
            </div>

            <DropdownMenuSeparator className="bg-gray-200/50 dark:bg-slate-700/50" />

            {/* Menu Items */}
            <div className="space-y-1 py-1">
              <DropdownMenuItem
                onClick={() => navigate('/settings')}
                className={cn(
                  "flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer",
                  "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white",
                  "hover:bg-gray-100/80 dark:hover:bg-slate-800/80 transition-all duration-200",
                  "focus:bg-gray-100/80 dark:focus:bg-slate-800/80"
                )}
              >
                <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-blue-100 dark:bg-blue-900/30">
                  <Settings className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Configurações</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Gerencie sua conta</p>
                </div>
              </DropdownMenuItem>

              <DropdownMenuItem
                onClick={handleSupportClick}
                className={cn(
                  "flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer",
                  "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white",
                  "hover:bg-gray-100/80 dark:hover:bg-slate-800/80 transition-all duration-200",
                  "focus:bg-gray-100/80 dark:focus:bg-slate-800/80"
                )}
              >
                <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-green-100 dark:bg-green-900/30">
                  <HeadphonesIcon className="h-4 w-4 text-green-600 dark:text-green-400" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Suporte</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Fale conosco via WhatsApp</p>
                </div>
              </DropdownMenuItem>

              <DropdownMenuItem
                onClick={handleFeedbackClick}
                className={cn(
                  "flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer",
                  "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white",
                  "hover:bg-gray-100/80 dark:hover:bg-slate-800/80 transition-all duration-200",
                  "focus:bg-gray-100/80 dark:focus:bg-slate-800/80"
                )}
              >
                <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-purple-100 dark:bg-purple-900/30">
                  <MessageSquare className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Feedback</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Envie sugestões</p>
                </div>
              </DropdownMenuItem>
            </div>

            <DropdownMenuSeparator className="bg-gray-200/50 dark:bg-slate-700/50 my-2" />

            {/* Logout */}
            <DropdownMenuItem
              onClick={handleLogout}
              className={cn(
                "flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer",
                "text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300",
                "hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-200",
                "focus:bg-red-50 dark:focus:bg-red-900/20"
              )}
            >
              <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-red-100 dark:bg-red-900/30">
                <LogOut className="h-4 w-4 text-red-600 dark:text-red-400" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium">Sair</p>
                <p className="text-xs text-red-500/70 dark:text-red-400/70">Desconectar da conta</p>
              </div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      {/* Support Dialog */}
      <Dialog open={showSupportDialog} onOpenChange={setShowSupportDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mb-4">
              <MessageCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <DialogTitle className="text-xl font-semibold">Precisa de Ajuda?</DialogTitle>
            <DialogDescription className="text-gray-600 dark:text-gray-400">
              Entre em contato conosco via WhatsApp para suporte rápido e personalizado.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="bg-gray-50 dark:bg-slate-800/50 rounded-lg p-4">
              <div className="flex items-center gap-3 mb-2">
                <Phone className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Número de Suporte
                </span>
              </div>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                (64) 99319-8433
              </p>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
              <div className="flex items-start gap-3">
                <MessageCircle className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5" />
                <div>
                  <h4 className="font-medium text-green-800 dark:text-green-300 mb-1">
                    Horário de Atendimento
                  </h4>
                  <p className="text-sm text-green-700 dark:text-green-400">
                    Segunda a Sexta: 8h às 18h<br />
                    Sábado: 8h às 12h
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => setShowSupportDialog(false)}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button
              onClick={handleWhatsAppContact}
              className="flex-1 bg-green-600 hover:bg-green-700 text-white"
            >
              <MessageCircle className="h-4 w-4 mr-2" />
              Abrir WhatsApp
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Feedback Dialog */}
      {user && (
        <Dialog
          open={showFeedbackDialog}
          onOpenChange={(value) => {
            setShowFeedbackDialog(value);
            // Ensure pointer events are re-enabled when dialog closes
            if (!value) {
              document.body.style.pointerEvents = 'auto';
            }
          }}
        >
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto dark:bg-slate-800">
            <DialogHeader>
              <DialogTitle>Feedback</DialogTitle>
            </DialogHeader>
            <FeedbackPage />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};
