import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useNotes } from "@/hooks/useNotes";
import { NoteEditor } from "@/components/notes/NoteEditor";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Save } from "lucide-react";
import { toast } from "sonner";

const NoteDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { notes, updateNote } = useNotes();
  const [content, setContent] = useState("");
  const [isEditing, setIsEditing] = useState(false);

  const note = notes?.find(note => note.id === id);

  useEffect(() => {
    if (note) {
      setContent(note.content);
    }
  }, [note]);

  if (!note) {
    return <div>Nota não encontrada</div>;
  }

  const handleSave = async () => {
    try {
      await updateNote.mutateAsync({
        id: note.id,
        content
      });
      toast.success("Nota atualizada com sucesso!");
      setIsEditing(false);
    } catch (error) {
      toast.error("Erro ao atualizar nota");
    }
  };

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      <div className="flex items-center justify-between mb-6">
        <Button
          variant="ghost"
          className="flex items-center gap-2"
          onClick={() => navigate("/notes")}
        >
          <ArrowLeft className="h-4 w-4" />
          Voltar
        </Button>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setIsEditing(!isEditing)}
          >
            {isEditing ? "Cancelar" : "Editar"}
          </Button>
          
          {isEditing && (
            <Button
              onClick={handleSave}
              className="flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              Salvar
            </Button>
          )}
        </div>
      </div>

      {isEditing ? (
        <NoteEditor content={content} onChange={setContent} />
      ) : (
        <div 
          className="prose max-w-none border rounded-lg p-6"
          dangerouslySetInnerHTML={{ __html: content }}
        />
      )}
    </div>
  );
};

export default NoteDetail;