
import { motion } from "framer-motion";
import { FileText, Plus, Stethoscope, ClipboardList } from "lucide-react";

export const WelcomeHeader = () => {
  return (
    <>
      <h2 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent dark:from-blue-400 dark:to-blue-600">
        Bem-vindo ao Sistema de Prescrições PedBook!
      </h2>
      <div className="space-y-4 text-gray-600 dark:text-gray-300">
        <p className="text-base md:text-lg">
          O PedBook é uma plataforma especializada para profissionais de saúde gerenciarem suas prescrições médicas de forma eficiente e segura.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 py-4">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="flex flex-col items-center p-4 bg-white/50 dark:bg-slate-800/50 rounded-lg shadow-sm"
          >
            <div className="h-10 w-10 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center mb-3">
              <Plus className="h-6 w-6 text-primary dark:text-blue-400" />
            </div>
            <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Passo 1</h3>
            <p className="text-sm text-center dark:text-gray-300">
              Clique no botão "Nova Prescrição" abaixo
            </p>
          </motion.div>

          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="flex flex-col items-center p-4 bg-white/50 dark:bg-slate-800/50 rounded-lg shadow-sm"
          >
            <div className="h-10 w-10 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center mb-3">
              <Stethoscope className="h-6 w-6 text-primary dark:text-blue-400" />
            </div>
            <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Passo 2</h3>
            <p className="text-sm text-center dark:text-gray-300">
              Selecione os medicamentos e configure as dosagens
            </p>
          </motion.div>

          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="flex flex-col items-center p-4 bg-white/50 dark:bg-slate-800/50 rounded-lg shadow-sm"
          >
            <div className="h-10 w-10 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center mb-3">
              <ClipboardList className="h-6 w-6 text-primary dark:text-blue-400" />
            </div>
            <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Passo 3</h3>
            <p className="text-sm text-center dark:text-gray-300">
              Revise e salve sua prescrição
            </p>
          </motion.div>
        </div>

        <ul className="space-y-2 text-center list-none text-sm md:text-base dark:text-gray-300">
          <li>Crie e organize suas prescrições personalizadas</li>
          <li>Mantenha um histórico organizado de suas prescrições</li>
        </ul>
      </div>
    </>
  );
};
