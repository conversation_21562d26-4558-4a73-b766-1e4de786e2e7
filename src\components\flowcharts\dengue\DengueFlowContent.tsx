
import { useState } from "react";
import { motion } from "framer-motion";
import { DengueQuestion } from "./DengueQuestion";
import { DengueResult } from "./DengueResult";
import { ArrowLeft, Info } from "lucide-react";
import { WeightAgeForm } from "./WeightAgeForm";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { getThemeClasses } from "@/components/ui/theme-utils";

export const DengueFlowContent = () => {
  const [step, setStep] = useState("intro");
  const [patientData, setPatientData] = useState({
    weight: 0,
    age: 0
  });
  const [answers, setAnswers] = useState<Record<string, boolean>>({});
  const [selectedAnswer, setSelectedAnswer] = useState<boolean | null>(null);

  console.log("Renderizando DengueFlowContent, step atual:", step);
  console.log("Histórico de respostas:", answers);

  const handleSetPatientData = (weight: number, age: number) => {
    setPatientData({ weight, age });
    setStep("question1");
  };

  const handleAnswer = (answer: boolean) => {
    console.log(`Resposta para ${step}:`, answer);
    setSelectedAnswer(answer);
    
    setTimeout(() => {
      const newAnswers = { ...answers, [step]: answer };
      setAnswers(newAnswers);

      // Lógica de navegação baseada nas respostas
      if (step === "question1") {
        if (answer) setStep("questionA1");
        else setStep("questionB1");
      } 
      else if (step === "questionA1") {
        if (answer) setStep("resultA1");
        else setStep("questionA2");
      }
      else if (step === "questionA2") {
        if (answer) setStep("resultA2");
        else setStep("resultA3");
      }
      else if (step === "questionB1") {
        if (answer) setStep("questionB2");
        else setStep("questionB3");
      }
      else if (step === "questionB2") {
        if (answer) setStep("resultB1");
        else setStep("resultB2");
      }
      else if (step === "questionB3") {
        if (answer) setStep("resultB3");
        else setStep("resultB4");
      }
      
      setSelectedAnswer(null);
    }, 300);
  };

  const goToStep = (nextStep: string) => {
    console.log(`Navegando para o step: ${nextStep}`);
    setStep(nextStep);
  };

  const resetFlow = () => {
    setStep("intro");
    setAnswers({});
    setPatientData({ weight: 0, age: 0 });
  };

  const renderStep = () => {
    switch (step) {
      case "intro":
        return (
          <div className="space-y-6 animate-slide-in-up">
            <Card className={getThemeClasses.gradientCard("orange", "p-6")}>
              <div className="flex items-start gap-4">
                <div className="mt-1">
                  <Info className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                </div>
                <div className="space-y-4">
                  <h3 className="font-semibold text-lg text-orange-800 dark:text-orange-300">
                    Fluxograma para manejo de casos suspeitos de Dengue
                  </h3>
                  <p className="text-gray-700 dark:text-gray-300">
                    Este fluxograma é baseado nas diretrizes do Ministério da Saúde para classificação de risco e manejo do paciente com suspeita de dengue.
                  </p>
                  <div className="text-gray-700 dark:text-gray-300 space-y-2">
                    <p className="font-medium">Considerar caso suspeito de dengue quando o paciente apresentar:</p>
                    <ul className="list-disc list-inside pl-2 space-y-1 text-sm">
                      <li>Febre de início súbito e sem causa aparente</li>
                      <li>No mínimo dois dos seguintes sintomas: náusea/vômitos, exantema, mialgias, artralgia, cefaleia, dor retroorbital, petéquias ou prova do laço positiva, leucopenia</li>
                      <li>Histórico de epidemia na região ou deslocamento para área endêmica nos últimos 14 dias</li>
                    </ul>
                  </div>
                </div>
              </div>
            </Card>

            <WeightAgeForm onSubmit={handleSetPatientData} />
          </div>
        );

      case "question1":
        return (
          <DengueQuestion
            question="O paciente apresenta algum dos sinais de alarme ou gravidade?"
            onAnswer={handleAnswer}
            selectedAnswer={selectedAnswer}
          />
        );

      case "questionA1":
        return (
          <DengueQuestion
            question="O paciente apresenta algum sinal de choque?"
            onAnswer={handleAnswer}
            selectedAnswer={selectedAnswer}
          />
        );

      case "questionA2":
        return (
          <DengueQuestion
            question="O paciente apresenta sangramento grave?"
            onAnswer={handleAnswer}
            selectedAnswer={selectedAnswer}
          />
        );

      case "questionB1":
        return (
          <DengueQuestion
            question="O paciente apresenta alguma condição clínica especial, comorbidade ou risco social?"
            onAnswer={handleAnswer}
            selectedAnswer={selectedAnswer}
          />
        );

      case "questionB2":
        return (
          <DengueQuestion
            question="O paciente apresenta sangramento espontâneo ou induzido (prova do laço positiva)?"
            onAnswer={handleAnswer}
            selectedAnswer={selectedAnswer}
          />
        );

      case "questionB3":
        return (
          <DengueQuestion
            question="O paciente tem hematócrito normal e boa condição clínica?"
            onAnswer={handleAnswer}
            selectedAnswer={selectedAnswer}
          />
        );

      case "resultA1":
        return (
          <DengueResult
            group="GRUPO D - INTERNAÇÃO OBRIGATÓRIA"
            color="bg-red-50"
            instructions={[
              "O paciente deve ser imediatamente transferido para leito de internação em unidade de maior complexidade.",
              "Iniciar hidratação IV imediata 20 mL/kg em 1 hora. Após, monitorar hematócrito e sinais de choque.",
              "Colher exames: Hemograma, eletrólitos, transaminases, RX Tórax ou USG Abdome e outros conforme necessidade clínica.",
              "Monitorizar sinais vitais continuamente, incluindo pressão arterial, perfusão periférica e diurese.",
              "Solicitar hemoderivados conforme necessidade e discutir o caso com especialista.",
              "O manejo deve ser realizado por equipe experiente em serviço com suporte avançado."
            ]}
            onReset={resetFlow}
            weight={patientData.weight}
            age={patientData.age}
          />
        );

      case "resultA2":
        return (
          <DengueResult
            group="GRUPO C - INTERNAÇÃO OBRIGATÓRIA"
            color="bg-orange-50"
            instructions={[
              "O paciente deve ser internado em leito de internação.",
              "Iniciar hidratação IV imediata (soro fisiológico): 20 mL/kg em até 20 minutos. Após, avaliar e repetir conforme necessário.",
              "Colher exames: Hemograma, eletrólitos, transaminases, albumina, RX Tórax ou USG Abdome.",
              "Monitorizar hematócrito a cada 2-4 horas até estabilização.",
              "Monitorizar sinais vitais (2-4h), incluindo diurese, sinais de alarme e sangramentos.",
              "Avaliar e tratar sangramentos graves, discutindo o caso com especialista.",
              "Seguir a taxa de hidratação conforme reavaliações clínicas."
            ]}
            onReset={resetFlow}
            weight={patientData.weight}
            age={patientData.age}
          />
        );

      case "resultA3":
        return (
          <DengueResult
            group="GRUPO C - INTERNAÇÃO OBRIGATÓRIA"
            color="bg-orange-50"
            instructions={[
              "O paciente deve ser internado para monitoramento constante.",
              "Iniciar hidratação IV imediata (soro fisiológico): 20 mL/kg em até 20 minutos. Após, avaliar e repetir conforme necessário.",
              "Colher exames: Hemograma, eletrólitos, transaminases, albumina, RX Tórax ou USG Abdome.",
              "Monitorizar hematócrito a cada 2-4 horas até estabilização.",
              "Monitorizar sinais vitais (2-4h), incluindo diurese, sinais de alarme e sangramentos.",
              "Reavaliar periodicamente os parâmetros hemodinâmicos.",
              "Seguir a taxa de hidratação conforme reavaliações clínicas."
            ]}
            onReset={resetFlow}
            weight={patientData.weight}
            age={patientData.age}
          />
        );

      case "resultB1":
        return (
          <DengueResult
            group="GRUPO B - ACOMPANHAMENTO EM UNIDADE DE SAÚDE"
            color="bg-yellow-50"
            instructions={[
              "O paciente deve permanecer na unidade de saúde para hidratação oral ou venosa supervisionada por no mínimo 6 horas.",
              "Iniciar hidratação oral supervisionada ou parenteral:",
              "- 80 mL/kg/dia sendo 1/3 no posto e 2/3 em casa (oral)",
              "- Solução salina (2-4 mL/kg/hora) se necessário (venosa)",
              "Realizar hemograma e avaliar hematócrito, plaquetas e leucograma.",
              "Orientar retorno para reavaliação clínica e laboratorial em 24h.",
              "Orientar sobre sinais de alarme e hidratação oral em domicílio.",
              "Notificar o caso e programar retorno no dia seguinte."
            ]}
            onReset={resetFlow}
            weight={patientData.weight}
            age={patientData.age}
            showHydration={true}
          />
        );

      case "resultB2":
        return (
          <DengueResult
            group="GRUPO B - ACOMPANHAMENTO EM UNIDADE DE SAÚDE"
            color="bg-yellow-50"
            instructions={[
              "O paciente deve permanecer na unidade de saúde para hidratação oral ou venosa supervisionada por no mínimo 6 horas.",
              "Iniciar hidratação oral supervisionada ou parenteral:",
              "- 80 mL/kg/dia sendo 1/3 no posto e 2/3 em casa (oral)",
              "- Solução salina (2-4 mL/kg/hora) se necessário (venosa)",
              "Realizar hemograma e avaliar hematócrito, plaquetas e leucograma.",
              "Orientar retorno para reavaliação clínica e laboratorial em 24h.",
              "Avaliar condições clínicas e de suporte familiar para hidratação em domicílio.",
              "Notificar o caso e programar retorno no dia seguinte."
            ]}
            onReset={resetFlow}
            weight={patientData.weight}
            age={patientData.age}
            showHydration={true}
          />
        );

      case "resultB3":
        return (
          <DengueResult
            group="GRUPO A - ATENDIMENTO AMBULATORIAL"
            color="bg-green-50"
            instructions={[
              "O paciente pode ser acompanhado em regime ambulatorial.",
              "Orientar hidratação oral (80 mL/kg/dia).",
              "Prescrever sintomáticos se necessário (evitar AINEs e AAS).",
              "Orientar sobre sinais de alarme: dor abdominal, vômitos persistentes, sangramentos, hipotensão, confusão mental, etc.",
              "Orientar retorno imediato aos serviços de urgência na presença de sinais de alarme.",
              "Realizar o cartão de acompanhamento da dengue.",
              "Notificar o caso suspeito e agendar retorno para reavaliação."
            ]}
            onReset={resetFlow}
            weight={patientData.weight}
            age={patientData.age}
            showHydration={true}
          />
        );

      case "resultB4":
        return (
          <DengueResult
            group="GRUPO A - ATENDIMENTO AMBULATORIAL"
            color="bg-green-50"
            instructions={[
              "O paciente pode ser acompanhado em regime ambulatorial, se tiver boas condições clínicas e hematócrito normal.",
              "Orientar hidratação oral abundante (80 mL/kg/dia).",
              "Prescrever sintomáticos se necessário (evitar AINEs e AAS).",
              "Orientar sobre sinais de alarme: dor abdominal, vômitos persistentes, sangramentos, hipotensão, confusão mental, etc.",
              "Orientar retorno imediato aos serviços de urgência na presença de sinais de alarme.",
              "Realizar o cartão de acompanhamento da dengue.",
              "Notificar o caso suspeito e agendar retorno para reavaliação em 24-48h."
            ]}
            onReset={resetFlow}
            weight={patientData.weight}
            age={patientData.age}
            showHydration={true}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen")}>
      <div className="max-w-3xl mx-auto px-4 py-6">
        <div className="mb-6 flex justify-between items-center">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center gap-2"
          >
            {step !== "intro" && (
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={resetFlow}
                className="hover:bg-orange-100 dark:hover:bg-orange-900/30"
              >
                <ArrowLeft className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              </Button>
            )}
            <div>
              <h2 className="text-2xl font-semibold text-orange-700 dark:text-orange-400">
                Fluxograma de Dengue
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Classificação de risco e manejo clínico
              </p>
            </div>
          </motion.div>
          {patientData.weight > 0 && (
            <div className="text-right text-sm text-gray-600 dark:text-gray-400">
              <p>Peso: {patientData.weight} kg</p>
              <p>Idade: {patientData.age} anos</p>
            </div>
          )}
        </div>

        {renderStep()}
      </div>
    </div>
  );
};
