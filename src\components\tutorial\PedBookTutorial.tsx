import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Brain, 
  Building2, 
  Calendar, 
  FileQuestion, 
  ArrowRight, 
  CheckCircle, 
  Sparkles, 
  Target,
  Play,
  X,
  ChevronRight
} from "lucide-react";

type TutorialStep = {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  badge?: string;
  demo?: React.ReactNode;
};

interface PedBookTutorialProps {
  isOpen: boolean;
  onClose: () => void;
  onStartStudy: () => void;
}

export const PedBookTutorial: React.FC<PedBookTutorialProps> = ({ 
  isOpen, 
  onClose, 
  onStartStudy 
}) => {
  const [currentStep, setCurrentStep] = useState(0);

  const tutorialSteps: TutorialStep[] = [
    {
      title: "🎯 Bem-vindo ao PedBook!",
      description: "Vamos te mostrar como usar os filtros inteligentes para estudar pediatria de forma eficiente. Este tutorial rápido vai te ensinar tudo que você precisa saber!",
      icon: Target,
      color: "from-blue-500 to-indigo-600",
      badge: "INÍCIO"
    },
    {
      title: "🧠 Especialidades → Temas → Focos",
      description: "A hierarquia mais importante! Navegue por ESPECIALIDADE → TEMA → FOCO. No PedBook, você terá acesso apenas à pediatria, mas com toda a profundidade de temas e focos específicos.",
      icon: Brain,
      color: "from-yellow-500 to-orange-600",
      badge: "HIERÁRQUICO",
      demo: (
        <div className="bg-gray-50 rounded-lg p-4 space-y-2">
          <div className="flex items-center gap-2 text-sm">
            <ChevronRight className="h-4 w-4 text-blue-600" />
            <span className="font-medium text-blue-600">Pediatria</span>
          </div>
          <div className="ml-6 space-y-1">
            <div className="flex items-center gap-2 text-sm">
              <ChevronRight className="h-3 w-3 text-purple-600" />
              <span className="text-purple-600">Cardiologia Pediátrica</span>
            </div>
            <div className="ml-4 text-xs text-gray-600">
              • Cardiopatias Congênitas
              <br />
              • Arritmias Pediátricas
            </div>
          </div>
        </div>
      )
    },
    {
      title: "🏥 Instituições",
      description: "Filtre por instituições específicas como UNIFESP, USP, UFMG e outras. Ideal para focar em bancas que você mais estuda ou que caem na sua prova de residência.",
      icon: Building2,
      color: "from-blue-500 to-cyan-600",
      demo: (
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="grid grid-cols-2 gap-2 text-xs">
            <Badge variant="outline" className="justify-center">UNIFESP</Badge>
            <Badge variant="outline" className="justify-center">USP</Badge>
            <Badge variant="outline" className="justify-center">UFMG</Badge>
            <Badge variant="outline" className="justify-center">UFRJ</Badge>
          </div>
        </div>
      )
    },
    {
      title: "📅 Anos das Provas",
      description: "Selecione anos específicos das provas. Questões mais recentes refletem tendências atuais, enquanto questões antigas testam conceitos consolidados da pediatria.",
      icon: Calendar,
      color: "from-green-500 to-emerald-600",
      demo: (
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex flex-wrap gap-2">
            {[2024, 2023, 2022, 2021].map(year => (
              <Badge key={year} variant="outline" className="text-xs">{year}</Badge>
            ))}
          </div>
        </div>
      )
    },
    {
      title: "🎯 Formato de Questão",
      description: "Filtre por formato: questões objetivas, discursivas, com imagens, casos clínicos. Personalize conforme seu estilo de estudo em pediatria.",
      icon: FileQuestion,
      color: "from-pink-500 to-rose-600",
      demo: (
        <div className="bg-gray-50 rounded-lg p-4 space-y-2">
          <div className="text-xs space-y-1">
            <div>✓ Questões Objetivas</div>
            <div>✓ Casos Clínicos</div>
            <div>✓ Com Imagens</div>
          </div>
        </div>
      )
    },
    {
      title: "🎲 Opções de Estudo",
      description: "Após filtrar, escolha: estudar questões FILTRADAS (seguindo seus critérios) ou um MIX ALEATÓRIO (variado para testar conhecimentos gerais de pediatria).",
      icon: Sparkles,
      color: "from-indigo-500 to-purple-600",
      badge: "FINAL",
      demo: (
        <div className="space-y-2">
          <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700">
            📋 Estudar Filtradas
          </Button>
          <Button size="sm" variant="outline" className="w-full">
            🎲 Mix Aleatório
          </Button>
        </div>
      )
    }
  ];

  const handleNext = () => {
    if (currentStep < tutorialSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onClose();
      onStartStudy();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  if (!isOpen) return null;

  const currentStepData = tutorialSteps[currentStep];

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4"
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0, y: 20 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.9, opacity: 0, y: 20 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          className="w-full max-w-lg"
        >
          <Card className="overflow-hidden border-2 border-gray-100 shadow-2xl">
            {/* Header com gradiente */}
            <div className={`bg-gradient-to-r ${currentStepData.color} p-6 text-white relative`}>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="absolute top-4 right-4 text-white hover:bg-white/20 h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
              
              <div className="flex items-center gap-3 mb-2">
                <div className="bg-white/20 p-2 rounded-lg">
                  <currentStepData.icon className="h-5 w-5" />
                </div>
                {currentStepData.badge && (
                  <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                    {currentStepData.badge}
                  </Badge>
                )}
              </div>
              
              <h3 className="text-xl font-bold">{currentStepData.title}</h3>
              <div className="text-sm text-white/80 mt-1">
                Passo {currentStep + 1} de {tutorialSteps.length}
              </div>
            </div>

            <CardContent className="p-6 space-y-4">
              <p className="text-gray-700 leading-relaxed">
                {currentStepData.description}
              </p>

              {/* Demo visual */}
              {currentStepData.demo && (
                <div className="border rounded-lg p-4 bg-gray-50">
                  <div className="text-xs text-gray-500 mb-2 font-medium">Exemplo:</div>
                  {currentStepData.demo}
                </div>
              )}

              {/* Barra de progresso */}
              <div className="w-full bg-gray-200 rounded-full h-2">
                <motion.div
                  className={`bg-gradient-to-r ${currentStepData.color} h-2 rounded-full`}
                  initial={{ width: 0 }}
                  animate={{ width: `${((currentStep + 1) / tutorialSteps.length) * 100}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>

              {/* Botões */}
              <div className="flex justify-between items-center pt-2">
                <Button
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={currentStep === 0}
                  className="px-4"
                >
                  Anterior
                </Button>

                <div className="text-xs text-gray-500">
                  {currentStep === 0 ? "Vamos começar!" :
                   currentStep === tutorialSteps.length - 1 ? "Pronto para estudar!" :
                   "Continue explorando"}
                </div>

                <Button
                  onClick={handleNext}
                  className={`bg-gradient-to-r ${currentStepData.color} hover:opacity-90 text-white font-medium px-6 flex items-center gap-2`}
                >
                  {currentStep < tutorialSteps.length - 1 ? (
                    <>
                      Próximo
                      <ArrowRight className="h-4 w-4" />
                    </>
                  ) : (
                    <>
                      Começar!
                      <Play className="h-4 w-4" />
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
