import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

// Função para converter dados para CSV
export const exportToCSV = (data: any[], filename: string) => {
  if (!data || data.length === 0) {
    alert("Nenhum dado disponível para exportação");
    return;
  }

  // Obter as chaves do primeiro objeto para criar o cabeçalho
  const headers = Object.keys(data[0]);
  
  // Criar o conteúdo CSV
  const csvContent = [
    // Cabeçalho
    headers.join(','),
    // Dados
    ...data.map(row => 
      headers.map(header => {
        const value = row[header];
        // Escapar valores que contêm vírgulas ou aspas
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      }).join(',')
    )
  ].join('\n');

  // C<PERSON>r e baixar o arquivo
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}_${format(new Date(), 'yyyy-MM-dd_HH-mm')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

// Função para exportar dados de analytics completos
export const exportAnalyticsData = async (
  overview: any,
  topMedications: any[],
  topCategories: any[],
  searchTerms: any[],
  userAnalytics: any,
  dateRange: { from?: Date; to?: Date }
) => {
  const period = dateRange.from && dateRange.to 
    ? `${format(dateRange.from, "dd-MM-yyyy", { locale: ptBR })}_${format(dateRange.to, "dd-MM-yyyy", { locale: ptBR })}`
    : "periodo_completo";

  // Exportar overview
  if (overview) {
    const overviewData = [{
      periodo_inicio: dateRange.from ? format(dateRange.from, "dd/MM/yyyy", { locale: ptBR }) : "N/A",
      periodo_fim: dateRange.to ? format(dateRange.to, "dd/MM/yyyy", { locale: ptBR }) : "N/A",
      total_eventos: overview.total_events || 0,
      sessoes_unicas: overview.unique_sessions || 0,
      usuarios_unicos: overview.unique_users || 0,
      visualizacoes_medicamentos: overview.medication_views || 0,
      calculos_dosagem: overview.dosage_calculations || 0,
      buscas: overview.searches || 0,
      visualizacoes_categorias: overview.category_views || 0
    }];
    
    exportToCSV(overviewData, `analytics_overview_${period}`);
  }

  // Exportar top medicamentos
  if (topMedications && topMedications.length > 0) {
    const medicationsData = topMedications.map((med, index) => ({
      ranking: index + 1,
      medicamento: med.name,
      categoria: med.category_name,
      visualizacoes: med.views,
      calculos: med.calculations,
      sessoes_unicas: med.unique_sessions
    }));
    
    exportToCSV(medicationsData, `top_medicamentos_${period}`);
  }

  // Exportar top categorias
  if (topCategories && topCategories.length > 0) {
    const categoriesData = topCategories.map((cat, index) => ({
      ranking: index + 1,
      categoria: cat.name,
      visualizacoes: cat.views,
      sessoes_unicas: cat.unique_sessions,
      medicamentos_visualizados: cat.unique_medications_viewed
    }));
    
    exportToCSV(categoriesData, `top_categorias_${period}`);
  }

  // Exportar termos de busca
  if (searchTerms && searchTerms.length > 0) {
    const searchData = searchTerms.map((term, index) => ({
      ranking: index + 1,
      termo_busca: term.search_term,
      quantidade_buscas: term.search_count,
      sessoes_unicas: term.unique_sessions
    }));
    
    exportToCSV(searchData, `termos_busca_${period}`);
  }

  // Exportar analytics de usuários
  if (userAnalytics) {
    const userData = [{
      periodo_inicio: dateRange.from ? format(dateRange.from, "dd/MM/yyyy", { locale: ptBR }) : "N/A",
      periodo_fim: dateRange.to ? format(dateRange.to, "dd/MM/yyyy", { locale: ptBR }) : "N/A",
      total_sessoes: userAnalytics.total_sessions || 0,
      usuarios_registrados: userAnalytics.registered_users || 0,
      sessoes_anonimas: userAnalytics.anonymous_sessions || 0,
      eventos_por_sessao: userAnalytics.avg_events_per_session || 0
    }];
    
    exportToCSV(userData, `analytics_usuarios_${period}`);
  }
};

// Função para exportar dados específicos
export const exportSpecificData = (data: any[], type: string, dateRange: { from?: Date; to?: Date }) => {
  if (!data || data.length === 0) {
    alert("Nenhum dado disponível para exportação");
    return;
  }

  const period = dateRange.from && dateRange.to 
    ? `${format(dateRange.from, "dd-MM-yyyy", { locale: ptBR })}_${format(dateRange.to, "dd-MM-yyyy", { locale: ptBR })}`
    : "periodo_completo";

  exportToCSV(data, `${type}_${period}`);
};
