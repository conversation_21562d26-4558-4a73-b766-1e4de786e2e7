
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { motion } from "framer-motion";
import { getThemeClasses } from "@/components/ui/theme-utils";
import { Card } from "@/components/ui/card";

interface WeightAgeFormProps {
  onSubmit: (weight: number, age: number) => void;
}

export const WeightAgeForm: React.FC<WeightAgeFormProps> = ({ onSubmit }) => {
  const [weight, setWeight] = useState<string>("");
  const [age, setAge] = useState<string>("");
  const [errors, setErrors] = useState<{weight?: string; age?: string}>({});

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate inputs
    const newErrors: {weight?: string; age?: string} = {};
    
    if (!weight.trim() || isNaN(Number(weight)) || Number(weight) <= 0) {
      newErrors.weight = "Peso inválido";
    }
    
    if (!age.trim() || isNaN(Number(age)) || Number(age) < 0) {
      newErrors.age = "Idade inválida";
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    onSubmit(Number(weight), Number(age));
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
    >
      <Card className={getThemeClasses.gradientCard("blue", "p-6")}>
        <h3 className="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-100">
          Informações do Paciente
        </h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="weight" className="text-gray-700 dark:text-gray-300">
              Peso (kg)
            </Label>
            <Input
              id="weight"
              type="number"
              step="0.1"
              min="0"
              value={weight}
              onChange={(e) => {
                setWeight(e.target.value);
                setErrors({...errors, weight: undefined});
              }}
              className="mt-1 bg-white/80 dark:bg-slate-800/80"
              placeholder="Ex: 25.5"
            />
            {errors.weight && (
              <p className="text-sm text-red-500 mt-1">{errors.weight}</p>
            )}
          </div>

          <div>
            <Label htmlFor="age" className="text-gray-700 dark:text-gray-300">
              Idade (anos)
            </Label>
            <Input
              id="age"
              type="number"
              step="0.1"
              min="0"
              value={age}
              onChange={(e) => {
                setAge(e.target.value);
                setErrors({...errors, age: undefined});
              }}
              className="mt-1 bg-white/80 dark:bg-slate-800/80"
              placeholder="Ex: 8.5"
            />
            {errors.age && (
              <p className="text-sm text-red-500 mt-1">{errors.age}</p>
            )}
          </div>

          <Button 
            type="submit"
            className="w-full mt-4 bg-orange-500 hover:bg-orange-600 text-white"
          >
            Iniciar Avaliação
          </Button>
        </form>
      </Card>
    </motion.div>
  );
};
