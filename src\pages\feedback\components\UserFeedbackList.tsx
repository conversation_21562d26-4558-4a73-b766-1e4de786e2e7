import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Card } from "@/components/ui/card";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { FeedbackResponseSection } from "./FeedbackResponseSection";
import { FeedbackStatusBadge } from "./FeedbackStatusBadge";
import { motion } from "framer-motion";

interface Feedback {
  id: string;
  type_id: string;
  message: string;
  status: string;
  created_at: string;
  title: string;
  responses: FeedbackResponse[];
}

interface FeedbackResponse {
  id: string;
  message: string;
  created_at: string;
  user: {
    full_name: string;
    is_admin: boolean;
  };
}

export function UserFeedbackList() {
  const [feedbacks, setFeedbacks] = useState<Feedback[]>([]);
  const [loading, setLoading] = useState(true);
  const [newResponses, setNewResponses] = useState<{ [key: string]: string }>({});
  const { user } = useAuth();
  const { toast } = useToast();

  const fetchFeedbacks = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from("pedbook_feedbacks")
        .select(`
          *,
          responses:pedbook_feedback_responses(
            id,
            message,
            created_at,
            user:profiles!pedbook_feedback_responses_user_profiles_fkey(
              full_name,
              is_admin
            )
          )
        `)
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching feedbacks:", error);
        toast({
          title: "Erro ao carregar feedbacks",
          description: error.message,
          variant: "destructive",
        });
        return;
      }

      if (data) {
        const transformedData = data.map(feedback => ({
          ...feedback,
          responses: feedback.responses.map(response => ({
            ...response,
            user: response.user || { full_name: "Usuário", is_admin: false }
          }))
        }));
        setFeedbacks(transformedData);
      }
    } catch (error: any) {
      console.error("Error in fetchFeedbacks:", error);
      toast({
        title: "Erro ao carregar feedbacks",
        description: "Ocorreu um erro ao carregar os feedbacks",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFeedbacks();
  }, [user]);

  const handleSendResponse = async (feedbackId: string) => {
    if (!user) return;
    
    const message = newResponses[feedbackId];
    if (!message?.trim()) return;

    try {
      const { error } = await supabase
        .from("pedbook_feedback_responses")
        .insert({
          feedback_id: feedbackId,
          message: message.trim(),
          user_id: user.id
        });

      if (error) throw error;

      toast({
        title: "Resposta enviada",
        description: "Sua resposta foi enviada com sucesso!"
      });

      setNewResponses(prev => ({ ...prev, [feedbackId]: "" }));
      fetchFeedbacks();
    } catch (error: any) {
      console.error("Error sending response:", error);
      toast({
        title: "Erro ao enviar resposta",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="animate-pulse text-primary">Carregando...</div>
      </div>
    );
  }

  if (feedbacks.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center p-8 bg-gradient-to-br from-white via-primary/5 to-white rounded-lg border border-primary/20"
      >
        <p className="text-gray-500">Você ainda não enviou nenhum feedback.</p>
      </motion.div>
    );
  }

  return (
    <div className="space-y-6">
      {feedbacks.map((feedback, index) => (
        <motion.div
          key={feedback.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
        >
          <Card className="overflow-hidden bg-gradient-to-br from-white via-primary/5 to-white border border-primary/20 shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div className="p-6 space-y-6">
              <div className="space-y-2">
                <div className="flex justify-between items-start">
                  <div className="space-y-1">
                    <h3 className="text-xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70">
                      {feedback.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">{feedback.message}</p>
                  </div>
                  <FeedbackStatusBadge status={feedback.status} />
                </div>
              </div>

              <div className="border-t border-primary/10 pt-6">
                <h4 className="font-medium text-gray-800 mb-4">Respostas</h4>
                <FeedbackResponseSection 
                  responses={feedback.responses}
                  feedbackId={feedback.id}
                  isResolved={feedback.status === "resolvido"}
                  newResponse={newResponses[feedback.id] || ""}
                  onResponseChange={(value) => setNewResponses(prev => ({
                    ...prev,
                    [feedback.id]: value
                  }))}
                  onSendResponse={() => handleSendResponse(feedback.id)}
                />
              </div>
            </div>
          </Card>
        </motion.div>
      ))}
    </div>
  );
}