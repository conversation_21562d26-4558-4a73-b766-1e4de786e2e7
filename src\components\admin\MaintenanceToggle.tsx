import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON>ch,
  Power,
  AlertTriangle,
  Clock,
  MessageSquare,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useMaintenanceMode } from '@/hooks/useMaintenanceMode';
import { useToast } from '@/hooks/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

const MaintenanceToggle: React.FC = () => {
  const { maintenanceStatus, isMaintenanceActive, isSuperAdmin, toggleMaintenance } = useMaintenanceMode();
  const { toast } = useToast();



  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState(maintenanceStatus?.message || '');
  const [estimatedDuration, setEstimatedDuration] = useState(maintenanceStatus?.estimated_duration || '');

  // Atualizar campos quando maintenanceStatus mudar
  React.useEffect(() => {
    if (maintenanceStatus) {
      setMessage(maintenanceStatus.message || '');
      setEstimatedDuration(maintenanceStatus.estimated_duration || '');
    }
  }, [maintenanceStatus]);

  const handleToggle = async (newState: boolean) => {
    if (!isSuperAdmin) {
      toast({
        title: "Acesso negado",
        description: "Apenas super admin pode alterar o modo de manutenção",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      await toggleMaintenance(
        newState,
        message || 'Site em manutenção. Voltaremos em breve!',
        estimatedDuration
      );

      toast({
        title: newState ? "Manutenção ativada" : "Manutenção desativada",
        description: newState
          ? "Todos os usuários serão redirecionados para a página de manutenção"
          : "O site voltou ao funcionamento normal",
        variant: newState ? "destructive" : "default",
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Não foi possível alterar o modo de manutenção",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isSuperAdmin) {
    return null;
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Wrench className="h-5 w-5" />
          Modo de Manutenção
        </CardTitle>
        <CardDescription>
          Controle o acesso ao site durante manutenções e atualizações
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Status atual */}
        <div className="flex items-center justify-between p-4 rounded-lg border bg-muted/50">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-full ${isMaintenanceActive ? 'bg-red-100 text-red-600' : 'bg-green-100 text-green-600'}`}>
              {isMaintenanceActive ? <XCircle className="h-4 w-4" /> : <CheckCircle className="h-4 w-4" />}
            </div>
            <div>
              <p className="font-medium">
                {isMaintenanceActive ? 'Site em Manutenção' : 'Site Funcionando'}
              </p>
              <p className="text-sm text-muted-foreground">
                {isMaintenanceActive
                  ? 'Usuários estão sendo redirecionados'
                  : 'Todos os usuários têm acesso normal'
                }
              </p>
            </div>
          </div>

          <motion.div
            animate={{ scale: isMaintenanceActive ? [1, 1.1, 1] : 1 }}
            transition={{ duration: 2, repeat: isMaintenanceActive ? Infinity : 0 }}
          >
            <Power className={`h-6 w-6 ${isMaintenanceActive ? 'text-red-500' : 'text-green-500'}`} />
          </motion.div>
        </div>

        {/* Configurações */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="message" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Mensagem para usuários
            </Label>
            <Textarea
              id="message"
              placeholder="Digite a mensagem que será exibida na página de manutenção..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="duration" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Duração estimada (opcional)
            </Label>
            <Input
              id="duration"
              placeholder="Ex: 2 horas, 30 minutos, etc."
              value={estimatedDuration}
              onChange={(e) => setEstimatedDuration(e.target.value)}
            />
          </div>
        </div>

        {/* Toggle de ativação */}
        <div className="flex items-center justify-between p-4 rounded-lg border">
          <div className="space-y-1">
            <p className="font-medium">Ativar Modo de Manutenção</p>
            <p className="text-sm text-muted-foreground">
              Redireciona todos os usuários (exceto você) para a página de manutenção
            </p>
          </div>

          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Switch
                checked={isMaintenanceActive}
                disabled={isLoading}
              />
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-amber-500" />
                  {isMaintenanceActive ? 'Desativar' : 'Ativar'} Modo de Manutenção
                </AlertDialogTitle>
                <AlertDialogDescription>
                  {isMaintenanceActive
                    ? 'Tem certeza que deseja desativar o modo de manutenção? O site voltará ao funcionamento normal.'
                    : 'Tem certeza que deseja ativar o modo de manutenção? Todos os usuários serão redirecionados para a página de manutenção.'
                  }
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => handleToggle(!isMaintenanceActive)}
                  className={isMaintenanceActive ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}
                >
                  {isMaintenanceActive ? 'Desativar' : 'Ativar'}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>

        {/* Informações adicionais */}
        {maintenanceStatus?.activated_at && (
          <div className="text-sm text-muted-foreground p-3 bg-muted/30 rounded-lg">
            <p>
              <strong>Última alteração:</strong> {new Date(maintenanceStatus.activated_at).toLocaleString('pt-BR')}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default MaintenanceToggle;
