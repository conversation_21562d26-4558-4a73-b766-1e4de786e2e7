import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

interface MedicineState {
  medicineId?: string;
  medicineName?: string;
  weight?: number;
  step: 'IDENTIFY_MEDICINE' | 'GET_WEIGHT' | 'SHOW_RESULT';
}

interface ChatRequest {
  message: string;
  state?: MedicineState;
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  // Criar cliente Supabase para verificar autenticação
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    {
      global: {
        headers: { Authorization: req.headers.get('Authorization')! },
      },
    }
  );

  // Verificar autenticação do usuário
  const {
    data: { user },
    error: authError,
  } = await supabaseClient.auth.getUser();

  if (authError || !user) {
    console.error("❌ Erro de autenticação:", authError?.message || "Usuário não autenticado");
    return new Response(
      JSON.stringify({
        error: "Não autorizado",
        message: "Você precisa estar autenticado para usar esta função"
      }),
      {
        status: 401,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  }

  try {
    const { message, state } = await req.json() as ChatRequest

    // Criar cliente Supabase com permissões de serviço para operações de banco de dados
    const serviceClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Estado inicial se não fornecido
    const currentState: MedicineState = state || { step: 'IDENTIFY_MEDICINE' }

    let response: any = {}

    switch (currentState.step) {
      case 'IDENTIFY_MEDICINE': {


        // Buscar medicamento pelo nome
        const { data: medications, error } = await serviceClient
          .from('pedbook_medications')
          .select('id, name, description')
          .ilike('name', `%${message}%`)
          .limit(1)

        if (error) throw error

        if (!medications || medications.length === 0) {
          return new Response(
            JSON.stringify({
              message: "Desculpe, não encontrei esse medicamento. Pode verificar o nome e tentar novamente?",
              state: currentState
            }),
            {
              headers: { ...corsHeaders, 'Content-Type': 'application/json' },
              status: 200
            }
          )
        }

        const medicine = medications[0]
        response = {
          message: `Encontrei o medicamento ${medicine.name}. Por favor, me informe o peso do paciente em kg.`,
          state: {
            medicineId: medicine.id,
            medicineName: medicine.name,
            step: 'GET_WEIGHT'
          }
        }
        break
      }

      case 'GET_WEIGHT': {


        // Extrair número do texto
        const weightMatch = message.match(/\d+/)
        if (!weightMatch) {
          return new Response(
            JSON.stringify({
              message: "Por favor, informe apenas o número do peso em kg (exemplo: 10).",
              state: currentState
            }),
            {
              headers: { ...corsHeaders, 'Content-Type': 'application/json' },
              status: 200
            }
          )
        }

        const weight = parseInt(weightMatch[0])

        // Buscar dosagens do medicamento
        const { data: medication, error } = await serviceClient
          .from('pedbook_medications')
          .select(`
            id,
            name,
            description,
            contraindications,
            guidelines,
            pedbook_medication_use_cases (
              id,
              name,
              description,
              pedbook_medication_dosages (*)
            )
          `)
          .eq('id', currentState.medicineId)
          .single()

        if (error) throw error

        response = {
          message: `Medicamento ${currentState.medicineName}, peso do paciente: ${weight}kg`,
          medication,
          state: {
            ...currentState,
            weight,
            step: 'SHOW_RESULT'
          }
        }
        break
      }

      default:
        response = {
          message: "Vamos começar novamente. Qual medicamento você quer consultar?",
          state: { step: 'IDENTIFY_MEDICINE' }
        }
    }

    return new Response(
      JSON.stringify(response),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {

    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})