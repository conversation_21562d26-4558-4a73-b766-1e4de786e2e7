import { SymptomCategory } from "./symptomData";

interface SymptomListProps {
  categories: SymptomCategory[];
  selectedSymptoms: string[];
  onSymptomToggle: (symptom: string) => void;
}

export function SymptomList({ categories, selectedSymptoms, onSymptomToggle }: SymptomListProps) {
  return (
    <div className="space-y-6">
      {categories.map((category) => (
        <div key={category.name} className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-800 bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent">
            {category.name}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
            {category.symptoms.map((symptom) => (
              <div
                key={symptom}
                onClick={() => onSymptomToggle(symptom)}
                className={`p-3 rounded-lg border-2 cursor-pointer transition-all duration-300 hover:shadow-md ${
                  selectedSymptoms.includes(symptom)
                    ? "border-primary bg-primary/10 shadow"
                    : "border-gray-200 hover:border-primary/50"
                }`}
              >
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 flex-shrink-0 rounded-full border-2 border-primary flex items-center justify-center">
                    {selectedSymptoms.includes(symptom) && (
                      <div className="w-2 h-2 rounded-full bg-primary animate-scale-in" />
                    )}
                  </div>
                  <span className="text-sm">{symptom}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}