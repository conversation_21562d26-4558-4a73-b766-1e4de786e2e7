
import React, { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, Save, Bold, Italic, Link as LinkIcon, List, ListOrdered, Image, Heading1, Heading2, Heading3 } from "lucide-react";
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Link from '@tiptap/extension-link';
import Underline from '@tiptap/extension-underline';
import TiptapImage from '@tiptap/extension-image';

interface MedicationInstructionsFormProps {
  medicationId: string;
  medicationName: string;
  existingInstructions: any | null;
  onSuccess: () => void;
}

const MenuBar = ({ editor }: { editor: any }) => {
  if (!editor) {
    return null;
  }

  const handleButtonClick = (callback: () => void) => (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    callback();
  };

  const setLink = () => {
    const url = window.prompt('URL:')
    if (url) {
      editor.chain().focus().setLink({ href: url }).run()
    }
  }

  const addImage = () => {
    const url = window.prompt('URL da imagem:')
    if (url) {
      editor.chain().focus().setImage({ src: url }).run()
    }
  }

  const addTitle = () => {
    // Adiciona um título formatado como no formato Condutas e Manejos (##.)
    editor.chain().focus()
      .insertContent('<h2><strong>##. Título</strong></h2>')
      .run();
  }

  const addSubtitle = () => {
    // Adiciona um subtítulo formatado como no formato Condutas e Manejos (##;)
    editor.chain().focus()
      .insertContent('<strong>##; Subtítulo</strong><br><br>')
      .run();
  }

  return (
    <div className="flex items-center gap-1 border-b p-2 flex-wrap">
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={handleButtonClick(() => editor.chain().focus().toggleBold().run())}
        className={editor.isActive('bold') ? 'bg-muted' : ''}
      >
        <Bold className="h-4 w-4" />
      </Button>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={handleButtonClick(() => editor.chain().focus().toggleItalic().run())}
        className={editor.isActive('italic') ? 'bg-muted' : ''}
      >
        <Italic className="h-4 w-4" />
      </Button>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={handleButtonClick(setLink)}
        className={editor.isActive('link') ? 'bg-muted' : ''}
      >
        <LinkIcon className="h-4 w-4" />
      </Button>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={handleButtonClick(() => editor.chain().focus().toggleBulletList().run())}
        className={editor.isActive('bulletList') ? 'bg-muted' : ''}
      >
        <List className="h-4 w-4" />
      </Button>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={handleButtonClick(() => editor.chain().focus().toggleOrderedList().run())}
        className={editor.isActive('orderedList') ? 'bg-muted' : ''}
      >
        <ListOrdered className="h-4 w-4" />
      </Button>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={handleButtonClick(addTitle)}
        title="Adicionar Título (##.)"
      >
        <Heading2 className="h-4 w-4" />
      </Button>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={handleButtonClick(addSubtitle)}
        title="Adicionar Subtítulo (##;)"
      >
        <Heading3 className="h-4 w-4" />
      </Button>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={handleButtonClick(addImage)}
      >
        <Image className="h-4 w-4" />
      </Button>
    </div>
  );
};

export function MedicationInstructionsForm({
  medicationId,
  medicationName,
  existingInstructions,
  onSuccess,
}: MedicationInstructionsFormProps) {
  const [content, setContent] = useState("");
  const [isPublished, setIsPublished] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState("edit");
  const [formatType, setFormatType] = useState<'standard' | 'simple'>(
    existingInstructions?.format_type === 'simple' ? 'simple' : 'standard'
  );

  const editor = useEditor({
    extensions: [
      StarterKit,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-500 underline'
        }
      }),
      Underline,
      TiptapImage.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg my-4',
        },
      }),
    ],
    content: "",
    onUpdate: ({ editor }) => {
      setContent(editor.getHTML());
    },
  });

  useEffect(() => {
    if (existingInstructions) {
      setContent(existingInstructions.content || "");
      setIsPublished(existingInstructions.is_published || false);
      setFormatType(existingInstructions.format_type === 'simple' ? 'simple' : 'standard');

      // Update editor content if it exists
      if (editor && existingInstructions.content) {
        editor.commands.setContent(existingInstructions.content);
      }
    }
  }, [existingInstructions, editor]);

  const handleSave = async () => {
    setIsSaving(true);
    console.log("🔄 Salvando bula para o medicamento ID:", medicationId);
    console.log("🔍 Tipo de formatação:", formatType);
    console.log("🔍 Conteúdo a ser salvo:", content);

    try {
      if (existingInstructions) {
        console.log("📝 Atualizando bula existente ID:", existingInstructions.id);
        const { data, error } = await supabase
          .from("pedbook_medication_instructions")
          .update({
            content,
            is_published: isPublished,
            format_type: formatType,
            updated_at: new Date().toISOString(),
          })
          .eq("id", existingInstructions.id);

        if (error) throw error;
        console.log("✅ Bula atualizada com sucesso:", data);
      } else {
        console.log("➕ Criando nova bula para medicamento ID:", medicationId);
        const { data, error } = await supabase
          .from("pedbook_medication_instructions")
          .insert({
            medication_id: medicationId,
            content,
            is_published: isPublished,
            format_type: formatType,
          });

        if (error) throw error;
        console.log("✅ Nova bula criada com sucesso:", data);
      }

      onSuccess();
    } catch (error) {
      console.error("❌ Erro ao salvar bula:", error);
    } finally {
      setIsSaving(false);
    }
  };

  // Função para processar o conteúdo HTML para o preview no formato padrão
  const processStandardContentForPreview = (html: string) => {
    if (!html) return '';

    // Processar subtítulos (##;)
    return html
      .replace(/<h2><strong>##\.\s*(.*?)<\/strong><\/h2>/g, '<h2 class="text-xl font-bold text-blue-600 mt-5 mb-2">$1</h2>')
      .replace(/<h3><strong>##\.\s*(.*?)<\/strong><\/h3>/g, '<h3 class="text-lg font-semibold text-blue-500 mt-3 mb-1">$1</h3>')
      .replace(/<strong>##;\s*(.*?)<\/strong>/g, '<h4 class="text-base font-semibold text-blue-500 mt-2 mb-1">$1</h4>');
  };

  // Função para processar o conteúdo HTML para o preview no formato simples
  const processSimpleContentForPreview = (html: string) => {
    if (!html) return '';

    // Primeiro, vamos extrair os marcadores ##. e ##; mesmo quando estão dentro de tags HTML
    // Procurar por ##. em qualquer lugar do HTML, incluindo dentro de tags
    let processedContent = html.replace(
      /(<[^>]*>)*##\.\s*(.*?)(<\/[^>]*>)*/g,
      (match, openTag, title) => {
        // Remover tags HTML do título
        const cleanTitle = title.replace(/<\/?[^>]+(>|$)/g, "");
        return `<h2 class="text-xl font-bold text-blue-600 mt-5 mb-2">${cleanTitle}</h2>`;
      }
    );

    // Remover qualquer ocorrência restante de ##. e seu texto até a próxima quebra de linha
    processedContent = processedContent.replace(/##\.\s*[^<\n\r]*(?:<br>|<\/p>|$)/g, '');

    // Procurar por ##; em qualquer lugar do HTML, incluindo dentro de tags
    processedContent = processedContent.replace(
      /(<[^>]*>)*##;\s*(.*?)(<\/[^>]*>)*/g,
      (match, openTag, subtitle) => {
        // Remover tags HTML do subtítulo
        const cleanSubtitle = subtitle.replace(/<\/?[^>]+(>|$)/g, "");
        return `<h4 class="text-base font-semibold text-blue-500 mt-2 mb-1">${cleanSubtitle}</h4>`;
      }
    );

    // Remover qualquer ocorrência restante de ##; e seu texto até a próxima quebra de linha
    processedContent = processedContent.replace(/##;\s*[^<\n\r]*(?:<br>|<\/p>|$)/g, '');

    console.log('🔍 [processSimpleContentForPreview] Conteúdo final processado:', processedContent);
    return processedContent;
  };

  // Função para processar o conteúdo HTML para o preview
  const processContentForPreview = (html: string) => {
    if (!html) return '';

    return formatType === 'simple'
      ? processSimpleContentForPreview(html)
      : processStandardContentForPreview(html);
  };

  // Mostrar instruções para formatação
  const renderFormatInstructions = () => (
    <div className="bg-blue-50 p-4 rounded-md mb-4 text-sm">
      <h4 className="font-semibold text-blue-700 mb-2">Formatação Especial</h4>
      {formatType === 'standard' ? (
        <ul className="space-y-1 list-disc pl-5">
          <li><strong>Título:</strong> Use o botão <Heading2 className="inline h-4 w-4" /> para adicionar um título principal (##. Título)</li>
          <li><strong>Subtítulo:</strong> Use o botão <Heading3 className="inline h-4 w-4" /> para adicionar um subtítulo (##; Subtítulo)</li>
          <li><em>Nota: Neste modo, os títulos e subtítulos devem ser criados usando os botões específicos</em></li>
        </ul>
      ) : (
        <ul className="space-y-1 list-disc pl-5">
          <li><strong>Título:</strong> Digite "##." seguido do título (ex: ##. Título)</li>
          <li><strong>Subtítulo:</strong> Digite "##;" seguido do subtítulo (ex: ##; Subtítulo)</li>
          <li><em>Nota: Neste modo, apenas os marcadores ##. e ##; são considerados, independente do tamanho da fonte ou formatação</em></li>
        </ul>
      )}
    </div>
  );

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Bula para {medicationName}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center space-x-4 mb-2">
          <Label htmlFor="format-type">Tipo de formatação:</Label>
          <Select
            value={formatType}
            onValueChange={(value) => setFormatType(value as 'standard' | 'simple')}
          >
            <SelectTrigger className="w-[220px]">
              <SelectValue placeholder="Selecione o tipo" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="standard">Padrão (com tamanhos específicos)</SelectItem>
              <SelectItem value="simple">Simples (apenas marcadores)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {renderFormatInstructions()}

        <Tabs
          defaultValue="edit"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList>
            <TabsTrigger value="edit">Editar</TabsTrigger>
            <TabsTrigger value="preview">Visualizar</TabsTrigger>
          </TabsList>
          <TabsContent value="edit" className="py-4">
            <div className="border rounded-md overflow-hidden">
              <MenuBar editor={editor} />
              <EditorContent
                editor={editor}
                className="prose prose-sm max-w-none p-4 min-h-[400px] focus:outline-none"
              />
            </div>
          </TabsContent>
          <TabsContent value="preview" className="py-4">
            <div className="border rounded-md p-4 min-h-[400px] prose max-w-none">
              {content ? (
                <div dangerouslySetInnerHTML={{ __html: processContentForPreview(content) }} />
              ) : (
                <p className="text-muted-foreground italic">
                  Nenhum conteúdo para visualizar
                </p>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex items-center space-x-2">
          <Switch
            id="publish"
            checked={isPublished}
            onCheckedChange={setIsPublished}
          />
          <Label htmlFor="publish">Publicar bula</Label>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button
          onClick={handleSave}
          disabled={isSaving || !content.trim()}
          className="gap-2"
        >
          {isSaving ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Save className="h-4 w-4" />
          )}
          Salvar
        </Button>
      </CardFooter>
    </Card>
  );
}
