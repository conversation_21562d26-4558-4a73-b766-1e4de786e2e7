
import React from "react";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { AnamneseForm } from "@/components/anamnese/AnamneseForm";

const Anamnese: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-white via-primary/5 to-primary/10">
      <HelmetWrapper>
        <title>Anamnese+ | PedBook - Calculadora Pediátrica</title>
        <meta name="description" content="Geração automática de textos personalizados de anamnese e exame físico, a partir de inputs simples, com base no perfil do paciente e nos achados alterados." />
      </HelmetWrapper>

      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-6">
        <div className="max-w-6xl mx-auto">
          <div className="mb-6 space-y-2">
            <h1 className="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70">
              Anamnese+
            </h1>
            <p className="text-gray-600">
              Gere textos personalizados de anamnese e exame físico de forma rápida e eficiente
            </p>
          </div>
          
          <AnamneseForm />
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Anamnese;
