import React from "react";
import { FilterItem } from "../../FilterItem";
import { useHierarchicalFilterCounts } from "@/hooks/useOptimizedFilterSelection";
import type { SelectedFilters } from "@/types/question";

interface QuestionFormatFilterSectionProps {
  selectedFormats: string[];
  onToggleFormat: (format: string) => void;
  searchTerm?: string;
  selectedFilters: SelectedFilters;
  questionCounts?: {
    totalCounts: {[key: string]: number};
    filteredCounts: {[key: string]: number};
  };
}

export const QuestionFormatFilterSection = ({
  selectedFormats,
  onToggleFormat,
  searchTerm = "",
  selectedFilters,
  questionCounts
}: QuestionFormatFilterSectionProps) => {
  // Verificar se há filtros anteriores na hierarquia selecionados
  const hasHierarchicalFilters = (
    (selectedFilters.specialties && selectedFilters.specialties.length > 0) ||
    (selectedFilters.themes && selectedFilters.themes.length > 0) ||
    (selectedFilters.focuses && selectedFilters.focuses.length > 0) ||
    (selectedFilters.locations && selectedFilters.locations.length > 0) ||
    (selectedFilters.years && selectedFilters.years.length > 0)
  );

  // Usar contagens hierárquicas se há filtros anteriores na hierarquia
  const { data: hierarchicalCounts, isLoading: isLoadingHierarchical } = useHierarchicalFilterCounts(
    selectedFilters,
    'formats'
  );

  // Função para obter a contagem correta
  const getFormatCount = (formatId: string) => {
    if (hasHierarchicalFilters && hierarchicalCounts) {
      return hierarchicalCounts[formatId] || 0;
    }
    return questionCounts?.totalCounts?.[formatId] || 0;
  };

  // Define question formats with their display names
  const questionFormats = [
    {
      id: 'ALTERNATIVAS',
      name: '📝 Alternativas',
      description: 'Questões de múltipla escolha',
      count: getFormatCount('ALTERNATIVAS')
    },
    {
      id: 'VERDADEIRO_FALSO',
      name: '✅ Verdadeiro ou Falso',
      description: 'Questões de V ou F',
      count: getFormatCount('VERDADEIRO_FALSO')
    },
    {
      id: 'DISSERTATIVA',
      name: '📄 Dissertativa',
      description: 'Questões abertas/texto livre',
      count: getFormatCount('DISSERTATIVA')
    }
  ].filter(format => format.count > 0); // Filtrar apenas formatos com questões

  // Filter question formats based on search term
  const filteredFormats = questionFormats.filter(format =>
    format.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    format.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Check if any filters are active
  const hasActiveFilters = Object.values(selectedFilters).some(filters =>
    Array.isArray(filters) && filters.length > 0
  );

  // Mostrar indicador de carregamento se estiver carregando contagens hierárquicas
  if (hasHierarchicalFilters && isLoadingHierarchical) {
    return (
      <div className="space-y-2">
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex items-center justify-between p-3 rounded-lg bg-gray-100 animate-pulse">
            <div className="flex items-center gap-3">
              <div className="w-5 h-5 rounded bg-gray-300"></div>
              <div className="h-4 w-32 bg-gray-300 rounded"></div>
            </div>
            <div className="h-6 w-12 bg-gray-300 rounded-full"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {filteredFormats.length === 0 ? (
        <div className="text-center py-4 text-gray-500">
          {hasHierarchicalFilters
            ? "Nenhum formato de questão encontrado para os filtros selecionados"
            : "Nenhum formato de questão encontrado"
          }
        </div>
      ) : (
        <div className="space-y-1">
          {filteredFormats.map(format => {
            const formatItem = {
              id: format.id,
              name: format.name,
              type: "question_format"
            };

            return (
              <div key={format.id} className="group">
                <FilterItem
                  item={formatItem}
                  level={0}
                  isExpanded={false}
                  isSelected={selectedFormats.includes(format.id)}
                  questionCount={{
                    total: format.count,
                    filtered: format.count
                  }}
                  hasChildren={false}
                  onToggleExpand={() => {}}
                  onToggleSelect={() => onToggleFormat(format.id)}
                  className={hasHierarchicalFilters ? "ring-2 ring-pink-200" : undefined} // Indicador visual
                />
                {/* Descrição opcional */}
                <div className="text-xs text-gray-500 ml-8 mt-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  {format.description}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Info sobre os formatos */}
      <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
        <div className="text-xs text-blue-700">
          <div className="font-medium mb-1">💡 Tipos de Questão:</div>
          <div className="space-y-1">
            <div>• <strong>Alternativas:</strong> Questões com múltiplas opções</div>
            <div>• <strong>V ou F:</strong> Questões de verdadeiro ou falso</div>
            <div>• <strong>Dissertativa:</strong> Questões abertas para texto livre</div>
          </div>
        </div>
      </div>
    </div>
  );
};
