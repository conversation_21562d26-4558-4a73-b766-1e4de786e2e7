import React from 'react';
import { cn } from '@/lib/utils';

interface MedicationSkeletonProps {
  className?: string;
}

export const MedicationSkeleton: React.FC<MedicationSkeletonProps> = ({ className }) => {
  return (
    <div className={cn("flex-1 space-y-6 animate-fade-in", className)}>
      {/* Header Skeleton */}
      <div className="bg-white/90 dark:bg-slate-800/90 shadow-md rounded-2xl border border-primary/10 dark:border-primary/20 overflow-hidden">
        <div className="h-2 bg-gradient-to-r from-primary/20 via-primary/10 to-primary/20 animate-pulse" />
        
        <div className="p-6 space-y-4">
          {/* Title skeleton */}
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded-xl animate-pulse" />
            <div className="flex-1 space-y-2">
              <div className="h-6 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded-lg w-2/3 animate-pulse" />
              <div className="h-4 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded w-1/2 animate-pulse" />
            </div>
          </div>
          
          {/* Description skeleton */}
          <div className="space-y-2">
            <div className="h-4 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded w-full animate-pulse" />
            <div className="h-4 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded w-3/4 animate-pulse" />
          </div>
        </div>
      </div>

      {/* Patient Info Skeleton */}
      <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg p-6 border border-primary/10">
        <div className="space-y-4">
          <div className="h-5 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded w-1/3 animate-pulse" />
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="h-4 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded w-1/2 animate-pulse" />
              <div className="h-10 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded-lg animate-pulse" />
            </div>
            <div className="space-y-2">
              <div className="h-4 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded w-1/2 animate-pulse" />
              <div className="h-10 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded-lg animate-pulse" />
            </div>
          </div>
        </div>
      </div>

      {/* Loading indicator */}
      <div className="flex items-center justify-center py-8">
        <div className="flex items-center gap-3">
          <div className="w-6 h-6 border-2 border-primary/20 border-t-primary rounded-full animate-spin" />
          <span className="text-sm text-gray-600 dark:text-gray-400 font-medium">
            Carregando informações do medicamento...
          </span>
        </div>
      </div>
    </div>
  );
};

export default MedicationSkeleton;
