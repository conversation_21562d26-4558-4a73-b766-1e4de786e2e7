
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Brain, ArrowLeft } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";
import { ChildcareSEO } from "@/components/seo/ChildcareSEO";
import { CHILDCARE_SEO_DATA } from "@/data/childcareSEOData";

const DNPM = () => {
  const seoData = CHILDCARE_SEO_DATA['dnpm'];

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 1;

  const { data: milestones, isLoading } = useQuery({
    queryKey: ["dnpm-milestones"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_dnpm_milestones")
        .select("*")
        .order("age_months");

      if (error) throw error;
      return data;
    },
  });

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col dark:bg-slate-900">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-12">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mx-auto"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mx-auto"></div>
            <div className="h-96 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  const totalPages = milestones ? Math.ceil(milestones.length / itemsPerPage) : 0;
  const currentMilestone = milestones ? milestones[currentPage - 1] : null;

  const handlePrevious = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNext = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <ChildcareSEO {...seoData} />
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-12">
        <Link 
          to="/puericultura" 
          className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors mb-8 dark:text-blue-400 dark:hover:text-blue-300"
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Voltar para Puericultura</span>
        </Link>

        <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-8 border border-blue-100 dark:border-slate-700 transition-all duration-500 hover:shadow-xl animate-fade-in-up">
          <div className="flex flex-col lg:flex-row gap-8">
            <div className="flex-1 space-y-6">
              <h2 className="text-2xl font-semibold flex items-center gap-2 text-blue-700 dark:text-blue-400">
                {currentMilestone.age_months} Meses
                {currentMilestone.age_type === "years" && (
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    ({currentMilestone.age_years} anos)
                  </span>
                )}
              </h2>

              <div className="space-y-4">
                <div className="p-4 rounded-lg bg-gradient-to-r from-blue-50 to-white border border-blue-100 transition-all duration-300 hover:shadow-md dark:from-blue-900/20 dark:to-slate-800 dark:border-blue-900/30">
                  <h3 className="font-semibold text-blue-600 dark:text-blue-400 flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-blue-400"></div>
                    Social e Emocional
                  </h3>
                  <p className="text-gray-700 dark:text-gray-300 mt-2">{currentMilestone.social_emotional}</p>
                </div>

                <div className="p-4 rounded-lg bg-gradient-to-r from-purple-50 to-white border border-purple-100 transition-all duration-300 hover:shadow-md dark:from-purple-900/20 dark:to-slate-800 dark:border-purple-900/30">
                  <h3 className="font-semibold text-purple-600 dark:text-purple-400 flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-purple-400"></div>
                    Linguagem e Comunicação
                  </h3>
                  <p className="text-gray-700 dark:text-gray-300 mt-2">{currentMilestone.language_communication}</p>
                </div>

                <div className="p-4 rounded-lg bg-gradient-to-r from-green-50 to-white border border-green-100 transition-all duration-300 hover:shadow-md dark:from-green-900/20 dark:to-slate-800 dark:border-green-900/30">
                  <h3 className="font-semibold text-green-600 dark:text-green-400 flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-green-400"></div>
                    Cognição
                  </h3>
                  <p className="text-gray-700 dark:text-gray-300 mt-2">{currentMilestone.cognition}</p>
                </div>

                <div className="p-4 rounded-lg bg-gradient-to-r from-orange-50 to-white border border-orange-100 transition-all duration-300 hover:shadow-md dark:from-orange-900/20 dark:to-slate-800 dark:border-orange-900/30">
                  <h3 className="font-semibold text-orange-600 dark:text-orange-400 flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-orange-400"></div>
                    Motora/Física
                  </h3>
                  <p className="text-gray-700 dark:text-gray-300 mt-2">{currentMilestone.motor_physical}</p>
                </div>
              </div>
            </div>

            {currentMilestone.image_url && (
              <div className="lg:w-96">
                <img
                  src={currentMilestone.image_url}
                  alt={`Desenvolvimento ${currentMilestone.age_months} meses`}
                  className="rounded-lg w-full h-auto shadow-lg transition-transform duration-300 hover:scale-105"
                />
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-between gap-4">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentPage === 1}
            className="w-full sm:w-auto bg-white hover:bg-blue-50 border-blue-200 text-blue-700 transition-all duration-300 hover:shadow-md dark:bg-slate-800 dark:hover:bg-slate-700 dark:border-slate-700 dark:text-blue-400"
          >
            <ChevronLeft className="w-4 h-4 mr-2" />
            Anterior
          </Button>

          <Button
            variant="outline"
            onClick={handleNext}
            disabled={currentPage === totalPages}
            className="w-full sm:w-auto bg-white hover:bg-blue-50 border-blue-200 text-blue-700 transition-all duration-300 hover:shadow-md dark:bg-slate-800 dark:hover:bg-slate-700 dark:border-slate-700 dark:text-blue-400"
          >
            Próximo
            <ChevronRight className="w-4 h-4 ml-2" />
          </Button>
        </div>

        <p className="text-center text-sm text-gray-500 dark:text-gray-400">
          Referência: Cartilha de Desenvolvimento 2m-5anos, CDC/SBP
        </p>
      </main>

      <Footer />
    </div>
  );
};

export default DNPM;
