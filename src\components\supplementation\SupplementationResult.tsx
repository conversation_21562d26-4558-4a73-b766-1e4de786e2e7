
import { useState } from "react";
import { SupplementationResult } from "@/types/supplementation";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Droplet, Sun, Pill, Calculator, ChevronDown, ChevronUp } from "lucide-react";

interface SupplementationResultProps {
  result: SupplementationResult;
  isVisible: boolean;
  patientWeight?: number;
  isEndemicArea?: boolean | null;
  onEndemicAreaChange?: (isEndemic: boolean) => void;
}

// Medicamentos de ferro disponíveis (igual à visão geral)
const ironMedications = [
  { name: "Sulfato ferroso gotas", concentration: 1.25, note: "Em geral disponível no Posto de Saúde" },
  { name: "Combiron gotas", concentration: 2.5, note: "glicinato" },
  { name: "Noripurum gotas", concentration: 2.5, note: "polimaltosado" },
  { name: "Neutrofer gotas", concentration: 2.5, note: "glicinato" },
  { name: "Endofer gotas", concentration: 2.5, note: "polimaltosado" }
];

export const SupplementationResultView = ({
  result,
  isVisible,
  patientWeight,
  isEndemicArea,
  onEndemicAreaChange
}: SupplementationResultProps) => {
  const [showIronPrescription, setShowIronPrescription] = useState(false);
  const [selectedIronMed, setSelectedIronMed] = useState(ironMedications[0]);

  if (!isVisible) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center space-y-4 p-8">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/40 dark:to-blue-800/40 flex items-center justify-center">
            <svg
              className="w-8 h-8 text-blue-500 dark:text-blue-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <p className="text-gray-600 dark:text-gray-300">
            Preencha os campos com as informações necessárias para calcularmos a suplementação adequada para a criança
          </p>
        </div>
      </div>
    );
  }

  // Extrair dose de ferro do resultado (igual à visão geral)
  const extractIronDose = (ironText: string): number => {
    const match = ironText.match(/(\d+(?:\.\d+)?)\s*mg.*?ferro.*?dia/i);
    return match ? parseFloat(match[1]) : 0;
  };

  const ironDose = extractIronDose(result.iron);

  // Calcular gotas necessárias (igual à visão geral)
  const calculateDrops = (medication: typeof ironMedications[0]) => {
    if (ironDose === 0) return 0;
    return Math.round((ironDose / medication.concentration) * 10) / 10;
  };

  return (
    <div className="space-y-4">
      {/* 1. Vitamina D (primeira) */}
      <Card className="p-4 bg-purple-50 border-purple-200 dark:bg-purple-900/20 dark:border-purple-800/50">
        <div className="flex items-center gap-2 mb-2">
          <Sun className="h-5 w-5 text-purple-600 dark:text-purple-400" />
          <h3 className="font-semibold text-purple-700 dark:text-purple-300">Vitamina D</h3>
        </div>
        <p className="text-purple-600 dark:text-purple-200/90">{result.vitaminD}</p>
      </Card>

      {/* 2. Ferro (segunda) com prescrição */}
      <Card className="p-4 bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800/50">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Pill className="h-5 w-5 text-red-600 dark:text-red-400" />
            <h3 className="font-semibold text-red-700 dark:text-red-300">Ferro</h3>
          </div>
          {ironDose > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowIronPrescription(!showIronPrescription)}
              className="text-red-600 border-red-200 hover:bg-red-100 dark:text-red-400 dark:border-red-700 dark:hover:bg-red-900/20"
            >
              <Calculator className="h-4 w-4 mr-2" />
              {showIronPrescription ? 'Ocultar' : 'Prescrição'}
              {showIronPrescription ? <ChevronUp className="h-4 w-4 ml-2" /> : <ChevronDown className="h-4 w-4 ml-2" />}
            </Button>
          )}
        </div>
        <p className="text-red-600 dark:text-red-200/90 mb-3">{result.iron}</p>

        {/* Prescrição expandida (igual à visão geral) */}
        {showIronPrescription && ironDose > 0 && (
          <div className="mt-4 p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg border border-red-200 dark:border-red-700">
            <h4 className="font-semibold text-red-700 dark:text-red-300 mb-3 flex items-center gap-2">
              <Calculator className="h-4 w-4" />
              Cálculo de Prescrição ({ironDose} mg/dia)
            </h4>

            {/* Seletor de medicamento */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-red-600 dark:text-red-300 mb-2">
                Selecione o medicamento:
              </label>
              <div className="grid gap-2">
                {ironMedications.map((med, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedIronMed(med)}
                    className={`p-3 text-left rounded-lg border transition-all ${
                      selectedIronMed.name === med.name
                        ? 'border-red-400 bg-red-100 dark:bg-red-900/30 dark:border-red-600'
                        : 'border-gray-200 hover:border-red-300 hover:bg-red-50 dark:border-gray-600 dark:hover:border-red-700 dark:hover:bg-red-900/20'
                    }`}
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium text-red-700 dark:text-red-300">{med.name}</p>
                        <p className="text-sm text-red-600 dark:text-red-400">{med.concentration} mg/gota ({med.note})</p>
                      </div>
                      <Badge variant={selectedIronMed.name === med.name ? "default" : "secondary"}>
                        {calculateDrops(med)} gotas/dia
                      </Badge>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Prescrição final */}
            <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-lg border border-red-300 dark:border-red-600">
              <h5 className="font-semibold text-red-800 dark:text-red-200 mb-2">📋 Prescrição:</h5>
              <p className="text-red-700 dark:text-red-300">
                <strong>{selectedIronMed.name}</strong><br/>
                <strong>{calculateDrops(selectedIronMed)} gotas por dia</strong><br/>
                <span className="text-sm">({selectedIronMed.concentration} mg/gota × {calculateDrops(selectedIronMed)} gotas = {ironDose} mg ferro elementar/dia)</span>
              </p>
            </div>
          </div>
        )}
      </Card>

      {/* 3. Vitamina A (terceira) com pergunta interativa */}
      <Card className="p-4 bg-amber-50 border-amber-200 dark:bg-amber-900/20 dark:border-amber-800/50">
        <div className="flex items-center gap-2 mb-2">
          <Droplet className="h-5 w-5 text-amber-600 dark:text-amber-400" />
          <h3 className="font-semibold text-amber-700 dark:text-amber-300">Vitamina A</h3>
        </div>

        {/* Pergunta sobre área endêmica para crianças ≥6 meses */}
        {result.vitaminA.includes('6 meses') && isEndemicArea === null && (
          <div className="mb-4 p-3 bg-amber-100 dark:bg-amber-900/30 rounded-lg border border-amber-200 dark:border-amber-700">
            <p className="text-amber-700 dark:text-amber-300 mb-3 font-medium">
              A criança vive em área endêmica para deficiência de vitamina A?
            </p>
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={() => onEndemicAreaChange?.(true)}
                className="bg-amber-600 hover:bg-amber-700 text-white"
              >
                Sim
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => onEndemicAreaChange?.(false)}
                className="border-amber-300 text-amber-700 hover:bg-amber-100 dark:border-amber-600 dark:text-amber-300"
              >
                Não
              </Button>
            </div>
          </div>
        )}

        {/* Resultado baseado na resposta */}
        {isEndemicArea !== null ? (
          <div>
            <div className="flex items-center gap-2 mb-2">
              <Badge variant={isEndemicArea ? "default" : "secondary"}>
                {isEndemicArea ? "Área Endêmica" : "Área Não-Endêmica"}
              </Badge>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => onEndemicAreaChange?.(null)}
                className="text-amber-600 hover:text-amber-700 dark:text-amber-400"
              >
                Reavaliar
              </Button>
            </div>
            <p className="text-amber-600 dark:text-amber-200/90">
              {isEndemicArea
                ? "Suplementação de Vitamina A indicada: 100.000 UI (6-11 meses) ou 200.000 UI (12-59 meses) a cada 6 meses."
                : "Suplementação de Vitamina A não está rotineiramente indicada em áreas não-endêmicas, exceto em casos de risco aumentado."
              }
            </p>
          </div>
        ) : (
          <p className="text-amber-600 dark:text-amber-200/90">{result.vitaminA}</p>
        )}
      </Card>
    </div>
  );
};
