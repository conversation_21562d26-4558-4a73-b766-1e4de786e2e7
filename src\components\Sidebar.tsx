import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Menu } from "lucide-react";
import { useState, useEffect } from "react";
import { useDebounce } from "@/hooks/useDebounce";

interface SidebarProps {
  selectedCategory: string | null;
  onCategorySelect: (categoryId: string | null) => void;
  searchTerm: string;
  onSearchChange: (term: string) => void;
}

export const Sidebar = ({
  selectedCategory,
  onCategorySelect,
  searchTerm,
  onSearchChange
}: SidebarProps) => {
  const [isOpen, setIsOpen] = useState(false);

  // Debounce do termo de busca para melhor performance INP
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Aplicar busca debounced
  useEffect(() => {
    if (debouncedSearchTerm !== searchTerm) {
      // O debounce já foi aplicado, não precisa fazer nada extra aqui
      // A busca real acontece no componente pai
    }
  }, [debouncedSearchTerm, searchTerm]);

  const { data: categories } = useQuery({
    queryKey: ["medication-categories"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_medication_categories")
        .select("*")
        .order("name");
      
      if (error) throw error;
      return data;
    },
  });

  const handleCategorySelect = (categoryId: string | null) => {
    onCategorySelect(categoryId);
    setIsOpen(false);
  };

  const CategoryList = () => (
    <div className="space-y-1">
      <Button 
        variant={selectedCategory === null ? "default" : "ghost"} 
        className="w-full justify-start"
        onClick={() => handleCategorySelect(null)}
      >
        Todas as categorias
      </Button>
      {categories?.map((category) => (
        <Button
          key={category.id}
          variant={selectedCategory === category.id ? "default" : "ghost"}
          className="w-full justify-start"
          onClick={() => handleCategorySelect(category.id)}
        >
          {category.name}
        </Button>
      ))}
    </div>
  );

  // Mobile view
  const MobileView = () => (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="outline" className="lg:hidden w-full mb-4">
          <Menu className="h-5 w-5 mr-2" />
          Filtrar por categoria
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-[300px] sm:w-[400px]">
        <div className="py-4">
          <div className="mb-4">
            <Input
              type="search"
              placeholder="Pesquisar..."
              className="w-full"
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
            />
          </div>
          <CategoryList />
        </div>
      </SheetContent>
    </Sheet>
  );

  // Desktop view
  const DesktopView = () => (
    <aside className="w-64 space-y-6 hidden lg:block">
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Filtros</h2>
        
        <div className="relative">
          <Input
            type="search"
            placeholder="Pesquisar..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
          />
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={18} />
        </div>

        <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-700">Categorias</h3>
          <CategoryList />
        </div>
      </div>
    </aside>
  );

  return (
    <>
      <MobileView />
      <DesktopView />
    </>
  );
};