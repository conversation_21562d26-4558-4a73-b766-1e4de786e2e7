
import React from "react";
import { Star } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { getThemeClasses } from "@/components/ui/theme-utils";

interface FeedbackFormProps {
  rating: number;
  setRating: (rating: number) => void;
  comment: string;
  setComment: (comment: string) => void;
  whatsapp: string;
  setWhatsapp: (whatsapp: string) => void;
}

export const FeedbackForm: React.FC<FeedbackFormProps> = ({
  rating,
  setRating,
  comment,
  setComment,
  whatsapp,
  setWhatsapp,
}) => {
  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-xl sm:text-2xl font-semibold tracking-tight dark:text-gray-100">Sua opinião é importante!</h2>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Ajude-nos a melhorar sua experiência compartilhando seu feedback.
        </p>
      </div>

      <div className="flex justify-center gap-2 sm:gap-3 py-2">
        {[1, 2, 3, 4, 5].map((value) => (
          <button
            key={value}
            onClick={() => setRating(value)}
            className={`transition-all duration-200 hover:scale-110 p-1.5 ${
              rating >= value 
                ? 'text-yellow-400 dark:text-yellow-300' 
                : 'text-gray-300 dark:text-gray-600'
            }`}
            type="button"
            aria-label={`Avaliação ${value} estrelas`}
          >
            <Star className="w-7 h-7 sm:w-8 sm:h-8" 
              fill={rating >= value ? 'currentColor' : 'none'}
              strokeWidth={1.5}
            />
          </button>
        ))}
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium dark:text-gray-300">
          {rating <= 4 ? (
            <span className="text-red-500 dark:text-red-400">
              Por favor, nos ajude a entender o que podemos melhorar *
            </span>
          ) : (
            "Suas sugestões para melhorarmos o PedBook"
          )}
        </label>
        <Textarea
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          placeholder={
            rating <= 4
              ? "Conte-nos o que não atendeu suas expectativas para que possamos melhorar..."
              : "Compartilhe suas ideias e sugestões para nos ajudar a melhorar cada vez mais!"
          }
          className="min-h-[120px] resize-none rounded-xl dark:bg-slate-700 dark:border-slate-600 dark:text-gray-200"
          required={rating <= 4}
        />
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium dark:text-gray-300">
          {rating <= 4 ? (
            <span className="text-red-500 dark:text-red-400">WhatsApp para contato *</span>
          ) : (
            "WhatsApp para contato (opcional)"
          )}
        </label>
        <Input
          type="tel"
          placeholder="Digite seu WhatsApp"
          value={whatsapp}
          onChange={(e) => setWhatsapp(e.target.value)}
          className="bg-white dark:bg-slate-700 rounded-xl dark:border-slate-600 dark:text-gray-200"
          required={rating <= 4}
        />
      </div>
    </div>
  );
};
