import { useState } from "react";
import { scorpionQuestions, scorpionResults } from "./constants";

type Step = "initial" | "observation" | "severity" | "discharge" | "mild" | "moderate" | "severe";

export const useScorpionFlow = () => {
  const [currentStep, setCurrentStep] = useState<Step>("initial");
  const [answers, setAnswers] = useState<Record<string, boolean | string>>({});

  const handleAnswer = (answer: boolean | string) => {
    const newAnswers = { ...answers, [currentStep]: answer };
    setAnswers(newAnswers);

    if (currentStep === "initial") {
      if (answer === false) {
        setCurrentStep("observation");
      } else {
        setCurrentStep("severity");
      }
    } else if (currentStep === "observation") {
      if (answer === false) {
        setCurrentStep("discharge");
      } else {
        setCurrentStep("severity");
      }
    } else if (currentStep === "severity") {
      setCurrentStep(answer as Step);
    }
  };

  const handleContinue = (nextStep: string) => {
    setCurrentStep(nextStep as Step);
  };

  const resetFlow = () => {
    setCurrentStep("initial");
    setAnswers({});
  };

  const getCurrentQuestion = () => {
    return scorpionQuestions[currentStep as keyof typeof scorpionQuestions];
  };

  const getCurrentResult = () => {
    if (currentStep === "observation" && !answers.initial) {
      return scorpionResults.noSigns;
    }
    return scorpionResults[currentStep as keyof typeof scorpionResults];
  };

  return {
    currentStep,
    answers,
    handleAnswer,
    handleContinue,
    resetFlow,
    getCurrentQuestion,
    getCurrentResult,
  };
};