import React from "react";
import { useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Skeleton } from "@/components/ui/skeleton";
import { motion } from "framer-motion";
import { useAuth } from "@/hooks/useAuth";
import { BlogPostHeader } from "@/components/blog/post/BlogPostHeader";
import { BlogPostContent } from "@/components/blog/post/BlogPostContent";
import { BlogPostReactions } from "@/components/blog/post/BlogPostReactions";

const BlogPost = () => {
  const { id } = useParams();
  const { user } = useAuth();

  const { data: post, isLoading } = useQuery({
    queryKey: ["blog-post", id],
    queryFn: async () => {
      if (!id) return null;

      const { data, error } = await supabase
        .from("pedbook_blog_posts")
        .select(`
          *,
          author:profiles!pedbook_blog_posts_author_id_fkey (full_name),
          pedbook_blog_categories (name)
        `)
        .eq("id", id)
        .maybeSingle();

      if (error) {
        console.error("Error fetching blog post:", error);
        if (error.code === "PGRST116") {
          return null;
        }
        throw error;
      }
      return data;
    },
    enabled: !!id,
  });

  const { data: userReaction } = useQuery({
    queryKey: ["blog-reaction", id, user?.id],
    enabled: !!user,
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_blog_reactions")
        .select("reaction_type")
        .eq("post_id", id)
        .eq("user_id", user?.id)
        .maybeSingle();

      if (error && error.code !== "PGRST116") {
        console.error("Error fetching user reaction:", error);
        throw error;
      }
      return data;
    },
  });

  const { reactionMutation } = BlogPostReactions({
    postId: id!,
    userId: user?.id,
    userReaction,
    post,
  });

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-white via-primary/5 to-white">
      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        {isLoading ? (
          <div className="max-w-4xl mx-auto space-y-6">
            <Skeleton className="h-[400px] w-full rounded-xl" />
            <Skeleton className="h-12 w-3/4" />
            <div className="flex gap-4">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-6 w-32" />
            </div>
            <Skeleton className="h-64" />
          </div>
        ) : post ? (
          <motion.article
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="max-w-4xl mx-auto"
          >
            <BlogPostHeader post={post} />
            <BlogPostContent
              post={post}
              id={id!}
              userReaction={userReaction}
              onReactionClick={(type) => reactionMutation.mutate(type)}
            />
          </motion.article>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <h2 className="text-2xl font-semibold text-gray-900">
              Post não encontrado
            </h2>
            <p className="mt-2 text-gray-600">
              O post que você está procurando não existe ou foi removido.
            </p>
          </motion.div>
        )}
      </main>

      <Footer />
    </div>
  );
};

export default BlogPost;