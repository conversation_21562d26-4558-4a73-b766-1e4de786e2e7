import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { LogoOptimizer } from "@/components/admin/LogoOptimizer";
import { Separator } from "@/components/ui/separator";

const SiteSettings = () => {
  const [logoUrl, setLogoUrl] = useState("");
  const [faviconUrl, setFaviconUrl] = useState("");
  const { toast } = useToast();

  const { data: settings, refetch } = useQuery({
    queryKey: ["site-settings"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_site_settings")
        .select("*");

      if (error) throw error;

      const settingsMap = data.reduce((acc: Record<string, string>, curr) => {
        acc[curr.key] = curr.value;
        return acc;
      }, {});

      setLogoUrl(settingsMap.logo_url || "");
      setFaviconUrl(settingsMap.favicon_url || "");

      return settingsMap;
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Update logo URL
      const { error: logoError } = await supabase
        .from("pedbook_site_settings")
        .update({ value: logoUrl })
        .eq("key", "logo_url");

      if (logoError) throw logoError;

      // Update favicon URL
      const { error: faviconError } = await supabase
        .from("pedbook_site_settings")
        .update({ value: faviconUrl })
        .eq("key", "favicon_url");

      if (faviconError) throw faviconError;

      toast({
        title: "Configurações atualizadas",
        description: "As configurações do site foram atualizadas com sucesso.",
      });

      // Update favicon in the document
      const faviconElement = document.querySelector("link[rel='icon']") as HTMLLinkElement;
      if (faviconElement) {
        faviconElement.href = faviconUrl;
      }

      refetch();
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao atualizar configurações",
        description: error.message,
      });
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Configurações do Site</h1>

      {/* Otimizador de Logo */}
      <div className="mb-8">
        <LogoOptimizer />
      </div>

      <Separator className="my-8" />

      <form onSubmit={handleSubmit} className="space-y-8 max-w-2xl">
        <div className="space-y-4">
          <div>
            <label htmlFor="logo" className="block text-lg font-medium mb-2">
              URL do Logo
            </label>
            <Input
              id="logo"
              value={logoUrl}
              onChange={(e) => setLogoUrl(e.target.value)}
              className="w-full"
            />
            {logoUrl && (
              <div className="mt-4">
                <p className="text-sm text-gray-500 mb-2">Preview:</p>
                <img src={logoUrl} alt="Logo Preview" className="h-8 w-auto" />
              </div>
            )}
          </div>

          <div>
            <label htmlFor="favicon" className="block text-lg font-medium mb-2">
              URL do Favicon
            </label>
            <Input
              id="favicon"
              value={faviconUrl}
              onChange={(e) => setFaviconUrl(e.target.value)}
              className="w-full"
            />
            {faviconUrl && (
              <div className="mt-4">
                <p className="text-sm text-gray-500 mb-2">Preview:</p>
                <img src={faviconUrl} alt="Favicon Preview" className="h-8 w-auto" />
              </div>
            )}
          </div>
        </div>

        <Button type="submit" className="w-full">
          Salvar Configurações
        </Button>
      </form>
    </div>
  );
};

export default SiteSettings;