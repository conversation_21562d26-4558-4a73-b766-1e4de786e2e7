import { Button } from "@/components/ui/button";
import { useState } from "react";
import { BasicInfoInputs } from "./form/BasicInfoInputs";
import { MeasurementsInputs } from "./form/MeasurementsInputs";
import { AdditionalInfoCard } from "./form/AdditionalInfoCard";

interface PatientFormProps {
  onSubmit: (data: {
    age: number;
    weight: number;
    birthWeight: number;
    height: number;
    gender: "male" | "female";
    headCircumference: number;
    exclusiveBreastfeeding: boolean;
    hasRiskFactors: boolean;
    riskFactors: string[];
    maturity: "Term" | "Pre-term";
    gestationalAge: number;
  }) => void;
  defaultValues?: {
    age?: number;
    weight?: number;
    birthWeight?: number;
    height?: number;
    gender?: "male" | "female";
    headCircumference?: number;
    exclusiveBreastfeeding?: boolean;
    hasRiskFactors?: boolean;
    riskFactors?: string[];
    maturity?: "Term" | "Pre-term";
    gestationalAge?: number;
  };
}

export function PatientForm({ onSubmit, defaultValues }: PatientFormProps) {
  const [exclusiveBreastfeeding, setExclusiveBreastfeeding] = useState(
    defaultValues?.exclusiveBreastfeeding || false
  );
  const [riskFactors, setRiskFactors] = useState<string[]>(
    defaultValues?.riskFactors || []
  );

  // Converter idade decimal de volta para meses e dias
  const getAgeFromDecimal = (ageDecimal: number) => {
    const months = Math.floor(ageDecimal);
    const days = Math.round((ageDecimal - months) * 30.44);
    return { months, days };
  };

  const defaultAge = defaultValues?.age ? getAgeFromDecimal(defaultValues.age) : { months: 0, days: 0 };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    const patientData = {
      age: Number(formData.get("age")),
      weight: Number(formData.get("weight")) / 1000, // Converter gramas para kg
      birthWeight: Number(formData.get("birthWeight")),
      height: Number(formData.get("height")),
      gender: formData.get("gender") as "male" | "female",
      headCircumference: Number(formData.get("headCircumference")),
      exclusiveBreastfeeding,
      hasRiskFactors: riskFactors.length > 0,
      riskFactors,
      maturity: formData.get("maturity") as "Term" | "Pre-term",
      gestationalAge: Number(formData.get("gestationalAge")),
    };

    onSubmit(patientData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Card 1: Dados Básicos */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/30 rounded-xl p-5 border border-blue-200 dark:border-blue-800 shadow-sm">
        <h3 className="text-base font-semibold text-blue-800 dark:text-blue-300 mb-4 flex items-center gap-2">
          <div className="w-3 h-3 bg-blue-500 rounded-full shadow-sm"></div>
          Dados Básicos
        </h3>
        <BasicInfoInputs
          defaultAgeMonths={defaultAge.months}
          defaultAgeDays={defaultAge.days}
          defaultGestationalAge={defaultValues?.gestationalAge}
        />
      </div>

      {/* Card 2: Medidas Antropométricas */}
      <div className="bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/30 rounded-xl p-5 border border-green-200 dark:border-green-800 shadow-sm">
        <h3 className="text-base font-semibold text-green-800 dark:text-green-300 mb-4 flex items-center gap-2">
          <div className="w-3 h-3 bg-green-500 rounded-full shadow-sm"></div>
          Medidas Antropométricas
        </h3>
        <MeasurementsInputs
          defaultValues={{
            weight: defaultValues?.weight,
            birthWeight: defaultValues?.birthWeight,
            height: defaultValues?.height,
            headCircumference: defaultValues?.headCircumference
          }}
        />
      </div>

      {/* Card 3: Informações Clínicas */}
      <div className="bg-gradient-to-br from-purple-50 to-violet-100 dark:from-purple-900/20 dark:to-violet-900/30 rounded-xl p-5 border border-purple-200 dark:border-purple-800 shadow-sm">
        <h3 className="text-base font-semibold text-purple-800 dark:text-purple-300 mb-4 flex items-center gap-2">
          <div className="w-3 h-3 bg-purple-500 rounded-full shadow-sm"></div>
          Informações Clínicas
        </h3>
        <AdditionalInfoCard
          exclusiveBreastfeeding={exclusiveBreastfeeding}
          riskFactors={riskFactors}
          onBreastfeedingChange={setExclusiveBreastfeeding}
          onRiskFactorsChange={setRiskFactors}
        />
      </div>

      {/* Botão de Ação */}
      <div className="pt-2">
        <Button
          type="submit"
          className="w-full h-14 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] border-0"
        >
          <div className="flex items-center justify-center gap-3">
            <div className="w-6 h-6 rounded-full bg-white/20 flex items-center justify-center">
              <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
            </div>
            <span className="text-lg">Analisar Paciente</span>
          </div>
        </Button>
      </div>
    </form>
  );
}