
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { <PERSON>, Target, Clock, Award, BookOpen, BookmarkCheck } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import StatCard from "@/components/StatCard";
import type { StudySessionStats } from "@/types/study-session";

interface SessionSummaryProps {
  stats: StudySessionStats;
  totalQuestions: number;
}

export const SessionSummary = ({ stats, totalQuestions }: SessionSummaryProps) => {
  const answeredQuestions = (stats.correct_answers || 0) + (stats.incorrect_answers || 0);
  const accuracy = answeredQuestions > 0
    ? Math.round((stats.correct_answers / answeredQuestions) * 100)
    : 0;

  const getProgressColorClass = (percentage: number) => {
    if (percentage >= 80) return "bg-green-500";
    if (percentage >= 60) return "bg-emerald-500";
    if (percentage >= 40) return "bg-yellow-500";
    if (percentage >= 20) return "bg-orange-500";
    return "bg-red-500";
  };



  return (
    <div className="space-y-8">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        <StatCard
          title="Total de Questões"
          value={totalQuestions}
          icon={<Brain className="w-6 h-6 text-blue-500" />}
          className="hover:scale-105 transition-transform border border-blue-100"
        />
        <StatCard
          title="Taxa de Acerto"
          value={`${accuracy}%`}
          icon={<Target className="w-6 h-6 text-green-500" />}
          className="hover:scale-105 transition-transform border border-green-100"
        />
        <StatCard
          title="Tempo Médio"
          value={`${Math.round(stats.time_spent / totalQuestions)}s`}
          icon={<Clock className="w-6 h-6 text-purple-500" />}
          className="hover:scale-105 transition-transform border border-purple-100"
        />
      </div>

      {/* Detailed Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Especialidades */}
        <Card className="hover:shadow-md transition-shadow border border-blue-100">
          <CardHeader className="bg-blue-50/50 border-b border-blue-100">
            <div className="flex items-center space-x-2">
              <Award className="h-5 w-5 text-blue-500" />
              <CardTitle className="text-lg text-blue-700">Especialidades</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4 pt-4">
            {Object.entries(stats.by_specialty || {}).map(([id, data]) => (
              <div key={id} className="animate-fade-in-up">
                <div className="flex justify-between text-sm mb-1.5">
                  <span className="font-medium text-gray-700 truncate max-w-[70%]" title={data.name}>{data.name}</span>
                  <span className="font-medium text-blue-600">
                    {Math.round((data.correct / data.total) * 100)}%
                  </span>
                </div>
                <Progress
                  value={(data.correct / data.total) * 100}
                  className={`h-2 ${getProgressColorClass((data.correct / data.total) * 100)}`}
                />
              </div>
            ))}
            {Object.keys(stats.by_specialty || {}).length === 0 && (
              <p className="text-sm text-gray-500 text-center py-2">Nenhuma informação disponível</p>
            )}
          </CardContent>
        </Card>

        {/* Temas */}
        <Card className="hover:shadow-md transition-shadow border border-green-100">
          <CardHeader className="bg-green-50/50 border-b border-green-100">
            <div className="flex items-center space-x-2">
              <BookOpen className="h-5 w-5 text-green-500" />
              <CardTitle className="text-lg text-green-700">Temas</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4 pt-4">
            {Object.entries(stats.by_theme || {}).map(([id, data]) => (
              <div key={id} className="animate-fade-in-up">
                <div className="flex justify-between text-sm mb-1.5">
                  <span className="font-medium text-gray-700 truncate max-w-[70%]" title={data.name}>{data.name}</span>
                  <span className="font-medium text-green-600">
                    {Math.round((data.correct / data.total) * 100)}%
                  </span>
                </div>
                <Progress
                  value={(data.correct / data.total) * 100}
                  className={`h-2 ${getProgressColorClass((data.correct / data.total) * 100)}`}
                />
              </div>
            ))}
            {Object.keys(stats.by_theme || {}).length === 0 && (
              <p className="text-sm text-gray-500 text-center py-2">Nenhuma informação disponível</p>
            )}
          </CardContent>
        </Card>

        {/* Focos */}
        <Card className="hover:shadow-md transition-shadow border border-purple-100">
          <CardHeader className="bg-purple-50/50 border-b border-purple-100">
            <div className="flex items-center space-x-2">
              <BookmarkCheck className="h-5 w-5 text-purple-500" />
              <CardTitle className="text-lg text-purple-700">Focos</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4 pt-4">
            {Object.entries(stats.by_focus || {}).map(([id, data]) => (
              <div key={id} className="animate-fade-in-up">
                <div className="flex justify-between text-sm mb-1.5">
                  <span className="font-medium text-gray-700 truncate max-w-[70%]" title={data.name}>{data.name}</span>
                  <span className="font-medium text-purple-600">
                    {Math.round((data.correct / data.total) * 100)}%
                  </span>
                </div>
                <Progress
                  value={(data.correct / data.total) * 100}
                  className={`h-2 ${getProgressColorClass((data.correct / data.total) * 100)}`}
                />
              </div>
            ))}
            {Object.keys(stats.by_focus || {}).length === 0 && (
              <p className="text-sm text-gray-500 text-center py-2">Nenhuma informação disponível</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
