import React from "react";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Calculator, FileText, Clock, MessageSquare } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

const WhatsAppBot: React.FC = () => {
  const handleWhatsAppClick = () => {
    window.open("https://api.whatsapp.com/send?phone=5511971424463&text=Me%20envie%20suas%20fun%C3%A7%C3%B5es", "_blank");
  };

  return (
    <div className="min-h-screen flex flex-col">
      <HelmetWrapper>
        <title>PedBook | Bot WhatsApp</title>
        <meta name="description" content="Acesse as dosagens pediátricas 24 horas por dia através do nosso bot no WhatsApp." />
      </HelmetWrapper>

      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8 md:py-16">
        <div className="max-w-4xl mx-auto">
          <div className="text-center space-y-6 mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 gradient-text">
              Bot WhatsApp PedBook
            </h1>
            <p className="text-xl text-gray-600">
              Seu assistente pediátrico disponível 24 horas por dia, 7 dias por semana
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <div className="glass-card p-6 space-y-4 rounded-xl">
              <div className="flex items-center gap-3">
                <div className="bg-primary/10 p-3 rounded-lg">
                  <Calculator className="w-6 h-6 text-primary" />
                </div>
                <h2 className="text-xl font-semibold">Cálculo Automático</h2>
              </div>
              <p className="text-gray-600">
                Calcule doses de medicamentos de forma rápida e precisa, com base no peso e idade do paciente.
              </p>
            </div>

            <div className="glass-card p-6 space-y-4 rounded-xl">
              <div className="flex items-center gap-3">
                <div className="bg-primary/10 p-3 rounded-lg">
                  <FileText className="w-6 h-6 text-primary" />
                </div>
                <h2 className="text-xl font-semibold">Prescrições Automáticas</h2>
              </div>
              <p className="text-gray-600">
                NOVIDADE! Gere prescrições completas automaticamente para as condições mais comuns.
              </p>
            </div>
          </div>

          <div className="glass-card p-6 md:p-8 rounded-xl text-center space-y-4 mb-12">
            <div className="flex justify-center gap-4 flex-wrap">
              <div className="flex items-center gap-2">
                <Clock className="w-5 h-5 text-primary" />
                <span className="text-gray-700">Disponível 24/7</span>
              </div>
              <div className="flex items-center gap-2">
                <MessageSquare className="w-5 h-5 text-green-500" />
                <span className="text-gray-700">Totalmente gratuito</span>
              </div>
            </div>

            <Button 
              onClick={handleWhatsAppClick}
              className="bg-green-500 hover:bg-green-600 text-white px-4 md:px-6 py-2 md:py-3 text-base md:text-lg rounded-xl transition-all duration-300 hover:scale-105 w-full md:w-auto"
            >
              <MessageSquare className="w-5 h-5 mr-2" />
              Iniciar conversa no WhatsApp
            </Button>

            <p className="text-xs md:text-sm text-gray-500">
              Clique no botão acima para começar a usar o bot gratuitamente
            </p>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default WhatsAppBot;