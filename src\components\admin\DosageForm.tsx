import { useState, useEffect } from "react";
import { useQueryClient, useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { DosageFormFields } from "./dosage/DosageFormFields";
import { DosageFormData, Tag } from "@/types/dosage";
import { convertTagsToTagData } from "@/services/tagService";
import { FormPersistence } from "./FormPersistence";

interface DosageFormProps {
  medicationId: string;
  dosage?: any;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function DosageForm({ medicationId, dosage, onSuccess, onCancel }: DosageFormProps) {
  const [formData, setFormData] = useState<DosageFormData>({
    medication_id: medicationId,
    name: "",
    summary: "",
    dosage_template: "",
    tags: [],
    use_case_id: "",
    age_group: "pediatric", // Default value
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: useCases } = useQuery({
    queryKey: ["medication-use-cases", medicationId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_medication_use_cases")
        .select("*")
        .eq("medication_id", medicationId)
        .order("name");

      if (error) throw error;
      return data;
    },
  });

  useEffect(() => {
    const loadDosageData = async () => {
      if (dosage) {
        const { data: tags } = await supabase
          .from("pedbook_medication_tags")
          .select("*")
          .eq("medication_id", medicationId);

        setFormData({
          medication_id: dosage.medication_id || medicationId,
          name: dosage.name || "",
          summary: dosage.summary || "",
          dosage_template: dosage.dosage_template || "",
          use_case_id: dosage.use_case_id || "",
          age_group: dosage.age_group || "pediatric",
          tags: tags?.map(tag => ({
            name: tag.name,
            multiplier: tag.multiplier || 0,
            maxValue: tag.max_value,
            type: tag.type as 'fixed' | 'multiplier' | 'age' | 'fixed_by_weight' | 'multiplier_by_fixed_age',
            ...(tag.type === 'age' || tag.type === 'multiplier_by_fixed_age') && {
              ageRanges: [{
                startMonth: tag.start_month || 0,
                endMonth: tag.end_month || 0,
                value: tag.multiplier || 0
              }]
            },
            ...(tag.type === 'fixed_by_weight' && {
              weightRanges: [{
                startWeight: tag.start_weight || 0,
                endWeight: tag.end_weight || 0,
                value: tag.multiplier || 0
              }]
            })
          })) || [],
        });
      }
    };

    loadDosageData();
  }, [dosage, medicationId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const dosageData = {
        medication_id: formData.medication_id,
        name: formData.name,
        summary: formData.summary || null,
        dosage_template: formData.dosage_template,
        use_case_id: formData.use_case_id || null,
        type: 'standard',
        age_group: formData.age_group,
      };

      let dosageId;

      if (dosage) {
        const { data, error } = await supabase
          .from("pedbook_medication_dosages")
          .update(dosageData)
          .eq("id", dosage.id)
          .select()
          .single();

        if (error) throw error;
        dosageId = dosage.id;

        const { error: deleteError } = await supabase
          .from("pedbook_medication_tags")
          .delete()
          .eq("medication_id", medicationId);

        if (deleteError) throw deleteError;

        if (formData.tags.length > 0) {
          const tagsData = convertTagsToTagData(formData.tags, medicationId);
          const { error: tagsError } = await supabase
            .from("pedbook_medication_tags")
            .insert(tagsData);

          if (tagsError) throw tagsError;
        }
      } else {
        const { data, error } = await supabase
          .from("pedbook_medication_dosages")
          .insert(dosageData)
          .select()
          .single();

        if (error) throw error;
        dosageId = data.id;

        if (formData.tags.length > 0) {
          const tagsData = convertTagsToTagData(formData.tags, medicationId);
          const { error: tagsError } = await supabase
            .from("pedbook_medication_tags")
            .insert(tagsData);

          if (tagsError) throw tagsError;
        }
      }

      toast({
        title: dosage ? "Dosagem atualizada com sucesso!" : "Dosagem criada com sucesso!",
        description: `A dosagem ${formData.name} foi ${dosage ? 'atualizada' : 'adicionada'}.`,
      });

      queryClient.invalidateQueries({ queryKey: ["medication-dosages"] });
      queryClient.invalidateQueries({ queryKey: ["medication-tags"] });
      onSuccess?.();
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao salvar dosagem",
        description: error.message,
      });
    }
  };



  return (
    <FormPersistence
      formId="dosage-form"
      storageKey={`dosage_${dosage?.id || 'new'}_${medicationId}`}
      debug={true}
    >
      <form id="dosage-form" onSubmit={handleSubmit} className="space-y-4">
        <DosageFormFields
          formData={formData}
          setFormData={setFormData}
          useCases={useCases || []}
          medicationId={medicationId}
        />

        <div className="flex justify-end gap-2">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancelar
            </Button>
          )}
          <Button type="submit">
            {dosage ? "Atualizar" : "Criar"}
          </Button>
        </div>
      </form>
    </FormPersistence>
  );
}