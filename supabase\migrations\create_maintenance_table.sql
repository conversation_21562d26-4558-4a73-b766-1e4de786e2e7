-- <PERSON><PERSON><PERSON> tabela para controle de manutenção do site
CREATE TABLE IF NOT EXISTS site_maintenance (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  is_active BOOLEAN DEFAULT FALSE NOT NULL,
  message TEXT DEFAULT 'Site em manutenção. Voltaremos em breve!' NOT NULL,
  estimated_duration TEXT,
  activated_by UUID REFERENCES auth.users(id),
  activated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Inserir registro inicial
INSERT INTO site_maintenance (is_active, message) 
VALUES (FALSE, 'Site em manutenção. Voltaremos em breve!')
ON CONFLICT DO NOTHING;

-- Criar índices para performance
CREATE INDEX IF NOT EXISTS idx_site_maintenance_is_active ON site_maintenance(is_active);
CREATE INDEX IF NOT EXISTS idx_site_maintenance_activated_by ON site_maintenance(activated_by);

-- <PERSON><PERSON><PERSON> trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_site_maintenance_updated_at 
    BEFORE UPDATE ON site_maintenance 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Política RLS (Row Level Security)
ALTER TABLE site_maintenance ENABLE ROW LEVEL SECURITY;

-- Política para leitura: todos podem ler o status de manutenção
CREATE POLICY "Anyone can read maintenance status" ON site_maintenance
    FOR SELECT USING (true);

-- Política para escrita: apenas usuários autenticados podem alterar
CREATE POLICY "Authenticated users can update maintenance" ON site_maintenance
    FOR UPDATE USING (auth.uid() IS NOT NULL);

-- Política para inserção: apenas usuários autenticados podem inserir
CREATE POLICY "Authenticated users can insert maintenance" ON site_maintenance
    FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- Comentários para documentação
COMMENT ON TABLE site_maintenance IS 'Tabela para controlar o modo de manutenção do site';
COMMENT ON COLUMN site_maintenance.is_active IS 'Indica se o site está em modo de manutenção';
COMMENT ON COLUMN site_maintenance.message IS 'Mensagem exibida na página de manutenção';
COMMENT ON COLUMN site_maintenance.estimated_duration IS 'Duração estimada da manutenção';
COMMENT ON COLUMN site_maintenance.activated_by IS 'ID do usuário que ativou a manutenção';
COMMENT ON COLUMN site_maintenance.activated_at IS 'Timestamp de quando a manutenção foi ativada';
COMMENT ON COLUMN site_maintenance.updated_at IS 'Timestamp da última atualização';
COMMENT ON COLUMN site_maintenance.created_at IS 'Timestamp de criação do registro';
