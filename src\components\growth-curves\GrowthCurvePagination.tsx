
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { cn } from "@/lib/utils";

interface GrowthCurvePaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export function GrowthCurvePagination({
  currentPage,
  totalPages,
  onPageChange,
}: GrowthCurvePaginationProps) {
  if (totalPages <= 1) return null;

  return (
    <div className="mt-8">
      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              onClick={() => onPageChange(Math.max(currentPage - 1, 1))}
              className={cn(
                currentPage === 1 && "pointer-events-none opacity-50",
                "dark:text-gray-300 dark:hover:bg-slate-700 dark:border-gray-700"
              )}
            />
          </PaginationItem>
          
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <PaginationItem key={page}>
              <PaginationLink
                onClick={() => onPageChange(page)}
                isActive={currentPage === page}
                className="dark:text-gray-300 dark:border-gray-700 dark:data-[active]:bg-primary dark:data-[active]:text-white"
              >
                {page}
              </PaginationLink>
            </PaginationItem>
          ))}
          
          <PaginationItem>
            <PaginationNext
              onClick={() => onPageChange(Math.min(currentPage + 1, totalPages))}
              className={cn(
                currentPage === totalPages && "pointer-events-none opacity-50",
                "dark:text-gray-300 dark:hover:bg-slate-700 dark:border-gray-700"
              )}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
}
