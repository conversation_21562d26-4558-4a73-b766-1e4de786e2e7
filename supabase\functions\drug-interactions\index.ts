
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
const claudeApiKey = Deno.env.get('CLAUDE_API_KEY');
const geminiApiKey = Deno.env.get('GEMINI_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': 'https://pedb.com.br',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Max-Age': '86400',
};

// Função para processar o resultado da API e melhorar a formatação
const processApiResult = (resultText: string): string => {
  // Detectar se há algum resumo no texto das condutas e reformatar adequadamente
  const improvedText = resultText.replace(/(\*\*Conduta Clínica Recomendada:\*\* [^\n]+)[\s\n]+#{1,3}\s*Resumo/gi,
    '$1\n\n**Importante:** ');

  // Adicionar logs para debug
  console.log("Texto processado para melhorar formatação");

  return improvedText;
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { medications, mode, patientData, originalMedicationsCount, manuallyAddedMedications = [] } = await req.json();

    if (!medications || medications.length === 0) {
      throw new Error("Medications list is required");
    }

    // Verificar o limite com base no número original de medicamentos, não nos componentes
    if (originalMedicationsCount > 6) {
      throw new Error("Maximum 6 medications allowed");
    }

    // Adicionar aviso sobre medicamentos adicionados manualmente
    const manualMedsWarning = manuallyAddedMedications.length > 0
      ? `\n\n⚠️ IMPORTANTE: Alguns medicamentos foram adicionados manualmente pelo usuário: ${manuallyAddedMedications.join(', ')}. Fique atento à possibilidade de erros de grafia ou medicamentos não padronizados.`
      : '';

    // Usar o prompt atualizado
    const promptTemplate = `
Você é uma IA médica especializada em farmacologia clínica. Sua tarefa é analisar interações medicamentosas entre até 6 medicamentos listados, classificando corretamente em categorias (Contraindicada, Grave, Moderada, Leve).

${mode === 'advanced' ? `### Dados do paciente:
- Idade: ${patientData?.age || 'N/A'} anos
- Sexo: ${patientData?.gender || 'N/A'}
- Comorbidades: ${patientData?.comorbidities || 'N/A'}
- Estado clínico relevante: ${patientData?.clinicalStatus || 'N/A'}
` : ''}

### Interações Medicamentosas entre ${medications.join(', ')}

#### ⚡️ Regras Gerais:
- Analise todas as combinações possíveis entre os medicamentos fornecidos.
- Baseie-se em evidência clínica para classificar as interações.
- Evite classificar interações como graves ou contraindicadas sem suporte clínico claro.
- Priorize clareza e objetividade. Cada interação deve ser clara, concisa e diretamente aplicável na prática clínica.
${manualMedsWarning}

#### ✅ Classificação das Interações:
1. **Interações Contraindicadas:**
   - Uso combinado apresenta risco crítico de morte, toxicidade grave ou efeitos fatais.

2. **Interações Graves:**
   - Risco significativo de efeitos adversos sérios, mas controlável com monitoramento rigoroso.

3. **Interações Moderadas:**
   - Efeitos adversos são possíveis, mas geralmente controláveis com ajuste de dose ou monitoramento.

4. **Interações Leves ou Sem Interação Relevante:**
   - Efeito mínimo ou clinicamente irrelevante.

### ✅ Formato de Resposta:
- As interações devem ser listadas nas seguintes categorias, com texto claro e objetivo.
- É OBRIGATÓRIO para cada interação seguir o formato abaixo, sempre com os nomes dos medicamentos entre colchetes []:

#### 1. Interações Contraindicadas
- **Interação:** [Medicamento A + Medicamento B]
  - **Gravidade:** Contraindicada
  - **Mecanismo:** [Descrição clara do mecanismo da interação]
  - **Conduta Clínica Recomendada:** [Orientação prática]

Se não houver interações nesta categoria:
- **Nenhuma interação contraindicada identificada entre ${medications.join(' e ')}.**

#### 2. Interações Graves
- **Interação:** [Medicamento A + Medicamento B]
  - **Gravidade:** Grave
  - **Mecanismo:** [Descrição clara do mecanismo da interação]
  - **Conduta Clínica Recomendada:** [Orientação prática]

Se não houver interações nesta categoria:
- **Nenhuma interação grave identificada entre ${medications.join(' e ')}.**

#### 3. Interações Moderadas
- **Interação:** [Medicamento A + Medicamento B]
  - **Gravidade:** Moderada
  - **Mecanismo:** [Descrição clara do mecanismo da interação]
  - **Conduta Clínica Recomendada:** [Orientação prática]

Se não houver interações nesta categoria:
- **Nenhuma interação moderada identificada entre ${medications.join(' e ')}.**

#### 4. Interações Leves ou Sem Interação Relevante
- **Interação:** [Medicamento A + Medicamento B]
  - **Gravidade:** Leve
  - **Mecanismo:** [Descrição clara do mecanismo, se aplicável]
  - **Conduta Clínica Recomendada:** [Orientação prática sobre uso seguro]

Se não houver interações nesta categoria:
- **Nenhuma interação leve identificada entre ${medications.join(' e ')}.**

### ✅ Regras Específicas:
- SEMPRE use colchetes [] ao redor dos nomes dos medicamentos na linha "Interação:".
- Exemplo correto: - **Interação:** [Clopidogrel + Varfarina Sódica]
- Exemplo incorreto: - **Interação:** Clopidogrel + Varfarina Sódica
- Nunca classifique uma interação como "Grave" ou "Contraindicada" sem suporte clínico claro.
- Evite classificar interações como "Grave" apenas com base em mecanismos teóricos.
- Priorize a precisão clínica e objetividade na descrição de cada interação.
- Considere apenas interações com relevância clínica real (ex: risco de morte, toxicidade grave, efeitos adversos significativos).
- Para interações leves, seja direto e evite explicações longas.
- IMPORTANTE: Na parte "Conduta Clínica Recomendada", inclua APENAS orientações práticas sobre o manejo da interação. Não adicione resumos ou considerações gerais sobre a interação ali.

### 🚀 Dicas de Desempenho:
- Identifique interações críticas primeiro (Contraindicadas e Graves) para garantir segurança.
- Simplifique a descrição em interações leves e moderadas, mantendo clareza.
- Priorize precisão clínica em todas as classificações.

${mode === 'advanced' ? `
### Resumo
Apresente os pontos mais importantes com foco prático, considerando o perfil do paciente:
- Destacar as interações **graves ou contraindicadas**, se houver.
- Sugerir vigilância clínica ou ajuste de dose, se aplicável.
- Mencionar fatores de risco específicos do paciente (idade, comorbidades) que podem afetar as interações.
${manuallyAddedMedications.length > 0 ? '- Alertar sobre possíveis imprecisões na análise devido aos medicamentos adicionados manualmente.' : ''}
` : `
### Resumo
Apresente os pontos mais importantes com foco prático:
- Destacar as interações **graves ou contraindicadas**, se houver.
- Sugerir vigilância clínica ou ajuste de dose, se aplicável.
${manuallyAddedMedications.length > 0 ? '- Alertar sobre possíveis imprecisões na análise devido aos medicamentos adicionados manualmente.' : ''}
`}

#### Medicamentos analisados:
${medications.map((med: string) => `- ${med}${manuallyAddedMedications.includes(med) ? ' (adicionado manualmente)' : ''}`).join('\n')}
`;

    // Use Gemini 2.5 Flash Preview
    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-goog-api-key': geminiApiKey,
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              { text: promptTemplate }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.2,
          maxOutputTokens: 8192,
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_ONLY_HIGH"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_ONLY_HIGH"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_ONLY_HIGH"
          },
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_ONLY_HIGH"
          }
        ]
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Gemini API error:", errorText);
      throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const rawResult = data.candidates[0].content.parts[0].text;

    // Processar o resultado para melhorar a formatação
    const processedResult = processApiResult(rawResult);

    return new Response(JSON.stringify({ result: processedResult }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error in drug-interactions function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
