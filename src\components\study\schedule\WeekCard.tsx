
import { motion } from "framer-motion";
import { Calendar, ChevronDown, ChevronUp, Clock } from "lucide-react";
import type { DaySchedule } from "@/types/study-schedule";
import { DayCard } from "./DayCard";
import { Badge } from "@/components/ui/badge";
import { useState, useEffect } from "react";
import { Dialog } from "@/components/ui/dialog";
import { TopicSourceDialog } from "./TopicSourceDialog";
import { Switch } from "@/components/ui/switch";
import { useIsMobile } from "@/hooks/use-mobile";
import { sumDurations, parseDurationToMinutes } from "@/utils/formatTime";

interface WeekCardProps {
  weekNumber: number;
  days: DaySchedule[];
  totalHours: number;
  weekStartDate: string;
  weekEndDate: string;
  isCurrentWeek?: boolean;
  defaultExpanded?: boolean;
  onAddTopic?: (dayOfWeek: string, weekNumber: number, source: 'platform' | 'manual') => void;
}

export const WeekCard = ({ 
  weekNumber, 
  days, 
  totalHours, 
  weekStartDate, 
  weekEndDate,
  isCurrentWeek = false,
  defaultExpanded = false,
  onAddTopic
}: WeekCardProps) => {
  const isMobile = useIsMobile();
  const [expanded, setExpanded] = useState(defaultExpanded || isCurrentWeek);
  const [selectedDay, setSelectedDay] = useState<string | null>(null);
  const [showTopicSourceDialog, setShowTopicSourceDialog] = useState(false);
  const [showOnlyActiveDays, setShowOnlyActiveDays] = useState(false);
  const [calculatedTotalHours, setCalculatedTotalHours] = useState<string>("0:00");
  
  // Format dates for display
  const formatDateForDisplay = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
  };
  
  const displayStartDate = formatDateForDisplay(weekStartDate);
  const displayEndDate = formatDateForDisplay(weekEndDate);
  
  // Update expansion state when the isCurrentWeek property changes
  useEffect(() => {
    if (isCurrentWeek) {
      setExpanded(true);
    }
  }, [isCurrentWeek]);
  
  // Calculate total hours whenever days or their topics change
  useEffect(() => {
    if (days && days.length > 0) {
      const allTopics = days.flatMap(day => day.topics || []);
      const totalDuration = sumDurations(allTopics.map(topic => topic.duration || "0:00"));
      
      console.log(`📊 [WeekCard] Calculated total for week ${weekNumber}:`, {
        topics: allTopics.length,
        durations: allTopics.map(topic => topic.duration),
        total: totalDuration
      });
      
      setCalculatedTotalHours(totalDuration);
    } else {
      setCalculatedTotalHours("0:00");
    }
  }, [days, weekNumber]);
  
  // Handle adding a topic to a specific day
  const handleAddTopic = (day: string) => {
    console.log("📅 Opening topic source dialog for day:", day);
    console.log("⚠️ Current state before:", { showTopicSourceDialog, selectedDay });
    setSelectedDay(day);
    setShowTopicSourceDialog(true);
  };

  // Handle topic source selection
  const handleSelectTopicSource = (source: 'platform' | 'manual') => {
    console.log("🔄 Selected topic source:", source, "for day:", selectedDay);
    if (selectedDay && onAddTopic) {
      onAddTopic(selectedDay, weekNumber, source);
    }
    setShowTopicSourceDialog(false);
    setSelectedDay(null);
  };
  
  // Filter days if showOnlyActiveDays is true
  const filteredDays = showOnlyActiveDays ? days.filter(day => day.topics.length > 0) : days;
  
  console.log('🔶 Rendering WeekCard:', { 
    weekNumber, 
    isCurrentWeek, 
    expanded,
    daysCount: days.length,
    activeDaysCount: days.filter(day => day.topics.length > 0).length,
    dates: `${weekStartDate} - ${weekEndDate}`,
    today: new Date().toISOString().split('T')[0],
    startMatch: weekStartDate <= new Date().toISOString().split('T')[0],
    endMatch: weekEndDate >= new Date().toISOString().split('T')[0],
    dialogState: { showTopicSourceDialog, selectedDay },
    calculatedTotalHours
  });
  
  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: weekNumber * 0.05 }}
        className={`rounded-xl overflow-hidden ${
          isCurrentWeek 
            ? 'ring-2 ring-[#58CC02] shadow-[0_0_10px_rgba(88,204,2,0.2)]' 
            : 'border border-gray-100 bg-white/80'
        }`}
      >
        <div 
          className={`px-3 sm:px-4 py-3 cursor-pointer flex items-center justify-between ${
            isCurrentWeek 
              ? 'bg-[#58CC02]/10' 
              : 'hover:bg-gray-50'
          }`}
          onClick={() => setExpanded(!expanded)}
        >
          <div className="flex items-center gap-2 sm:gap-3">
            <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center ${
              isCurrentWeek 
                ? 'bg-[#58CC02] text-white' 
                : 'bg-gray-100 text-gray-500'
            }`}>
              <span className="text-base sm:text-lg font-bold">{weekNumber}</span>
            </div>
            <div>
              <div className="flex items-center gap-1 sm:gap-2">
                <span className="font-semibold text-sm sm:text-base text-gray-800">
                  {isMobile ? `S${weekNumber}` : `Semana ${weekNumber}`}
                </span>
                {isCurrentWeek && (
                  <Badge className="bg-[#58CC02] text-white ml-1 px-1.5 py-0.5 text-[10px] sm:text-xs animate-pulse font-medium">
                    Atual
                  </Badge>
                )}
              </div>
              <p className="text-xs sm:text-sm text-gray-500">{displayStartDate} - {displayEndDate}</p>
            </div>
          </div>
          <div className="flex items-center gap-2 sm:gap-3">
            <div className="flex items-center gap-1 bg-gray-100 px-2 py-0.5 rounded-full">
              <Clock className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-gray-600" />
              <span className="text-xs sm:text-sm font-medium text-gray-700">
                {calculatedTotalHours}
              </span>
            </div>
            {expanded ? (
              <ChevronUp className="h-4 w-4 sm:h-5 sm:w-5 text-gray-500" />
            ) : (
              <ChevronDown className="h-4 w-4 sm:h-5 sm:w-5 text-gray-500" />
            )}
          </div>
        </div>
        
        {expanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-3 p-3 sm:p-4 bg-white border-t border-gray-100"
          >
            <div className="flex justify-end mb-1 sm:mb-2">
              <div className="flex items-center space-x-1 sm:space-x-2 bg-gray-50 rounded-lg p-1.5 sm:p-2">
                <Switch 
                  id={`show-active-days-${weekNumber}`}
                  checked={showOnlyActiveDays}
                  onCheckedChange={setShowOnlyActiveDays}
                  className="scale-75 sm:scale-100"
                />
                <label 
                  htmlFor={`show-active-days-${weekNumber}`} 
                  className="text-xs sm:text-sm text-gray-600 cursor-pointer"
                >
                  {isMobile ? "Dias com estudos" : "Apenas dias com estudos"}
                </label>
              </div>
            </div>
            
            {/* Changed to flex-col to stack days vertically instead of using a grid */}
            <div className="flex flex-col gap-3 sm:gap-4">
              {filteredDays.length > 0 ? (
                filteredDays.map((day, index) => (
                  <DayCard 
                    key={index} 
                    {...day} 
                    onAddTopic={onAddTopic ? handleAddTopic : undefined}
                  />
                ))
              ) : (
                <div className="flex flex-col items-center justify-center py-5 sm:py-6 text-center bg-gray-50 rounded-lg">
                  <p className="text-gray-500 text-sm sm:text-base">Não há dias com estudos para exibir</p>
                  <p className="text-xs sm:text-sm text-gray-400 mt-1">Desative o filtro para ver todos os dias</p>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </motion.div>

      <TopicSourceDialog 
        open={showTopicSourceDialog}
        onOpenChange={setShowTopicSourceDialog}
        onSelectSource={handleSelectTopicSource}
      />
    </>
  );
};
