import { BasicInfoSection } from "./form-sections/BasicInfoSection";
import { SymptomsSection } from "./form-sections/SymptomsSection";
import { MedicalHistorySection } from "./form-sections/MedicalHistorySection";

interface FormFieldsProps {
  formData: any;
  setFormData: (data: any) => void;
}

export function FormFields({ formData, setFormData }: FormFieldsProps) {
  return (
    <div className="space-y-8">
      <BasicInfoSection formData={formData} setFormData={setFormData} />
      <SymptomsSection formData={formData} setFormData={setFormData} />
      <MedicalHistorySection formData={formData} setFormData={setFormData} />
    </div>
  );
}