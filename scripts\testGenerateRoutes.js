import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://bxedpdmgvgatjdfxgxij.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ4ZWRwZG1ndmdhdGpkZnhneGlqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzIyNzk3MTgsImV4cCI6MjA0Nzg1NTcxOH0.cjoaggOXt1kY9WmVNbAipCOQ2dP4PWLP43KMf8cO8Wo';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testFetchMedications() {
  console.log('🔍 Testando busca de medicamentos...');
  
  try {
    const { data, error } = await supabase
      .from('pedbook_medications')
      .select(`
        id,
        slug,
        name,
        brands,
        contraindications,
        guidelines,
        scientific_references,
        pedbook_medication_categories (
          name
        ),
        pedbook_medication_dosages (
          name,
          summary,
          dosage_template
        )
      `)
      .limit(3);

    if (error) {
      console.error('❌ Erro:', error);
      return;
    }

    console.log(`✅ Encontrados ${data?.length || 0} medicamentos`);
    
    if (data && data.length > 0) {
      console.log('\n📋 Primeiro medicamento:');
      console.log('Nome:', data[0].name);
      console.log('Slug:', data[0].slug);
      console.log('Marcas:', data[0].brands ? 'Sim' : 'Não');
      console.log('Categoria:', data[0].pedbook_medication_categories?.name || 'N/A');
      console.log('Dosagens:', data[0].pedbook_medication_dosages?.length || 0);
      
      if (data[0].pedbook_medication_dosages && data[0].pedbook_medication_dosages.length > 0) {
        console.log('Primeira dosagem:', data[0].pedbook_medication_dosages[0].name);
      }
    }
    
  } catch (err) {
    console.error('❌ Erro na conexão:', err.message);
  }
}

testFetchMedications();
