
import React from 'react';
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import type { SelectedFilters } from "@/types/question";

interface YearFilterProps {
  selectedYears: string[];
  onToggleYear: (year: string) => void;
  questionCounts: {
    totalCounts: { [key: string]: number };
    filteredCounts: { [key: string]: number };
  };
  hasActiveFilters: boolean;
  selectedFilters: SelectedFilters;
  searchTerm?: string;
}

export const YearFilter = ({ 
  selectedYears, 
  onToggleYear,
  questionCounts,
  hasActiveFilters,
  selectedFilters,
  searchTerm = ""
}: YearFilterProps) => {
  // Generate years from current year back to 2010
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: currentYear - 2009 }, (_, i) => currentYear - i);

  // Filter years based on search term if provided
  const filteredYears = searchTerm 
    ? years.filter(year => year.toString().includes(searchTerm))
    : years;

  return (
    <ScrollArea className="h-72 pr-4" style={{ overflowY: 'auto' }}>
      <div className="space-y-4 pb-4">
        {filteredYears.map((year) => {
          const yearStr = year.toString();
          return (
            <div
              key={year}
              className="flex items-center space-x-2 p-2 hover:bg-accent rounded-lg cursor-pointer"
              onClick={() => onToggleYear(yearStr)}
            >
              <Checkbox
                id={`year-${year}`}
                checked={selectedYears.includes(yearStr)}
                onCheckedChange={() => onToggleYear(yearStr)}
              />
              <label
                htmlFor={`year-${year}`}
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
              >
                {year}
              </label>
            </div>
          );
        })}
      </div>
    </ScrollArea>
  );
};
