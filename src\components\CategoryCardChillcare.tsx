
import { Link } from "react-router-dom";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import "./CategoryCardChillcare.css";

interface CategoryCardProps {
  title: string;
  description?: string;
  icon: LucideIcon;
  color: string;
  path: string;
  badge?: string;
  isHighlighted?: boolean;
}

const CategoryCard = ({
  title,
  description,
  icon: Icon,
  color,
  path,
  badge,
  isHighlighted = false,
}: CategoryCardProps) => {

  // Extract color name from the color class (e.g., "bg-amber-50" -> "amber")
  const colorName = color.includes("-") ? color.split("-")[1] : color;
  
  // Borda dinamicamente baseada na cor
  const getBorderColorClass = () => {
    if (color.includes("yellow")) return "border-yellow-500 dark:border-yellow-600";
    if (color.includes("purple")) return "border-purple-500 dark:border-purple-600";
    if (color.includes("blue")) return "border-blue-500 dark:border-blue-600";
    if (color.includes("pink")) return "border-pink-500 dark:border-pink-600";
    if (color.includes("green")) return "border-green-500 dark:border-green-600";
    if (color.includes("amber")) return "border-amber-500 dark:border-amber-600";
    if (color.includes("red")) return "border-red-500 dark:border-red-600";
    if (color.includes("cyan")) return "border-cyan-500 dark:border-cyan-600";
    if (color.includes("indigo")) return "border-indigo-500 dark:border-indigo-600";
    if (color.includes("rose")) return "border-rose-500 dark:border-rose-600";
    if (color === "bg-white") return "border-slate-300 dark:border-slate-600";
    return "border-primary dark:border-primary/70";
  };

  // Cor do texto baseada na cor do card
  const getTextColorClass = () => {
    if (color.includes("yellow")) return "text-yellow-700 dark:text-yellow-300";
    if (color.includes("purple")) return "text-purple-700 dark:text-purple-300";
    if (color.includes("blue")) return "text-blue-700 dark:text-blue-300";
    if (color.includes("pink")) return "text-pink-700 dark:text-pink-300";
    if (color.includes("green")) return "text-green-700 dark:text-green-300";
    if (color.includes("amber")) return "text-amber-700 dark:text-amber-300";
    if (color.includes("red")) return "text-red-700 dark:text-red-300";
    if (color.includes("cyan")) return "text-cyan-700 dark:text-cyan-300";
    if (color.includes("indigo")) return "text-indigo-700 dark:text-indigo-300";
    if (color.includes("rose")) return "text-rose-700 dark:text-rose-300";
    if (color === "bg-white") return "text-primary dark:text-blue-400";
    return "text-gray-700 dark:text-gray-300";
  };

  // Cor de fundo do ícone baseada na cor do card
  const getBackgroundColorClass = () => {
    if (color.includes("yellow")) return "bg-yellow-50 dark:bg-yellow-900/30";
    if (color.includes("purple")) return "bg-purple-50 dark:bg-purple-900/30";
    if (color.includes("blue")) return "bg-blue-50 dark:bg-blue-900/30";
    if (color.includes("pink")) return "bg-pink-50 dark:bg-pink-900/30";
    if (color.includes("green")) return "bg-green-50 dark:bg-green-900/30";
    if (color.includes("amber")) return "bg-amber-50 dark:bg-amber-900/30";
    if (color.includes("red")) return "bg-red-50 dark:bg-red-900/30";
    if (color.includes("cyan")) return "bg-cyan-50 dark:bg-cyan-900/30";
    if (color.includes("indigo")) return "bg-indigo-50 dark:bg-indigo-900/30";
    if (color.includes("rose")) return "bg-rose-50 dark:bg-rose-900/30";
    if (color === "bg-white") return "bg-white dark:bg-slate-800";
    return "bg-gray-50 dark:bg-slate-800";
  };

  const borderColor = getBorderColorClass();
  const textColor = getTextColorClass();
  const bgColor = getBackgroundColorClass();
  
  return (
    <Link to={path} className="block h-full relative">
      <div 
        className={cn(
          "h-full rounded-xl transition-all duration-300 hover:shadow-lg cursor-pointer",
          "p-5",
          "border-2", 
          borderColor,
          "bg-white/55 dark:bg-slate-800/75",
          "glass-card card-enhanced relative overflow-hidden",
          "hover:shadow-md hover:scale-[1.02]",
          isHighlighted && "border-blue-500 dark:border-blue-400 shadow-md isHighlighted"
        )}
      >
        {/* Ribbon para o card destacado */}
        {isHighlighted && (
          <div className="absolute -right-12 top-5 bg-blue-600 text-white text-xs py-1 w-36 transform rotate-45 text-center shadow-md z-10 dark:bg-blue-500">
            Destaque
          </div>
        )}
        
        <div className="flex flex-col items-center text-center h-full">
          <div 
            className={cn(
              "w-16 h-16 rounded-xl flex items-center justify-center mb-4 shadow-md",
              bgColor,
              "border-2",
              borderColor
            )}
          >
            <Icon 
              className={cn(
                "h-8 w-8", 
                textColor
              )} 
            />
          </div>
          
          <h3 className={cn(
            "font-bold text-gray-800 dark:text-gray-200 mb-2",
            "text-lg"
          )}>
            {title}
          </h3>
          
          {description && (
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-3">
              {description}
            </p>
          )}
          
          {badge && (
            <div className="mt-auto">
              <Badge 
                variant={isHighlighted ? "default" : "outline"} 
                className={cn(
                  isHighlighted 
                    ? "bg-blue-500 text-white dark:bg-blue-600 font-medium"
                    : "bg-white/90 dark:bg-slate-800/90 font-medium border-primary/30 dark:border-primary/40 text-primary dark:text-blue-400",
                  "shadow-sm"
                )}
              >
                {badge}
              </Badge>
            </div>
          )}
        </div>

        {/* Background gradient for highlighted card */}
        {isHighlighted && (
          <div className="absolute inset-0 bg-gradient-to-br from-blue-100/30 to-transparent dark:from-blue-900/20 dark:to-transparent -z-10"></div>
        )}
      </div>
    </Link>
  );
};

export default CategoryCard;
