import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { VaccineDialog } from "@/components/admin/vaccine/VaccineDialog";
import { VaccineList } from "@/components/admin/vaccine/VaccineList";

export default function VaccineManagement() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { toast } = useToast();

  const { data: vaccines, isLoading } = useQuery({
    queryKey: ['vaccines'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_vaccines')
        .select(`
          id,
          name,
          description,
          pedbook_vaccine_doses (
            id,
            dose_number,
            age_recommendation,
            description,
            type
          )
        `)
        .order('name');

      if (error) throw error;
      return data;
    },
  });

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Gerenciamento de Vacinas</h1>
        <Button onClick={() => setIsDialogOpen(true)}>
          Adicionar Vacina
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center">
          <p>Carregando vacinas...</p>
        </div>
      ) : (
        <VaccineList vaccines={vaccines || []} />
      )}

      <VaccineDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
      />
    </div>
  );
}