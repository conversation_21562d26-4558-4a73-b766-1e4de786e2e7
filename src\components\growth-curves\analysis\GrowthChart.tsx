import { ChartLegend } from './chart/ChartLegend';
import { COLORS, MEASUREMENT_LABELS } from './constants';
import { useEffect, useRef, useState } from 'react';
import { formatAge, formatAgeCompact } from '@/utils/formatAge';
import { getOptimalAgeRange, getOptimalTickInterval } from '@/utils/chartRangeCalculator';

// Importar recharts diretamente (usado apenas em páginas específicas)
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

interface ChartData {
  age: number;
  p3: number;
  p15: number;
  p50: number;
  p85: number;
  p97: number;
  patient?: number;
}

interface GrowthChartProps {
  data: ChartData[];
  measurementType: 'weight' | 'height' | 'bmi' | 'head-circumference';
}

const formatXAxisTick = (value: number) => {
  if (value === 0) return '0';
  return formatAgeCompact(value);
};

const formatTooltipValue = (value: number, measurementType: string) => {
  const unit = measurementType === 'weight' ? 'kg' : 
              measurementType === 'height' ? 'cm' : 
              measurementType === 'bmi' ? 'kg/m²' :
              'cm';
  return `${value.toFixed(1)} ${unit}`;
};

const formatTooltipLabel = (age: number) => {
  return formatAge(age);
};

export function GrowthChart({ data, measurementType }: GrowthChartProps) {
  // Verificar pontos do paciente
  const patientPoints = data?.filter(point => point.patient !== undefined) || [];

  // NOVO: Calcular configuração otimizada do eixo X baseada nos dados
  const calculateXAxisConfig = () => {
    if (!data || data.length === 0) return { interval: 2 };

    const ages = data.map(point => point.age);
    const minAge = Math.min(...ages);
    const maxAge = Math.max(...ages);
    const totalMonths = maxAge - minAge;

    const interval = getOptimalTickInterval(totalMonths);

    return { interval, domain: [minAge, maxAge] };
  };

  const xAxisConfig = calculateXAxisConfig();

  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  useEffect(() => {
    if (containerRef.current) {
      const resizeObserver = new ResizeObserver(entries => {
        for (let entry of entries) {
          const { width, height } = entry.contentRect;
          setDimensions({ width, height });
        }
      });

      resizeObserver.observe(containerRef.current);

      return () => {
        resizeObserver.disconnect();
      };
    }
  }, []);

  return (
    <div ref={containerRef} className="w-full h-[500px] min-h-[500px]">
      {dimensions.width > 0 && dimensions.height > 0 && (
        <ResponsiveContainer width="100%" height="100%">
          <LineChart 
            data={data}
            margin={{ top: 5, right: 0, left: -20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="age"
              tick={{ fontSize: 10 }}
              tickFormatter={formatXAxisTick}
              interval={xAxisConfig.interval}
              domain={xAxisConfig.domain || ['dataMin', 'dataMax']}
            />
            <YAxis 
              tick={{ fontSize: 10 }}
              domain={['auto', 'auto']}
            />
            <Tooltip 
              formatter={(value: number, name: string) => [
                formatTooltipValue(value, measurementType),
                name === 'patient' ? 'Paciente' : name
              ]}
              labelFormatter={formatTooltipLabel}
              contentStyle={{
                backgroundColor: 'white',
                border: '1px solid #ccc',
                borderRadius: '4px',
                padding: '8px',
                fontSize: '12px'
              }}
            />
            <Legend 
              content={props => <ChartLegend {...props} measurementType={measurementType} />}
            />
            {Object.entries(COLORS).map(([key, color]) => (
              key !== 'patient' && (
                <Line 
                  key={key}
                  type="monotone" 
                  dataKey={key} 
                  stroke={color}
                  dot={false}
                  strokeDasharray={['p3', 'p15', 'p85', 'p97'].includes(key) ? '5 5' : undefined}
                  strokeWidth={key === 'p50' ? 2 : 1}
                />
              )
            ))}
            <Line
              type="monotone"
              dataKey="patient"
              stroke={COLORS.patient}
              strokeWidth={2}
              dot={{ r: 6, fill: COLORS.patient }}
              connectNulls={false}
            />
          </LineChart>
        </ResponsiveContainer>
      )}
      <div className="text-center text-[8px] text-gray-500 mt-2">
        WHO Child Growth Standards
      </div>
    </div>
  );
}