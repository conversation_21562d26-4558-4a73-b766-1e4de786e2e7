import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Pencil, Trash2 } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useQueryClient } from "@tanstack/react-query";

interface Formula {
  id: string;
  name: string;
  brand: string;
  description: string | null;
  age_range: string;
  image_url: string | null;
  pedbook_formula_categories: {
    id: string;
    name: string;
  } | null;
}

interface FormulaListProps {
  formulas: Formula[];
  onEdit: (formula: Formula) => void;
}

export function FormulaList({ formulas, onEdit }: FormulaListProps) {
  const [formulaToDelete, setFormulaToDelete] = useState<Formula | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const handleDelete = async () => {
    if (!formulaToDelete) return;

    try {
      const { error } = await supabase
        .from('pedbook_formulas')
        .delete()
        .eq('id', formulaToDelete.id);

      if (error) throw error;

      toast({
        title: "Fórmula removida com sucesso!",
        description: `A fórmula ${formulaToDelete.name} foi removida.`,
      });

      queryClient.invalidateQueries({ queryKey: ['formulas'] });
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao remover fórmula",
        description: error.message,
      });
    } finally {
      setFormulaToDelete(null);
    }
  };

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {formulas.map((formula) => (
          <Card key={formula.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            {formula.image_url && (
              <div className="relative h-48">
                <img
                  src={formula.image_url}
                  alt={formula.name}
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            <div className="p-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">{formula.name}</h3>
                  <p className="text-sm text-muted-foreground">{formula.brand}</p>
                  {formula.pedbook_formula_categories && (
                    <span className="inline-block px-2 py-1 rounded-full text-xs bg-primary/10 text-primary">
                      {formula.pedbook_formula_categories.name}
                    </span>
                  )}
                </div>
                {formula.description && (
                  <p className="text-sm text-muted-foreground">{formula.description}</p>
                )}
                <p className="text-sm text-muted-foreground">Faixa etária: {formula.age_range}</p>
                <div className="flex gap-2 pt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEdit(formula)}
                  >
                    <Pencil className="h-4 w-4 mr-2" />
                    Editar
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => setFormulaToDelete(formula)}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Remover
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      <AlertDialog open={!!formulaToDelete} onOpenChange={() => setFormulaToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Você tem certeza?</AlertDialogTitle>
            <AlertDialogDescription>
              Esta ação não pode ser desfeita. Isso removerá permanentemente a fórmula{' '}
              <span className="font-semibold">{formulaToDelete?.name}</span>.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Remover</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}