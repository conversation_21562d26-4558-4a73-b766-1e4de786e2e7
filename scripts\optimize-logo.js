#!/usr/bin/env node

/**
 * Script para otimizar o logo faviconx.webp
 * Usa ferramentas online ou cria versões manuais
 */

import fs from 'fs';
import path from 'path';

async function optimizeLogo() {
  try {
    console.log('🚀 Iniciando otimização do logo...');

    const originalPath = path.join(process.cwd(), 'public', 'faviconx.webp');

    if (!fs.existsSync(originalPath)) {
      console.error('❌ Arquivo faviconx.webp não encontrado!');
      process.exit(1);
    }

    // Verificar tamanho original
    const originalStats = fs.statSync(originalPath);
    console.log(`📊 Tamanho original: ${(originalStats.size / 1024).toFixed(2)} KB`);

    // Backup do original
    const backupPath = path.join(process.cwd(), 'public', 'faviconx-original.webp');
    fs.copyFileSync(originalPath, backupPath);
    console.log(`📦 Backup criado: faviconx-original.webp`);

    console.log('\n📋 PRÓXIMOS PASSOS MANUAIS:');
    console.log('1. Acesse: https://squoosh.app/');
    console.log('2. Faça upload do faviconx.webp');
    console.log('3. Configure:');
    console.log('   - Resize: 48x48 pixels');
    console.log('   - Format: WebP');
    console.log('   - Quality: 85%');
    console.log('4. Download e substitua o arquivo original');
    console.log('\nOu use o comando: npm run optimize-images');

  } catch (error) {
    console.error('❌ Erro durante a otimização:', error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
optimizeLogo();
