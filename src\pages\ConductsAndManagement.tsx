
import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { supabase } from "@/integrations/supabase/client";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { ChevronLeft, Search, Loader2, Zap } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate } from 'react-router-dom';
import { Input } from "@/components/ui/input";
import { CategoryCard } from "@/components/conducts/CategoryCard";
import { performFuzzySearch } from "@/components/search/utils/fuzzySearch";

interface Topic {
  id: string;
  name: string;
  slug: string;
  category_id: string;
}

interface Category {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  slug: string;
  image_url: string;
  coming_soon: boolean;
  summary_count?: number;
  topics?: Topic[];
  isFuzzyMatch?: boolean; // Flag para indicar busca fuzzy
}

const ConductsAndManagement = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredCategories, setFilteredCategories] = useState<Category[]>([]);
  const [fuzzyCategories, setFuzzyCategories] = useState<Category[]>([]);
  const navigate = useNavigate();

  // Carregar categorias e tópicos de uma vez
  useEffect(() => {
    const fetchCategoriesWithTopics = async () => {
      try {
        
        // Primeiro, pegar todas as categorias
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('pedbook_conducts_categories')
          .select('*')
          .order('display_order', { ascending: true });

        if (categoriesError) throw categoriesError;

        if (!categoriesData || categoriesData.length === 0) {
          setCategories([]);
          setLoading(false);
          return;
        }

        // Segundo, pegar todos os tópicos de uma vez
        const { data: allTopicsData, error: topicsError } = await supabase
          .from('pedbook_conducts_topics')
          .select('id, name, slug, category_id');

        if (topicsError) throw topicsError;

        // Terceiro, pegar contagem de resumos para todos os tópicos
        const { data: summariesData, error: summariesError } = await supabase
          .from('pedbook_conducts_summaries')
          .select('topic_id')
          .eq('published', true);

        if (summariesError) throw summariesError;

        // Processar contagens
        const topicCounts: Record<string, number> = {};
        const categoryCounts: Record<string, number> = {};
        
        if (summariesData) {
          summariesData.forEach(summary => {
            if (summary.topic_id) {
              topicCounts[summary.topic_id] = (topicCounts[summary.topic_id] || 0) + 1;
            }
          });
        }

        // Agrupar tópicos por categoria
        const topicsByCategory: Record<string, Topic[]> = {};
        
        if (allTopicsData) {
          allTopicsData.forEach(topic => {
            if (!topicsByCategory[topic.category_id]) {
              topicsByCategory[topic.category_id] = [];
            }
            topicsByCategory[topic.category_id].push(topic);
            
            // Calcular contagens de categoria somando tópicos
            if (topicCounts[topic.id]) {
              categoryCounts[topic.category_id] = (categoryCounts[topic.category_id] || 0) + 1;
            }
          });
        }

        // Montar categorias com tópicos e contagens
        const enrichedCategories = categoriesData.map(category => ({
          ...category,
          topics: topicsByCategory[category.id] || [],
          summary_count: categoryCounts[category.id] || 0
        }));

        // Ordenar: primeiro as categorias com conteúdo e não "coming_soon"
        const sortedCategories = enrichedCategories.sort((a, b) => {
          if (a.coming_soon && !b.coming_soon) return 1;
          if (!a.coming_soon && b.coming_soon) return -1;
          
          return (b.summary_count || 0) - (a.summary_count || 0);
        });

        setCategories(sortedCategories);
        setFilteredCategories(sortedCategories);
      } catch (error) {
        console.error("Error fetching categories with topics:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategoriesWithTopics();
  }, []);

  // Filtro de busca avançado que procura em categorias e tópicos + fuzzy search
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredCategories(categories);
      setFuzzyCategories([]);
    } else {
      // Busca normal em múltiplos níveis (categorias e tópicos)
      const filtered = categories.filter(category => {
        // Verificar correspondência no nome da categoria
        const nameMatch = category.name.toLowerCase().includes(searchTerm.toLowerCase());

        // Verificar correspondência na descrição da categoria
        const descriptionMatch = category.description &&
          category.description.toLowerCase().includes(searchTerm.toLowerCase());

        // Verificar correspondência nos tópicos da categoria
        const topicMatch = category.topics && category.topics.some(
          topic => topic.name.toLowerCase().includes(searchTerm.toLowerCase())
        );

        return nameMatch || descriptionMatch || topicMatch;
      });

      setFilteredCategories(filtered);

      // Se poucos resultados normais, fazer busca fuzzy
      if (filtered.length < 3 && searchTerm.length >= 3) {
        const fuzzyResults = performFuzzySearch(
          categories,
          searchTerm,
          ['name', 'description'],
          5, // Máximo 5 resultados fuzzy
          0.5 // Threshold mais permissivo
        );

        // Filtrar duplicatas com resultados normais
        const uniqueFuzzyResults = fuzzyResults
          .filter(result => !filtered.some(cat => cat.id === result.item.id))
          .map(result => ({
            ...result.item,
            isFuzzyMatch: true
          }));

        setFuzzyCategories(uniqueFuzzyResults);
      } else {
        setFuzzyCategories([]);
      }
    }
  }, [searchTerm, categories]);

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800">
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-8"
          >
            <div className="flex items-center gap-4 mb-6">
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-10 w-10 md:h-12 md:w-12 hover:bg-blue-50/60 dark:hover:bg-slate-800/60 transition-colors"
                onClick={() => navigate('/')}
                aria-label="Voltar ao menu inicial"
              >
                <ChevronLeft className="h-5 w-5 md:h-6 md:w-6" />
              </Button>
              
              <div className="flex-1">
                <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-500">
                  Condutas e Manejos Pediátricos
                </h1>
                <p className="text-sm md:text-base lg:text-lg text-gray-600 dark:text-gray-300">
                  Acesse protocolos e condutas organizados por categoria para auxiliar na sua prática clínica
                </p>
              </div>
            </div>
            
            <div className="relative my-8">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                type="search"
                placeholder="Buscar por categoria ou tópico específico..."
                className="pl-10 py-6 border-blue-100 dark:border-slate-700 bg-white dark:bg-slate-800 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </motion.div>

          {loading ? (
            <div className="flex flex-col items-center justify-center py-20">
              <Loader2 className="w-12 h-12 animate-spin text-primary" />
              <p className="mt-4 text-gray-600 dark:text-gray-300">Carregando categorias...</p>
            </div>
          ) : (
            <div className="space-y-8">
              {/* Resultados normais */}
              {filteredCategories.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredCategories.map((category, index) => (
                    <CategoryCard
                      key={category.id}
                      category={category}
                      index={index}
                      searchTerm={searchTerm}
                    />
                  ))}
                </div>
              )}

              {/* Resultados fuzzy */}
              {fuzzyCategories.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-4">
                    <Zap className="h-5 w-5 text-blue-500" />
                    <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                      Você quis dizer ?
                    </h3>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {fuzzyCategories.map((category, index) => (
                      <CategoryCard
                        key={`fuzzy-${category.id}`}
                        category={category}
                        index={index + filteredCategories.length}
                        searchTerm={searchTerm}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* Mensagem quando não há resultados */}
              {filteredCategories.length === 0 && fuzzyCategories.length === 0 && !loading && (
                <div className="text-center py-12">
                  <p className="text-lg text-gray-600 dark:text-gray-300 mb-2">
                    Nenhuma categoria ou tópico encontrado para:
                  </p>
                  <p className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-4 bg-gray-200/50 dark:bg-slate-600/50 px-4 py-2 rounded-md inline-block">
                    "{searchTerm}"
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Tente usar termos mais gerais ou verifique a ortografia.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default ConductsAndManagement;
