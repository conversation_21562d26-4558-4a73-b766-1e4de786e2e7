import { ChevronRight } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface FilterItemProps {
  item: {
    id: string;
    name: string;
    type: string;
  };
  level: number;
  isExpanded: boolean;
  isSelected: boolean;
  questionCount: {
    total: number;
    filtered: number;
  };
  hasChildren: boolean;
  onToggleExpand: (id: string) => void;
  onToggleSelect: (id: string, type: string) => void;
}

export const FilterItem = ({
  item,
  level,
  isExpanded,
  isSelected,
  questionCount,
  hasChildren,
  onToggleExpand,
  onToggleSelect
}: FilterItemProps) => {
  const handleSelect = () => {
    onToggleSelect(item.id, item.type);
  };

  // Get colors based on the hierarchy level (MedEvo style)
  const getBorderColor = () => {
    switch (level) {
      case 0: return 'border-blue-200'; // Specialties (top level)
      case 1: return 'border-green-200'; // Themes
      case 2: return 'border-amber-200'; // Focuses
      default: return 'border-gray-200';
    }
  };

  const getHoverColor = () => {
    switch (level) {
      case 0: return 'hover:bg-blue-50/60';
      case 1: return 'hover:bg-green-50/60';
      case 2: return 'hover:bg-amber-50/60';
      default: return 'hover:bg-gray-50/60';
    }
  };

  const getSelectedColor = () => {
    switch (level) {
      case 0: return 'bg-blue-50';
      case 1: return 'bg-green-50';
      case 2: return 'bg-amber-50';
      default: return 'bg-[#FEF7CD]';
    }
  };

  const getBadgeColor = () => {
    if (isSelected) return 'bg-[#FF6B00] text-white border-none';

    switch (level) {
      case 0: return 'bg-blue-100 text-blue-700 border-blue-200';
      case 1: return 'bg-green-100 text-green-700 border-green-200';
      case 2: return 'bg-amber-100 text-amber-700 border-amber-200';
      default: return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };

  return (
    <div
      className={cn(
        'flex items-center justify-between p-2 rounded-lg border transition-all duration-200',
        getBorderColor(),
        getHoverColor(),
        isSelected && getSelectedColor(),
        level > 0 && 'sm:ml-6 ml-3'
      )}
    >
      <div className="flex items-center gap-2 flex-1">
        {hasChildren && (
          <Button
            variant="ghost"
            size="sm"
            className="p-0 h-6 w-6 hover:bg-transparent"
            onClick={() => onToggleExpand(item.id)}
          >
            <ChevronRight
              className={cn(
                "h-4 w-4 transition-transform duration-200 text-gray-400",
                isExpanded && "rotate-90"
              )}
            />
          </Button>
        )}

        <div
          className="flex items-center gap-3 flex-1 cursor-pointer"
          onClick={handleSelect}
        >
          <div
            className={cn(
              "w-5 h-5 rounded border transition-all duration-200 flex items-center justify-center",
              isSelected
                ? "bg-[#FF6B00] border-[#FF6B00] text-white"
                : "border-gray-300 hover:border-[#FF6B00]",
            )}
          >
            {isSelected && (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                className="w-3.5 h-3.5"
              >
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
            )}
          </div>

          <span className={cn(
            "text-sm transition-colors duration-200",
            isSelected ? "text-[#FF6B00] font-medium" : "text-gray-700"
          )}>
            {item.name}
          </span>
        </div>
      </div>

      <Badge
        variant="outline"
        className={cn(
          "min-w-[3rem] justify-center transition-all duration-200 border",
          getBadgeColor()
        )}
      >
        {questionCount.total}
      </Badge>
    </div>
  );
};
