
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface GrowthCurveFiltersProps {
  selectedGender: string | null;
  selectedGestationalAge: string | null;
  selectedGrowthType: string | null;
  onGenderChange: (value: string) => void;
  onGestationalAgeChange: (value: string) => void;
  onGrowthTypeChange: (value: string) => void;
  onClearFilters: () => void;
}

export function GrowthCurveFilters({
  selectedGender,
  selectedGestationalAge,
  selectedGrowthType,
  onGenderChange,
  onGestationalAgeChange,
  onGrowthTypeChange,
  onClearFilters,
}: GrowthCurveFiltersProps) {
  return (
    <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6 mb-8 max-w-3xl mx-auto border border-gray-100 dark:border-gray-700">
      <div className="flex flex-wrap items-center justify-center gap-4">
        <Select value={selectedGender || ""} onValueChange={onGenderChange}>
          <SelectTrigger className="w-auto min-w-[140px] dark:bg-slate-700 dark:border-gray-600 dark:text-gray-100">
            <SelectValue placeholder="Filtrar por gênero" />
          </SelectTrigger>
          <SelectContent className="bg-white dark:bg-slate-800 dark:border-gray-700">
            <SelectItem value="male" className="dark:text-gray-100">Menino</SelectItem>
            <SelectItem value="female" className="dark:text-gray-100">Menina</SelectItem>
          </SelectContent>
        </Select>

        <Select value={selectedGestationalAge || ""} onValueChange={onGestationalAgeChange}>
          <SelectTrigger className="w-auto min-w-[140px] dark:bg-slate-700 dark:border-gray-600 dark:text-gray-100">
            <SelectValue placeholder="Idade gestacional" />
          </SelectTrigger>
          <SelectContent className="bg-white dark:bg-slate-800 dark:border-gray-700">
            <SelectItem value="term" className="dark:text-gray-100">A Termo</SelectItem>
            <SelectItem value="preterm" className="dark:text-gray-100">Pré-termo</SelectItem>
          </SelectContent>
        </Select>

        <Select value={selectedGrowthType || ""} onValueChange={onGrowthTypeChange}>
          <SelectTrigger className="w-auto min-w-[140px] dark:bg-slate-700 dark:border-gray-600 dark:text-gray-100">
            <SelectValue placeholder="Tipo de crescimento" />
          </SelectTrigger>
          <SelectContent className="bg-white dark:bg-slate-800 dark:border-gray-700">
            <SelectItem value="healthy" className="dark:text-gray-100">Saudável</SelectItem>
            <SelectItem value="disorder" className="dark:text-gray-100">Transtorno</SelectItem>
          </SelectContent>
        </Select>

        {(selectedGender || selectedGestationalAge || selectedGrowthType) && (
          <Button 
            variant="ghost" 
            onClick={onClearFilters} 
            className="gap-2 dark:text-gray-300 dark:hover:bg-slate-700"
          >
            <X className="h-4 w-4" />
            Limpar filtros
          </Button>
        )}
      </div>
    </div>
  );
}
