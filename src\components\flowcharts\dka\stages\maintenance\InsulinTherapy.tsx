import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";

interface InsulinTherapyProps {
  weight: number;
  severity?: "mild" | "moderate" | "severe";
  onPotassiumNormal: (isNormal: boolean) => void;
}

export const InsulinTherapy = ({ weight, severity = "moderate", onPotassiumNormal }: InsulinTherapyProps) => {
  const [insulinType, setInsulinType] = useState<"regular" | "ultrafast" | null>(null);
  const [showInfo, setShowInfo] = useState(false);

  const handleResponse = (isNormal: boolean) => {
    setShowInfo(isNormal);
    onPotassiumNormal(isNormal);
  };

  const showUltrafastOption = severity === "mild";

  return (
    <Card className="p-6 space-y-4">
      <h3 className="text-lg font-semibold">Avaliação para Insulinoterapia</h3>
      
      {!showInfo ? (
        <>
          <p>Apresenta potássio sérico normal (3,5-5,5 mEq/L)?</p>
          <div className="flex gap-4">
            <Button 
              variant="outline" 
              onClick={() => handleResponse(true)}
              className="flex-1"
            >
              Sim
            </Button>
            <Button 
              variant="outline" 
              onClick={() => handleResponse(false)}
              className="flex-1"
            >
              Não
            </Button>
          </div>
        </>
      ) : (
        <>
          <Alert className="bg-blue-50 border-blue-200">
            <AlertDescription className="space-y-2">
              <h4 className="font-medium text-blue-800">Opções de Insulinoterapia:</h4>
              <RadioGroup onValueChange={(value) => setInsulinType(value as "regular" | "ultrafast")}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="regular" id="regular" />
                  <Label htmlFor="regular">
                    Insulina regular EV ({(0.1 * weight).toFixed(2)} UI/kg/hora)
                  </Label>
                </div>
                {showUltrafastOption && (
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="ultrafast" id="ultrafast" />
                    <Label htmlFor="ultrafast">
                      Insulina ultrarrápida SC ({(0.15 * weight).toFixed(2)} UI/kg, 2/2h)
                    </Label>
                  </div>
                )}
              </RadioGroup>
            </AlertDescription>
          </Alert>
        </>
      )}
    </Card>
  );
};