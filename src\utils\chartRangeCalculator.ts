/**
 * Utilitário para calcular ranges otimizados de visualização em gráficos de crescimento
 * Melhora a legibilidade limitando os dados a 1 ano antes e 1 ano depois da idade atual
 */

export interface AgeRange {
  minAge: number;
  maxAge: number;
  totalMonths: number;
  description: string;
}

/**
 * Calcula o range ótimo de idade para visualização do gráfico
 * @param patientAge Idade atual do paciente em meses
 * @param buffer Buffer em meses para cada lado (padrão: 12 meses = 1 ano)
 * @returns Range otimizado com idade mínima e máxima
 */
export const getOptimalAgeRange = (patientAge: number, buffer: number = 12): AgeRange => {
  // Calcular range inicial
  let minAge = Math.max(0, patientAge - buffer);
  let maxAge = Math.min(60, patientAge + buffer);
  
  // Garantir range mínimo de 18 meses para visualização adequada
  const currentRange = maxAge - minAge;
  const minRangeSize = 18;
  
  if (currentRange < minRangeSize) {
    const center = (minAge + maxAge) / 2;
    const halfRange = minRangeSize / 2;
    
    minAge = Math.max(0, center - halfRange);
    maxAge = Math.min(60, center + halfRange);
    
    // Ajustar se ainda não atingiu o mínimo (casos extremos)
    if (maxAge - minAge < minRangeSize) {
      if (minAge === 0) {
        maxAge = Math.min(60, minRangeSize);
      } else if (maxAge === 60) {
        minAge = Math.max(0, 60 - minRangeSize);
      }
    }
  }
  
  // Arredondar para meses inteiros
  minAge = Math.floor(minAge);
  maxAge = Math.ceil(maxAge);
  
  // Gerar descrição do range
  const formatAge = (months: number): string => {
    if (months === 0) return '0m';
    if (months < 12) return `${months}m`;
    const years = Math.floor(months / 12);
    const remainingMonths = months % 12;
    if (remainingMonths === 0) return `${years}a`;
    return `${years}a${remainingMonths}m`;
  };
  
  const description = `${formatAge(minAge)} - ${formatAge(maxAge)}`;
  
  return {
    minAge,
    maxAge,
    totalMonths: maxAge - minAge,
    description
  };
};

/**
 * Filtra dados de crescimento baseado no range de idade
 * @param data Array de dados de crescimento
 * @param ageRange Range de idade calculado
 * @returns Dados filtrados dentro do range especificado
 */
export const filterDataByAgeRange = (data: any[], ageRange: AgeRange) => {
  if (!data || !Array.isArray(data)) {
    return [];
  }

  const filteredData = data.filter((point: any) => {
    const age = point.age_months || point.age || 0;
    return age >= ageRange.minAge && age <= ageRange.maxAge;
  });

  return filteredData;
};

/**
 * Calcula intervalo otimizado para ticks do eixo X baseado no range
 * @param totalMonths Total de meses no range
 * @returns Intervalo recomendado para ticks
 */
export const getOptimalTickInterval = (totalMonths: number): number => {
  if (totalMonths <= 12) return 0; // Mostrar todos os ticks
  if (totalMonths <= 24) return 1; // A cada 2 meses
  if (totalMonths <= 36) return 2; // A cada 3 meses
  return 3; // A cada 4 meses para ranges maiores
};

/**
 * Gera configuração otimizada para o eixo X do gráfico
 * @param ageRange Range de idade
 * @returns Configuração para o componente XAxis
 */
export const getXAxisConfig = (ageRange: AgeRange) => {
  const tickInterval = getOptimalTickInterval(ageRange.totalMonths);
  
  return {
    domain: [ageRange.minAge, ageRange.maxAge] as [number, number],
    interval: tickInterval,
    ticks: generateOptimalTicks(ageRange),
  };
};

/**
 * Gera ticks otimizados para o eixo X
 * @param ageRange Range de idade
 * @returns Array de valores para ticks
 */
const generateOptimalTicks = (ageRange: AgeRange): number[] => {
  const { minAge, maxAge, totalMonths } = ageRange;
  const ticks: number[] = [];
  
  // Sempre incluir o início e fim
  ticks.push(minAge);
  
  // Calcular step baseado no total de meses
  let step: number;
  if (totalMonths <= 12) step = 2; // A cada 2 meses
  else if (totalMonths <= 24) step = 3; // A cada 3 meses
  else if (totalMonths <= 36) step = 6; // A cada 6 meses
  else step = 12; // A cada ano
  
  // Gerar ticks intermediários
  for (let age = minAge + step; age < maxAge; age += step) {
    ticks.push(age);
  }
  
  // Sempre incluir o fim se não estiver já incluído
  if (!ticks.includes(maxAge)) {
    ticks.push(maxAge);
  }
  
  return ticks.sort((a, b) => a - b);
};
