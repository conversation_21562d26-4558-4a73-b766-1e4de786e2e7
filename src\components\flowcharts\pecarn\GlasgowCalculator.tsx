import { useState } from "react";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export const GlasgowCalculator = () => {
  const [values, setValues] = useState({
    eyeOpening: null,
    motorResponse: null,
    verbalResponse: null,
  });

  const criteria = {
    eyeOpening: [
      { value: 4, label: "Espontânea" },
      { value: 3, label: "Ao chamado" },
      { value: 2, label: "Estímulo álgico" },
      { value: 1, label: "Não responde" },
    ],
    motorResponse: [
      { value: 6, label: "Movimentos com propósito" },
      { value: 5, label: "Localiza dor" },
      { value: 4, label: "Retira membros à dor" },
      { value: 3, label: "Flexão anormal (decorticação)" },
      { value: 2, label: "Extensão anormal (descerebração)" },
      { value: 1, label: "Não responde" },
    ],
    verbalResponse: [
      { value: 5, label: "<PERSON><PERSON><PERSON>, sons próprios da idade" },
      { value: 4, label: "Choro consolável" },
      { value: 3, label: "Choro inconsolável" },
      { value: 2, label: "Grunidos ou gemência à dor" },
      { value: 1, label: "Não responde" },
    ],
  };

  const calculateScore = () => {
    return Object.values(values).reduce((sum, value) => sum + (value || 0), 0);
  };

  const score = calculateScore();
  const allFieldsFilled = Object.values(values).every((value) => value !== null);

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <Label>Abertura Ocular</Label>
          <Select
            value={values.eyeOpening?.toString() || ""}
            onValueChange={(value) =>
              setValues((prev) => ({ ...prev, eyeOpening: parseInt(value) }))
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Selecione uma opção" />
            </SelectTrigger>
            <SelectContent>
              {criteria.eyeOpening.map((option) => (
                <SelectItem key={option.value} value={option.value.toString()}>
                  {option.label} ({option.value} pontos)
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Melhor Resposta Motora</Label>
          <Select
            value={values.motorResponse?.toString() || ""}
            onValueChange={(value) =>
              setValues((prev) => ({ ...prev, motorResponse: parseInt(value) }))
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Selecione uma opção" />
            </SelectTrigger>
            <SelectContent>
              {criteria.motorResponse.map((option) => (
                <SelectItem key={option.value} value={option.value.toString()}>
                  {option.label} ({option.value} pontos)
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Melhor Resposta Verbal</Label>
          <Select
            value={values.verbalResponse?.toString() || ""}
            onValueChange={(value) =>
              setValues((prev) => ({ ...prev, verbalResponse: parseInt(value) }))
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Selecione uma opção" />
            </SelectTrigger>
            <SelectContent>
              {criteria.verbalResponse.map((option) => (
                <SelectItem key={option.value} value={option.value.toString()}>
                  {option.label} ({option.value} pontos)
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {allFieldsFilled && (
        <div className="pt-4 border-t">
          <div className="text-center space-y-2">
            <div className="text-4xl font-bold text-primary">{score} pontos</div>
            <div className="text-sm text-muted-foreground">
              {score <= 8
                ? "Trauma grave"
                : score <= 13
                ? "Trauma moderado"
                : "Trauma leve"}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};