import { useState, useEffect } from 'react';
import { useNavigate } from "react-router-dom";
import { QuestionCard } from "@/components/question/QuestionCard";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useStudySession } from "@/hooks/useStudySession";
import { useSessionPersistence } from "@/hooks/useSessionPersistence";
import { useSessionTimer } from "@/hooks/useSessionTimer";
import { useAnswerSubmission } from "@/hooks/useAnswerSubmission";
import { QuestionNavigation } from "./QuestionNavigation";
import { Progress } from "@/components/ui/progress";
import type { Question } from "@/types/question";
import { useQueryClient } from '@tanstack/react-query';

interface QuestionSolverProps {
  questions: Question[];
  sessionId: string;
  userId: string;
}

export const QuestionSolver = ({ questions, sessionId, userId }: QuestionSolverProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState<Record<string, string | null>>({});
  const [answeredQuestions, setAnsweredQuestions] = useState<Set<string>>(new Set());
  const [feedbackStates, setFeedbackStates] = useState<Record<string, boolean>>({});
  const [hasLoadedProgress, setHasLoadedProgress] = useState(false);
  const [correctnessStatuses, setCorrectnessStatuses] = useState<Record<string, boolean | null>>({});
  const navigate = useNavigate();
  const { toast } = useToast();
  const { updateSessionProgress } = useStudySession();
  const { markQuestionAsAnswered } = useSessionPersistence(userId);
  const queryClient = useQueryClient();

  // Hook para submissão de respostas
  const { submitAnswer } = useAnswerSubmission(sessionId, () => {
    // Callback chamado após submissão bem-sucedida
    if (currentQuestionId) {
      markQuestionAsAnswered(currentQuestionId);
      setAnsweredQuestions(prev => new Set([...prev, currentQuestionId]));
      setFeedbackStates(prev => ({
        ...prev,
        [currentQuestionId]: true
      }));
    }
  });

  // Novo sistema de timer robusto
  const {
    totalElapsedTime,
    currentQuestionTime,
    startQuestionTimer,
    pauseCurrentTimer,
    getQuestionTime,
    finishSession: finishTimerSession,
    isActive: isTimerActive
  } = useSessionTimer(sessionId, userId);

  const currentQuestion = questions[currentIndex];
  const currentQuestionId = currentQuestion?.id;
  const isLastQuestion = currentIndex === questions.length - 1;
  const isFirstQuestion = currentIndex === 0;
  const allQuestionsAnswered = answeredQuestions.size === questions.length;
  const progressPercentage = (answeredQuestions.size / questions.length) * 100;

  // Iniciar timer quando questão muda
  useEffect(() => {
    if (currentQuestionId) {

      startQuestionTimer(currentQuestionId);
    }
  }, [currentQuestionId, startQuestionTimer, currentIndex]);

  useEffect(() => {
    const loadSessionProgress = async () => {
      if (!sessionId || hasLoadedProgress) return;

      try {
        // Verificar se o usuário está autenticado
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          console.error('❌ [QuestionSolver] Usuário não autenticado');
          toast({
            title: "Erro de autenticação",
            description: "Você precisa estar logado para acessar o progresso da sessão.",
            variant: "destructive"
          });
          return;
        }

        // Verificar se o usuário tem permissão para acessar esta sessão
        const { data: sessionData, error: sessionError } = await supabase
          .from('study_sessions')
          .select('user_id')
          .eq('id', sessionId)
          .single();

        if (sessionError) {
          if (sessionError.code === '42P01' || sessionError.code === '42501') {
            // Não foi possível acessar a tabela study_sessions
          } else {
            throw sessionError;
          }
        } else if (sessionData && sessionData.user_id !== user.id) {
          // Verificar se o usuário é admin

          const { data: profileData } = await supabase
            .from('profiles')
            .select('is_admin')
            .eq('id', user.id)
            .single();

          const isAdmin = profileData?.is_admin || false;

          if (!isAdmin) {
            console.error('❌ [QuestionSolver] Tentativa de acessar sessão de outro usuário');
            toast({
              title: "Erro de permissão",
              description: "Você não tem permissão para acessar esta sessão.",
              variant: "destructive"
            });
            return;
          }
        }



        const { data: events, error } = await supabase
          .from('session_events')
          .select('question_id, response_status, response')
          .eq('session_id', sessionId);

        if (error) {
          console.error('❌ [QuestionSolver] Erro detalhado ao carregar progresso:', {
            code: error.code,
            message: error.message,
            details: error.details,
            hint: error.hint,
            sessionId
          });

          // Se o erro for relacionado à tabela não existir ou permissão negada, apenas logamos e continuamos
          if (error.code === '42P01') {
            console.warn('⚠️ [QuestionSolver] Tabela session_events não encontrada:', error);
            return; // Continuamos sem os dados de progresso
          } else if (error.code === '42501') {
            console.warn('⚠️ [QuestionSolver] Permissão negada para acessar a tabela session_events:', error);
            return; // Continuamos sem os dados de progresso
          } else {
            console.error('❌ [QuestionSolver] Erro ao carregar progresso da sessão:', error);
            throw error;
          }
        }

        if (events) {
          const answered = new Set(events.map(event => event.question_id));
          setAnsweredQuestions(answered);

          const feedback: Record<string, boolean> = {};
          const correctness: Record<string, boolean | null> = {};
          const answers: Record<string, string | null> = {};

          events.forEach(event => {
            feedback[event.question_id] = true;
            correctness[event.question_id] = event.response_status;

            if (event.response !== null) {
              answers[event.question_id] = event.response;
            }
          });

          setFeedbackStates(feedback);
          setCorrectnessStatuses(correctness);
          setSelectedAnswers(answers);
          setHasLoadedProgress(true);
        }
      } catch (error) {
        console.error('❌ [QuestionSolver] Erro ao processar o progresso da sessão:', error);
      }
    };

    loadSessionProgress();
  }, [sessionId, hasLoadedProgress, questions.length]);

  const handleSubmitAnswer = async (timeSpent: number) => {
    if (!currentQuestionId || !selectedAnswers[currentQuestionId]) return;

    try {
      const submittedAnswerValue = selectedAnswers[currentQuestionId];

      // Usar o tempo real da questão atual em vez do parâmetro
      const actualTimeSpent = getQuestionTime(currentQuestionId);

      // Usar o hook useAnswerSubmission que salva corretamente na tabela user_answers

      const success = await submitAnswer(
        userId,
        currentQuestion,
        submittedAnswerValue,
        actualTimeSpent
      );

      if (success) {
        // Determinar se a resposta está correta para atualizar o status
        const correctAnswerValue = typeof currentQuestion.correct_answer === 'number'
          ? (currentQuestion.correct_answer + 1).toString()
          : (parseInt(String(currentQuestion.correct_answer)) + 1).toString();

        const isCorrect = submittedAnswerValue === correctAnswerValue;

        setCorrectnessStatuses(prev => ({
          ...prev,
          [currentQuestionId]: isCorrect
        }));


      }
    } catch (error) {
      // Error handling
    }
  };

  const handleSelectAnswer = (questionId: string, answer: string) => {
    setSelectedAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleSelectQuestion = (index: number) => {
    setCurrentIndex(index);
  };

  const handleNextQuestion = () => {
    if (currentIndex < questions.length - 1) {
      setCurrentIndex(prevIndex => prevIndex + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentIndex > 0) {
      setCurrentIndex(prevIndex => prevIndex - 1);
    }
  };

  const handleFinishSession = async () => {
    try {


      // Finalizar sistema de timer primeiro
      await finishTimerSession();

      // Verificar se o usuário está autenticado
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('❌ [QuestionSolver] Usuário não autenticado');
        toast({
          title: "Erro de autenticação",
          description: "Você precisa estar logado para finalizar a sessão.",
          variant: "destructive"
        });
        return;
      }

      // Verificar se o usuário tem permissão para finalizar esta sessão
      const { data: sessionData, error: sessionError } = await supabase
        .from('study_sessions')
        .select('user_id')
        .eq('id', sessionId)
        .single();

      if (sessionError) {
        if (sessionError.code === '42P01' || sessionError.code === '42501') {
          console.warn('⚠️ [QuestionSolver] Não foi possível acessar a tabela study_sessions:', sessionError);
        } else {
          console.error('❌ [QuestionSolver] Erro ao verificar permissão da sessão:', sessionError);
          throw sessionError;
        }
      } else if (sessionData && sessionData.user_id !== user.id) {
        // Verificar se o usuário é admin

        const { data: profileData } = await supabase
          .from('profiles')
          .select('is_admin')
          .eq('id', user.id)
          .single();

        const isAdmin = profileData?.is_admin || false;

        if (!isAdmin) {
          console.error('❌ [QuestionSolver] Tentativa de finalizar sessão de outro usuário');
          toast({
            title: "Erro de permissão",
            description: "Você não tem permissão para finalizar esta sessão.",
            variant: "destructive"
          });
          return;
        }
      }

      const { error } = await supabase
        .from('study_sessions')
        .update({
          completed_at: new Date().toISOString(),
          status: 'completed'
        })
        .eq('id', sessionId);

      if (error) {
        // Se o erro for relacionado à tabela não existir ou permissão negada, apenas logamos e continuamos
        if (error.code === '42P01') {
          console.warn('⚠️ [QuestionSolver] Tabela study_sessions não encontrada. Continuando sem finalizar a sessão:', error);
        } else if (error.code === '42501') {
          console.warn('⚠️ [QuestionSolver] Permissão negada para acessar a tabela study_sessions. Continuando sem finalizar a sessão:', error);
        } else {
          console.error('❌ [QuestionSolver] Erro ao finalizar sessão:', error);
          throw error;
        }
      }

      // ✅ INVALIDAR TODOS OS CACHES quando sessão é finalizada manualmente
      // Invalidar estatísticas
      await queryClient.refetchQueries({ queryKey: ['user-statistics'] });
      await queryClient.refetchQueries({ queryKey: ['user-study-stats'] });

      // Invalidar sessões
      await queryClient.refetchQueries({
        predicate: (query) => {
          const key = query.queryKey[0] as string;
          return key === 'study-sessions' || key === 'flashcard-sessions';
        }
      });

      // Invalidar a query de questões acertadas para forçar uma atualização
      queryClient.invalidateQueries({ queryKey: ['correct-questions'] });

      // Toast removido para experiência mais limpa

      navigate(`/results/${sessionId}`);
    } catch {
      toast({
        title: "Erro ao finalizar sessão",
        description: "Tente novamente.",
        variant: "destructive"
      });
    }
  };

  if (!questions.length) return null;

  const indexedAnswerStatuses: Record<number, boolean | null> = {};

  questions.forEach((question, index) => {
    if (question.id in correctnessStatuses) {
      indexedAnswerStatuses[index] = correctnessStatuses[question.id];
    }
  });

  const isQuestionAnswered = currentQuestionId ? answeredQuestions.has(currentQuestionId) : false;
  const showFeedback = currentQuestionId ? feedbackStates[currentQuestionId] : false;

  return (
    <div className="container mx-auto px-4">
      {/* Barra de progresso próxima ao header */}
      <div className="mb-4 bg-white rounded-xl shadow-lg p-4 -mt-6 md:-mt-12">
        <div className="space-y-2">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm font-medium text-gray-500">
              Progresso: {answeredQuestions.size}/{questions.length} questões
            </span>
            <span className="text-sm font-medium text-gray-500">
              {progressPercentage.toFixed(0)}%
            </span>
          </div>
          <Progress value={progressPercentage} className="h-3 bg-gray-100" />
        </div>
      </div>

      <QuestionNavigation
        currentIndex={currentIndex}
        totalQuestions={questions.length}
        onSelectQuestion={handleSelectQuestion}
        onFinishSession={handleFinishSession}
        elapsedTime={totalElapsedTime}
        onTimeUpdate={() => {}} // Não usado mais - timer é gerenciado pelo hook
        questionId={currentQuestion?.id || ""}
        userId={userId}
        answeredStatuses={indexedAnswerStatuses}
      />

      <div className="space-y-6 pb-8">
        <QuestionCard
          question={currentQuestion}
          selectedAnswer={selectedAnswers[currentQuestionId] || null}
          hasAnswered={isQuestionAnswered}
          onSelectAnswer={(answer) => handleSelectAnswer(currentQuestionId, answer)}
          onSubmitAnswer={handleSubmitAnswer}
          onNext={isLastQuestion && allQuestionsAnswered ? handleFinishSession : handleNextQuestion}
          onPrevious={isFirstQuestion ? undefined : handlePreviousQuestion}
          userId={userId}
          sessionId={sessionId}
          isAnswered={isQuestionAnswered}
          timeSpent={currentQuestionTime}
          showFeedback={showFeedback}
          isLastQuestion={isLastQuestion}
          isFirstQuestion={isFirstQuestion}
          allQuestionsAnswered={allQuestionsAnswered}
        />
      </div>
    </div>
  );
};

export default QuestionSolver;
