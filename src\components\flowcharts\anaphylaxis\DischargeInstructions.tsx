import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { ClipboardList } from "lucide-react";

export const DischargeInstructions: React.FC = () => {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ClipboardList className="h-6 w-6 text-green-500" />
          Orientações de Alta
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h3 className="font-medium">Tempo de Observação:</h3>
          <ul className="list-disc pl-6 space-y-1">
            <li>6-8 horas: (Caso tenha apenas sintomas respiratórios);</li>
            <li>12-24 horas: (Caso tenha hipotensão ou disfunção orgânica);</li>
          </ul>
          </div>
        <div className="space-y-2">
          <h3 className="font-medium">Prescrição e Orientações:</h3>
          <ul className="list-disc pl-6 space-y-1">
            <li>Prednisolona 1x/dia VO 3-7dias;</li>
            <li>Antihistamínico (Ex. Fexofenadina, Deslpratadina) VO por 3-7 dias;</li>
            <li>Restrição do fator causal, se suspeito ou conhecido;</li>
            <li>Encaminhar para o especialista;</li>
            <li>Fornecer orientações sobre manejo de crises futuras;</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};