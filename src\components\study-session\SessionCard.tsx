
import React from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { CalendarIcon, CheckCircle2, PlayCircle, Clock, Trash2 } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useIsMobile } from "@/hooks/use-mobile";

interface SessionCardProps {
  session: {
    id: string;
    started_at: string;
    total_questions: number;
    current_question_index: number;
    status: "in_progress" | "completed";
    stats: {
      correct_answers: number;
      total_questions: number;
    };
    specialty_name?: string;
    title?: string;
  };
  onDelete: (id: string) => void;
  onNavigate: (id: string) => void;
}

export const SessionCard = ({ session, onDelete, onNavigate }: SessionCardProps) => {
  const isMobile = useIsMobile();
  
  const dateFormatted = formatDistanceToNow(new Date(session.started_at), {
    addSuffix: true,
    locale: ptBR,
  });

  const isComplete = session.status === "completed";
  const progress = Math.round(
    (session.current_question_index / session.total_questions) * 100
  );
  
  const accuracyPercent = isComplete && session.stats.total_questions > 0
    ? Math.round((session.stats.correct_answers / session.stats.total_questions) * 100)
    : 0;
  
  const getAccuracyColor = (percent: number) => {
    if (percent >= 80) return "text-primary";
    if (percent >= 60) return "text-amber-500";
    return "text-red-500";
  };

  // Verificar se TODAS as questões foram realmente respondidas, independente do status da sessão
  const allQuestionsAnswered = session.current_question_index >= session.total_questions;
  
  // Formatar a exibição de questões corretamente
  const questionsDisplay = allQuestionsAnswered 
    ? "Todas questões" 
    : `${session.current_question_index}/${session.total_questions}`;

  // Determinar a cor de fundo do card baseado no status
  const getCardBackground = () => {
    if (isComplete) {
      return "bg-gradient-to-r from-green-50 to-green-100/60 border-l-4 border-primary";
    }
    return "bg-gradient-to-r from-blue-50 to-blue-100/60 border-l-4 border-primary";
  };


  return (
    <Card className={`p-3 sm:p-4 hover:shadow-md transition-shadow ${getCardBackground()}`}>
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-3">
        <div className="flex flex-col">
          {/* Título com truncamento para evitar overflow */}
          <div className="flex items-center gap-1.5 sm:gap-2">
            <h3 className="font-semibold text-gray-800 text-sm sm:text-base line-clamp-1 max-w-[160px] sm:max-w-none">
              {session.title || session.specialty_name || "Sessão de estudo"}
            </h3>
            {isComplete ? (
              <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-primary whitespace-nowrap">
                <CheckCircle2 className="h-3 w-3 mr-0.5" />
                <span className={isMobile ? "" : "hidden xs:inline"}>Concluído</span>
              </span>
            ) : (
              <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-primary whitespace-nowrap">
                <Clock className="h-3 w-3 mr-0.5" />
                <span className={isMobile ? "" : "hidden xs:inline"}>Em progresso</span>
              </span>
            )}
          </div>
          
          <div className="flex items-center gap-1 sm:gap-2 mt-1 text-gray-500 text-xs sm:text-sm">
            <CalendarIcon className="h-3 w-3 sm:h-3.5 sm:w-3.5 flex-shrink-0" />
            <span className="truncate">{dateFormatted}</span>
          </div>

          {/* Barra de progresso */}
          <div className="mt-2 sm:mt-3 mb-1 sm:mb-2 w-full h-1.5 sm:h-2 bg-gray-100 rounded-full overflow-hidden">
            <div 
              className={`h-full rounded-full bg-primary`}
              style={{ width: `${progress}%` }}
            />
          </div>

          <div className="grid grid-cols-2 gap-2 sm:gap-3 mt-1.5 sm:mt-2">
            <div className="flex items-center gap-1 rounded-lg py-1 sm:py-1.5 px-2 sm:px-2.5 bg-white shadow-sm">
              {isComplete ? (
                <>
                  <div className={`flex items-center gap-1 ${getAccuracyColor(accuracyPercent)} font-medium text-xs sm:text-sm`}>
                    <CheckCircle2 className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                    <span>{accuracyPercent}%</span>
                  </div>
                  <span className="text-xs text-gray-500 ml-1 hidden sm:inline">de acertos</span>
                </>
              ) : (
                <>
                  <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-primary flex-shrink-0" />
                  <span className="text-xs sm:text-sm font-medium text-primary truncate ml-1">
                    {progress}% concluído
                  </span>
                </>
              )}
            </div>
            
            <div className="flex items-center gap-1 rounded-lg py-1 sm:py-1.5 px-2 sm:px-2.5 bg-white shadow-sm">
              <div className="flex gap-1 items-center">
                {allQuestionsAnswered ? (
                  <CheckCircle2 className="h-3 w-3 sm:h-4 sm:w-4 text-primary flex-shrink-0" />
                ) : (
                  <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-primary flex-shrink-0" />
                )}
                <span className={`text-xs font-medium truncate ${allQuestionsAnswered ? 'text-primary' : 'text-gray-700'}`}>
                  {questionsDisplay}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex space-x-2 self-end sm:self-center mt-2 sm:mt-0">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onNavigate(session.id)}
            className="border-primary text-primary hover:bg-primary/10 h-8 px-2 sm:px-3 text-xs sm:text-sm"
          >
            <PlayCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
            <span className="truncate">{isComplete ? "Ver" : "Continuar"}</span>
          </Button>

          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="border-red-300 text-red-500 hover:bg-red-50 h-8 px-2"
              >
                <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Remover sessão</AlertDialogTitle>
                <AlertDialogDescription>
                  Tem certeza que deseja remover esta sessão de estudo? Esta ação não pode ser desfeita.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => onDelete(session.id)}
                  className="bg-red-500 hover:bg-red-600"
                >
                  Remover
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>
    </Card>
  );
};
