import HelmetWrapper from "@/components/utils/HelmetWrapper";

export const GINAMetaTags = () => {
  const pageTitle = "PedBook | GINA - Avaliação do Controle da Asma";
  const description = "Calculadora para avaliação do controle da asma baseada nos critérios GINA 2022. Ferramenta essencial para pediatras e pneumologistas na avaliação e manejo da asma em crianças.";
  const pageUrl = "https://pedb.com.br/calculadoras/gina";
  const keywords = [
    "GINA 2022",
    "controle da asma",
    "avaliação asma",
    "pediatria calculadora",
    "pneumologia pediátrica",
    "manejo da asma",
    "sintomas asma",
    "critérios GINA",
    "asma infantil",
    "tratamento asma",
    "score asma"
  ].join(", ");

  return (
    <HelmetWrapper>
      <title>{pageTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="robots" content="index, follow, max-image-preview:large" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta charSet="UTF-8" />
      <link rel="canonical" href={pageUrl} />

      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content="article" />
      <meta property="og:url" content={pageUrl} />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />
      <meta property="og:image" content="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/calculadora.webp" />
      <meta property="og:image:alt" content="Interface da calculadora GINA exibindo critérios detalhados para avaliação do controle da asma, incluindo frequência de sintomas e limitações de atividades" />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />

      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/calculadora.webp" />
      <meta name="twitter:site" content="@PedBook" />

      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "MedicalWebPage",
          "name": pageTitle,
          "description": description,
          "url": pageUrl,
          "keywords": keywords.split(", "),
          "inLanguage": "pt-BR",
          "mainEntity": {
            "@type": "MedicalCalculator",
            "name": "Calculadora GINA para Controle da Asma",
            "description": description,
            "medicalSpecialty": {
              "@type": "MedicalSpecialty",
              "name": "Pediatria"
            },
            "relevantSpecialty": [
              {
                "@type": "MedicalSpecialty",
                "name": "Pneumologia Pediátrica"
              },
              {
                "@type": "MedicalSpecialty",
                "name": "Pediatria"
              }
            ]
          },
          "about": {
            "@type": "MedicalCondition",
            "name": "Asma",
            "description": "Avaliação do controle da asma através dos critérios GINA 2022"
          },
          "audience": {
            "@type": "MedicalAudience",
            "audienceType": "Profissionais de Saúde"
          },
          "publisher": {
            "@type": "Organization",
            "name": "PedBook",
            "url": "https://pedb.com.br",
            "logo": {
              "@type": "ImageObject",
              "url": "https://pedb.com.br/logo.png"
            }
          }
        })}
      </script>
    </HelmetWrapper>
  );
};