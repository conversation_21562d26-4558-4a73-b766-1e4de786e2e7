import { But<PERSON> } from "@/components/ui/button";
import { ThumbsUp, ThumbsDown } from "lucide-react";

interface ReactionButtonProps {
  type: 'like' | 'dislike';
  count: number;
  isActive: boolean;
  onClick: () => void;
  disabled: boolean;
}

export const ReactionButton = ({
  type,
  count,
  isActive,
  onClick,
  disabled
}: ReactionButtonProps) => {
  const Icon = type === 'like' ? ThumbsUp : ThumbsDown;
  const baseClasses = "gap-1 transition-all duration-300 h-6 px-2 text-xs";
  const activeClasses = "bg-primary text-white hover:bg-primary/90";
  const inactiveClasses = "hover:scale-105 active:scale-95";
  
  return (
    <Button
      variant="outline"
      size="sm"
      className={`${baseClasses} ${isActive ? activeClasses : inactiveClasses}`}
      onClick={onClick}
      disabled={disabled}
    >
      <Icon className="h-3 w-3" />
      <span>{count}</span>
    </Button>
  );
};