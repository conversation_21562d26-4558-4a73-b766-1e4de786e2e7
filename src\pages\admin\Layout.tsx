import { Outlet, Navigate, useLocation } from "react-router-dom";
import AdminNav from "@/components/AdminNav";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { useEffect, useRef } from "react";

export default function AdminLayout() {
  const location = useLocation();
  const layoutRef = useRef<HTMLDivElement>(null);



  // Se estamos exatamente em /admin, redirecionar para o dashboard
  if (location.pathname === "/admin") {
    return <Navigate to="/admin/dashboard" replace />;
  }

  return (
    <div className="min-h-screen flex flex-col" ref={layoutRef}>
      <Header />
      <AdminNav />
      <main className="flex-1 py-6">
        <Outlet />
      </main>
      <div className="hidden sm:block">
        <Footer />
      </div>
    </div>
  );
}
