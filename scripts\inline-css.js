#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 Starting CSS inline process...');

const distDir = path.join(process.cwd(), 'dist');
const htmlFile = path.join(distDir, 'index.html');

// Verificar se o arquivo HTML existe
if (!fs.existsSync(htmlFile)) {
  console.error('❌ index.html not found in dist directory');
  process.exit(1);
}

// Ler o conteúdo do HTML
let htmlContent = fs.readFileSync(htmlFile, 'utf8');
console.log('📖 HTML file read successfully');

// Encontrar todos os arquivos CSS na pasta dist/assets
const assetsDir = path.join(distDir, 'assets');
const cssFiles = fs.readdirSync(assetsDir).filter(file => file.endsWith('.css'));

console.log('🔍 CSS files found:', cssFiles);

if (cssFiles.length === 0) {
  console.log('⚠️ No CSS files found to inline');
  process.exit(0);
}

// Processar cada arquivo CSS
cssFiles.forEach(cssFile => {
  const cssPath = path.join(assetsDir, cssFile);
  const cssContent = fs.readFileSync(cssPath, 'utf8');
  
  console.log(`✅ Processing CSS file: ${cssFile} (${cssContent.length} chars)`);
  
  // Remover referência ao CSS externo do HTML
  const cssLinkRegex = new RegExp(`<link[^>]*href="[^"]*${cssFile.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}"[^>]*>`, 'g');
  htmlContent = htmlContent.replace(cssLinkRegex, '');
  
  // Adicionar CSS inline antes do </head>
  htmlContent = htmlContent.replace('</head>', `<style>${cssContent}</style></head>`);
  
  // Remover arquivo CSS
  fs.unlinkSync(cssPath);
  console.log(`🗑️ Removed CSS file: ${cssFile}`);
});

// Escrever HTML atualizado
fs.writeFileSync(htmlFile, htmlContent);
console.log('💾 HTML file updated with inline CSS');

// Verificar tamanho final
const finalSize = fs.statSync(htmlFile).size;
console.log(`📊 Final HTML size: ${(finalSize / 1024).toFixed(2)} KB`);

console.log('🎉 CSS inline process completed successfully!');
