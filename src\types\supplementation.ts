export type Maturity = 'Term' | 'Pre-term';

export type RiskFactor =
  | "prematurity" // Prematuro (< 37 semanas)
  | "low_birth_weight" // Peso ao nascer < 2500g
  | "multiple_gestation" // Gêmeos, trigêmeos etc.
  | "exclusive_breastfeeding_gt_6m_without_supplement" // AME > 6m sem ferro
  | "poor_iron_diet" // Alimentação complementar pobre em ferro
  | "early_cow_milk_exposure" // Leite de vaca < 12 meses
  | "maternal_anemia" // Anemia na gestação ou lactação
  | "frequent_infections" // Infecções respiratórias ou gastro recorrentes
  | "low_socioeconomic_status" // Vulnerabilidade social importante
  | "vegetarian_diet_without_supplement"; // Dieta vegetariana estrita

export interface SupplementationInput {
  ageInDays: number;
  currentWeight: number;
  birthWeight: number;
  maturity: Maturity;
  exclusiveBreastfeeding: boolean;
  riskFactors?: RiskFactor[];
}

export interface SupplementationResult {
  vitaminA: string;
  vitaminD: string;
  iron: string;
}