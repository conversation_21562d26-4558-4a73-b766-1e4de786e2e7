
import { supabase } from "@/integrations/supabase/client";
import { getOrCreateCategory } from "./import/categoryImporter";
import { getOrCreateLocation } from "./import/locationImporter";
import { getOrCreateYear } from "./import/yearImporter";
import type { ImportQuestion, ImportResults } from "@/types/import";

interface ImportData {
  questions: ImportQuestion[];
}

function normalizeAnswerType(type?: string): 'ALTERNATIVAS' | 'DISSERTATIVA' | 'VERDADEIRO_FALSO' {
  if (!type) return 'ALTERNATIVAS';

  const normalizedType = type.toUpperCase();

  // Mapear SEMPRE para os tipos em português que existem no banco
  switch (normalizedType) {
    // Tipos de múltipla escolha (independente do número de alternativas)
    case 'MULTIPLE_CHOICE':
    case 'MULTIPLE_CHOICE_FOUR':
    case 'MULTIPLE_CHOICE_4':
    case 'ALTERNATIVAS':
    case 'MÚLTIPLA ESCOLHA':
    case 'MULTIPLA ESCOLHA':
    case 'MÚLTIPLA ESCOLHA 4':
    case 'MULTIPLA ESCOLHA 4':
      return 'ALTERNATIVAS';

    // Tipos verdadeiro/falso
    case 'TRUE_OR_FALSE':
    case 'VERDADEIRO_FALSO':
    case 'VERDADEIRO OU FALSO':
    case 'VERDADEIRO_OU_FALSO':
    case 'V_OU_F':
    case 'VF':
      return 'VERDADEIRO_FALSO';

    // Tipos dissertativos
    case 'DISCURSIVE':
    case 'DISSERTATIVA':
    case 'DISCURSIVA':
      return 'DISSERTATIVA';

    default:
      return 'ALTERNATIVAS';
  }
}

function normalizeImages(images?: string | string[]): string[] {
  if (!images || images === "") return [];

  if (typeof images === 'string') {
    return images.trim() ? [images] : [];
  }

  return Array.isArray(images) ? images : [];
}

export async function importQuestions(data: ImportData) {
  try {
    const results: ImportResults = {
      success: 0,
      errors: [],
      created: {
        specialties: new Map(),
        themes: new Map(),
        focuses: new Map(),
        locations: new Map(),
        years: new Set<number>()
      }
    };

    for (const question of data.questions) {
      try {
        console.log('🔄 [importQuestions] Processando questão:', {
          tipo: question.tipo,
          numero: question.numero,
          statement: question.statement?.substring(0, 50) + '...',
          is_annulled: question.is_annulled ? 'ANULADA' : 'Normal'
        });

        const statement = question.statement_text || question.statement;
        if (!statement) {
          throw new Error('Questão sem enunciado');
        }

        let specialty = null;
        if (question.specialty) {
          specialty = results.created.specialties.get(`specialty:${question.specialty}`);
          if (!specialty) {
            specialty = await getOrCreateCategory(question.specialty, 'specialty');
            if (specialty) {
              results.created.specialties.set(`specialty:${question.specialty}`, specialty);
            }
          }
        }

        let theme = null;
        if (question.theme && specialty) {
          theme = results.created.themes.get(`theme:${question.theme}`);
          if (!theme) {
            theme = await getOrCreateCategory(question.theme, 'theme', specialty.id);
            if (theme) {
              results.created.themes.set(`theme:${question.theme}`, theme);
            }
          }
        }

        let focus = null;
        if (question.focus && theme) {
          focus = results.created.focuses.get(`focus:${question.focus}`);
          if (!focus) {
            focus = await getOrCreateCategory(question.focus, 'focus', theme.id);
            if (focus) {
              results.created.focuses.set(`focus:${question.focus}`, focus);
            }
          }
        }

        let location = null;
        if (question.institution_id || question.location) {
          const locationName = question.institution_id || question.location;
          if (locationName) {
            location = await getOrCreateLocation(locationName);
          }
        }

        if (question.year) {
          await getOrCreateYear(question.year);
          results.created.years.add(question.year);
        }

        const answer_type = normalizeAnswerType(question.answer_type);
        const normalizedImages = normalizeImages(question.images);

        // Prepare tags object for custom fields
        const tags = {};
        if (question.topics && question.topics.length > 0) {
          tags['topics'] = question.topics;
        }
        if (question.is_annulled) {
          tags['is_annulled'] = true;
        }

        const questionData = {
          question_content: statement,
          response_choices: question.alternatives,
          correct_choice: String(question.correct_answer),
          specialty_id: specialty?.id,
          theme_id: theme?.id,
          focus_id: focus?.id,
          exam_location: location?.id,
          exam_year: question.year,
          question_format: answer_type,
          knowledge_domain: question.specialty,
          specialty_name: question.specialty,
          media_attachments: normalizedImages,
          assessment_type: question.tipo,
          question_number: question.numero,
          content_tags: Object.keys(tags).length > 0 ? tags : null,
        };

        console.log('📋 [importQuestions] Dados da questão a serem inseridos:', {
          assessment_type: questionData.assessment_type,
          question_number: questionData.question_number,
          media_attachments: normalizedImages.length > 0 ? `${normalizedImages.length} imagens` : 'sem imagens',
          topics: question.topics?.length > 0 ? `${question.topics.length} tópicos` : 'sem tópicos',
          is_annulled: question.is_annulled ? 'ANULADA' : 'Normal'
        });

        const { error: questionError } = await supabase
          .from('questions')
          .insert(questionData)
          .select()
          .single();

        if (questionError) throw questionError;
        
        results.success++;

      } catch (error: any) {
        const errorMessage = `Erro ao importar questão: ${error.message}`;
        console.error('❌ [importQuestions] ' + errorMessage);
        results.errors.push(errorMessage);
      }
    }

    return results;
  } catch (error: any) {
    console.error('❌ [importQuestions] Erro geral:', error);
    throw new Error(`Erro na importação: ${error.message}`);
  }
}

export async function processImportFile(file: File): Promise<ImportResults> {
  try {
    const fileContent = await file.text();
    const data = JSON.parse(fileContent);

    if (!Array.isArray(data.questions)) {
      throw new Error('O arquivo deve conter um array de questões');
    }

    return await importQuestions(data);
  } catch (error: any) {
    console.error('Erro ao processar arquivo:', error);
    throw new Error(`Erro ao processar arquivo: ${error.message}`);
  }
}
