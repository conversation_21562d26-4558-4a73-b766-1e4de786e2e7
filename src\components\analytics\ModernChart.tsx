import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface ModernChartProps {
  title: string;
  data: any[];
  dataKey: string;
  xAxisKey?: string;
  height?: number;
  color?: string;
  isHourlyData?: boolean;
}

export const ModernChart = ({
  title,
  data,
  dataKey,
  xAxisKey = 'name',
  height = 400,
  color = '#3b82f6',
  isHourlyData = false
}: ModernChartProps) => {
  
  const chartData = {
    labels: data.map(item => item[xAxisKey]),
    datasets: [
      {
        label: 'Page Views',
        data: data.map(item => item[dataKey]),
        borderColor: color,
        backgroundColor: `${color}20`,
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: color,
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: '#1F2937',
        titleColor: '#F9FAFB',
        bodyColor: '#F9FAFB',
        borderColor: color,
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
        callbacks: {
          title: (context: any) => {
            const item = data[context[0].dataIndex];
            return item.fullDate || item[xAxisKey];
          },
          label: (context: any) => {
            return `${context.parsed.y} visualizações`;
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          color: '#374151',
          lineWidth: 1,
        },
        ticks: {
          color: '#9CA3AF',
          font: {
            size: 11,
          },
          maxRotation: isHourlyData ? 45 : 0,
          minRotation: isHourlyData ? 45 : 0,
        },
        border: {
          display: false,
        },
      },
      y: {
        grid: {
          color: '#374151',
          lineWidth: 1,
        },
        ticks: {
          color: '#9CA3AF',
          font: {
            size: 12,
          },
        },
        border: {
          display: false,
        },
        beginAtZero: true,
      },
    },
    elements: {
      point: {
        hoverBackgroundColor: color,
      },
    },
    interaction: {
      intersect: false,
      mode: 'index' as const,
    },
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div style={{ height: `${height}px` }}>
          {data && data.length > 0 ? (
            <Line data={chartData} options={options} />
          ) : (
            <div className="flex items-center justify-center h-full text-muted-foreground">
              Nenhum dado disponível para o período selecionado
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
