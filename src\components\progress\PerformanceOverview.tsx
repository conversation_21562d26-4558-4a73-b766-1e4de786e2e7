import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON>, TrendingUp, AlertTriangle } from "lucide-react";

interface PerformanceData {
  name: string;
  accuracy: number;
  total: number;
}

interface PerformanceOverviewProps {
  specialties: PerformanceData[];
  themes: PerformanceData[];
  focuses: PerformanceData[];
}

export const PerformanceOverview = ({ specialties, themes, focuses }: PerformanceOverviewProps) => {
  const sortByAccuracy = (data: PerformanceData[]) => 
    [...data].sort((a, b) => b.accuracy - a.accuracy);

  const getBestAndWorst = (data: PerformanceData[], count: number = 3) => {
    const sorted = sortByAccuracy(data);
    return {
      best: sorted.slice(0, count),
      worst: sorted.slice(-count).reverse()
    };
  };

  const renderPerformanceList = (items: PerformanceData[], type: 'best' | 'worst') => {
    const Icon = type === 'best' ? TrendingUp : AlertTriangle;
    const colorClass = type === 'best' ? 'text-green-500' : 'text-red-500';

    return items.map((item, index) => (
      <div key={index} className="flex items-center justify-between p-3 bg-secondary/10 rounded-lg mb-2">
        <div className="flex items-center gap-2">
          <Icon className={`h-4 w-4 ${colorClass}`} />
          <span className="font-medium">{item.name}</span>
        </div>
        <span className={`${colorClass} font-semibold`}>
          {Math.round(item.accuracy)}%
        </span>
      </div>
    ));
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Melhores Desempenhos */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-green-500" />
            Melhores Desempenhos
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {specialties.length > 0 && (
            <div>
              <h3 className="text-sm font-semibold mb-2">Especialidades</h3>
              {renderPerformanceList(getBestAndWorst(specialties).best, 'best')}
            </div>
          )}
          {themes.length > 0 && (
            <div>
              <h3 className="text-sm font-semibold mb-2">Temas</h3>
              {renderPerformanceList(getBestAndWorst(themes).best, 'best')}
            </div>
          )}
          {focuses.length > 0 && (
            <div>
              <h3 className="text-sm font-semibold mb-2">Focos</h3>
              {renderPerformanceList(getBestAndWorst(focuses).best, 'best')}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Áreas para Melhorar */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            Áreas para Melhorar
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {specialties.length > 0 && (
            <div>
              <h3 className="text-sm font-semibold mb-2">Especialidades</h3>
              {renderPerformanceList(getBestAndWorst(specialties).worst, 'worst')}
            </div>
          )}
          {themes.length > 0 && (
            <div>
              <h3 className="text-sm font-semibold mb-2">Temas</h3>
              {renderPerformanceList(getBestAndWorst(themes).worst, 'worst')}
            </div>
          )}
          {focuses.length > 0 && (
            <div>
              <h3 className="text-sm font-semibold mb-2">Focos</h3>
              {renderPerformanceList(getBestAndWorst(focuses).worst, 'worst')}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};