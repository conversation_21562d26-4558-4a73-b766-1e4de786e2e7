import React, { useState, useEffect } from 'react';

interface ProgressiveLoadingProps {
  children: React.ReactNode;
  minLoadTime?: number; // Tempo mínimo de carregamento em ms
  fallback?: React.ReactNode;
}

/**
 * Componente que mostra um indicador de carregamento por um tempo mínimo
 * antes de renderizar o conteúdo, garantindo uma experiência de usuário mais suave.
 */
export const ProgressiveLoading: React.FC<ProgressiveLoadingProps> = ({
  children,
  minLoadTime = 300,
  fallback = (
    <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
    </div>
  ),
}) => {
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsReady(true);
    }, minLoadTime);

    return () => clearTimeout(timer);
  }, [minLoadTime]);

  return isReady ? <>{children}</> : <>{fallback}</>;
};

export default ProgressiveLoading;
