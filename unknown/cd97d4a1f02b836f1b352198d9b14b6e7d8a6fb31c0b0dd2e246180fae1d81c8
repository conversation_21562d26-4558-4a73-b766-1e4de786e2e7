
import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Flame, CheckCircle, Trophy } from 'lucide-react';
import { useUserStatistics } from '@/hooks/useUserStatistics';
import { format, startOfWeek, addDays, parseISO, isWithinInterval } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useAuth } from '@/hooks/useAuth';

type DayActivityProps = {
  day: string;
  shortDay: string;
  active: boolean;
  index: number;
}

const DayActivity = ({ day, shortDay, active, index }: DayActivityProps) => {
  return (
    <motion.div 
      className="flex flex-col items-center"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1 * index }}
    >
      <span className="text-xs font-medium text-gray-500 mb-1">{shortDay}</span>
      <div className={`
        w-6 h-6 rounded-full flex items-center justify-center
        ${active 
          ? 'bg-orange-500 text-white shadow-md shadow-orange-200' 
          : 'bg-gray-100 text-gray-400'
        }
        transition-all duration-300 ease-in-out
      `}>
        {active && <CheckCircle className="h-3 w-3" />}
      </div>
    </motion.div>
  );
};

// Frases motivacionais para cada dia da semana
const MOTIVATIONAL_QUOTES = [
  "Todos os grandes médicos tiraram sua educação dos pavimentos de terra e da pobreza - não de pisos de mármore e fundações. — Martin H. Fischer", // Domingo
  "Não delego sem ter responsabilidade. Nunca operei sozinho. — Ivo Pitanguy", // Segunda
  "Veja o que ninguém mais vê. Veja o que todo mundo escolhe não ver - por medo, conformidade ou preguiça. Veja o mundo todo de novo a cada dia. — Hunter Doherty \"Patch\" Adams", // Terça
  "A singularidade do indivíduo é incontestável na visão científica. — Burrhus Frederic Skinner", // Quarta
  "Não há maior agonia do que ter uma história não contada dentro de você. — Maya Angelou", // Quinta
  "Descobrimos o segredo da vida. — Francis Crick", // Sexta
  "Sorria sempre que puder, é mais barato que remédio. — Lord Byron" // Sábado
];

const StudyStreak = () => {
  const { data: userStats, isLoading } = useUserStatistics();
  const { user } = useAuth();
  const currentStreak = userStats?.streak_days || 0;
  const maxStreak = userStats?.max_streak || 0;
  const [weekActivities, setWeekActivities] = useState<boolean[]>([]);
  
  // Obter o horário para a saudação
  const currentHour = new Date().getHours();
  const greeting = currentHour < 12 
    ? "Bom dia" 
    : currentHour < 18 
      ? "Boa tarde" 
      : "Boa noite";
  
  // Obter o dia da semana (0-6, onde 0 é domingo)
  const today = new Date();
  const dayOfWeek = today.getDay();
  const quoteOfTheDay = MOTIVATIONAL_QUOTES[dayOfWeek];
  
  useEffect(() => {
    if (userStats) {
      // Determinar início da semana (domingo)
      const today = new Date();
      const weekStart = startOfWeek(today);
      
      // Se temos atividades de estudo recentes, vamos usá-las para destacar os dias
      if (userStats.streak_days > 0) {
        // Pegar as datas das sessões completadas
        const sessionDates = Array.from({ length: 7 }, (_, i) => {
          // Verificar para cada dia da semana se houve atividade
          const currentDay = addDays(weekStart, i);
          const isDayActive = isDayInStreak(currentDay, userStats.streak_days);
          return isDayActive;
        });
        
        setWeekActivities(sessionDates);
      } else {
        // Nenhuma atividade recente
        setWeekActivities(Array(7).fill(false));
      }
    }
  }, [userStats]);

  // Função para determinar se um dia faz parte da sequência atual
  const isDayInStreak = (day: Date, streakDays: number) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Para uma sequência de 4 dias (incluindo hoje), precisamos verificar se o dia está
    // nos últimos 4 dias (hoje, ontem, anteontem e 3 dias atrás)
    const startStreak = new Date(today);
    startStreak.setDate(today.getDate() - (streakDays - 1));
    
    return isWithinInterval(day, { start: startStreak, end: today });
  };

  // Gerar os 7 dias da semana, começando pelo domingo
  const weekDays = Array.from({ length: 7 }, (_, i) => {
    const date = addDays(startOfWeek(new Date()), i);
    return {
      date,
      fullName: format(date, 'EEEE', { locale: ptBR }),
      shortName: format(date, 'EEEEE', { locale: ptBR }).toUpperCase(),
      active: weekActivities[i] || false,
      key: `day-${i}` // Chave única para cada dia
    };
  });

  // Obter o nome do usuário dos metadados ou usar um nome padrão
  const userName = user?.user_metadata?.name || user?.user_metadata?.full_name || "Estudante";

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl p-4 shadow-sm border border-gray-100 w-full max-w-md"
    >
      <div className="flex items-center gap-2 mb-3">
        <div className="bg-orange-100 p-1.5 rounded-full">
          <Flame className="h-5 w-5 text-orange-500" />
        </div>
        <div className="flex items-center justify-between w-full">
          <span className="text-sm font-bold text-orange-500">
            {currentStreak} {currentStreak === 1 ? 'dia' : 'dias'} de sequência
          </span>
          
          {maxStreak > 0 && (
            <div className="flex items-center text-xs text-gray-500">
              <Trophy className="h-3.5 w-3.5 mr-1 text-amber-500" />
              <span>Recorde: {maxStreak}</span>
            </div>
          )}
        </div>
      </div>
      
      {/* Saudação personalizada */}
      <p className="text-sm font-medium text-gray-700 mb-2">
        {greeting}, {userName}!
      </p>
      
      {/* Frase motivacional do dia */}
      <p className="text-xs italic text-gray-600 mb-3 border-l-2 border-primary/30 pl-2">
        "{quoteOfTheDay}"
      </p>
      
      <div className="flex justify-between items-center">
        {weekDays.map((day, index) => (
          <DayActivity 
            key={day.key} 
            day={day.fullName} 
            shortDay={day.shortName} 
            active={day.active} 
            index={index}
          />
        ))}
      </div>
    </motion.div>
  );
};

export default StudyStreak;
