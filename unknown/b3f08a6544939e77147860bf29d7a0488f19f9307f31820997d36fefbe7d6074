
import { Button } from "@/components/ui/button";
import { Copy } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import type { PrescriptionWithMedications } from "../types";
import type { DosageResult } from "@/lib/dosageCalculator";

interface PrescriptionMedicationListProps {
  prescription: PrescriptionWithMedications;
  calculatedDosages: Record<string, DosageResult | string>;
}

export const PrescriptionMedicationList = ({
  prescription,
  calculatedDosages,
}: PrescriptionMedicationListProps) => {
  const { toast } = useToast();

  const generatePrescriptionText = () => {
    let text = `Prescrição: ${prescription.name}\n\n`;
    let currentSection = "";
    
    prescription.pedbook_prescription_medications.forEach((med, index) => {
      if (med.section_title && med.section_title !== currentSection) {
        currentSection = med.section_title;
        text += `\n${currentSection}\n`;
      }
      
      const medicationName = med.is_custom_medication 
        ? med.medication_name 
        : med.pedbook_medications?.name;
      const dosageName = med.pedbook_medication_dosages?.name || med.dosage_name;
      const dosageValue = calculatedDosages[med.id];
      const calculatedDosage = typeof dosageValue === 'string' 
        ? dosageValue 
        : dosageValue?.text;
      const quantity = med.quantity ? ` - ${med.quantity}` : '';

      text += `${index + 1}) ${medicationName} ${dosageName}${quantity}\n`;
      text += `${calculatedDosage}\n\n`;
    });
    return text;
  };

  const copyToClipboard = async () => {
    const text = generatePrescriptionText();
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Prescrição copiada",
        description: "O texto da prescrição foi copiado para a área de transferência",
      });
    } catch (err) {
      toast({
        variant: "destructive",
        title: "Erro ao copiar",
        description: "Não foi possível copiar a prescrição para a área de transferência",
      });
    }
  };

  let currentSection = "";

  return (
    <div className="space-y-4">
      {prescription.pedbook_prescription_medications.map((med, index) => {
        const medicationName = med.is_custom_medication 
          ? med.medication_name 
          : med.pedbook_medications?.name;
        const dosageName = med.pedbook_medication_dosages?.name || med.dosage_name;
        
        if (!medicationName) return null;

        const showSectionTitle = med.section_title && med.section_title !== currentSection;
        if (showSectionTitle) {
          currentSection = med.section_title!;
        }

        const dosageValue = calculatedDosages[med.id];
        const dosageText = typeof dosageValue === 'string' 
          ? dosageValue 
          : dosageValue?.text || "";
        
        // Check if there's a restriction message
        const restrictionMessage = typeof dosageValue === 'object' && 
          dosageValue?.restrictions ? 
          formatRestrictionMessage(dosageValue.restrictions) : 
          null;

        return (
          <div key={med.id}>
            {showSectionTitle && (
              <h3 className="text-lg font-semibold text-primary mt-6 mb-4">
                {currentSection}
              </h3>
            )}
            <div className="space-y-2">
              <div className="text-base">
                <span className="mr-2">{index + 1})</span>
                <span className="text-primary font-medium">
                  {medicationName} {dosageName}
                </span>
                {med.quantity && (
                  <span className="text-gray-600 ml-1">
                    - {med.quantity}
                  </span>
                )}
              </div>
              {restrictionMessage ? (
                <div className="text-orange-600 text-sm font-medium">
                  {restrictionMessage}
                </div>
              ) : (
                <div className="text-gray-600 text-sm">
                  {dosageText}
                </div>
              )}
            </div>
          </div>
        );
      })}

      <div className="flex gap-2 pt-4 border-t border-gray-200">
        <Button
          variant="outline"
          size="sm"
          onClick={copyToClipboard}
          className="flex items-center gap-2"
        >
          <Copy className="h-4 w-4" />
          Copiar
        </Button>
      </div>
    </div>
  );
};

// Helper function to format restriction message based on type and values
function formatRestrictionMessage(restrictions: {
  type: "age" | "weight" | "both";
  minAge?: number;
  minWeight?: number;
}): string {
  if (restrictions.type === "age" && restrictions.minAge) {
    return `Esta apresentação é indicada para pacientes a partir de ${formatAgeRestriction(restrictions.minAge)} de idade.`;
  } else if (restrictions.type === "weight" && restrictions.minWeight) {
    return `Esta apresentação é indicada para pacientes com peso mínimo de ${restrictions.minWeight}kg.`;
  } else if (restrictions.type === "both" && restrictions.minAge && restrictions.minWeight) {
    return `Esta apresentação é indicada para pacientes a partir de ${formatAgeRestriction(restrictions.minAge)} de idade e com peso mínimo de ${restrictions.minWeight}kg.`;
  }
  
  return "";
}

// Convert months to a readable age format
function formatAgeRestriction(months: number): string {
  if (months < 12) {
    return `${months} ${months === 1 ? 'mês' : 'meses'}`;
  } else {
    const years = Math.floor(months / 12);
    const remainingMonths = months % 12;
    
    if (remainingMonths === 0) {
      return `${years} ${years === 1 ? 'ano' : 'anos'}`;
    } else {
      return `${years} ${years === 1 ? 'ano' : 'anos'} e ${remainingMonths} ${remainingMonths === 1 ? 'mês' : 'meses'}`;
    }
  }
}
