
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { motion } from "framer-motion";
import { Play, Shuffle, AlertCircle, Info } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface FilterActionsProps {
  questionCount: number;
  onShowRandomDialog: () => void;
  onStartStudy: () => void;
}

export const FilterActions = ({
  questionCount,
  onShowRandomDialog,
  onStartStudy
}: FilterActionsProps) => {
  // O botão "Iniciar Estudos" só fica habilitado quando há questões selecionadas
  // e não excede o limite máximo (300)
  const hasValidQuestionCount = questionCount > 0 && questionCount <= 300;
  const MAX_QUESTIONS_ALLOWED = 300;
  const isOverLimit = questionCount > MAX_QUESTIONS_ALLOWED;
  const hasNoQuestions = questionCount === 0;

  // Mensagem informativa para o botão de iniciar estudos
  const getStartButtonMessage = () => {
    if (questionCount === 0) {
      return "Selecione pelo menos uma questão para iniciar";
    } else if (questionCount > MAX_QUESTIONS_ALLOWED) {
      return `Limite excedido: Máximo de ${MAX_QUESTIONS_ALLOWED} questões permitido`;
    } else {
      return `${questionCount} questões selecionadas`;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
    >
      {/* Status Info - MedEvo Style */}
      {(hasNoQuestions || isOverLimit) && (
        <div className="mb-4 text-center px-2">
          <div className={`inline-block text-xs sm:text-sm px-3 sm:px-4 py-2 rounded-lg border-2 max-w-full ${
            isOverLimit
              ? "bg-red-50 text-red-800 border-red-300"
              : "bg-amber-50 text-amber-800 border-amber-300"
          }`}>
            {isOverLimit ? (
              <div className="space-y-1">
                <div className="font-semibold flex items-center justify-center gap-1 flex-wrap">
                  ⚠️ Muitas questões ({questionCount.toLocaleString()})
                </div>
                <div className="text-xs opacity-90 text-center">
                  📚 <span className="font-medium">Estudar:</span> Filtre até {MAX_QUESTIONS_ALLOWED}
                </div>
                <div className="text-xs opacity-90 text-center">
                  🎲 <span className="font-medium">Mix:</span> Sem limite
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center gap-1">
                ℹ️ Selecione filtros para começar
              </div>
            )}
          </div>
        </div>
      )}

      <Separator className="my-6" />

      {/* Título da seção */}
      <h4 className="text-sm font-semibold text-gray-800 mb-4 text-center flex items-center justify-center gap-2">
        🚀 Iniciar Estudos
      </h4>

      <div className="flex flex-col sm:flex-row justify-center items-center gap-4">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                onClick={onShowRandomDialog}
                className="w-full sm:w-[200px] border-2 border-primary/20 hover:border-primary/30 hover:bg-primary/5 transition-all duration-300"
              >
                <Shuffle className="h-4 w-4 mr-2" />
                Mix de Questões
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Escolha questões aleatórias de pediatria</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={onStartStudy}
                disabled={!hasValidQuestionCount}
                className={`w-full sm:w-[200px] shadow-md hover:shadow-lg transition-all duration-300 border-2 ${
                  hasValidQuestionCount
                    ? "bg-[#FF6B00] hover:bg-[#FF6B00]/90 text-white border-black"
                    : (isOverLimit
                        ? "bg-red-100 text-red-700 border-red-200 hover:bg-red-200"
                        : "bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100")
                }`}
              >
                <Play className="h-4 w-4 mr-2" />
                {isOverLimit ? "Muitos resultados" : "Iniciar Estudos"}
                {isOverLimit && (
                  <AlertCircle className="h-4 w-4 ml-2 text-red-500" />
                )}
                {hasNoQuestions && (
                  <Info className="h-4 w-4 ml-2 text-amber-500" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent className={
              isOverLimit ? "bg-red-50 border-red-200" :
              hasNoQuestions ? "bg-amber-50 border-amber-200" : ""
            }>
              <p>{getStartButtonMessage()}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </motion.div>
  );
};
