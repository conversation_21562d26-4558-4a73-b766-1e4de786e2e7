import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { Upload, ChartLine } from "lucide-react";
import { GrowthCurveType, GrowthCurveGender, GrowthCurveDataPoint, GrowthCurveMetadata } from "./types";
import { useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

interface ImportGrowthCurveMetadataProps {
  onImport: (data: { type: GrowthCurveType; gender: GrowthCurveGender; data: GrowthCurveDataPoint[] }) => void;
}

export const ImportGrowthCurveMetadata = ({ onImport }: ImportGrowthCurveMetadataProps) => {
  const [type, setType] = useState<GrowthCurveType>("weight");
  const [gender, setGender] = useState<GrowthCurveGender>("male");
  const [jsonData, setJsonData] = useState("");
  const queryClient = useQueryClient();

  const validateDataPoint = (point: any): point is GrowthCurveDataPoint => {
    // Check required numeric fields
    const hasRequiredFields = 
      typeof point.age_months === "number" &&
      typeof point.L === "number" &&
      typeof point.M === "number" &&
      typeof point.S === "number";

    // Check if percentiles exist and are numbers
    const hasValidPercentiles = point.percentiles && 
      typeof point.percentiles["1st"] === "number" &&
      typeof point.percentiles["3rd"] === "number" &&
      typeof point.percentiles["5th"] === "number" &&
      typeof point.percentiles["15th"] === "number" &&
      typeof point.percentiles["25th"] === "number" &&
      typeof point.percentiles["50th"] === "number" &&
      typeof point.percentiles["75th"] === "number" &&
      typeof point.percentiles["85th"] === "number" &&
      typeof point.percentiles["95th"] === "number" &&
      typeof point.percentiles["97th"] === "number";

    // SD is optional but must be a number if present
    const hasValidSD = !point.SD || typeof point.SD === "number";

    return hasRequiredFields && hasValidPercentiles && hasValidSD;
  };

  const handleImport = async () => {
    try {
      const parsedData = JSON.parse(jsonData) as GrowthCurveDataPoint[];
      
      // Validate data structure
      const isValid = parsedData.every(validateDataPoint);

      if (!isValid) {
        throw new Error("Invalid data structure");
      }

      // Check if record exists
      const { data: existingData, error: fetchError } = await supabase
        .from('pedbook_growth_curve_metadata')
        .select('id')
        .eq('type', type)
        .eq('gender', gender);

      if (fetchError) {
        throw fetchError;
      }

      if (existingData && existingData.length > 0) {
        // Update existing record
        const { error: updateError } = await supabase
          .from('pedbook_growth_curve_metadata')
          .update({ data: parsedData })
          .eq('id', existingData[0].id);

        if (updateError) throw updateError;
      } else {
        // Insert new record
        const { error: insertError } = await supabase
          .from('pedbook_growth_curve_metadata')
          .insert([{ type, gender, data: parsedData }]);

        if (insertError) throw insertError;
      }

      // Invalidate and refetch queries
      await queryClient.invalidateQueries({ queryKey: ["growth-curve-metadata"] });

      onImport({ type, gender, data: parsedData });
      toast.success("Dados importados com sucesso!");
      setJsonData("");
    } catch (error) {
      console.error('Import error:', error);
      toast.error("Erro ao importar dados. Verifique o formato JSON.");
    }
  };

  return (
    <div className="space-y-6 p-6 bg-white rounded-lg shadow-sm">
      <div className="flex items-center gap-2 text-lg font-semibold text-gray-900">
        <ChartLine className="h-5 w-5" />
        <h2>Importar Dados da Curva</h2>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="type">Tipo de Curva</Label>
          <Select value={type} onValueChange={(value) => setType(value as GrowthCurveType)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="weight">Peso</SelectItem>
              <SelectItem value="height">Altura</SelectItem>
              <SelectItem value="bmi">IMC</SelectItem>
              <SelectItem value="head-circumference">Perímetro Cefálico</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="gender">Gênero</Label>
          <Select value={gender} onValueChange={(value) => setGender(value as GrowthCurveGender)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="male">Masculino</SelectItem>
              <SelectItem value="female">Feminino</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="jsonData">Dados JSON</Label>
        <Textarea
          id="jsonData"
          value={jsonData}
          onChange={(e) => setJsonData(e.target.value)}
          placeholder="Cole aqui os dados JSON da curva..."
          className="h-[300px] font-mono"
        />
      </div>

      <Button 
        onClick={handleImport}
        className="w-full"
        disabled={!jsonData.trim()}
      >
        <Upload className="h-4 w-4 mr-2" />
        Importar Dados
      </Button>
    </div>
  );
};