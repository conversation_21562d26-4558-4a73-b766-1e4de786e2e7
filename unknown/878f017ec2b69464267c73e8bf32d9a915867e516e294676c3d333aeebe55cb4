import type { Database } from "@/integrations/supabase/types";

type PrescriptionBase = Database["public"]["Tables"]["pedbook_prescriptions"]["Row"];

export type PrescriptionMedication = {
  id: string;
  medication_id: string;
  dosage_id: string;
  prescription_id: string;
  notes?: string | null;
  created_at?: string | null;
  updated_at?: string | null;
  is_custom_medication?: boolean;
  medication_name?: string;
  medication_description?: string;
  dosage_name?: string;
  dosage_description?: string;
  dosage_template?: string;
  section_title?: string | null;
  display_order?: number;
  quantity?: string | null;
  pedbook_medications: {
    name: string;
    brands?: string | null;
    description?: string | null;
  } | null;
  pedbook_medication_dosages: {
    name: string;
    dosage_template: string;
    summary?: string | null;
  } | null;
};

export type PrescriptionWithMedications = PrescriptionBase & {
  pedbook_prescription_medications?: PrescriptionMedication[];
  profiles?: {
    full_name: string;
    formation_area: string;
  };
};