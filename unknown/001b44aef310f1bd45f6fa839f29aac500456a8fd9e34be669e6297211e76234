import { useEffect, useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { FormulaFields } from "./FormulaFields";

interface FormulaDialogProps {
  formula?: any;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function FormulaDialog({ 
  formula,
  open,
  onOpenChange,
}: FormulaDialogProps) {
  const [name, setName] = useState("");
  const [brand, setBrand] = useState("");
  const [categoryId, setCategoryId] = useState("");
  const [description, setDescription] = useState("");
  const [ageRange, setAgeRange] = useState("");
  const [price, setPrice] = useState("");
  const [imageUrl, setImageUrl] = useState("");
  const { toast } = useToast();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (formula) {
      setName(formula.name);
      setBrand(formula.brand);
      setCategoryId(formula.category_id || "");
      setDescription(formula.description || "");
      setAgeRange(formula.age_range);
      setPrice(formula.price?.toString() || "");
      setImageUrl(formula.image_url || "");
    } else {
      setName("");
      setBrand("");
      setCategoryId("");
      setDescription("");
      setAgeRange("");
      setPrice("");
      setImageUrl("");
    }
  }, [formula]);

  const { data: categories } = useQuery({
    queryKey: ["formula-categories"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_formula_categories")
        .select("*")
        .order("name");
      
      if (error) throw error;
      return data;
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const formulaData = {
        name,
        brand,
        category_id: categoryId || null,
        description,
        age_range: ageRange,
        price: price ? parseFloat(price) : null,
        image_url: imageUrl,
      };

      if (formula) {
        const { error } = await supabase
          .from("pedbook_formulas")
          .update(formulaData)
          .eq("id", formula.id);

        if (error) throw error;

        toast({
          title: "Fórmula atualizada com sucesso!",
          description: `A fórmula ${name} foi atualizada.`,
        });
      } else {
        const { error } = await supabase
          .from("pedbook_formulas")
          .insert([formulaData]);

        if (error) throw error;

        toast({
          title: "Fórmula criada com sucesso!",
          description: `A fórmula ${name} foi adicionada.`,
        });
      }

      queryClient.invalidateQueries({ queryKey: ["formulas"] });
      onOpenChange(false);
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao salvar fórmula",
        description: error.message || "Ocorreu um erro ao salvar a fórmula.",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {formula ? "Editar Fórmula" : "Nova Fórmula"}
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <FormulaFields
            name={name}
            setName={setName}
            brand={brand}
            setBrand={setBrand}
            categoryId={categoryId}
            setCategoryId={setCategoryId}
            description={description}
            setDescription={setDescription}
            ageRange={ageRange}
            setAgeRange={setAgeRange}
            price={price}
            setPrice={setPrice}
            imageUrl={imageUrl}
            setImageUrl={setImageUrl}
            categories={categories || []}
          />
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancelar
            </Button>
            <Button type="submit">
              {formula ? "Atualizar" : "Criar"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}