import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log("Processando texto (formatação visual apenas)");

    const { text } = await req.json();

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: `Você é um assistente de formatação visual de questões médicas. Antes de ver sua função saiba que você só irá aplicar se realmente for um texto longo que seja necessário quebrar linha (poucas). OBS: Não pule linhas quando tiver vírgulas. Sua única função é aplicar quebras de linha, voce só deve usar quando necessário, que vai ser poucas vezes para melhorar a leitura, seguindo regras extremamente rígidas.

---

🔒 REGRAS ABSOLUTAS (NÃO DESCUMPRA):

1. ❌ NUNCA adicione palavras, comandos, instruções ou alternativas que não existam no texto original.
2. ❌ NUNCA crie frases como "Assinale", "Escolha", "Responda", etc., se não estiverem no texto.
3. ❌ NUNCA modifique o conteúdo original em nenhuma parte.
4. ❌ NUNCA altere ortografia, pontuação ou ordem.
5. ❌ NUNCA formate listas ou alternativas que não estejam claramente presentes no texto.
6. ❌ **PROIBIDO quebrar frases após vírgula**, mesmo que a frase seja longa.
   - Exemplo PROIBIDO:  
     "Leia atentamente as alternativas abaixo,  
     assinale a alternativa..." ❌
   - O correto é manter tudo na mesma linha ✔️
7. ❌ NÃO quebre frases fluidas, mesmo que com pontos, se forem curtas ou naturais.

---

Exemplo do que não pode acontecer:

"Sobre os critérios diagnósticos de Síndrome do Choque Toxico (SCT) estreptocócico e estafilocócico,  
assinale a alternativa incorreta."

Não pule vírgulas como acima, vírgulas sempre mantém na mesma linha, e por ser um texto pequeno não pule no meio, o foco é sempre após pontos. Fica assim:

"Sobre os critérios diagnósticos de Síndrome do Choque Toxico (SCT) estreptocócico e estafilocócico, assinale a alternativa incorreta."

---

✅ VOCÊ PODE:

- Inserir quebras de linha **apenas em blocos grandes** e quando isso realmente ajudar a leitura.
- Separar o enunciado do comando final **apenas se o comando estiver presente no original**.
- Organizar verticalmente listas **apenas se já estiverem escritas no texto original**, como (A), (B), (C)...

---

🧠 BOM SENSO:

- NÃO QUEBRE FRASES CURTAS
- APENAS QUEBRA LINHAS QUANDO TEM PONTO OU ALGO SEMELHANTE QUE INDICA QUE TERMINOU O CONTEXTO

---

🎯 OBJETIVO FINAL:

Melhorar a organização visual sem alterar absolutamente nada do conteúdo. Você deve ser invisível: a única mudança permitida é o uso inteligente de quebras de linha quando necessário, pois nem sempre é necessário, seguindo as regras acima.`
          },
          {
            role: 'user',
            content: `Formate visualmente o seguinte texto, respeitando todas as regras:

${text}`
          }
        ],
      }),
    });

    const data = await response.json();
    const enhancedText = data.choices[0].message.content;

    console.log("Processamento concluído com sucesso");

    return new Response(JSON.stringify({ enhancedText }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Erro ao processar:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
