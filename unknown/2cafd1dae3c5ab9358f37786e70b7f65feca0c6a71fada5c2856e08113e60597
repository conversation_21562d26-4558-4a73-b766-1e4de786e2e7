
import React, { useEffect, useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { ThumbsUp, ThumbsDown, <PERSON>h, Heart } from "lucide-react";
import { Card } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";

interface MedicationFeedbackProps {
  medicationId: string;
  medicationName: string;
}

const RATING_OPTIONS = [
  {
    value: 'excellent',
    label: 'Excelente',
    icon: Heart,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 hover:bg-blue-100',
  },
  {
    value: 'good',
    label: 'Bom',
    icon: ThumbsUp,
    color: 'text-green-500',
    bgColor: 'bg-green-50 hover:bg-green-100',
  },
  {
    value: 'regular',
    label: 'Regular',
    icon: Meh,
    color: 'text-yellow-500',
    bgColor: 'bg-yellow-50 hover:bg-yellow-100',
  },
  {
    value: 'poor',
    label: 'Ruim',
    icon: ThumbsDown,
    color: 'text-red-500',
    bgColor: 'bg-red-50 hover:bg-red-100',
  },
] as const;

export function MedicationFeedback({ medicationId, medicationName }: MedicationFeedbackProps) {
  const { user } = useAuth();
  const [existingFeedback, setExistingFeedback] = useState<string | null>(null);
  const [showCommentDialog, setShowCommentDialog] = useState(false);
  const [comment, setComment] = useState("");
  const [pendingRating, setPendingRating] = useState<typeof RATING_OPTIONS[number]['value'] | null>(null);
  const [feedbackStatus, setFeedbackStatus] = useState<string | null>(null);

  useEffect(() => {
    const checkExistingFeedback = async () => {
      if (!user) return;

      try {
        const { data, error } = await supabase
          .from('pedbook_medications_feedback')
          .select('rating')
          .eq('medication_id', medicationId)
          .eq('user_id', user.id)
          .single();

        if (error && error.code !== "PGRST116") {
          console.error('Error checking existing feedback:', error);
          return;
        }

        if (data) {
          setExistingFeedback(data.rating);
          console.log('User already provided feedback:', data.rating);
        }
      } catch (error) {
        console.error('Error checking feedback:', error);
      }
    };

    checkExistingFeedback();
  }, [user, medicationId]);

  const askForComment = (rating: typeof RATING_OPTIONS[number]['value']) => {
    if (rating !== 'excellent') {
      setPendingRating(rating);
      setShowCommentDialog(true);
    } else {
      handleFeedback(rating);
    }
  };

  const handleFeedback = async (rating: typeof RATING_OPTIONS[number]['value'], userComment?: string) => {
    if (!user) {
      setFeedbackStatus("Você precisa estar logado para enviar feedback.");
      return;
    }

    if (existingFeedback) {
      setFeedbackStatus("Você já avaliou esta bula anteriormente.");
      return;
    }

    try {
      const { error } = await supabase
        .from('pedbook_medications_feedback')
        .insert({
          medication_id: medicationId,
          medication_name: medicationName,
          user_id: user.id,
          rating: rating,
          comment: userComment,
        });

      if (error) {
        if (error.code === '23505') {
          setFeedbackStatus("Você já avaliou esta bula anteriormente.");
          return;
        }
        throw error;
      }

      setExistingFeedback(rating);
      setFeedbackStatus("Feedback enviado com sucesso! Obrigado pela sua avaliação.");
      setTimeout(() => setFeedbackStatus(null), 3000);
    } catch (error: any) {
      console.error('Error submitting feedback:', error);
      setFeedbackStatus(`Erro ao enviar feedback: ${error.message}`);
    }
  };

  const handleCommentSubmit = () => {
    if (pendingRating && comment.trim()) {
      handleFeedback(pendingRating, comment.trim());
      setShowCommentDialog(false);
      setComment("");
      setPendingRating(null);
    }
  };

  return (
    <>
      <div className="mt-6 border-t pt-6">
        <div className="text-center space-y-2">
          <h3 className="text-base font-medium text-gray-900 dark:text-gray-100">
            {existingFeedback ? 'Sua avaliação' : 'Esta bula foi útil para você?'}
          </h3>
          
          {feedbackStatus && (
            <div className={`text-sm px-4 py-2 rounded-md ${feedbackStatus.includes('Erro') ? 'bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-400' : 'bg-green-50 text-green-600 dark:bg-green-900/20 dark:text-green-400'}`}>
              {feedbackStatus}
            </div>
          )}
        </div>

        <div className="mt-4 flex justify-center gap-3">
          {RATING_OPTIONS.map((option) => {
            const isSelected = existingFeedback === option.value;
            return (
              <Card
                key={option.value}
                className={`flex flex-col items-center justify-center p-2 cursor-${existingFeedback ? 'default' : 'pointer'} transition-all 
                  ${isSelected ? `${option.bgColor} ring-2 ring-${option.color}` : 'bg-gray-50 dark:bg-gray-800'}
                  ${!existingFeedback ? `${option.bgColor} hover:ring-2 hover:ring-${option.color}` : ''}
                  w-16 h-16`}
                onClick={() => !existingFeedback && askForComment(option.value)}
              >
                <option.icon className={`w-6 h-6 ${isSelected ? option.color : 'text-gray-400'}`} />
                <span className={`mt-1 text-xs font-medium ${isSelected ? 'text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'}`}>
                  {option.label}
                </span>
              </Card>
            );
          })}
        </div>
      </div>

      <AlertDialog open={showCommentDialog} onOpenChange={setShowCommentDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Deixe seu comentário</AlertDialogTitle>
            <AlertDialogDescription>
              Sua opinião é muito importante para melhorarmos nosso conteúdo. Por favor, compartilhe suas sugestões ou críticas construtivas.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="my-4">
            <Textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Digite seu comentário aqui..."
              className="min-h-[100px]"
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setShowCommentDialog(false);
              if (pendingRating) {
                handleFeedback(pendingRating);
              }
            }}>
              Cancelar
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleCommentSubmit}>
              Enviar comentário
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
