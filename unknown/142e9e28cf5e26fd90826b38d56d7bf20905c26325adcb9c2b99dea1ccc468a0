import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { X } from "lucide-react";
import { AgeRange } from "@/types/dosage";

interface AgeRangeListProps {
  ranges: AgeRange[];
  onRemove: (index: number) => void;
}

export function AgeRangeList({ ranges, onRemove }: AgeRangeListProps) {
  if (ranges.length === 0) return null;

  return (
    <div className="space-y-2">
      <Label>Faixas de idade configuradas:</Label>
      {ranges.map((range, index) => (
        <div key={index} className="flex items-center gap-2 bg-gray-50 p-2 rounded-lg">
          <span className="flex-1">
            {range.startMonth}-{range.endMonth} meses: {range.value}
          </span>
          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={() => onRemove(index)}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      ))}
    </div>
  );
}