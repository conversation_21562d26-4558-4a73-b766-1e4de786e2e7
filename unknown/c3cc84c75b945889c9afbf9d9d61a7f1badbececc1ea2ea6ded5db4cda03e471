import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface GrowthCurve {
  id: string;
  title: string;
  description: string | null;
  gender: string;
  gestational_age: string;
  growth_type: string;
  image_url: string | null;
}

interface GrowthCurveListProps {
  curves: GrowthCurve[];
}

export function GrowthCurveList({ curves }: GrowthCurveListProps) {
  const getGenderLabel = (gender: string) => {
    return gender === "male" ? "Menino" : "Menina";
  };

  const getGestationalAgeLabel = (age: string) => {
    return age === "term" ? "A Termo" : "Pré-termo";
  };

  const getGrowthTypeLabel = (type: string) => {
    return type === "healthy" ? "Saudável" : "Transtorno";
  };

  return (
    <div className="border rounded-lg">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Título</TableHead>
            <TableHead>Gênero</TableHead>
            <TableHead>Tempo Gestacional</TableHead>
            <TableHead>Tipo</TableHead>
            <TableHead>Descrição</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {curves.map((curve) => (
            <TableRow key={curve.id}>
              <TableCell>{curve.title}</TableCell>
              <TableCell>{getGenderLabel(curve.gender)}</TableCell>
              <TableCell>{getGestationalAgeLabel(curve.gestational_age)}</TableCell>
              <TableCell>{getGrowthTypeLabel(curve.growth_type)}</TableCell>
              <TableCell>{curve.description}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}