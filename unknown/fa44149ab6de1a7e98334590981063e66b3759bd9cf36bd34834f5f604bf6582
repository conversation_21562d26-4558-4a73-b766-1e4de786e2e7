
import { Pill } from "lucide-react";

interface MedicationCardProps {
  name: string;
  category: string;
}

export function MedicationCard({ name, category }: MedicationCardProps) {
  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm p-4 border border-gray-200 dark:border-gray-700">
      <div className="flex items-start gap-3">
        <div className="p-2 bg-primary/10 dark:bg-blue-900/30 rounded-full">
          <Pill className="h-5 w-5 text-primary dark:text-blue-400" />
        </div>
        
        <div>
          <h3 className="text-lg font-semibold dark:text-gray-200">{name}</h3>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-300 mt-1">
            {category}
          </span>
        </div>
      </div>
    </div>
  );
}
