import { useState, useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { slugify } from "@/utils/slugify";
import { BasicInfoFields } from "./form/BasicInfoFields";
import { DetailsFields } from "./form/DetailsFields";
import { ReferencesFields } from "./form/ReferencesFields";

interface MedicationFormProps {
  medication?: any;
  categories: Array<{ id: string; name: string }>;
  onClose: () => void;
}

export function MedicationForm({ medication, categories, onClose }: MedicationFormProps) {
  const [formData, setFormData] = useState({
    name: medication?.name || "",
    categoryId: medication?.category_id || "",
    description: medication?.description || "",
    brands: medication?.brands || "",
    contraindications: medication?.contraindications || "",
    guidelines: medication?.guidelines || "",
    scientific_references: medication?.scientific_references || "",
    required_measures: medication?.required_measures || ["weight", "age"],
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (medication) {
      setFormData({
        name: medication.name || "",
        categoryId: medication.category_id || "",
        description: medication.description || "",
        brands: medication.brands || "",
        contraindications: medication.contraindications || "",
        guidelines: medication.guidelines || "",
        scientific_references: medication.scientific_references || "",
        required_measures: medication.required_measures || ["weight", "age"],
      });
    }
  }, [medication]);

  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const slug = slugify(formData.name);
      const medicationData = {
        name: formData.name,
        category_id: formData.categoryId,
        description: formData.description,
        brands: formData.brands,
        contraindications: formData.contraindications,
        guidelines: formData.guidelines,
        scientific_references: formData.scientific_references,
        required_measures: formData.required_measures,
        slug,
      };

      if (medication) {
        const { error } = await supabase
          .from("pedbook_medications")
          .update(medicationData)
          .eq("id", medication.id);

        if (error) throw error;

        toast({
          title: "Medicamento atualizado com sucesso!",
          description: `O medicamento ${formData.name} foi atualizado.`,
        });
      } else {
        const { error } = await supabase
          .from("pedbook_medications")
          .insert([medicationData]);

        if (error) throw error;

        toast({
          title: "Medicamento criado com sucesso!",
          description: `O medicamento ${formData.name} foi adicionado.`,
        });
      }

      await queryClient.invalidateQueries({ queryKey: ["medications"] });
      onClose();
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao salvar medicamento",
        description: error.message || "Ocorreu um erro ao salvar o medicamento.",
      });
    }
  };





  return (
    <form id="medication-form" onSubmit={handleSubmit} className="space-y-4">
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic">Informações Básicas</TabsTrigger>
          <TabsTrigger value="details">Detalhes</TabsTrigger>
          <TabsTrigger value="references">Referências</TabsTrigger>
        </TabsList>

        <TabsContent value="basic">
          <BasicInfoFields
            formData={formData}
            categories={categories}
            onChange={handleFormChange}
          />
        </TabsContent>

        <TabsContent value="details">
          <DetailsFields
            formData={formData}
            onChange={handleFormChange}
          />
        </TabsContent>

        <TabsContent value="references">
          <ReferencesFields
            formData={formData}
            onChange={handleFormChange}
          />
        </TabsContent>
      </Tabs>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onClose}>
          Cancelar
        </Button>
        <Button type="submit">Salvar</Button>
      </div>
    </form>
  );
}