import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card";
import { Syringe, AlertCircle } from "lucide-react";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";

interface TreatmentPlanProps {
  weight: number;
  doseNumber: number;
  adrenalineDose: string;
  requiresICU?: boolean;
}

export const TreatmentPlan: React.FC<TreatmentPlanProps> = ({
  weight,
  doseNumber,
  adrenalineDose,
  requiresICU = false,
}) => {
  const calculateICUDose = (weight: number) => {
    const minDose = (0.1 * weight).toFixed(2);
    const maxDose = (1 * weight).toFixed(2);
    return `${minDose}-${maxDose}`;
  };

  return (
    <Card className="w-full animate-fade-in">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Syringe className="h-6 w-6 text-red-500" />
          Plano de Tratamento
        </CardTitle>
        <CardDescription>
          {requiresICU ? "Tratamento em UTI" : `${doseNumber}ª dose de Adrenalina`}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {requiresICU ? (
          <div className="space-y-4">
            <div className="bg-red-50 p-4 rounded-lg border border-red-200">
              <h3 className="font-medium text-red-700 mb-2">Adrenalina IV em infusão contínua</h3>
              <div className="space-y-2">
                <p className="text-red-600">Dose calculada: {calculateICUDose(weight)} mcg/min</p>
                <div className="text-sm text-red-500 space-y-1">
                  <p>Diluição padrão:</p>
                  <ul className="list-disc pl-4">
                    <li>Adrenalina (1 mg/mL) 1 mL + 99 mL de SG 5%</li>
                    <li>Concentração final: 10 microgramas/mL</li>
                    <li>Dose: 0,1-1 microgramas/kg/minuto EV em infusão contínua</li>
                  </ul>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium">Ações Imediatas:</h3>
              <ul className="list-disc pl-6 space-y-1">
                <li>Encaminhar o paciente para a UTI pediátrica</li>
                <li>Garantir monitorização contínua de sinais vitais</li>
              </ul>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="bg-red-50 p-4 rounded-lg border border-red-200">
              <h3 className="font-medium text-red-700 mb-2">Administração de Adrenalina IM</h3>
              <p className="text-red-600">
                Dose calculada: {adrenalineDose} mL de adrenalina 1 mg/mL (1:1000)
              </p>
              <p className="text-sm text-red-500 mt-1">
                Aplicar no vasto lateral da coxa
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium">Monitorização:</h3>
              <ul className="list-disc pl-6 space-y-1">
                <li>Reavaliar ABCDE</li>
                <li>Manter o paciente em observação por no mínimo 4 horas</li>
              </ul>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};