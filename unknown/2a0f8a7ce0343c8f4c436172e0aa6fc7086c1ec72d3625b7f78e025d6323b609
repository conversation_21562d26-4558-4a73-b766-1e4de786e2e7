import { useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { MedicationFormFields } from "./medication/MedicationFormFields";
import { DosageFormFields } from "./dosage/DosageFormFields";
import { DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { slugify } from "@/utils/slugify";

interface NewMedicationFormProps {
  onSuccess: (medicationId: string, dosageId: string) => void;
  onCancel: () => void;
}

export function NewMedicationForm({ onSuccess, onCancel }: NewMedicationFormProps) {
  const [formData, setFormData] = useState({
    name: "",
    categoryId: "",
    description: "",
    brands: "",
    dosageName: "",
    dosageType: "weight",
    dosageTemplate: "",
    summary: "",
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const handleFormChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const { data: medication, error: medicationError } = await supabase
        .from("pedbook_medications")
        .insert({
          name: formData.name,
          category_id: formData.categoryId || null,
          description: formData.description || null,
          brands: formData.brands || null,
          slug: slugify(formData.name),
        })
        .select()
        .single();

      if (medicationError) throw medicationError;

      const { data: dosage, error: dosageError } = await supabase
        .from("pedbook_medication_dosages")
        .insert({
          medication_id: medication.id,
          name: formData.dosageName,
          type: formData.dosageType,
          summary: formData.summary || null,
          dosage_template: formData.dosageTemplate,
        })
        .select()
        .single();

      if (dosageError) throw dosageError;

      await queryClient.invalidateQueries({ queryKey: ["medications"] });

      onSuccess(medication.id, dosage.id);
    } catch (error: any) {
      console.error("Error in form submission:", error);
      toast({
        variant: "destructive",
        title: "Erro ao criar medicamento",
        description: error.message,
      });
    }
  };

  return (
    <div className="space-y-6">
      <DialogHeader>
        <DialogTitle>Novo Medicamento e Dosagem</DialogTitle>
        <DialogDescription>
          Adicione um novo medicamento e sua dosagem à prescrição
        </DialogDescription>
      </DialogHeader>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-4">
          <MedicationFormFields
            formData={formData}
            onChange={handleFormChange}
          />

          <div className="border-t pt-4 mt-4">
            <h4 className="font-medium mb-4">Informações da Dosagem</h4>
            <DosageFormFields
              formData={formData}
              onChange={handleFormChange}
            />
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancelar
          </Button>
          <Button type="submit">
            Criar Medicamento
          </Button>
        </div>
      </form>
    </div>
  );
}