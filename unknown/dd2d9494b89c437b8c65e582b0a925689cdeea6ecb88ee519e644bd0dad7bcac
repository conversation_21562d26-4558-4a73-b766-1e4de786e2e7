
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { UserRound, KeyRound } from "lucide-react";
import SignInForm from "./SignInForm";
import SignUpForm from "./SignUpForm";
import ResetPasswordForm from "./ResetPasswordForm";
// import "./AuthDialog.css"; // Removido temporariamente devido a erro de sintaxe

interface AuthDialogProps {
  defaultOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  hidden?: boolean;
  open?: boolean;
  onSuccess?: () => void;
}

const AuthDialog = ({ defaultOpen, onOpenChange, hidden, open, onSuccess }: AuthDialogProps = {}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen || false);
  const [mode, setMode] = useState<"signin" | "signup" | "reset">("signin");

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    onOpenChange?.(open);
  };

  const handleSuccess = () => {
    handleOpenChange(false);
    onSuccess?.();
  };

  // Use controlled open state if provided, otherwise use internal state
  const dialogOpen = open !== undefined ? open : isOpen;

  const getTitle = () => {
    switch (mode) {
      case "signin":
        return "Entrar na sua conta";
      case "signup":
        return "Criar sua conta";
      case "reset":
        return "Recuperar senha";
    }
  };

  const getDescription = () => {
    switch (mode) {
      case "signin":
        return "Faça login para acessar sua conta";
      case "signup":
        return "Crie sua conta para começar";
      case "reset":
        return "Digite seu email para receber instruções de recuperação";
    }
  };

  return (
    <Dialog open={dialogOpen} onOpenChange={handleOpenChange}>
      {!hidden && (
        <DialogTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            <UserRound className="h-4 w-4" />
            Entrar
          </Button>
        </DialogTrigger>
      )}
      <DialogContent className="auth-dialog-content fixm" style={{
        top: window.innerWidth <= 640 ? '20%' : '50%',
        transform: window.innerWidth <= 640 ? 'translate(-50%, -20%)' : 'translate(-50%, -50%)',
        maxHeight: window.innerWidth <= 640 ? '75dvh' : '75dvh',
        width: window.innerWidth <= 640 ? (window.innerHeight > window.innerWidth ? '98%' : '75%') : 'calc(100vw-2rem)',
        maxWidth: window.innerWidth <= 640 ? (window.innerHeight > window.innerWidth ? '520px' : '350px') : '512px'
      }}>
        <DialogHeader>
          <DialogTitle className="text-xl sm:text-2xl text-center">
            {getTitle()}
          </DialogTitle>
          <DialogDescription className="text-center text-muted-foreground">
            {getDescription()}
          </DialogDescription>
        </DialogHeader>
        {mode === "signin" ? (
          <SignInForm
            onModeChange={() => setMode("signup")}
            onResetPassword={() => setMode("reset")}
            onSuccess={handleSuccess}
          />
        ) : mode === "signup" ? (
          <SignUpForm
            onModeChange={() => setMode("signin")}
            onSuccess={handleSuccess}
          />
        ) : (
          <ResetPasswordForm onBackToLogin={() => setMode("signin")} />
        )}
        <p className="text-[10px] text-muted-foreground/70 text-center mt-6 font-light leading-tight max-w-[80%] mx-auto">
        Esta plataforma é voltada para profissionais de saúde e fornece suporte à prática clínica, sem substituir o julgamento médico ou diretrizes institucionais.
        </p>
      </DialogContent>
    </Dialog>
  );
};

export default AuthDialog;
