import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { Plus, X } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

interface ExamLocation {
  id: string;
  name: string;
}

const ExamLocationManager = () => {
  const { toast } = useToast();
  const [locations, setLocations] = useState<ExamLocation[]>([]);
  const [newLocation, setNewLocation] = useState("");

  useEffect(() => {
    fetchLocations();
  }, []);

  const fetchLocations = async () => {
    try {
      const { data, error } = await supabase
        .from('exam_locations')
        .select('*');

      if (error) throw error;

      setLocations(data);
    } catch (error: any) {
      toast({
        title: "Erro ao carregar locais",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const addLocation = async () => {
    if (!newLocation.trim()) {
      toast({
        title: "Erro",
        description: "O nome do local não pode estar vazio",
        variant: "destructive"
      });
      return;
    }

    try {
      const { data, error } = await supabase
        .from('exam_locations')
        .insert({ name: newLocation })
        .select()
        .single();

      if (error) throw error;

      setLocations(prev => [...prev, data]);
      setNewLocation("");
      toast({
        title: "Sucesso",
        description: "Local adicionado com sucesso!"
      });
    } catch (error: any) {
      toast({
        title: "Erro ao adicionar local",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const removeLocation = async (id: string) => {
    try {
      const { error } = await supabase
        .from('exam_locations')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setLocations(prev => prev.filter(loc => loc.id !== id));
      toast({
        title: "Sucesso",
        description: "Local removido com sucesso!"
      });
    } catch (error: any) {
      toast({
        title: "Erro ao remover local",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-lg font-semibold">Gerenciar Locais de Prova</h2>
      <div className="flex gap-2">
        <Input
          placeholder="Nome do local de prova"
          value={newLocation}
          onChange={(e) => setNewLocation(e.target.value)}
        />
        <Button onClick={addLocation}>
          <Plus className="h-4 w-4 mr-2" />
          Adicionar
        </Button>
      </div>

      <div className="border rounded-lg p-4">
        {locations.map(location => (
          <div
            key={location.id}
            className="flex items-center justify-between p-2 hover:bg-gray-100 rounded"
          >
            <span>{location.name}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => removeLocation(location.id)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ExamLocationManager;