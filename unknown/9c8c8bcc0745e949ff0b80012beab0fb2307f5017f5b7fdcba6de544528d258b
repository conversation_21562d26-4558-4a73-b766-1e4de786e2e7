/**
 * Script para corrigir todos os slugs da tabela pedbook_conducts_summaries
 * Normaliza slugs com acentos, espaços e caracteres especiais
 */

import { createClient } from '@supabase/supabase-js';

console.log('🔧 Corrigindo slugs das condutas...');

const supabaseUrl = 'https://bxedpdmgvgatjdfxgxij.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ4ZWRwZG1ndmdhdGpkZnhneGlqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzIyNzk3MTgsImV4cCI6MjA0Nzg1NTcxOH0.cjoaggOXt1kY9WmVNbAipCOQ2dP4PWLP43KMf8cO8Wo';
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Função para normalizar slug (criar slug seguro para URLs)
 */
function normalizeSlug(slug) {
  return slug
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove acentos
    .replace(/[^\w\s-]/g, '') // Remove caracteres especiais exceto hífens
    .replace(/\s+/g, '-') // Substitui espaços por hífens
    .replace(/-+/g, '-') // Remove hífens duplos
    .replace(/^-|-$/g, '') // Remove hífens no início e fim
    .trim();
}

/**
 * Buscar todos os slugs atuais
 */
async function getAllSlugs() {
  try {
    console.log('🔍 Buscando todos os slugs atuais...');
    
    const { data, error } = await supabase
      .from('pedbook_conducts_summaries')
      .select('id, title, slug')
      .order('title');

    if (error) {
      console.error('❌ Erro ao buscar slugs:', error);
      return [];
    }

    console.log(`✅ Encontrados ${data.length} registros`);
    return data || [];
    
  } catch (err) {
    console.error('❌ Erro na conexão:', err.message);
    return [];
  }
}

/**
 * Atualizar slug no banco
 */
async function updateSlug(id, newSlug) {
  try {
    const { error } = await supabase
      .from('pedbook_conducts_summaries')
      .update({ slug: newSlug })
      .eq('id', id);

    if (error) {
      console.error(`❌ Erro ao atualizar slug ${id}:`, error);
      return false;
    }

    return true;
  } catch (err) {
    console.error(`❌ Erro na atualização ${id}:`, err.message);
    return false;
  }
}

/**
 * Processar todos os slugs
 */
async function fixAllSlugs() {
  const records = await getAllSlugs();
  
  if (records.length === 0) {
    console.log('❌ Nenhum registro encontrado');
    return;
  }

  console.log('\n🔧 Processando slugs...\n');
  
  let updatedCount = 0;
  let skippedCount = 0;

  for (const record of records) {
    const originalSlug = record.slug;
    const normalizedSlug = normalizeSlug(originalSlug);
    
    if (originalSlug !== normalizedSlug) {
      console.log(`🔄 "${record.title}"`);
      console.log(`   ❌ ANTES: "${originalSlug}"`);
      console.log(`   ✅ DEPOIS: "${normalizedSlug}"`);
      
      const success = await updateSlug(record.id, normalizedSlug);
      
      if (success) {
        console.log(`   ✅ Atualizado com sucesso!\n`);
        updatedCount++;
      } else {
        console.log(`   ❌ Falha na atualização!\n`);
      }
    } else {
      console.log(`✅ "${record.title}" - Slug já está correto: "${originalSlug}"`);
      skippedCount++;
    }
  }

  console.log('\n📊 RESUMO:');
  console.log(`✅ ${updatedCount} slugs atualizados`);
  console.log(`⏭️ ${skippedCount} slugs já estavam corretos`);
  console.log(`🎯 Total processado: ${records.length}`);
  
  if (updatedCount > 0) {
    console.log('\n🎉 Correção concluída! Agora todos os slugs estão normalizados.');
    console.log('💡 Você pode remover a função normalizeSlug() do código e usar os slugs diretamente.');
  } else {
    console.log('\n✅ Todos os slugs já estavam corretos!');
  }
}

// Executar correção
fixAllSlugs().catch(console.error);
