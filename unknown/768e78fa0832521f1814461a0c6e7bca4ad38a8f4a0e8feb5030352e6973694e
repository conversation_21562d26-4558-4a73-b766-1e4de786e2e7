import { Star } from "lucide-react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

export const Testimonials = () => {
  const testimonials = [
    {
      text: "Nunca aprendi tanto em tão pouco tempo! O planner organizou meus estudos de um jeito que nunca imaginei possível.",
      author: "<PERSON>",
      role: "estudante de Medicina",
    },
    {
      text: "Meu desempenho nas provas melhorou absurdamente! Finalmente tenho um método eficiente.",
      author: "<PERSON><PERSON>.",
      role: "residente de Pediatria",
    },
    {
      text: "A melhor decisão que tomei foi começar a usar esta plataforma. Minha rotina de estudos mudou completamente!",
      author: "<PERSON>",
      role: "estudante de Medicina",
    },
  ];

  return (
    <div className="py-20 px-4 bg-gradient-to-b from-white to-gray-50 relative overflow-hidden">
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-5" />
      <div className="max-w-6xl mx-auto relative z-10">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600 animate-fadeIn">
          O Que Estudantes Estão Dizendo?
        </h2>
        <Carousel className="w-full max-w-4xl mx-auto">
          <CarouselContent>
            {testimonials.map((testimonial, index) => (
              <CarouselItem key={index}>
                <div className="bg-white/80 backdrop-blur-sm p-8 rounded-xl shadow-lg transform transition-all duration-300 hover:shadow-2xl mx-4 animate-fadeIn">
                  <div className="flex gap-1 mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <p className="text-lg mb-6 text-gray-700 italic">{testimonial.text}</p>
                  <div className="border-t pt-4">
                    <p className="font-semibold text-gray-800">{testimonial.author}</p>
                    <p className="text-gray-600">{testimonial.role}</p>
                  </div>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="hidden md:flex" />
          <CarouselNext className="hidden md:flex" />
        </Carousel>
      </div>
    </div>
  );
};