// vite.config.ts
import { defineConfig } from "file:///C:/Users/<USER>/Desktop/Meu%20projeto/tinyhealth-guide/node_modules/vite/dist/node/index.js";
import react from "file:///C:/Users/<USER>/Desktop/Meu%20projeto/tinyhealth-guide/node_modules/@vitejs/plugin-react-swc/index.mjs";
import path from "path";
import { componentTagger } from "file:///C:/Users/<USER>/Desktop/Meu%20projeto/tinyhealth-guide/node_modules/lovable-tagger/dist/index.js";
var __vite_injected_original_dirname = "C:\\Users\\<USER>\\Desktop\\Meu projeto\\tinyhealth-guide";
var vite_config_default = defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080
  },
  plugins: [
    react(),
    mode === "development" && componentTagger()
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__vite_injected_original_dirname, "./src")
    }
  },
  build: {
    ssrManifest: true,
    modulePreload: {
      polyfill: true
    },
    rollupOptions: {
      treeshake: {
        moduleSideEffects: false,
        propertyReadSideEffects: false,
        tryCatchDeoptimization: false
      },
      output: {
        manualChunks: {
          // Core React
          "react-vendor": ["react", "react-dom"],
          "router-vendor": ["react-router-dom"],
          // UI Libraries - Split into smaller chunks
          "radix-core": [
            "@radix-ui/react-dialog",
            "@radix-ui/react-toast",
            "@radix-ui/react-dropdown-menu",
            "@radix-ui/react-popover"
          ],
          "radix-forms": [
            "@radix-ui/react-select",
            "@radix-ui/react-checkbox",
            "@radix-ui/react-radio-group",
            "@radix-ui/react-label",
            "@radix-ui/react-switch"
          ],
          "radix-layout": [
            "@radix-ui/react-accordion",
            "@radix-ui/react-tabs",
            "@radix-ui/react-collapsible",
            "@radix-ui/react-separator",
            "@radix-ui/react-scroll-area"
          ],
          "radix-navigation": [
            "@radix-ui/react-navigation-menu",
            "@radix-ui/react-menubar",
            "@radix-ui/react-context-menu"
          ],
          "radix-feedback": [
            "@radix-ui/react-alert-dialog",
            "@radix-ui/react-hover-card",
            "@radix-ui/react-tooltip",
            "@radix-ui/react-progress"
          ],
          // Backend & State
          "supabase-vendor": ["@supabase/supabase-js", "@supabase/auth-helpers-react", "@supabase/auth-ui-react"],
          "query-vendor": ["@tanstack/react-query"],
          "state-vendor": ["zustand"],
          // Editors & Rich Content
          "editor-vendor": [
            "@tiptap/react",
            "@tiptap/starter-kit",
            "@tiptap/extension-color",
            "@tiptap/extension-image",
            "@tiptap/extension-link",
            "@tiptap/extension-text-style",
            "@tiptap/extension-underline"
          ],
          // Charts & Visualization - Separados para melhor tree-shaking
          "chart-js": ["chart.js"],
          "recharts": ["recharts"],
          "motion-vendor": ["framer-motion"],
          // Utilities
          "date-vendor": ["date-fns"],
          "form-vendor": ["react-hook-form", "@hookform/resolvers", "zod"],
          "dnd-vendor": ["@dnd-kit/core", "@dnd-kit/sortable"],
          "markdown-vendor": ["react-markdown", "remark-math", "rehype-katex"],
          // Heavy Libraries
          "pdf-vendor": ["jspdf"],
          "carousel-vendor": ["embla-carousel-react"]
        }
      }
    },
    // Otimizar o tamanho do bundle
    minify: "terser",
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ["console.log", "console.info", "console.debug"],
        passes: 2
      },
      mangle: {
        safari10: true
      }
    },
    // Configurações adicionais de otimização
    chunkSizeWarningLimit: 500,
    // Reduzido para detectar chunks grandes
    assetsInlineLimit: 2048,
    // Reduzido para evitar inline de assets grandes
    reportCompressedSize: false,
    // Melhora performance do build
    sourcemap: false
    // Remove sourcemaps em produção
  },
  ssr: {
    noExternal: ["react-helmet-async"],
    target: "node",
    format: "esm"
  }
}));
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
