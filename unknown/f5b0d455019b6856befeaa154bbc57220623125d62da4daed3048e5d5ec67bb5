import { useState, useEffect } from 'react';
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import type { QuestionStats } from '@/types/question';

interface SessionStatistics {
  avg_response_time: number;
  total_correct: number;
  total_incorrect: number;
  by_theme: Record<string, { id: string; correct: number; total: number }>;
  by_specialty: Record<string, { id: string; correct: number; total: number }>;
  by_focus: Record<string, { id: string; correct: number; total: number }>;
}

export const useResultsData = (sessionId: string | null) => {
  const [stats, setStats] = useState<QuestionStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchSessionStats = async (currentSessionId: string) => {
      try {
        const { data: sessionStats, error: statsError } = await supabase
          .rpc('get_session_statistics', { p_session_id: currentSessionId });

        if (statsError) throw statsError;
        if (!sessionStats?.length) throw new Error('No statistics found');

        const rawStats = sessionStats[0] as SessionStatistics;

        const categoryIds = [
          ...Object.keys(rawStats.by_specialty || {}),
          ...Object.keys(rawStats.by_theme || {}),
          ...Object.keys(rawStats.by_focus || {})
        ];

        const { data: categories } = await supabase
          .from('study_categories')
          .select('id, name, type')
          .in('id', categoryIds);

        const categoryMap = (categories || []).reduce((acc, cat) => ({
          ...acc,
          [cat.id]: cat.name
        }), {} as Record<string, string>);

        const convertedStats: QuestionStats = {
          time_spent: rawStats.avg_response_time || 0,
          correct_answers: rawStats.total_correct || 0,
          incorrect_answers: rawStats.total_incorrect || 0,
          by_theme: convertCategoryStats(rawStats.by_theme, categoryMap),
          by_specialty: convertCategoryStats(rawStats.by_specialty, categoryMap),
          by_focus: convertCategoryStats(rawStats.by_focus, categoryMap)
        };

        setStats(convertedStats);
      } catch (error: any) {
        toast({
          title: "Erro ao carregar estatísticas",
          description: error.message,
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (sessionId) {
      fetchSessionStats(sessionId);
    }
  }, [sessionId, toast]);

  return { stats, isLoading };
};

const convertCategoryStats = (
  stats: Record<string, { id: string; correct: number; total: number }> | undefined,
  categoryMap: Record<string, string>
) => {
  if (!stats) return {};

  return Object.entries(stats).reduce((acc, [id, data]) => ({
    ...acc,
    [id]: {
      name: categoryMap[id] || 'Unknown',
      correct: data.correct,
      total: data.total
    }
  }), {});
};
