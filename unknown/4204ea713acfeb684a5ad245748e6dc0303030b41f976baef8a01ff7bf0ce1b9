import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

export function NewsletterSignup() {
  const [email, setEmail] = useState("");
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implement newsletter signup
    toast({
      title: "Inscrição realizada!",
      description: "Você receberá nossas atualizações no email informado.",
    });
    setEmail("");
  };

  return (
    <section className="bg-primary/5 rounded-xl p-8 text-center space-y-4">
      <h2 className="text-2xl font-bold">Fique por dentro!</h2>
      <p className="text-muted-foreground max-w-md mx-auto">
        <PERSON><PERSON><PERSON> as últimas atualizações e artigos diretamente no seu email.
      </p>
      <form onSubmit={handleSubmit} className="flex gap-2 max-w-md mx-auto">
        <Input
          type="email"
          placeholder="Seu melhor email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="flex-1"
        />
        <Button type="submit">Inscrever</Button>
      </form>
    </section>
  );
}