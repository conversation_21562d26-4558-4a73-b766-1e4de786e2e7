import { Tag, TagData } from "@/types/dosage";

export function convertTagsToTagData(tags: Tag[], medicationId: string): TagData[] {
  return tags.map(tag => {
    const baseTagData: TagData = {
      medication_id: medicationId,
      name: tag.name,
      type: tag.type,
      multiplier: tag.multiplier,
      max_value: tag.maxValue,
      round_result: tag.roundResult,
    };

    if (tag.type === 'age' && tag.ageRanges?.[0]) {
      return {
        ...baseTagData,
        start_month: tag.ageRanges[0].startMonth,
        end_month: tag.ageRanges[0].endMonth,
        multiplier: tag.ageRanges[0].value,
      };
    }
    if (tag.type === 'multiplier_by_fixed_age' && tag.ageRanges?.[0]) {
      return {
        ...baseTagData,
        start_month: tag.ageRanges[0].startMonth,
        end_month: tag.ageRanges[0].endMonth,
        multiplier: tag.ageRanges[0].value,
      };
    }
    if (tag.type === 'multiplier_by_fixed_age' && tag.ageRanges?.[0]) {
      return {
        ...baseTagData,
        start_month: tag.ageRanges[0].startMonth,
        end_month: tag.ageRanges[0].endMonth,
        multiplier: tag.ageRanges[0].value,
      };
    }
    if (tag.type === 'fixed_by_weight' && tag.weightRanges?.[0]) {
      return {
        ...baseTagData,
        start_weight: tag.weightRanges[0].startWeight,
        end_weight: tag.weightRanges[0].endWeight,
        multiplier: tag.weightRanges[0].value,
      };
    }

    return baseTagData;
  });
}