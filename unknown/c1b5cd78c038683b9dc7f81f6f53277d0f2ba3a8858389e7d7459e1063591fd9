import React from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { BhutaniHeader } from "@/components/calculators/bhutani/BhutaniHeader";
import { BhutaniForm } from "@/components/calculators/bhutani/BhutaniForm";
import { BhutaniFooter } from "@/components/calculators/bhutani/BhutaniFooter";
import { CalculatorSEO } from "@/components/seo/CalculatorSEO";
import { CALCULATOR_SEO_DATA } from "@/data/calculatorSEOData";

const BhutaniCalculator = () => {
  const seoData = CALCULATOR_SEO_DATA['bhutani'];

  return (
    <div className="min-h-screen flex flex-col">
      <CalculatorSEO {...seoData} />

      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-8">
          <BhutaniHeader />
          <BhutaniForm />
          <BhutaniFooter />
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default BhutaniCalculator;