import { Input } from "@/components/ui/input";

interface FixedValueInputProps {
  value: number;
  onChange: (value: number) => void;
}

export function FixedValueInput({ value, onChange }: FixedValueInputProps) {
  const formatNumber = (value: number): string => {
    // For very small numbers, use a fixed number of decimal places
    if (value > 0 && value < 0.001) {
      return value.toFixed(4);
    }
    // For other numbers, use locale string with up to 4 decimal places
    return value.toLocaleString('pt-BR', { 
      minimumFractionDigits: 0,
      maximumFractionDigits: 4 
    });
  };

  const parseFormattedNumber = (value: string): number => {
    // Remove dots from thousands and replace comma with dot for decimal
    const numberStr = value.replace(/\./g, '').replace(',', '.');
    // Parse with higher precision
    const parsed = parseFloat(numberStr);
    // Round to 4 decimal places to avoid floating point precision issues
    return parsed ? Number(parsed.toFixed(4)) : 0;
  };

  return (
    <div className="w-32">
      <Input
        type="text"
        placeholder="Valor fixo"
        value={formatNumber(value)}
        onChange={(e) => onChange(parseFormattedNumber(e.target.value))}
      />
    </div>
  );
}