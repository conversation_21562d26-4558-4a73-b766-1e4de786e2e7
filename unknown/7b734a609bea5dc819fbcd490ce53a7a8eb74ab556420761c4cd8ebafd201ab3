import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { age, weight, gender, symptoms, hasChronicDiseases, chronicDiseases, symptomsIntensity, hasRecentExams, examDetails, isPregnant, isPediatric, manualSymptoms } = await req.json();

    const prompt = `Você é um médico especialista realizando uma análise diagnóstica. Com base nas seguintes informações do paciente:

Idade: ${age} anos
Peso: ${weight} kg
Gênero: ${gender === 'male' ? 'Masculino' : 'Feminino'}
${gender === 'female' && isPregnant !== undefined ? `Gestante: ${isPregnant ? 'Sim' : 'Não'}` : ''}
Paciente Pediátrico: ${isPediatric ? 'Sim' : 'Não'}
Sintomas principais: ${symptoms.join(', ')}
${manualSymptoms ? `Sintomas adicionais: ${manualSymptoms}` : ''}
${hasChronicDiseases ? `Doenças crônicas: ${chronicDiseases}` : 'Sem doenças crônicas'}
Intensidade dos sintomas: ${symptomsIntensity}/10
${hasRecentExams ? `Exames recentes: ${examDetails}` : 'Sem exames recentes'}

Por favor, forneça uma resposta no seguinte formato JSON:

{
  "diagnoses": [
    {
      "condition": "Nome do diagnóstico",
      "probability": "número entre 0 e 100",
      "clinicalPresentation": "Quadro clínico resumido",
      "exams": "Lista de exames principais",
      "treatment": "Tratamento inicial recomendado"
    }
  ],
  "summary": "Resumo geral e recomendações"
}

Importante:
1. Retorne APENAS o objeto JSON, sem texto adicional
2. Não use formatação markdown
3. Não inclua explicações ou notas
4. Mantenha as respostas concisas e diretas
5. Certifique-se que o JSON é válido`;


    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { 
            role: 'system', 
            content: 'Você é um médico especialista que fornece diagnósticos em formato JSON válido, sem texto adicional ou formatação.' 
          },
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (!data.choices?.[0]?.message?.content) {
      throw new Error('Invalid response format from OpenAI');
    }

    let content = data.choices[0].message.content.trim();
    
    // Remove any markdown code block indicators if present
    content = content.replace(/```json\n?|\n?```/g, '');
    
    try {
      const result = JSON.parse(content);
      
      // Validate the expected structure
      if (!Array.isArray(result.diagnoses) || !result.summary) {
        console.error('Invalid response structure:', result);
        throw new Error('Invalid response structure');
      }
      
      return new Response(JSON.stringify(result), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    } catch (parseError) {
      console.error('Error parsing OpenAI response:', parseError);
      console.error('Raw content:', content);
      throw new Error('Failed to parse OpenAI response as JSON');
    }
  } catch (error) {
    console.error('Error in diagnose function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});