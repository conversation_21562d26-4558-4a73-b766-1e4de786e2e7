
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Filter } from "lucide-react";

interface VaccineFiltersProps {
  showOnlyCurrent: boolean;
  setShowOnlyCurrent: (value: boolean) => void;
  showOnlyUpcoming: boolean;
  setShowOnlyUpcoming: (value: boolean) => void;
  showSUS: boolean;
  setShowSUS: (value: boolean) => void;
  showPrivate: boolean;
  setShowPrivate: (value: boolean) => void;
}

export function VaccineFilters({
  showOnlyCurrent,
  setShowOnlyCurrent,
  showOnlyUpcoming,
  setShowOnlyUpcoming,
  showSUS,
  setShowSUS,
  showPrivate,
  setShowPrivate,
}: VaccineFiltersProps) {
  console.log("🔄 Renderizando VaccineFilters");
  
  return (
    <div className="flex justify-center my-4">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="gap-2">
            <Filter className="h-4 w-4" />
            Filtros
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56 p-4 bg-white dark:bg-slate-800 border dark:border-slate-700">
          <DropdownMenuLabel className="text-gray-800 dark:text-gray-200">Visualização</DropdownMenuLabel>
          <div className="flex items-center space-x-2 mt-2">
            <Checkbox 
              id="current" 
              checked={showOnlyCurrent} 
              onCheckedChange={(checked) => {
                setShowOnlyCurrent(checked === true);
                if (checked) setShowOnlyUpcoming(false);
              }}
            />
            <label htmlFor="current" className="text-sm text-gray-700 dark:text-gray-300">Apenas vacinas atuais</label>
          </div>
          <div className="flex items-center space-x-2 mt-2">
            <Checkbox 
              id="upcoming" 
              checked={showOnlyUpcoming}
              onCheckedChange={(checked) => {
                setShowOnlyUpcoming(checked === true);
                if (checked) setShowOnlyCurrent(false);
              }}
            />
            <label htmlFor="upcoming" className="text-sm text-gray-700 dark:text-gray-300">Apenas próximas vacinas</label>
          </div>
          <DropdownMenuSeparator className="my-2 bg-gray-200 dark:bg-slate-600" />
          <DropdownMenuLabel className="text-gray-800 dark:text-gray-200">Tipo de Vacina</DropdownMenuLabel>
          <div className="flex items-center space-x-2 mt-2">
            <Checkbox 
              id="sus" 
              checked={showSUS}
              onCheckedChange={(checked) => setShowSUS(checked === true)}
            />
            <label htmlFor="sus" className="text-sm text-gray-700 dark:text-gray-300">SUS</label>
          </div>
          <div className="flex items-center space-x-2 mt-2">
            <Checkbox 
              id="private" 
              checked={showPrivate}
              onCheckedChange={(checked) => setShowPrivate(checked === true)}
            />
            <label htmlFor="private" className="text-sm text-gray-700 dark:text-gray-300">Particular</label>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
