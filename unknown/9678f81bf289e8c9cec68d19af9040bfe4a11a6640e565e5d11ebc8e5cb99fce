
import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";

interface PoisoningPatientInputProps {
  onWeightChange: (weight: number) => void;
  onAgeChange: (ageInMonths: number) => void;
  showWeight?: boolean;
  showAge?: boolean;
}

export const PoisoningPatientInput: React.FC<PoisoningPatientInputProps> = ({
  onWeightChange,
  onAgeChange,
  showWeight = true,
  showAge = true,
}) => {
  const [weight, setWeight] = useState<string>("");
  const [age, setAge] = useState<string>("");

  const handleWeightChange = (value: string) => {
    setWeight(value);
    const numValue = parseFloat(value);
    if (!isNaN(numValue) && numValue > 0) {
      onWeightChange(numValue);
    }
  };

  const handleAgeChange = (value: string) => {
    setAge(value);
    const numValue = parseFloat(value);
    if (!isNaN(numValue) && numValue >= 0) {
      onAgeChange(numValue * 12); // Convertendo anos para meses
    }
  };

  return (
    <Card className="p-6 space-y-4 bg-white dark:bg-slate-800 shadow-lg border-2 border-red-200/80 dark:border-red-900/50">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {showWeight && (
          <div className="space-y-2 animate-fade-in">
            <Label htmlFor="weight" className="block text-lg font-medium text-gray-800 dark:text-gray-200">
              Peso (kg)
            </Label>
            <Input
              id="weight"
              type="number"
              placeholder="Ex: 20"
              className="text-lg h-12 dark:bg-slate-700 dark:border-slate-600 dark:text-gray-100"
              value={weight}
              onChange={(e) => handleWeightChange(e.target.value)}
            />
          </div>
        )}
        {showAge && (
          <div className="space-y-2 animate-fade-in">
            <Label htmlFor="age" className="block text-lg font-medium text-gray-800 dark:text-gray-200">
              Idade (anos)
            </Label>
            <Input
              id="age"
              type="number"
              placeholder="Ex: 5"
              className="text-lg h-12 dark:bg-slate-700 dark:border-slate-600 dark:text-gray-100"
              value={age}
              onChange={(e) => handleAgeChange(e.target.value)}
            />
          </div>
        )}
      </div>
    </Card>
  );
};
