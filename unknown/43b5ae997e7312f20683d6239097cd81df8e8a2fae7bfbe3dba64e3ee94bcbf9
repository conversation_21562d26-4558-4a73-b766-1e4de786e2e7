import React, { useState } from "react";
import { Pill, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface AppStyleMedicationItemProps {
  name: string;
  category: string;
  onClick?: () => void;
  color?: string;
}

const AppStyleMedicationItem: React.FC<AppStyleMedicationItemProps> = ({
  name,
  category,
  onClick,
  color = "blue",
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = () => {
    if (isLoading) return; // Prevenir cliques duplos
    setIsLoading(true);
    onClick?.();
    // Reset após um tempo para permitir novos cliques
    setTimeout(() => setIsLoading(false), 2000);
  };
  // Cor da barra superior
  const getTopBarColorClass = () => {
    if (color.includes("yellow")) return "bg-yellow-500";
    if (color.includes("purple")) return "bg-purple-500";
    if (color.includes("blue")) return "bg-blue-500";
    if (color.includes("pink")) return "bg-pink-500";
    if (color.includes("green")) return "bg-green-500";
    if (color.includes("amber")) return "bg-amber-500";
    if (color.includes("red")) return "bg-red-500";
    if (color.includes("cyan")) return "bg-cyan-500";
    if (color.includes("indigo")) return "bg-indigo-500";
    if (color.includes("rose")) return "bg-rose-500";
    return "bg-primary";
  };

  return (
    <div
      className={cn(
        "relative w-full p-3 rounded-lg transition-all duration-300 cursor-pointer",
        "bg-white/90 dark:bg-slate-800/90 backdrop-blur-md shadow-sm hover:shadow-md",
        "border border-gray-100 dark:border-gray-700/50",
        "hover:-translate-y-1 flex items-center gap-3",
        isLoading && "opacity-75 scale-95 pointer-events-none"
      )}
      onClick={handleClick}
    >
      {/* Barra de cor na parte superior para aparência de app */}
      <div className={cn(
        "absolute top-0 left-0 right-0 h-1 rounded-t-lg",
        getTopBarColorClass()
      )} />

      <div className="p-2 bg-primary/10 rounded-full dark:bg-blue-800/40 flex-shrink-0">
        {isLoading ? (
          <Loader2 className="h-5 w-5 text-primary dark:text-blue-400 animate-spin" />
        ) : (
          <Pill className="h-5 w-5 text-primary dark:text-blue-400" />
        )}
      </div>
      
      <div className="flex-1 text-left min-w-0">
        <h4 className="font-medium text-primary dark:text-blue-400 text-sm sm:text-base truncate">
          {name}
        </h4>
        <span className="text-[10px] sm:text-xs text-gray-500 dark:text-gray-400">
          {category}
        </span>
      </div>
    </div>
  );
};

export default AppStyleMedicationItem;
