
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { PatientProfile } from "./PatientProfile";
import { AnamneseBlocks } from "./AnamneseBlocks";
import { ExameBlocks } from "./ExameBlocks";
import { GeneratedText } from "./GeneratedText";
import { TemplateSelector } from "./TemplateSelector";
import { PediatricBlocks } from "./PediatricBlocks";
import { PediatricExameBlocks } from "./PediatricExameBlocks";
import { FileText, Copy, Save, RefreshCw, Zap, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

export interface PatientData {
  gender: "male" | "female" | "other";
  age: number;
  ageUnit: "days" | "months" | "years";
  appointmentType: string;
  mainComplaint: string;
  birthDate?: string;
  name?: string;
  informant?: string;
  previousDiagnosis?: string;
  premature?: boolean;
  correctedAge?: string;
}

export interface AnamneseData {
  hda: string;
  perinatais: string;
  patologicos: string;
  cirurgicos: string;
  alergicos: string;
  medicamentosos: string;
  vacinacao: string;
  desenvolvimento: string;
  alimentacao: string;
  ambiente: string;
}

export interface PediatricData {
  headSymptoms: string;
  mouthSymptoms: string;
  earSymptoms: string;
  eyeSymptoms: string;
  breathingIssues: string;
  feedingIssues: string;
  digestionIssues: string;
  urineStool: string;
  behaviorIssues: string;
  breastfeeding: string;
  breastfeedingTechnique: string;
  artificialFeeding: string;
  sleep: string;
  careRoutines: string;
  motherMeds: string;
  babyMeds: string;
  vaccineReactions: string;
  dnpmObservations: string;
}

export interface ExameData {
  estadoGeral: string;
  sinaisVitais: {
    fc: string;
    fr: string;
    pa: string;
    temp: string;
    sat: string;
  };
  cabecaPescoco: string;
  torax: string;
  abdome: {
    distensao: boolean;
    rhaAumentados: boolean;
    dorPalpacao: boolean;
    defesaInvoluntaria: boolean;
    massaPalpavel: boolean;
    detalhes: string;
  };
  geniturinario: string;
  locomocao: string;
  neurologico: string;
  peleMucosas: string;
}

export interface PediatricExameData {
  weight: string;
  height: string;
  headCircumference: string;
  anteriorFontanelle: string;
  posteriorFontanelle: string;
  sutures: string;
  heartRate: string;
  respiratoryRate: string;
  temperature: string;
  headNeck: string;
  redReflex: string;
  oropharynx: string;
  otoscopy: string;
  cardiovascular: string;
  respiratory: string;
  abdomen: string;
  peripheralPulses: string;
  genitalia: string;
  bcgScar: string;
  ortolaniManeuver: string;
  reflexWalking: string;
  moro: string;
  asimetricTonicNeck: string;
  followsObjects: string;
  socialSmile: string;
  screeningTests: string;
}

export type TemplateType = "traditional" | "simple" | "pediatric_1_2m";

export const AnamneseForm: React.FC = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("template");
  const [isGenerating, setIsGenerating] = useState(false);
  const [templateType, setTemplateType] = useState<TemplateType>("traditional");

  const [patientData, setPatientData] = useState<PatientData>({
    gender: "male",
    age: 0,
    ageUnit: "years",
    appointmentType: "",
    mainComplaint: "",
  });

  const [anamneseData, setAnamneseData] = useState<AnamneseData>({
    hda: "",
    perinatais: "",
    patologicos: "",
    cirurgicos: "",
    alergicos: "",
    medicamentosos: "",
    vacinacao: "",
    desenvolvimento: "",
    alimentacao: "",
    ambiente: "",
  });

  const [pediatricData, setPediatricData] = useState<PediatricData>({
    headSymptoms: "",
    mouthSymptoms: "",
    earSymptoms: "",
    eyeSymptoms: "",
    breathingIssues: "",
    feedingIssues: "",
    digestionIssues: "",
    urineStool: "",
    behaviorIssues: "",
    breastfeeding: "",
    breastfeedingTechnique: "",
    artificialFeeding: "",
    sleep: "",
    careRoutines: "",
    motherMeds: "",
    babyMeds: "",
    vaccineReactions: "",
    dnpmObservations: "",
  });

  const [exameData, setExameData] = useState<ExameData>({
    estadoGeral: "",
    sinaisVitais: {
      fc: "",
      fr: "",
      pa: "",
      temp: "",
      sat: "",
    },
    cabecaPescoco: "",
    torax: "",
    abdome: {
      distensao: false,
      rhaAumentados: false,
      dorPalpacao: false,
      defesaInvoluntaria: false,
      massaPalpavel: false,
      detalhes: "",
    },
    geniturinario: "",
    locomocao: "",
    neurologico: "",
    peleMucosas: "",
  });

  const [pediatricExameData, setPediatricExameData] = useState<PediatricExameData>({
    weight: "",
    height: "",
    headCircumference: "",
    anteriorFontanelle: "",
    posteriorFontanelle: "",
    sutures: "",
    heartRate: "",
    respiratoryRate: "",
    temperature: "",
    headNeck: "",
    redReflex: "",
    oropharynx: "",
    otoscopy: "",
    cardiovascular: "",
    respiratory: "",
    abdomen: "",
    peripheralPulses: "",
    genitalia: "",
    bcgScar: "",
    ortolaniManeuver: "",
    reflexWalking: "",
    moro: "",
    asimetricTonicNeck: "",
    followsObjects: "",
    socialSmile: "",
    screeningTests: "",
  });

  const [generatedText, setGeneratedText] = useState({
    anamnese: "",
    exame: "",
  });

  const handleTemplateSelection = (type: TemplateType) => {
    setTemplateType(type);
    setActiveTab("perfil");

    // Set default age for pediatric template
    if (type === "pediatric_1_2m") {
      setPatientData({
        ...patientData,
        age: 1,
        ageUnit: "months",
      });
    }

    toast({
      title: type === "traditional"
        ? "Modelo de anamnese tradicional selecionado"
        : type === "pediatric_1_2m"
          ? "Modelo de puericultura 1-2 meses selecionado"
          : "Modelo de anamnese simplificada selecionado",
      description: "Continue preenchendo os dados do paciente.",
    });
  };

  const handlePatientDataChange = (data: Partial<PatientData>) => {
    setPatientData({ ...patientData, ...data });
  };

  const handleAnamneseDataChange = (data: Partial<AnamneseData>) => {
    setAnamneseData({ ...anamneseData, ...data });
  };

  const handlePediatricDataChange = (data: Partial<PediatricData>) => {
    setPediatricData({ ...pediatricData, ...data });
  };

  const handleExameDataChange = (data: Partial<ExameData>) => {
    setExameData({ ...exameData, ...data });
  };

  const handlePediatricExameDataChange = (data: Partial<PediatricExameData>) => {
    setPediatricExameData({ ...pediatricExameData, ...data });
  };

  // Gerar texto utilizando AI para completar os campos
  const generateTextWithAI = async () => {
    setIsGenerating(true);

    try {
      // Validar dados básicos do paciente
      if (patientData.age === 0) {
        toast({
          title: "Erro ao gerar texto",
          description: "Por favor, informe a idade do paciente.",
          variant: "destructive"
        });
        setIsGenerating(false);
        return;
      }

      // Preparar os dados para enviar para a AI
      const aiPrompt: any = {
        patient: patientData,
        templateType: templateType
      };

      // Adicionar dados específicos com base no tipo de template
      if (templateType === "traditional") {
        aiPrompt.anamnese = anamneseData;
        aiPrompt.exame = exameData;
      } else if (templateType === "pediatric_1_2m") {
        aiPrompt.pediatric = pediatricData;
        aiPrompt.exame = pediatricExameData;
      } else {
        // Para outros templates futuros
        aiPrompt.anamnese = anamneseData;
        aiPrompt.exame = exameData;
      }

      // Chamar a Edge Function do Supabase que usa a OpenAI
      const { data, error } = await supabase.functions.invoke('generate-anamnese', {
        body: aiPrompt
      });

      if (error) {
        throw new Error(error.message);
      }

      // Verificar se a resposta contém os campos esperados
      if (!data.anamnese || !data.exame) {
        throw new Error("A resposta da IA está incompleta ou mal formatada");
      }

      setGeneratedText({
        anamnese: data.anamnese,
        exame: data.exame
      });

      // Mudar para a aba de resultado
      setActiveTab("resultado");

      toast({
        title: "Texto gerado com sucesso!",
        description: "O texto foi gerado usando inteligência artificial para complementar os campos não preenchidos.",
      });
    } catch (error) {
      console.error("Erro ao gerar texto:", error);
      toast({
        title: "Erro ao gerar texto",
        description: error instanceof Error ? error.message : "Ocorreu um erro ao processar sua solicitação. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = () => {
    const fullText = `ANAMNESE\n${generatedText.anamnese}\n\nEXAME FÍSICO\n${generatedText.exame}`;
    navigator.clipboard.writeText(fullText);

    toast({
      title: "Texto copiado!",
      description: "O texto completo foi copiado para a área de transferência.",
    });
  };

  const saveAsTemplate = () => {
    // Implementação para salvar como modelo
    toast({
      title: "Modelo salvo!",
      description: "Este modelo foi salvo e estará disponível para uso futuro.",
    });
  };

  const resetForm = () => {
    setPatientData({
      gender: "male",
      age: 0,
      ageUnit: "years",
      appointmentType: "",
      mainComplaint: "",
    });

    setAnamneseData({
      hda: "",
      perinatais: "",
      patologicos: "",
      cirurgicos: "",
      alergicos: "",
      medicamentosos: "",
      vacinacao: "",
      desenvolvimento: "",
      alimentacao: "",
      ambiente: "",
    });

    setPediatricData({
      headSymptoms: "",
      mouthSymptoms: "",
      earSymptoms: "",
      eyeSymptoms: "",
      breathingIssues: "",
      feedingIssues: "",
      digestionIssues: "",
      urineStool: "",
      behaviorIssues: "",
      breastfeeding: "",
      breastfeedingTechnique: "",
      artificialFeeding: "",
      sleep: "",
      careRoutines: "",
      motherMeds: "",
      babyMeds: "",
      vaccineReactions: "",
      dnpmObservations: "",
    });

    setExameData({
      estadoGeral: "",
      sinaisVitais: {
        fc: "",
        fr: "",
        pa: "",
        temp: "",
        sat: "",
      },
      cabecaPescoco: "",
      torax: "",
      abdome: {
        distensao: false,
        rhaAumentados: false,
        dorPalpacao: false,
        defesaInvoluntaria: false,
        massaPalpavel: false,
        detalhes: "",
      },
      geniturinario: "",
      locomocao: "",
      neurologico: "",
      peleMucosas: "",
    });

    setPediatricExameData({
      weight: "",
      height: "",
      headCircumference: "",
      anteriorFontanelle: "",
      posteriorFontanelle: "",
      sutures: "",
      heartRate: "",
      respiratoryRate: "",
      temperature: "",
      headNeck: "",
      redReflex: "",
      oropharynx: "",
      otoscopy: "",
      cardiovascular: "",
      respiratory: "",
      abdomen: "",
      peripheralPulses: "",
      genitalia: "",
      bcgScar: "",
      ortolaniManeuver: "",
      reflexWalking: "",
      moro: "",
      asimetricTonicNeck: "",
      followsObjects: "",
      socialSmile: "",
      screeningTests: "",
    });

    setGeneratedText({
      anamnese: "",
      exame: "",
    });

    setActiveTab("template");

    toast({
      title: "Formulário resetado",
      description: "Todos os campos foram limpos.",
    });
  };

  const quickMode = () => {
    toast({
      title: "Modo rápido ativado",
      description: "Digite apenas os achados alterados e deixe que o sistema complete o resto.",
      action: (
        <Button onClick={() => {
          if (templateType === "traditional") {
            setActiveTab("anamnese");
          } else if (templateType === "pediatric_1_2m") {
            setActiveTab("pediatric");
          }
        }} variant="outline" size="sm">
          Começar
        </Button>
      ),
    });
  };

  return (
    <Card className="p-6 shadow-md">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid grid-cols-5 w-full">
          <TabsTrigger value="template">Tipo de Atendimento</TabsTrigger>
          <TabsTrigger value="perfil">Perfil do Paciente</TabsTrigger>
          {templateType === "traditional" ? (
            <>
              <TabsTrigger value="anamnese">Anamnese</TabsTrigger>
              <TabsTrigger value="exame">Exame Físico</TabsTrigger>
            </>
          ) : templateType === "pediatric_1_2m" ? (
            <>
              <TabsTrigger value="pediatric">Questionário</TabsTrigger>
              <TabsTrigger value="pediatricExame">Exame Físico</TabsTrigger>
            </>
          ) : (
            <>
              <TabsTrigger value="anamnese">Anamnese</TabsTrigger>
              <TabsTrigger value="exame">Exame Físico</TabsTrigger>
            </>
          )}
          <TabsTrigger value="resultado">Resultado</TabsTrigger>
        </TabsList>

        <TabsContent value="template" className="space-y-6">
          <TemplateSelector onSelect={handleTemplateSelection} />
        </TabsContent>

        <TabsContent value="perfil" className="space-y-6">
          <PatientProfile
            data={patientData}
            onChange={handlePatientDataChange}
            templateType={templateType}
          />
          <div className="flex justify-between">
            <Button variant="outline" onClick={() => setActiveTab("template")}>
              Voltar: Tipo de Atendimento
            </Button>
            <Button onClick={() => {
              if (templateType === "traditional") {
                setActiveTab("anamnese");
              } else if (templateType === "pediatric_1_2m") {
                setActiveTab("pediatric");
              }
            }}>
              Próximo: {templateType === "traditional" ? "Anamnese" : "Questionário"}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="anamnese" className="space-y-6">
          <AnamneseBlocks data={anamneseData} onChange={handleAnamneseDataChange} templateType={templateType} />
          <div className="flex justify-between">
            <Button variant="outline" onClick={() => setActiveTab("perfil")}>
              Voltar: Perfil
            </Button>
            <Button onClick={() => setActiveTab("exame")}>
              Próximo: Exame Físico
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="pediatric" className="space-y-6">
          <PediatricBlocks data={pediatricData} onChange={handlePediatricDataChange} />
          <div className="flex justify-between">
            <Button variant="outline" onClick={() => setActiveTab("perfil")}>
              Voltar: Perfil
            </Button>
            <Button onClick={() => setActiveTab("pediatricExame")}>
              Próximo: Exame Físico
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="exame" className="space-y-6">
          <ExameBlocks data={exameData} onChange={handleExameDataChange} />
          <div className="flex justify-between">
            <Button variant="outline" onClick={() => setActiveTab("anamnese")}>
              Voltar: Anamnese
            </Button>
            <Button
              onClick={generateTextWithAI}
              disabled={isGenerating}
              className="relative"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Gerando Texto...
                </>
              ) : "Gerar Texto"}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="pediatricExame" className="space-y-6">
          <PediatricExameBlocks data={pediatricExameData} onChange={handlePediatricExameDataChange} />
          <div className="flex justify-between">
            <Button variant="outline" onClick={() => setActiveTab("pediatric")}>
              Voltar: Questionário
            </Button>
            <Button
              onClick={generateTextWithAI}
              disabled={isGenerating}
              className="relative"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Gerando Texto...
                </>
              ) : "Gerar Texto"}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="resultado" className="space-y-6">
          <GeneratedText anamnese={generatedText.anamnese} exame={generatedText.exame} />

          <div className="flex flex-wrap gap-2 justify-between">
            <div className="flex flex-wrap gap-2">
              <Button onClick={copyToClipboard} className="flex items-center gap-1">
                <Copy className="h-4 w-4" />
                Copiar tudo
              </Button>
              <Button onClick={saveAsTemplate} variant="outline" className="flex items-center gap-1">
                <Save className="h-4 w-4" />
                Salvar como modelo
              </Button>
              <Button onClick={resetForm} variant="outline" className="flex items-center gap-1">
                <RefreshCw className="h-4 w-4" />
                Resetar tudo
              </Button>
              <Button onClick={quickMode} variant="outline" className="flex items-center gap-1">
                <Zap className="h-4 w-4" />
                Modo rápido
              </Button>
            </div>

            <Button onClick={() => setActiveTab("template")} variant="secondary">
              <FileText className="h-4 w-4 mr-1" />
              Criar nova anamnese
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </Card>
  );
};
