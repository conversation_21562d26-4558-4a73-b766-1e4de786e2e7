/**
 * Script completo para gerar todas as páginas SEO
 * Medicamentos + Condutas + Sitemap
 */

import { execSync } from 'child_process';

async function buildComplete() {
  try {
    console.log('🚀 Iniciando build completo com SEO...');
    
    // Passo 1: Build normal do Vite
    console.log('\n🔨 Passo 1: Executando build do Vite...');
    execSync('npm run build', { stdio: 'inherit' });

    // Passo 2: Gerar páginas de medicamentos (inclui sitemap)
    console.log('\n💊 Passo 2: Gerando páginas de medicamentos...');
    execSync('node scripts/simpleBuild.js', { stdio: 'inherit' });

    // Passo 3: Gerar páginas de condutas
    console.log('\n📋 Passo 3: Gerando páginas de condutas...');
    execSync('node scripts/generateConducts.js', { stdio: 'inherit' });

    // Passo 4: Gerar páginas das novas seções
    console.log('\n🆕 Passo 4: Gerando páginas das novas seções...');
    execSync('node scripts/generateNewSections.js', { stdio: 'inherit' });

    console.log('\n🎉 Build completo finalizado!');
    console.log('📊 Resumo:');
    console.log('   💊 138 páginas de medicamentos');
    console.log('   📋 48 páginas de condutas');
    console.log('   🧮 Calculadoras e escalas');
    console.log('   👶 Puericultura e DNPM');
    console.log('   ☠️ Intoxicações pediátricas');
    console.log('   🌊 Fluxogramas clínicos');
    console.log('   🏥 CID-10 pediátrico');
    console.log('   📋 Bulas profissionais');
    console.log('   🗺️ Sitemap completo com todas as seções');
    console.log('   🎯 SEO otimizado para todas as páginas');

  } catch (error) {
    console.error('\n❌ Erro no build completo:', error);
    process.exit(1);
  }
}

buildComplete();
