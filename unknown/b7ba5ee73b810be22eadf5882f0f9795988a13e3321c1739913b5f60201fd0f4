import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface DehydrationAssessmentProps {
  onAssessmentComplete: (hasDehydration: boolean) => void;
}

export const DehydrationAssessment = ({ onAssessmentComplete }: DehydrationAssessmentProps) => {
  const [respostas, setRespostas] = useState<Record<string, string>>({});
  const [resultado, setResultado] = useState<string>("");

  const perguntas = [
    { 
      id: "estadoGeral", 
      texto: "Estado geral do paciente", 
      opcoes: ["Ativo e alerta", "Irritado ou intranquilo", "Comatoso ou letárgico"] 
    },
    { 
      id: "olhos", 
      texto: "Olhos do paciente", 
      opcoes: ["Sem alteração", "Fundos", "Muito fundos"] 
    },
    { 
      id: "sede", 
      texto: "O paciente está com sede?", 
      opcoes: ["Sem sede", "Sedento e bebe rápido", "Não consegue beber"] 
    },
    { 
      id: "lagrimas", 
      texto: "Lágrimas", 
      opcoes: ["Presentes", "Ausentes"] 
    },
    { 
      id: "boca", 
      texto: "Mucosa oral", 
      opcoes: ["Úmida", "Seca", "Muito seca"] 
    },
    { 
      id: "prega", 
      texto: "Sinal da prega abdominal", 
      opcoes: ["Desaparece imediatamente", "Desaparece lentamente", "Desaparece muito lentamente (> 2 segundos)"] 
    },
    { 
      id: "pulso", 
      texto: "Pulso do paciente", 
      opcoes: ["Cheio", "Fraco ou ausente"] 
    },
  ];

  const avaliarDesidratacao = () => {
    let sinais = 0;
    let sinaisGraves = 0;

    if (respostas.estadoGeral === "Irritado ou intranquilo") sinais++;
    if (respostas.estadoGeral === "Comatoso ou letárgico") sinaisGraves++;
    
    if (respostas.olhos === "Fundos") sinais++;
    if (respostas.olhos === "Muito fundos") sinaisGraves++;
    
    if (respostas.sede === "Sedento e bebe rápido") sinais++;
    if (respostas.sede === "Não consegue beber") sinaisGraves++;
    
    if (respostas.lagrimas === "Ausentes") sinais++;
    if (respostas.boca === "Seca") sinais++;
    if (respostas.boca === "Muito seca") sinaisGraves++;
    
    if (respostas.prega === "Desaparece lentamente") sinais++;
    if (respostas.prega === "Desaparece muito lentamente (> 2 segundos)") sinaisGraves++;
    
    if (respostas.pulso === "Fraco ou ausente") sinaisGraves++;

    if (sinaisGraves >= 1) {
      setResultado("Desidratação grave (Plano C)");
      onAssessmentComplete(true);
    } else if (sinais >= 2) {
      setResultado("Com desidratação (Plano B)");
      onAssessmentComplete(true);
    } else {
      setResultado("Sem sinais de desidratação (Plano A)");
      onAssessmentComplete(false);
    }
  };

  const handleChange = (id: string, value: string) => {
    setRespostas(prev => ({ ...prev, [id]: value }));
  };

  const getResultadoStyle = () => {
    if (resultado.includes("grave")) {
      return "bg-red-100 border-red-200 text-red-800 font-medium";
    } else if (resultado.includes("Com desidratação")) {
      return "bg-yellow-100 border-yellow-200 text-yellow-800 font-medium";
    } else if (resultado.includes("Sem sinais")) {
      return "bg-green-100 border-green-200 text-green-800 font-medium";
    }
    return "";
  };

  return (
    <Card className="p-6 space-y-4">
      <h3 className="text-lg font-semibold">Ferramenta de Avaliação da Desidratação</h3>
      <p className="text-sm text-gray-600">
        Esta ferramenta auxilia na avaliação dos sinais de desidratação, mas não substitui o julgamento clínico.
      </p>
      
      <div className="space-y-4">
        {perguntas.map((pergunta) => (
          <div key={pergunta.id} className="space-y-2">
            <label className="text-sm font-medium">{pergunta.texto}</label>
            <select
              onChange={(e) => handleChange(pergunta.id, e.target.value)}
              className="w-full p-2 border rounded-md"
              value={respostas[pergunta.id] || ""}
            >
              <option value="">Selecione uma opção</option>
              {pergunta.opcoes.map((opcao, index) => (
                <option key={index} value={opcao}>
                  {opcao}
                </option>
              ))}
            </select>
          </div>
        ))}
      </div>

      <Button
        onClick={avaliarDesidratacao}
        className="w-full"
        variant="outline"
      >
        Avaliar Desidratação
      </Button>

      {resultado && (
        <Alert className={getResultadoStyle()}>
          <AlertDescription className="font-medium">
            Resultado: {resultado}
          </AlertDescription>
        </Alert>
      )}
    </Card>
  );
};