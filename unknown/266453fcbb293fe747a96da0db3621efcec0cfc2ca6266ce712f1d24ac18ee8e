
import { useMemo } from "react";
import { FilterItem } from "../../FilterItem";
import { useHierarchicalFilterCounts } from "@/hooks/useOptimizedFilterSelection";
import type { SelectedFilters } from "@/types/question";

interface LocationFilterSectionProps {
  locations: any[];
  selectedLocations: string[];
  onToggleLocation: (id: string) => void;
  selectedFilters: SelectedFilters;
  searchTerm: string;
}

export const LocationFilterSection = ({
  locations = [],
  selectedLocations = [],
  onToggleLocation,
  selectedFilters,
  searchTerm = ""
}: LocationFilterSectionProps) => {
  // Verificar se há filtros de categoria selecionados
  const hasCategoryFilters = (
    (selectedFilters.specialties && selectedFilters.specialties.length > 0) ||
    (selectedFilters.themes && selectedFilters.themes.length > 0) ||
    (selectedFilters.focuses && selectedFilters.focuses.length > 0)
  );

  // Usar contagens hierárquicas se há filtros de categoria
  const { data: hierarchicalCounts, isLoading: isLoadingHierarchical } = useHierarchicalFilterCounts(
    selectedFilters,
    'locations'
  );

  // Função para obter a contagem correta
  const getLocationCount = (location: any) => {
    if (hasCategoryFilters && hierarchicalCounts) {
      return hierarchicalCounts[location.id] || 0;
    }
    return location.question_count || location.count || 0;
  };

  // Filter locations that have a count > 0 (usando contagem hierárquica se aplicável)
  const validLocations = useMemo(() => {
    return locations.filter(location => getLocationCount(location) > 0);
  }, [locations, hasCategoryFilters, hierarchicalCounts]);

  // Filter by search term
  const filteredLocations = useMemo(() => {
    return validLocations.filter(
      location => location.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [validLocations, searchTerm]);

  // Sort locations by count (highest first)
  const sortedLocations = useMemo(() => {
    return [...filteredLocations].sort((a, b) => getLocationCount(b) - getLocationCount(a));
  }, [filteredLocations, hasCategoryFilters, hierarchicalCounts]);

  // Mostrar indicador de carregamento se estiver carregando contagens hierárquicas
  if (hasCategoryFilters && isLoadingHierarchical) {
    return (
      <div className="space-y-2">
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex items-center justify-between p-3 rounded-lg bg-gray-100 animate-pulse">
            <div className="flex items-center gap-3">
              <div className="w-5 h-5 rounded bg-gray-300"></div>
              <div className="h-4 w-40 bg-gray-300 rounded"></div>
            </div>
            <div className="h-6 w-12 bg-gray-300 rounded-full"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {sortedLocations.map(location => {
        const count = getLocationCount(location);
        return (
          <FilterItem
            key={location.id}
            item={{ ...location, type: "location" }}
            level={0}
            isExpanded={false}
            isSelected={selectedLocations.includes(location.id)}
            questionCount={{
              total: count,
              filtered: count
            }}
            hasChildren={false}
            onToggleExpand={() => {}}
            onToggleSelect={onToggleLocation}
            className={hasCategoryFilters ? "ring-2 ring-blue-200" : undefined} // Indicador visual
          />
        );
      })}
      {sortedLocations.length === 0 && !isLoadingHierarchical && (
        <div className="text-center text-gray-500 py-4">
          {hasCategoryFilters
            ? "Nenhuma instituição encontrada para as especialidades/temas/focos selecionados"
            : "Nenhuma instituição encontrada"
          }
        </div>
      )}
    </div>
  );
};
