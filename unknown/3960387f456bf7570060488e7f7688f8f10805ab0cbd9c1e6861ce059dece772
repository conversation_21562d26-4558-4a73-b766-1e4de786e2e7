
import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogClose
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { BookOpen, PenLine, X, HelpCircle, CheckCircle } from "lucide-react";

interface TopicSourceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectSource: (source: 'platform' | 'manual') => void;
}

export const TopicSourceDialog: React.FC<TopicSourceDialogProps> = ({
  open,
  onOpenChange,
  onSelectSource
}) => {
  console.log("🔄 Renderizando TopicSourceDialog, estado:", { open });
  
  const handlePlatformClick = () => {
    console.log("🎯 TopicSourceDialog - Platform button clicked");
    console.trace("Call stack for platform button click:");
    onSelectSource('platform');
  };

  const handleManualClick = () => {
    console.log("✏️ TopicSourceDialog - Manual button clicked");
    console.trace("Call stack for manual button click:");
    onSelectSource('manual');
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md w-[calc(100%-2rem)] p-0 gap-0 overflow-hidden max-h-[90vh] max-w-[95vw] mx-auto" hideCloseButton>
        <DialogHeader className="bg-gradient-to-r from-[#58CC02] to-[#46a302] text-white p-6 relative">
          <DialogTitle className="text-xl font-bold text-center">Adicionar Tópico</DialogTitle>
          <DialogDescription className="text-white/90 text-center mt-1">
            Escolha como você deseja adicionar um tópico ao seu cronograma de estudos
          </DialogDescription>
          <DialogClose className="absolute right-4 top-4 rounded-full bg-white/20 p-1 opacity-70 hover:opacity-100">
            <X className="h-4 w-4" />
          </DialogClose>
        </DialogHeader>
        
        <div className="p-5 flex flex-col gap-5 w-full overflow-y-auto">
          {/* Mensagem informativa */}
          <div className="bg-blue-50 border border-blue-100 rounded-lg p-3 flex items-start gap-3">
            <HelpCircle className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
            <p className="text-sm text-blue-700 break-words">
              Você pode selecionar um tópico pronto da nossa plataforma ou criar seu próprio tópico personalizado.
            </p>
          </div>
          
          {/* Opção 1: Tópico da Plataforma */}
          <Button
            variant="outline"
            className="flex items-center justify-center w-full h-auto px-4 py-3 border-2 border-blue-100 hover:border-blue-300 hover:bg-blue-50 rounded-xl"
            onClick={handlePlatformClick}
          >
            <div className="flex w-full items-center gap-3 min-w-0">
              <div className="bg-blue-100 p-2.5 rounded-full group-hover:bg-blue-200 transition-colors flex-shrink-0">
                <BookOpen className="h-5 w-5 text-blue-600" />
              </div>
              <div className="text-start flex-grow overflow-hidden">
                <div className="flex items-center gap-2 flex-wrap">
                  <h3 className="font-bold text-blue-600">Tópico da Plataforma</h3>
                  <span className="bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded-full">Recomendado</span>
                </div>
                <p className="text-xs text-gray-500 mt-1 break-words overflow-hidden">
                  Escolha entre centenas de temas organizados por especialidade. 
                  Ideal para quem não sabe por onde começar.
                </p>
              </div>
            </div>
          </Button>

          {/* Vantagens de usar Tópico da Plataforma */}
          <div className="pl-4">
            <ul className="space-y-2">
              <li className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-xs text-gray-600 break-words">Tópicos estruturados por especialistas</span>
              </li>
              <li className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-xs text-gray-600 break-words">Acompanhamento de revisões programadas</span>
              </li>
            </ul>
          </div>

          {/* Opção 2: Tópico Manual */}
          <Button
            variant="outline"
            className="flex items-center justify-center w-full h-auto px-4 py-3 border-2 border-purple-100 hover:border-purple-300 hover:bg-purple-50 rounded-xl"
            onClick={handleManualClick}
          >
            <div className="flex w-full items-center gap-3 min-w-0">
              <div className="bg-purple-100 p-2.5 rounded-full group-hover:bg-purple-200 transition-colors flex-shrink-0">
                <PenLine className="h-5 w-5 text-purple-600" />
              </div>
              <div className="text-start flex-grow overflow-hidden">
                <h3 className="font-bold text-purple-600">Tópico Manual</h3>
                <p className="text-xs text-gray-500 mt-1 break-words overflow-hidden">
                  Crie seu próprio tópico personalizado com conteúdo específico 
                  para suas necessidades de estudo.
                </p>
              </div>
            </div>
          </Button>
          
          {/* Vantagens de usar Tópico Manual */}
          <div className="pl-4">
            <ul className="space-y-2">
              <li className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-xs text-gray-600 break-words">Flexibilidade total para personalizar conteúdos</span>
              </li>
              <li className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-xs text-gray-600 break-words">Ideal para tópicos específicos não cobertos pela plataforma</span>
              </li>
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TopicSourceDialog;
