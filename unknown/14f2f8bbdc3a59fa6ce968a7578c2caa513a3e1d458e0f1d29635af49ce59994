import { useState, useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface ICD10Code {
  code_range: string;
  name: string;
}

interface ICD10CategoryDialogProps {
  category?: any;
  parentId: string | null;
  isOpen: boolean;
  onClose: () => void;
}

export function ICD10CategoryDialog({
  category,
  parentId,
  isOpen,
  onClose,
}: ICD10CategoryDialogProps) {
  const [formData, setFormData] = useState({
    code_range: "",
    name: "",
    description: "",
    level: 1,
  });
  const [codesJson, setCodes<PERSON>son] = useState("");
  const [codes, setCodes] = useState<ICD10Code[]>([]);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (isOpen && category) {
      setFormData({
        code_range: category.code_range || "",
        name: category.name || "",
        description: category.description || "",
        level: category.level || 1,
      });
      
      // Fetch existing codes for this category
      fetchCodes(category.id);
    } else {
      setFormData({
        code_range: "",
        name: "",
        description: "",
        level: parentId ? 2 : 1,
      });
      setCodesJson("");
      setCodes([]);
    }
  }, [isOpen, category, parentId]);

  const fetchCodes = async (categoryId: string) => {
    const { data, error } = await supabase
      .from("pedbook_icd10_codes")
      .select("*")
      .eq("category_id", categoryId);

    if (!error && data) {
      setCodes(data);
      const jsonStr = data.map(({ code_range, name }) => ({ 
        codigocid: code_range, 
        nome: name 
      }));
      setCodesJson(JSON.stringify(jsonStr, null, 2));
    }
  };

  const handleJsonImport = () => {
    try {
      const parsedCodes = JSON.parse(codesJson);
      const formattedCodes = parsedCodes.map((code: any) => ({
        code_range: code.codigocid,
        name: code.nome,
      }));
      setCodes(formattedCodes);
      toast({
        title: "Códigos importados com sucesso!",
        description: `${formattedCodes.length} códigos foram importados.`,
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Erro ao importar códigos",
        description: "Verifique se o formato do JSON está correto.",
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (category) {
        // Update category
        const { error: categoryError } = await supabase
          .from("pedbook_icd10_categories")
          .update({
            ...formData,
            parent_id: parentId,
          })
          .eq("id", category.id);

        if (categoryError) throw categoryError;

        // Update codes
        if (codes.length > 0) {
          // Delete existing codes
          await supabase
            .from("pedbook_icd10_codes")
            .delete()
            .eq("category_id", category.id);

          // Insert new codes
          const { error: codesError } = await supabase
            .from("pedbook_icd10_codes")
            .insert(
              codes.map(code => ({
                ...code,
                category_id: category.id,
              }))
            );

          if (codesError) throw codesError;
        }

        toast({
          title: "Categoria atualizada com sucesso!",
          description: `A categoria ${formData.name} foi atualizada.`,
        });
      } else {
        // Insert new category
        const { data: newCategory, error: categoryError } = await supabase
          .from("pedbook_icd10_categories")
          .insert([{
            ...formData,
            parent_id: parentId,
          }])
          .select()
          .single();

        if (categoryError) throw categoryError;

        // Insert codes if any
        if (codes.length > 0 && newCategory) {
          const { error: codesError } = await supabase
            .from("pedbook_icd10_codes")
            .insert(
              codes.map(code => ({
                ...code,
                category_id: newCategory.id,
              }))
            );

          if (codesError) throw codesError;
        }

        toast({
          title: "Categoria criada com sucesso!",
          description: `A categoria ${formData.name} foi adicionada.`,
        });
      }

      queryClient.invalidateQueries({ queryKey: ["icd10-categories"] });
      onClose();
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao salvar categoria",
        description: error.message,
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {category ? "Editar Categoria" : "Nova Categoria"}
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="code_range">Código CID-10</Label>
            <Input
              id="code_range"
              value={formData.code_range}
              onChange={(e) =>
                setFormData({ ...formData, code_range: e.target.value })
              }
              placeholder="Ex: A00-A09"
              required
            />
          </div>
          <div>
            <Label htmlFor="name">Nome</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              required
            />
          </div>
          <div>
            <Label htmlFor="description">Descrição (opcional)</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
            />
          </div>
          
          <div className="space-y-2">
            <Label>Códigos CID-10 (JSON)</Label>
            <ScrollArea className="h-[200px] w-full rounded-md border">
              <Textarea
                value={codesJson}
                onChange={(e) => setCodesJson(e.target.value)}
                placeholder='[{"codigocid": "B90-B94", "nome": "Sequelas de doenças infecciosas e parasitárias"}]'
                className="min-h-[200px] border-none"
              />
            </ScrollArea>
            <Button 
              type="button" 
              variant="secondary"
              onClick={handleJsonImport}
              className="w-full"
            >
              Importar Códigos
            </Button>
          </div>

          {codes.length > 0 && (
            <div className="space-y-2">
              <Label>Códigos Importados ({codes.length})</Label>
              <ScrollArea className="h-[200px] w-full rounded-md border p-4">
                <div className="space-y-2">
                  {codes.map((code, index) => (
                    <div 
                      key={index}
                      className="flex items-center justify-between p-2 rounded-lg bg-secondary/20"
                    >
                      <span className="font-medium">{code.code_range}</span>
                      <span className="text-sm text-muted-foreground">{code.name}</span>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}

          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button type="submit">Salvar</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}