import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { VaccineRelationship } from "./types";

export const useAvailableVaccines = (vaccineId: string | undefined, enabled: boolean) => {
  return useQuery({
    queryKey: ['available-vaccines', vaccineId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_vaccines')
        .select('id, name')
        .order('name');
      
      if (error) throw error;
      return data.filter(v => v.id !== vaccineId);
    },
    enabled,
  });
};

interface VaccineRelationshipResponse {
  child_vaccine_id: string;
  dose_number: number | null;
  dose_type: string | null;
}

export const useExistingRelationships = (vaccineId: string | undefined, enabled: boolean) => {
  return useQuery({
    queryKey: ['vaccine-relationships', vaccineId],
    queryFn: async () => {
      if (!vaccineId) return [];
      
      const { data, error } = await supabase
        .from('pedbook_vaccine_relationships')
        .select('child_vaccine_id, dose_number, dose_type')
        .eq('parent_vaccine_id', vaccineId)
        .returns<VaccineRelationshipResponse[]>();
      
      if (error) throw error;
      
      // Ensure the data matches the VaccineRelationship type
      return (data || []).map(rel => ({
        child_vaccine_id: rel.child_vaccine_id,
        dose_number: rel.dose_number,
        dose_type: rel.dose_type || 'dose'
      })) as VaccineRelationship[];
    },
    enabled: !!vaccineId && enabled,
  });
};