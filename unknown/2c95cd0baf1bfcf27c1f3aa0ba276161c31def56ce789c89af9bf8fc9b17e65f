
import { useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Plus, Info, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { DosageForm } from "@/components/admin/DosageForm";
import { DosageList } from "@/components/admin/DosageList";
import { UseCaseForm } from "@/components/admin/UseCaseForm";
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { supabase } from "@/integrations/supabase/client";
import { getThemeClasses } from "@/components/ui/theme-utils";

// Separate the UseCaseList into its own component file
import { UseCaseList } from "./medication/UseCaseList";

export default function Dosages() {
  const [showDosageForm, setShowDosageForm] = useState(false);
  const [showUseCaseForm, setShowUseCaseForm] = useState(false);
  const [editingDosage, setEditingDosage] = useState<any>(null);
  const [selectedMedicationId, setSelectedMedicationId] = useState<string>("");

  const { data: medications } = useQuery({
    queryKey: ["medications"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_medications")
        .select("id, name")
        .order("name");

      if (error) throw error;
      return data;
    },
  });

  return (
    <div className={getThemeClasses.pageBackground("container mx-auto py-8")}>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold dark:text-gray-100">Gerenciar Dosagens</h1>
        <div className="flex items-center gap-4">
          <div className="w-64">
            <Select
              value={selectedMedicationId}
              onValueChange={setSelectedMedicationId}
            >
              <SelectTrigger className={getThemeClasses.select()}>
                <SelectValue placeholder="Selecione um medicamento" />
              </SelectTrigger>
              <SelectContent>
                {medications?.map((medication) => (
                  <SelectItem key={medication.id} value={medication.id}>
                    {medication.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Button
            onClick={() => {
              if (!selectedMedicationId) return;
              setShowDosageForm(true);
            }}
            disabled={!selectedMedicationId}
          >
            <Plus className="h-4 w-4 mr-2" />
            Nova Dosagem
          </Button>
          <Button
            onClick={() => {
              if (!selectedMedicationId) return;
              setShowUseCaseForm(true);
            }}
            disabled={!selectedMedicationId}
            variant="secondary"
          >
            <Plus className="h-4 w-4 mr-2" />
            Nova Indicação
          </Button>
        </div>
      </div>

      {selectedMedicationId && (
        <Tabs defaultValue="dosages" className="space-y-4">
          <TabsList>
            <TabsTrigger value="dosages">Dosagens</TabsTrigger>
            <TabsTrigger value="usecases">Indicações de Uso</TabsTrigger>
          </TabsList>

          <TabsContent value="dosages">
            <DosageList
              selectedMedicationId={selectedMedicationId}
              onEdit={(dosage) => {
                setEditingDosage(dosage);
                setShowDosageForm(true);
              }}
            />
          </TabsContent>

          <TabsContent value="usecases">
            <UseCaseList medicationId={selectedMedicationId} />
          </TabsContent>
        </Tabs>
      )}

      <Dialog 
        open={showDosageForm} 
        onOpenChange={(open) => {
          if (!open) {
            setShowDosageForm(false);
            setEditingDosage(null);
          }
        }}
      >
        <DialogContent className="dialog-content-70 dark:bg-slate-800 dark:border-slate-700">
          <DialogHeader>
            <DialogTitle className="dark:text-gray-100">
              {editingDosage ? "Editar Dosagem" : "Nova Dosagem"}
            </DialogTitle>
            <DialogDescription className="dark:text-gray-400">
              {editingDosage
                ? "Edite as informações da dosagem"
                : "Preencha as informações da nova dosagem"}
            </DialogDescription>
          </DialogHeader>
          {selectedMedicationId && (
            <DosageForm
              medicationId={selectedMedicationId}
              dosage={editingDosage}
              onSuccess={() => {
                setShowDosageForm(false);
                setEditingDosage(null);
              }}
              onCancel={() => {
                setShowDosageForm(false);
                setEditingDosage(null);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      <Dialog 
        open={showUseCaseForm} 
        onOpenChange={setShowUseCaseForm}
      >
        <DialogContent className="dark:bg-slate-800 dark:border-slate-700">
          <DialogHeader>
            <DialogTitle className="dark:text-gray-100">Nova Indicação de Uso</DialogTitle>
            <DialogDescription className="dark:text-gray-400">
              Adicione uma nova indicação de uso para este medicamento
            </DialogDescription>
          </DialogHeader>
          {selectedMedicationId && (
            <UseCaseForm
              medicationId={selectedMedicationId}
              onSuccess={() => setShowUseCaseForm(false)}
              onCancel={() => setShowUseCaseForm(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
