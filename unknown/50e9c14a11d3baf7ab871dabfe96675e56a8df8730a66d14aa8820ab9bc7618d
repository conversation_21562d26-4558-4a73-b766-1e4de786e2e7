import React, { useState } from "react";
import { Folder, Plus, Star, Tag, Trash } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useFolders } from "@/hooks/useFolders";
import { useTags } from "@/hooks/useTags";

export const NotesSidebar: React.FC = () => {
  const [newFolderName, setNewFolderName] = useState("");
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const { folders, isLoading: foldersLoading, createFolder, deleteFolder } = useFolders();
  const { tags, isLoading: tagsLoading } = useTags();

  const handleCreateFolder = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newFolderName.trim()) return;

    await createFolder.mutateAsync(newFolderName);
    setNewFolderName("");
    setIsCreatingFolder(false);
  };

  return (
    <div className="space-y-6">
      <div>
        <Button variant="ghost" className="w-full justify-start gap-2">
          <Star className="h-4 w-4" />
          Favoritos
        </Button>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between px-3">
          <h2 className="text-sm font-semibold text-gray-500">Pastas</h2>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => setIsCreatingFolder(!isCreatingFolder)}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        
        {isCreatingFolder && (
          <form onSubmit={handleCreateFolder} className="px-3">
            <Input
              type="text"
              placeholder="Nome da pasta"
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              className="h-8 text-sm"
            />
          </form>
        )}

        {foldersLoading ? (
          <div className="px-3 text-sm text-gray-500">Carregando...</div>
        ) : (
          folders?.map((folder) => (
            <div key={folder.id} className="group flex items-center">
              <Button
                variant="ghost"
                className="flex-1 justify-start gap-2"
              >
                <Folder className="h-4 w-4" />
                {folder.name}
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => deleteFolder.mutate(folder.id)}
              >
                <Trash className="h-4 w-4 text-gray-500" />
              </Button>
            </div>
          ))
        )}
      </div>

      <div className="space-y-2">
        <h2 className="text-sm font-semibold text-gray-500 px-3">Tags</h2>
        {tagsLoading ? (
          <div className="px-3 text-sm text-gray-500">Carregando...</div>
        ) : (
          tags?.map((tag) => (
            <Button
              key={tag.id}
              variant="ghost"
              className="w-full justify-start gap-2"
            >
              <Tag className="h-4 w-4" />
              {tag.name}
            </Button>
          ))
        )}
      </div>
    </div>
  );
};