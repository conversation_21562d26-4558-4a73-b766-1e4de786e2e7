import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle, AlertTriangle, AlertOctagon } from 'lucide-react';
import { Stage } from '../types';

interface CutaneousQuestionProps {
  onAnswer: (stage: Stage) => void;
}

export const CutaneousQuestion: React.FC<CutaneousQuestionProps> = ({ onAnswer }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto p-4">
      <Card className="p-6 transition-all duration-300 hover:scale-105 border-2 bg-[#F2FCE2] border-[#E8F7D4]">
        <div className="flex items-center gap-3 mb-4">
          <AlertCircle className="h-6 w-6 text-emerald-600" />
          <h3 className="text-xl font-semibold text-gray-800">
            Quadro Leve
          </h3>
        </div>
        <p className="text-gray-600 mb-6 min-h-[60px]">
          Lesão incaracterística: sem comprometimento do estado geral e sem sinal de hemólise.
        </p>
        <Button
          variant="outline"
          onClick={() => onAnswer("cutaneous-mild")}
          className="w-full justify-between border-2 hover:bg-white/50 text-gray-700"
        >
          Ver tratamento
        </Button>
      </Card>

      <Card className="p-6 transition-all duration-300 hover:scale-105 border-2 bg-[#FEF7CD] border-[#FDF2B8]">
        <div className="flex items-center gap-3 mb-4">
          <AlertTriangle className="h-6 w-6 text-amber-600" />
          <h3 className="text-xl font-semibold text-gray-800">
            Quadro Moderado
          </h3>
        </div>
        <p className="text-gray-600 mb-6 min-h-[60px]">
          Lesão provável ou característica com placa marmórea {"<"} 3cm, com ou sem comprometimento do estado geral e sem sinal de hemólise.
        </p>
        <Button
          variant="outline"
          onClick={() => onAnswer("cutaneous-moderate")}
          className="w-full justify-between border-2 hover:bg-white/50 text-gray-700"
        >
          Ver tratamento
        </Button>
      </Card>

      <Card className="p-6 transition-all duration-300 hover:scale-105 border-2 bg-[#FFDEE2] border-[#FFD4D9]">
        <div className="flex items-center gap-3 mb-4">
          <AlertOctagon className="h-6 w-6 text-rose-600" />
          <h3 className="text-xl font-semibold text-gray-800">
            Quadro Grave
          </h3>
        </div>
        <p className="text-gray-600 mb-6 min-h-[60px]">
          Lesão característica com placa marmórea {">"}3cm, com ou sem comprometimento do estado geral e sem sinal de hemólise.
        </p>
        <Button
          variant="outline"
          onClick={() => onAnswer("cutaneous-severe")}
          className="w-full justify-between border-2 hover:bg-white/50 text-gray-700"
        >
          Ver tratamento
        </Button>
      </Card>
    </div>
  );
};