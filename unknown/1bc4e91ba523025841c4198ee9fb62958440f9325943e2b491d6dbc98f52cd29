
export const processHtmlContent = (content: string): string => {
  if (!content) return '';

  // console.log('🔧 [htmlProcessor] CONTEÚDO ORIGINAL RECEBIDO:', content);
  // console.log('🔧 [htmlProcessor] CONTEÚDO EM JSON:', JSON.stringify(content));
  // console.log('🔧 [htmlProcessor] ANÁLISE DE QUEBRAS:', {
  //   temQuebraLinha: content.includes('\n'),
  //   temQuebraLinhaWindows: content.includes('\r\n'),
  //   temBR: content.includes('<br'),
  //   temDiv: content.includes('<div'),
  //   temP: content.includes('<p'),
  //   length: content.length,
  //   bytes: new TextEncoder().encode(content).length
  // });

  // Detectamos todos os caracteres de quebra de linha
  const quebrasN = (content.match(/\n/g) || []).length;
  const quebrasRN = (content.match(/\r\n/g) || []).length;
  const quebrasBR = (content.match(/<br\s*\/?>/g) || []).length;



  // Normalizar as quebras de linha (importante!)
  let processedContent = content
    .replace(/\r\n/g, '\n')
    .replace(/\r/g, '\n');



  // Converter HTML tags em quebras de linha
  processedContent = processedContent
    .replace(/<div><br\/?><\/div>/g, '\n')
    .replace(/<br\/?>/g, '\n')
    .replace(/<div>/g, '')
    .replace(/<\/div>/g, '\n')
    .replace(/<p>/g, '')
    .replace(/<\/p>/g, '\n');

  // Remover outras tags HTML
  processedContent = processedContent.replace(/<[^>]+>/g, '');

  // Tratar padrões específicos de questões V/F
  processedContent = processedContent
    .replace(/\(\s*\)/g, '( )')
    .replace(/\(\s*V\s*\)/g, '(V)')
    .replace(/\(\s*F\s*\)/g, '(F)');

  // Preservar quebras de linha duplas e remover quebras extras
  processedContent = processedContent
    .replace(/\n{3,}/g, '\n\n')  // Mais de 2 quebras viram duplas
    .replace(/^\s+|\s+$/g, '');  // Remove espaços no início e fim



  // IMPORTANTE: Não convertemos \n para <br /> aqui, deixamos essa responsabilidade
  // para os componentes de exibição, que usarão dangerouslySetInnerHTML
  return processedContent;
};
