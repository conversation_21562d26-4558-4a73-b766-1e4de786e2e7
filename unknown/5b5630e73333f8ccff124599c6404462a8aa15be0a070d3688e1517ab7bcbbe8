
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

interface SearchResult {
  type: 'medication' | 'category';
  data: any;
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const { query } = await req.json()

    // Normalizar a query (remover acentos, converter para minúsculas)
    const normalizedQuery = query.toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")

    // Tentar identificar peso na query
    const weightMatch = normalizedQuery.match(/(\d+)\s*(k[g]|quilo|peso)/i)
    const weight = weightMatch ? parseInt(weightMatch[1]) : null

    // Verificar se é uma busca por categoria
    const { data: categories } = await supabase
      .from('pedbook_medication_categories')
      .select('id, name')

    const categoryMatch = categories?.find(cat => 
      normalizedQuery.includes(cat.name.toLowerCase()
        .normalize("NFD")
        .replace(/[\u0300-\u036f]/g, ""))
    )

    if (categoryMatch) {
      // Buscar medicamentos da categoria
      const { data: medications, error } = await supabase
        .from('pedbook_medications')
        .select(`
          id,
          name,
          description,
          brands,
          category:pedbook_medication_categories(
            id,
            name
          )
        `)
        .eq('category_id', categoryMatch.id)
        .limit(10)

      if (error) throw error

      return new Response(
        JSON.stringify({
          type: 'category',
          data: {
            category: categoryMatch,
            medications
          }
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Buscar medicamento pelo nome
    const { data: medications } = await supabase
      .from('pedbook_medications')
      .select(`
        id,
        name,
        description,
        contraindications,
        guidelines,
        pedbook_medication_dosages (
          id,
          name,
          type,
          summary,
          description,
          dosage_template,
          use_case:pedbook_medication_use_cases(
            name
          )
        )
      `)
      .ilike('name', `%${normalizedQuery.split(' ')[0]}%`)
      .limit(1)
      .single()

    if (medications) {
      // Se encontrou peso, calcular dosagens
      if (weight) {
        const dosages = medications.pedbook_medication_dosages.map((dosage: any) => {
          let calculatedDosage = dosage.dosage_template
          
          // Substituir variáveis no template
          calculatedDosage = calculatedDosage
            .replace('{weight}', weight.toString())
            .replace(/\{([^}]+)\}/g, (match, expr) => {
              try {
                return eval(expr)
              } catch {
                return match
              }
            })

          return {
            name: dosage.name,
            use_case: dosage.use_case?.name,
            calculated_dosage: calculatedDosage,
            summary: dosage.summary,
            description: dosage.description
          }
        })

        return new Response(
          JSON.stringify({
            type: 'medication',
            data: {
              id: medications.id,
              name: medications.name,
              description: medications.description,
              dosages,
              contraindications: medications.contraindications,
              guidelines: medications.guidelines
            }
          }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }

      // Se não encontrou peso, retornar apenas info do medicamento
      return new Response(
        JSON.stringify({
          type: 'medication',
          data: medications
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Nenhum resultado encontrado
    return new Response(
      JSON.stringify({ 
        type: 'none',
        message: 'Nenhum resultado encontrado' 
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error processing request:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
