import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface SeverityClassificationProps {
  onClassificationComplete: (severity: "mild" | "moderate" | "severe") => void;
}

export const SeverityClassification = ({ onClassificationComplete }: SeverityClassificationProps) => {
  const [showInfo, setShowInfo] = useState(false);

  return (
    <Card className="p-6 space-y-4">
      <h3 className="text-lg font-semibold">Classificação da Gravidade</h3>
      
      <Alert className="bg-blue-50 border-blue-200">
        <AlertDescription className="space-y-4">
          <div>
            <h4 className="font-medium text-blue-800">Leve:</h4>
            <p className="text-blue-700">pH {"<"} 7,3 OU bicarbonato {"<"} 18 mEq/L</p>
          </div>
          
          <div>
            <h4 className="font-medium text-blue-800">Moderada:</h4>
            <p className="text-blue-700">pH {"<"} 7,2 OU bicarbonato {"<"} 10 mEq/L</p>
          </div>
          
          <div>
            <h4 className="font-medium text-blue-800">Grave:</h4>
            <p className="text-blue-700">pH {"<"} 7,1 OU bicarbonato {"<"} 5 mEq/L</p>
          </div>
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 gap-2">
        <Button 
          variant="outline"
          onClick={() => onClassificationComplete("mild")}
          className="justify-start"
        >
          Leve
        </Button>
        <Button 
          variant="outline"
          onClick={() => onClassificationComplete("moderate")}
          className="justify-start"
        >
          Moderada
        </Button>
        <Button 
          variant="outline"
          onClick={() => onClassificationComplete("severe")}
          className="justify-start"
        >
          Grave
        </Button>
      </div>
    </Card>
  );
};