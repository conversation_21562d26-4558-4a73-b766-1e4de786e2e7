
import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>Tex<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { TemplateType } from "./AnamneseForm";

interface TemplateSelectorProps {
  onSelect: (templateType: TemplateType) => void;
}

export const TemplateSelector: React.FC<TemplateSelectorProps> = ({ onSelect }) => {
  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-primary">Selecione o Tipo de Anamnese</h2>
        <p className="text-gray-500 mt-2">
          Escolha o modelo de anamnese que deseja utilizar
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        <Card className="border-2 border-primary hover:shadow-lg transition-all cursor-pointer">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-primary" />
              Anamnese Tradicional
            </CardTitle>
            <CardDescription>
              Modelo completo com todas as seções organizadas para pediatria
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 list-disc list-inside text-sm">
              <li>Identificação completa do paciente</li>
              <li>História da doença atual detalhada</li>
              <li>Antecedentes pessoais e familiares</li>
              <li>Histórico gestacional e neonatal</li>
              <li>Desenvolvimento neuropsicomotor</li>
              <li>Exame físico completo por sistemas</li>
            </ul>
          </CardContent>
          <CardFooter>
            <Button onClick={() => onSelect("traditional")} className="w-full">
              Selecionar Modelo Tradicional
            </Button>
          </CardFooter>
        </Card>

        <Card className="border-2 border-primary hover:shadow-lg transition-all cursor-pointer">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Baby className="h-5 w-5 text-primary" />
              Puericultura 1-2 meses
            </CardTitle>
            <CardDescription>
              Modelo específico para consulta de 1-2 meses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 list-disc list-inside text-sm">
              <li>Dados cadastrais completos</li>
              <li>Questionário dirigido para a faixa etária</li>
              <li>Alimentação e amamentação</li>
              <li>Medicamentos e vacinação</li>
              <li>Desenvolvimento neuropsicomotor</li>
              <li>Exame físico específico para a idade</li>
            </ul>
          </CardContent>
          <CardFooter>
            <Button onClick={() => onSelect("pediatric_1_2m")} className="w-full">
              Selecionar Puericultura 1-2m
            </Button>
          </CardFooter>
        </Card>

        <Card className="border border-muted hover:shadow-lg transition-all cursor-pointer opacity-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ClipboardCheck className="h-5 w-5 text-primary" />
              Anamnese Simplificada
            </CardTitle>
            <CardDescription>
              Modelo mais conciso com campos essenciais (Em breve)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 list-disc list-inside text-sm">
              <li>Identificação básica do paciente</li>
              <li>Queixa principal e história breve</li>
              <li>Antecedentes relevantes</li>
              <li>Exame físico com achados principais</li>
              <li>Ideal para consultas de rotina</li>
              <li>Formato mais direto e objetivo</li>
            </ul>
          </CardContent>
          <CardFooter>
            <Button disabled variant="outline" className="w-full">
              Em Breve
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};
