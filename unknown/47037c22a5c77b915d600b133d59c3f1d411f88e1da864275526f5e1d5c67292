
import React, { useState } from 'react';
import { motion } from "framer-motion";
import { BookO<PERSON>, Clock, CheckCircle } from 'lucide-react';
import type { StudyTopic } from '@/types/study-schedule';
import { Badge } from '@/components/ui/badge';
import { TopicDetailsDialog } from './TopicDetailsDialog';

interface TopicCardProps extends StudyTopic {
  onMarkStudied?: (topicId: string) => void;
  onDelete?: (topic: StudyTopic) => void;
}

export const TopicCard: React.FC<TopicCardProps> = ({
  specialty,
  theme,
  focus,
  difficulty,
  activity,
  startTime,
  duration,
  study_status,
  id,
  next_revision_date,
  revision_number,
  onMarkStudied,
  onDelete,
  ...rest
}) => {
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Fácil': return 'bg-green-50 text-green-600 border-green-200';
      case 'Médio': return 'bg-yellow-50 text-yellow-600 border-yellow-200';
      case 'Difícil': return 'bg-red-50 text-red-600 border-red-200';
      default: return 'bg-gray-50 text-gray-600 border-gray-200';
    }
  };

  const formatTime = (time: string) => {
    if (!time) return 'Horário não definido';
    
    // If time already includes AM/PM or is in 24h format like "14:30", return it as is
    if (time.includes('AM') || time.includes('PM') || time.includes(':')) {
      return time;
    }
    
    // Try to parse time string to a standard format
    try {
      const timeDate = new Date(`2000-01-01T${time}`);
      return timeDate.toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    } catch (error) {
      console.log("Error formatting time:", error);
      return time;
    }
  };

  const handleCardClick = () => {
    console.log('Opening details dialog for topic:', focus);
    setDetailsDialogOpen(true);
  };

  const topic: StudyTopic = {
    id,
    specialty,
    theme,
    focus,
    difficulty,
    activity,
    startTime,
    duration,
    study_status,
    next_revision_date,
    revision_number,
    ...rest
  };

  return (
    <>
      <motion.div 
        whileHover={{ scale: 1.01 }}
        className={`p-3 rounded-lg border-2 ${
          study_status === 'completed' 
            ? 'bg-green-50 border-green-200' 
            : 'bg-white border-gray-200'
        } shadow-sm hover:shadow-md transition-all cursor-pointer`}
        onClick={handleCardClick}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <BookOpen className="h-4 w-4 text-primary flex-shrink-0" />
              <h3 className="font-medium text-gray-800 break-words line-clamp-2 pr-1">
                {focus}
              </h3>
              {study_status === 'completed' && (
                <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
              )}
            </div>
            
            <div className="mt-1 text-xs text-gray-500 line-clamp-1">
              {specialty} &gt; {theme}
            </div>
          </div>
        </div>
        
        <div className="mt-2 flex flex-wrap gap-2">
          <Badge variant="outline" className={`text-xs ${getDifficultyColor(difficulty)}`}>
            {difficulty}
          </Badge>
          
          <Badge variant="outline" className="text-xs bg-blue-50 text-blue-600 border-blue-200">
            <Clock className="h-3 w-3 mr-1 flex-shrink-0" />
            {formatTime(startTime)}
          </Badge>
          
          <Badge variant="outline" className="text-xs">
            {duration}
          </Badge>
        </div>
        
        <div className="mt-2 text-xs text-gray-600 break-words line-clamp-2">
          {activity}
        </div>
      </motion.div>
      
      <TopicDetailsDialog 
        open={detailsDialogOpen}
        onOpenChange={setDetailsDialogOpen}
        topic={topic}
        onMarkStudied={onMarkStudied}
        onDelete={onDelete}
      />
    </>
  );
};
