import HelmetWrapper from "@/components/utils/HelmetWrapper";

export const ApgarMetaTags = () => {
  const pageTitle = "PedBook | Escore de APGAR - Calculadora de Vitalidade Neonatal";
  const description = "Calculadora do Escore de APGAR para avaliação da vitalidade de recém-nascidos. Ferramenta essencial para pediatras e neonatologistas na avaliação dos cinco sinais vitais no primeiro e quinto minuto de vida.";
  const pageUrl = "https://pedb.com.br/calculadoras/apgar";
  const keywords = [
    "escore de apgar",
    "calculadora apgar",
    "vitalidade neonatal",
    "avaliação recém-nascido",
    "pediatria calculadora",
    "neonatologia",
    "sinais vitais recém-nascido",
    "frequência cardíaca",
    "respiração",
    "tônus muscular",
    "irritabilidade reflexa",
    "cor da pele"
  ].join(", ");

  return (
    <HelmetWrapper>
      {/* Título e Descrição */}
      <title>{pageTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />

      {/* Configurações Gerais */}
      <meta name="robots" content="index, follow, max-image-preview:large" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta charSet="UTF-8" />
      <link rel="canonical" href={pageUrl} />

      {/* Open Graph para Redes Sociais */}
      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content="article" />
      <meta property="og:url" content={pageUrl} />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />
      <meta property="og:image" content="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/calculadora.webp" />
      <meta property="og:image:alt" content="Interface interativa da calculadora do Escore de APGAR mostrando os cinco parâmetros de avaliação: frequência cardíaca, respiração, tônus muscular, irritabilidade reflexa e cor da pele" />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />

      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/calculadora.webp" />
      <meta name="twitter:site" content="@PedBook" />

      {/* JSON-LD Schema.org */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "MedicalWebPage",
          "name": pageTitle,
          "description": description,
          "url": pageUrl,
          "keywords": keywords.split(", "),
          "inLanguage": "pt-BR",
          "mainEntity": {
            "@type": "MedicalCalculator",
            "name": "Calculadora do Escore de APGAR",
            "description": description,
            "medicalSpecialty": {
              "@type": "MedicalSpecialty",
              "name": "Pediatria"
            },
            "relevantSpecialty": [
              {
                "@type": "MedicalSpecialty",
                "name": "Neonatologia"
              },
              {
                "@type": "MedicalSpecialty",
                "name": "Pediatria"
              }
            ]
          },
          "about": {
            "@type": "MedicalCondition",
            "name": "Avaliação da Vitalidade Neonatal",
            "description": "Avaliação padronizada da vitalidade de recém-nascidos através do Escore de APGAR"
          },
          "audience": {
            "@type": "MedicalAudience",
            "audienceType": "Profissionais de Saúde"
          },
          "publisher": {
            "@type": "Organization",
            "name": "PedBook",
            "url": "https://pedb.com.br",
            "logo": {
              "@type": "ImageObject",
              "url": "https://pedb.com.br/logo.png"
            }
          }
        })}
      </script>
    </HelmetWrapper>
  );
};