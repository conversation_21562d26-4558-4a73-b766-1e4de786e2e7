
import { QuestionTimerProps } from "./types";
import { formatTime } from "@/utils/formatTime";

export const QuestionTimer = ({ elapsedTime, onTimeUpdate, isActive = true }: QuestionTimerProps) => {
  // Timer agora é apenas display - o tempo é gerenciado pelo useSessionTimer

  return (
    <div className="text-lg font-mono bg-gray-50 px-4 py-2 rounded-lg border border-gray-200">
      <div className="flex items-center gap-2">
        <span className={`w-2 h-2 rounded-full ${isActive ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></span>
        {formatTime(elapsedTime)}
      </div>
    </div>
  );
};
