import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface DosageCardProps {
  name: string;
  medicationName: string;
  summary?: string | null;
  description?: string | null;
  dosageTemplate: string;
  multiplier?: number | null;
  maxValue?: number | null;
  type: string;
}

export function DosageCard({
  name,
  medicationName,
  summary,
  description,
  dosageTemplate,
  multiplier,
  maxValue,
  type,
}: DosageCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">{name}</CardTitle>
        <p className="text-sm text-primary font-medium">{medicationName}</p>
      </CardHeader>
      <CardContent className="space-y-2">
        {summary && (
          <p className="text-sm text-gray-600">{summary}</p>
        )}
        {description && (
          <div className="text-sm text-gray-600 whitespace-pre-line">
            {description}
          </div>
        )}
        <div className="flex flex-wrap gap-2 mt-4">
          <Badge variant="outline">Template: {dosageTemplate}</Badge>
          <Badge variant="outline">Tag: {type}</Badge>
          {multiplier && (
            <Badge variant="outline">Multiplicador: {multiplier}</Badge>
          )}
          {maxValue && (
            <Badge variant="outline">Valor Máximo: {maxValue}</Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
}