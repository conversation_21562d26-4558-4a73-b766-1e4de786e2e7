import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";

interface BasicInfoFieldsProps {
  formData: {
    name: string;
    categoryId: string;
    description: string;
    brands: string;
    required_measures: string[];
  };
  categories: Array<{ id: string; name: string }>;
  onChange: (field: string, value: any) => void;
}

export function BasicInfoFields({ formData, categories, onChange }: BasicInfoFieldsProps) {
  const handleMeasureChange = (measure: string, checked: boolean) => {
    const currentMeasures = new Set(formData.required_measures);
    if (checked) {
      currentMeasures.add(measure);
    } else {
      currentMeasures.delete(measure);
    }
    onChange("required_measures", Array.from(currentMeasures));
  };

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="name">Nome do Medicamento</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => onChange("name", e.target.value)}
          required
        />
      </div>

      <div>
        <Label htmlFor="category">Categoria</Label>
        <Select
          value={formData.categoryId}
          onValueChange={(value) => onChange("categoryId", value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Selecione uma categoria" />
          </SelectTrigger>
          <SelectContent>
            {categories?.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="description">Descrição</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => onChange("description", e.target.value)}
        />
      </div>

      <div>
        <Label htmlFor="brands">Marcas Comerciais</Label>
        <Input
          id="brands"
          value={formData.brands}
          onChange={(e) => onChange("brands", e.target.value)}
        />
      </div>

      <div className="space-y-2">
        <Label>Medidas Requeridas</Label>
        <div className="flex gap-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="weight"
              checked={formData.required_measures.includes('weight')}
              onCheckedChange={(checked) => handleMeasureChange('weight', checked as boolean)}
            />
            <Label htmlFor="weight" className="cursor-pointer">Peso</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="age"
              checked={formData.required_measures.includes('age')}
              onCheckedChange={(checked) => handleMeasureChange('age', checked as boolean)}
            />
            <Label htmlFor="age" className="cursor-pointer">Idade</Label>
          </div>
        </div>
      </div>
    </div>
  );
}