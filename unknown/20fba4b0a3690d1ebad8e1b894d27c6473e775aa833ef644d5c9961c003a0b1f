
import React from "react";
import { Card } from "@/components/ui/card";
import { Folder, PencilLine, Trash2 } from "lucide-react";

interface FolderCardProps {
  id: string;
  name: string;
  onSelect: (folderId: string) => void;
  onEdit: (folderId: string, currentName: string) => void;
  onDelete: (folderId: string) => void;
}

export const FolderCard: React.FC<FolderCardProps> = ({
  id,
  name,
  onSelect,
  onEdit,
  onDelete,
}) => {
  console.log('🎨 Rendering FolderCard with dark mode improvements', { folderName: name });

  return (
    <Card
      className="p-6 flex flex-col items-center gap-4 hover:shadow-lg transition-all relative cursor-pointer bg-white dark:bg-slate-800 border-gray-200 dark:border-gray-700"
      onClick={() => onSelect(id)}
    >
      <div className="absolute top-2 right-2 flex gap-2 z-10">
        <button
          type="button"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onEdit(id, name);
          }}
          className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
        >
          <PencilLine className="h-4 w-4" />
        </button>
        <button
          type="button"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onDelete(id);
          }}
          className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
        >
          <Trash2 className="h-4 w-4" />
        </button>
      </div>
      <Folder className="h-8 w-8 text-primary dark:text-blue-400" />
      <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{name}</span>
    </Card>
  );
};
