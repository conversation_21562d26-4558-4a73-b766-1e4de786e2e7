/**
 * Script avançado de SSR com Puppeteer
 * Gera HTML com conteúdo real renderizado para SEO
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { generateRoutes, generateSitemap } from './generateRoutes.js';

console.log('🚀 Iniciando build avançado com SSR real...');

async function buildWithAdvancedSSR() {
  try {
    // Passo 1: Gerar rotas dinamicamente
    console.log('\n📋 Passo 1: Gerando rotas...');
    const routes = await generateRoutes();
    
    // Passo 2: Gerar sitemap
    console.log('\n🗺️ Passo 2: Gerando sitemap...');
    await generateSitemap(routes);
    
    // Passo 3: Build normal do Vite
    console.log('\n🔨 Passo 3: Executando build do Vite...');
    execSync('npm run build', { stdio: 'inherit' });
    
    // Passo 4: Iniciar servidor local para SSR
    console.log('\n🌐 Passo 4: Iniciando servidor local...');
    const serverProcess = await startLocalServer();
    
    // Aguardar servidor inicializar
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Passo 5: SSR com Puppeteer
    console.log('\n🎭 Passo 5: Renderizando páginas com conteúdo real...');
    await renderPagesWithContent(routes);
    
    // Passo 6: Parar servidor
    console.log('\n🛑 Passo 6: Parando servidor local...');
    serverProcess.kill();
    
    console.log('\n🎉 Build avançado concluído!');
    
  } catch (error) {
    console.error('\n❌ Erro no build avançado:', error);
    process.exit(1);
  }
}

/**
 * Inicia servidor local para renderização
 */
async function startLocalServer() {
  const { spawn } = await import('child_process');
  
  console.log('🌐 Iniciando servidor na porta 8080...');
  
  const serverProcess = spawn('npx', ['vite', 'preview', '--port', '8080'], {
    stdio: 'pipe',
    detached: false
  });
  
  return serverProcess;
}

/**
 * Renderiza páginas com conteúdo real usando Puppeteer
 */
async function renderPagesWithContent(routes) {
  const puppeteer = await import('puppeteer');
  
  console.log('🎭 Iniciando renderização com conteúdo real...');
  
  const browser = await puppeteer.default.launch({
    headless: true,
    args: [
      '--no-sandbox', 
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu'
    ]
  });
  
  const distPath = path.join(process.cwd(), 'dist');
  
  // Carregar dados das rotas
  const routesData = JSON.parse(
    fs.readFileSync(path.join(process.cwd(), 'prerender-routes.json'), 'utf8')
  );
  
  // Páginas prioritárias para renderização completa
  const priorityPages = [
    ...routesData.medications.slice(0, 10).map(med => ({
      url: `/medicamentos/${med.slug}`,
      type: 'medication',
      data: med
    })),
    ...routesData.calculators.slice(0, 5).map(calc => ({
      url: `/calculadoras/${calc.slug}`,
      type: 'calculator', 
      data: calc
    }))
  ];
  
  console.log(`🎯 Renderizando ${priorityPages.length} páginas prioritárias...`);
  
  for (const pageInfo of priorityPages) {
    try {
      const page = await browser.newPage();
      
      // Configurar página para melhor renderização
      await page.setViewport({ width: 1200, height: 800 });
      await page.setUserAgent('Mozilla/5.0 (compatible; PedBookBot/1.0; +https://pedb.com.br)');
      
      // Interceptar requests para acelerar
      await page.setRequestInterception(true);
      page.on('request', (req) => {
        if(req.resourceType() == 'stylesheet' || req.resourceType() == 'font'){
          req.abort();
        } else {
          req.continue();
        }
      });
      
      // Navegar para a página
      const url = `http://localhost:8080${pageInfo.url}`;
      console.log(`📄 Renderizando: ${pageInfo.url}`);
      
      await page.goto(url, { 
        waitUntil: 'networkidle0',
        timeout: 30000 
      });
      
      // Aguardar renderização específica baseada no tipo
      if (pageInfo.type === 'medication') {
        // Aguardar elementos específicos de medicamentos
        try {
          await page.waitForSelector('[data-testid="medication-info"]', { timeout: 10000 });
        } catch {
          await page.waitForTimeout(5000);
        }
      } else if (pageInfo.type === 'calculator') {
        // Aguardar elementos específicos de calculadoras
        try {
          await page.waitForSelector('[data-testid="calculator-form"]', { timeout: 10000 });
        } catch {
          await page.waitForTimeout(5000);
        }
      }
      
      // Aguardar renderização adicional
      await page.waitForTimeout(3000);
      
      // Obter HTML renderizado
      const html = await page.content();
      
      // Extrair informações do conteúdo renderizado
      const pageData = await extractPageData(page, pageInfo);
      
      // Processar e otimizar HTML
      const optimizedHtml = await optimizeHtmlForSEO(html, pageInfo, pageData);
      
      // Salvar HTML otimizado
      const filePath = path.join(distPath, pageInfo.url.slice(1), 'index.html');
      const dir = path.dirname(filePath);
      
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      
      fs.writeFileSync(filePath, optimizedHtml);
      console.log(`✅ Renderizado: ${pageInfo.url}`);
      
      await page.close();
      
    } catch (error) {
      console.warn(`⚠️ Erro ao renderizar ${pageInfo.url}:`, error.message);
    }
  }
  
  await browser.close();
  console.log('🎉 Renderização com conteúdo real concluída!');
}

/**
 * Extrai dados da página renderizada
 */
async function extractPageData(page, pageInfo) {
  try {
    const data = await page.evaluate(() => {
      // Extrair título da página
      const title = document.title || '';
      
      // Extrair descrição do conteúdo
      const contentElements = document.querySelectorAll('p, div[class*="description"], div[class*="content"]');
      let description = '';
      
      for (let elem of contentElements) {
        const text = elem.textContent?.trim();
        if (text && text.length > 50 && text.length < 300) {
          description = text;
          break;
        }
      }
      
      // Extrair informações específicas de medicamentos
      let medicationInfo = {};
      if (window.location.pathname.includes('/medicamentos/')) {
        const doseElements = document.querySelectorAll('[class*="dose"], [class*="posologia"]');
        const indicationElements = document.querySelectorAll('[class*="indication"], [class*="indicacao"]');
        
        medicationInfo = {
          dose: Array.from(doseElements).map(el => el.textContent?.trim()).filter(Boolean)[0] || '',
          indications: Array.from(indicationElements).map(el => el.textContent?.trim()).filter(Boolean)
        };
      }
      
      return {
        title,
        description: description || 'Informações detalhadas sobre pediatria e cálculos médicos.',
        medicationInfo,
        contentLength: document.body.textContent?.length || 0
      };
    });
    
    return data;
  } catch (error) {
    console.warn('⚠️ Erro ao extrair dados da página:', error.message);
    return {
      title: pageInfo.data.name || 'PedBook',
      description: pageInfo.data.description || 'Informações pediátricas detalhadas.',
      medicationInfo: {},
      contentLength: 0
    };
  }
}

/**
 * Otimiza HTML para SEO
 */
async function optimizeHtmlForSEO(html, pageInfo, pageData) {
  let optimizedHtml = html;
  
  // Gerar meta tags otimizadas baseadas no conteúdo real
  const optimizedTitle = generateOptimizedTitle(pageInfo, pageData);
  const optimizedDescription = generateOptimizedDescription(pageInfo, pageData);
  const canonicalUrl = `https://pedb.com.br${pageInfo.url}`;
  
  // Substituir title
  optimizedHtml = optimizedHtml.replace(
    /<title>.*?<\/title>/,
    `<title>${optimizedTitle}</title>`
  );
  
  // Substituir description
  optimizedHtml = optimizedHtml.replace(
    /<meta name="description" content=".*?">/,
    `<meta name="description" content="${optimizedDescription}">`
  );
  
  // Corrigir canonical URL
  optimizedHtml = optimizedHtml.replace(
    /<link rel="canonical" href=".*?">/,
    `<link rel="canonical" href="${canonicalUrl}">`
  );
  
  // Atualizar Open Graph
  optimizedHtml = optimizedHtml.replace(
    /<meta property="og:title" content=".*?">/,
    `<meta property="og:title" content="${optimizedTitle}">`
  );
  
  optimizedHtml = optimizedHtml.replace(
    /<meta property="og:description" content=".*?">/,
    `<meta property="og:description" content="${optimizedDescription}">`
  );
  
  optimizedHtml = optimizedHtml.replace(
    /<meta property="og:url" content=".*?">/,
    `<meta property="og:url" content="${canonicalUrl}">`
  );
  
  // Atualizar Twitter Cards
  optimizedHtml = optimizedHtml.replace(
    /<meta name="twitter:title" content=".*?">/,
    `<meta name="twitter:title" content="${optimizedTitle}">`
  );
  
  optimizedHtml = optimizedHtml.replace(
    /<meta name="twitter:description" content=".*?">/,
    `<meta name="twitter:description" content="${optimizedDescription}">`
  );
  
  optimizedHtml = optimizedHtml.replace(
    /<meta name="twitter:url" content=".*?">/,
    `<meta name="twitter:url" content="${canonicalUrl}">`
  );
  
  return optimizedHtml;
}

/**
 * Gera título otimizado baseado no conteúdo real
 */
function generateOptimizedTitle(pageInfo, pageData) {
  if (pageInfo.type === 'medication') {
    const medName = pageInfo.data.name;
    return `${medName} - Dose Pediátrica e Posologia | PedBook`;
  } else if (pageInfo.type === 'calculator') {
    const calcName = pageInfo.data.name;
    return `${calcName} Pediátrica - Calculadora Online | PedBook`;
  }
  
  return pageData.title || 'PedBook - Calculadora Pediátrica';
}

/**
 * Gera descrição otimizada baseada no conteúdo real
 */
function generateOptimizedDescription(pageInfo, pageData) {
  if (pageInfo.type === 'medication') {
    const medName = pageInfo.data.name;
    let description = `Dose pediátrica do ${medName}: calculadora automática, posologia, indicações e contraindicações.`;
    
    if (pageData.medicationInfo.dose) {
      description += ` Dose: ${pageData.medicationInfo.dose.substring(0, 100)}`;
    }
    
    return description.substring(0, 160);
  } else if (pageInfo.type === 'calculator') {
    const calcName = pageInfo.data.name;
    return `${calcName} pediátrica online. Ferramenta gratuita para profissionais de saúde com cálculos precisos e confiáveis.`.substring(0, 160);
  }
  
  return pageData.description.substring(0, 160);
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  buildWithAdvancedSSR();
}

export { buildWithAdvancedSSR };
