import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface GlucoseMonitoringProps {
  weight: number;
  onTargetReached: (reached: boolean) => void;
  showPotassiumInfo?: boolean;
  insulinType: "regular" | "ultrafast" | null;
}

export const GlucoseMonitoring = ({ 
  weight, 
  onTargetReached, 
  showPotassiumInfo = false,
  insulinType
}: GlucoseMonitoringProps) => {
  const [showMessage, setShowMessage] = useState(false);

  const handleResponse = (reached: boolean) => {
    if (!reached) {
      setShowMessage(true);
    } else {
      onTargetReached(true);
    }
  };

  const getInsulinDose = () => {
    if (insulinType === "regular") {
      return `${(0.1 * weight).toFixed(2)} unidades/hora EV em bomba de infusão`;
    }
    return `${(0.15 * weight).toFixed(2)} unidades SC a cada 2 horas`;
  };

  return (
    <Card className="p-6 space-y-4">
      <h3 className="text-lg font-semibold">Monitoramento</h3>
      <div className="space-y-4">
        {showPotassiumInfo && (
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h4 className="font-medium text-blue-800">Incluir na hidratação de manutenção:</h4>
            <p className="text-blue-700">Reposição de potássio: 40 mEq/L EV</p>
          </div>
        )}
        
        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
          <h4 className="font-medium text-green-800">Insulinoterapia:</h4>
          <p className="text-green-700">{getInsulinDose()}</p>
          <p className="text-green-700 mt-2">Objetivo: Redução de 50-100 mg/dL por hora</p>
        </div>
        
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <h4 className="font-medium text-blue-800">Monitoramento:</h4>
          <ul className="list-disc pl-4 text-blue-700">
            <li>Reavaliar glicemia e clínica a cada hora</li>
            <li>Exames laboratoriais a cada 2-4 horas</li>
            <li>Cetonúria a cada micção</li>
          </ul>
        </div>
      </div>
      
      <p>Glicemia atingiu a faixa de 250-300 mg/dL?</p>
      <div className="flex gap-4">
        <Button 
          variant="outline" 
          onClick={() => handleResponse(true)}
          className="flex-1"
        >
          Sim
        </Button>
        <Button 
          variant="outline" 
          onClick={() => handleResponse(false)}
          className="flex-1"
        >
          Não
        </Button>
      </div>

      {showMessage && (
        <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200 mt-4">
          <p className="text-yellow-800 font-medium">
            Mantenha a insulinoterapia e continue monitorando.
          </p>
        </div>
      )}
    </Card>
  );
};