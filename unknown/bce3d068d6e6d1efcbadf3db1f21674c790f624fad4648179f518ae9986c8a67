import { Star } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Note } from "@/types/notes";

interface NoteCardProps {
  note: Note;
  onNoteClick: (note: Note) => void;
  onToggleFavorite: (note: Note) => void;
}

export function NoteCard({ note, onNoteClick, onToggleFavorite }: NoteCardProps) {
  return (
    <Card 
      className="bg-white p-3 flex flex-col h-[140px] transition-all duration-300 hover:shadow-lg hover:-translate-y-1 cursor-pointer relative"
      onClick={() => onNoteClick(note)}
    >
      <div className="flex items-start justify-between gap-2 mb-2">
        <div className="flex items-start gap-2 flex-1 min-w-0">
          <span className="text-xl flex-shrink-0">📝</span>
          <h3 className="font-medium text-gray-900 text-sm break-words line-clamp-2 overflow-hidden">
            {note.title || "Sem título"}
          </h3>
        </div>
        
        <button
          className="text-gray-400 hover:text-yellow-400 transition-colors flex-shrink-0"
          onClick={(e) => {
            e.stopPropagation();
            onToggleFavorite(note);
          }}
        >
          <Star 
            className={`h-4 w-4 ${note.is_favorite ? "text-yellow-400 fill-current" : ""}`} 
          />
        </button>
      </div>

      {note.tags && note.tags.length > 0 && (
        <div className="mt-auto flex flex-wrap gap-1">
          {note.tags.map((tag: string) => (
            <Badge 
              key={tag} 
              variant="secondary" 
              className="bg-primary-light text-primary text-[10px] px-2 py-0"
            >
              {tag}
            </Badge>
          ))}
        </div>
      )}
    </Card>
  );
}