
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { ICDCategoryCard } from "./ICDCategoryCard";
import { Skeleton } from "@/components/ui/skeleton";

interface ICDCategory {
  id: string;
  code_range: string;
  name: string;
  description: string | null;
}

interface ICDCategoryListProps {
  searchTerm: string;
  currentParentId: string | null;
  onCategoryClick: (category: ICDCategory) => void;
}

export const ICDCategoryList = ({ 
  searchTerm, 
  currentParentId, 
  onCategoryClick 
}: ICDCategoryListProps) => {
  const { data: categories, isLoading } = useQuery({
    queryKey: ["icd-categories", currentParentId, searchTerm],
    queryFn: async () => {
     
      if (currentParentId) {
        // First, try to find in chapters (capítulos)
        const { data: chapters } = await supabase
          .from('temp_capitulos')
          .select('*')
          .eq('uuid', currentParentId);
        
        if (chapters && chapters.length > 0) {
     
          // If found chapter, get groups from this chapter
          const { data: groups } = await supabase
            .from('cid_grupo')
            .select('*')
            .eq('capitulo_id', chapters[0].id)
            .order('id');
          
          return groups?.map(group => ({
            id: group.uuid,
            code_range: `${group.cat_inicio}-${group.cat_fim}`,
            name: group.descricao,
            description: null
          })) || [];
        }

        // If not a chapter, try to find in groups
        const { data: groups } = await supabase
          .from('cid_grupo')
          .select('*')
          .eq('uuid', currentParentId);

        if (groups && groups.length > 0) {
          // Se encontrou um grupo, buscar categorias dentro do intervalo de códigos
          const { data: categories } = await supabase
            .from('cid_categoria')
            .select('*')
            .gte('id', groups[0].cat_inicio)
            .lte('id', groups[0].cat_fim)
            .order('id');
          
          return categories?.map(category => ({
            id: category.uuid,
            code_range: category.id,
            name: category.descricao,
            description: null
          })) || [];
        }

        // If not a group, try to find in categories
        const { data: categories } = await supabase
          .from('cid_categoria')
          .select('*')
          .eq('uuid', currentParentId);

        if (categories && categories.length > 0) {
          // If found category, get its subcategories
          const { data: subcategories } = await supabase
            .from('cid_sub_categoria')
            .select('*')
            .ilike('id', `${categories[0].id}.%`)
            .order('id');
          
          return subcategories?.map(sub => ({
            id: sub.uuid,
            code_range: sub.id,
            name: sub.descricao,
            description: null
          })) || [];
        }

        return [];
      }
      
      // Initial load - get chapters
      let query = supabase
        .from("temp_capitulos")
        .select("*")
        .order("id");

      if (searchTerm) {
        query = query.or(`descricao.ilike.%${searchTerm}%,cat_inicio.ilike.%${searchTerm}%,cat_fim.ilike.%${searchTerm}%`);
      }

      const { data } = await query;
      
      return (data || []).map(item => ({
        id: item.uuid,
        code_range: `${item.cat_inicio}-${item.cat_fim}`,
        name: item.descricao,
        description: null
      }));
    },
  });

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Skeleton key={i} className="h-32 rounded-xl dark:bg-slate-700/50" />
        ))}
      </div>
    );
  }

  if (!categories?.length) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground dark:text-gray-400">
          Nenhuma categoria encontrada.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {categories.map((category) => (
        <ICDCategoryCard
          key={category.id}
          category={category}
          onClick={() => onCategoryClick(category)}
        />
      ))}
    </div>
  );
};
