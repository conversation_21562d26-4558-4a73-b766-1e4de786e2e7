import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Loader2 } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useStudySession } from "@/hooks/useStudySession";
import { ensureUserId } from "@/utils/ensureUserId";
import type { SelectedFilters } from "@/types/question";
import { useDomain } from "@/hooks/useDomain";

interface RandomQuestionsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  filteredQuestions: any[];
  totalQuestionCount: number;
  filters: SelectedFilters;
  domain?: string;
}

const RandomQuestionsDialog = ({
  open,
  onOpenChange,
  filteredQuestions,
  totalQuestionCount,
  filters
}: RandomQuestionsDialogProps) => {
  const { domain } = useDomain();
  const { toast } = useToast();
  const navigate = useNavigate();
  const { createSession } = useStudySession();
  const [isLoading, setIsLoading] = useState(false);
  const [questionCount, setQuestionCount] = useState(10);
  const [sessionTitle, setSessionTitle] = useState("Questões Aleatórias de Pediatria");

  // Maximum number of questions allowed - use totalQuestionCount which reflects the actual filtered count
  const MAX_QUESTIONS = totalQuestionCount > 0
    ? Math.min(50, totalQuestionCount)
    : 50;

  // Adjust questionCount if it exceeds the new maximum
  useEffect(() => {
    if (questionCount > MAX_QUESTIONS) {
      setQuestionCount(Math.min(questionCount, MAX_QUESTIONS));
    }
  }, [MAX_QUESTIONS, questionCount]);

  const handleCreateRandomSession = async () => {
    try {
      setIsLoading(true);

      // If we have filtered questions, use them
      if (filteredQuestions.length > 0) {
        // Randomly select questions
        const shuffled = [...filteredQuestions].sort(() => 0.5 - Math.random());
        const selected = shuffled.slice(0, questionCount);
        const questionIds = selected.map(q => q.id);

        const userId = await ensureUserId();
        const session = await createSession(userId, questionIds, sessionTitle);

        if (!session?.id) {
          throw new Error("Falha ao criar sessão");
        }

        navigate(`/questions/${session.id}`);
      }
      // Otherwise, fetch random questions from the database (pediatric only)
      else {
        // Call the stored procedure to get random pediatric questions
        const { data, error } = await supabase.rpc('get_random_pediatric_questions', {
          p_quantity: questionCount
        });

        if (error) {
          throw error;
        }

        if (!data || data.length === 0) {
          throw new Error("Nenhuma questão encontrada");
        }

        // Extract question IDs from the result
        const questionIds = data.map((q: any) => q.id);

        // Create a study session with these questions
        const userId = await ensureUserId();
        const session = await createSession(userId, questionIds, sessionTitle);

        if (!session?.id) {
          throw new Error("Falha ao criar sessão");
        }

        navigate(`/questions/${session.id}`);
      }
    } catch (error: any) {
      toast({
        title: "Erro ao criar sessão aleatória",
        description: error.message || "Ocorreu um erro ao criar a sessão de estudos",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95vw] max-w-[425px] max-h-[85dvh] rounded-2xl sm:rounded-xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>🎲 Questões Aleatórias de Pediatria</DialogTitle>
          <DialogDescription>
            {filteredQuestions.length > 0
              ? `Criar uma sessão com questões aleatórias dos ${totalQuestionCount} filtrados.`
              : "Criar uma sessão com questões aleatórias de pediatria."}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-6 py-4">
          <div className="space-y-2">
            <Label htmlFor="title" className="text-sm font-medium">
              Título da Sessão
            </Label>
            <Input
              id="title"
              value={sessionTitle}
              onChange={(e) => setSessionTitle(e.target.value)}
              className="w-full"
              placeholder="Digite o título da sessão"
            />
          </div>
          <div className="space-y-3">
            <Label htmlFor="count" className="text-sm font-medium">
              Quantidade de Questões
            </Label>
            <div className="space-y-2">
              <Slider
                id="count"
                value={[questionCount]}
                min={1}
                max={MAX_QUESTIONS}
                step={1}
                onValueChange={(value) => setQuestionCount(value[0])}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>1</span>
                <span className="font-medium text-base text-gray-900">{questionCount} questões</span>
                <span>{MAX_QUESTIONS}</span>
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row justify-end gap-3 pt-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="w-full sm:w-auto order-2 sm:order-1"
          >
            Cancelar
          </Button>
          <Button
            onClick={handleCreateRandomSession}
            disabled={isLoading}
            className="w-full sm:w-auto order-1 sm:order-2 bg-[#FF6B00] hover:bg-[#FF6B00]/90"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Criando...
              </>
            ) : (
              "Criar Sessão"
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default RandomQuestionsDialog;
