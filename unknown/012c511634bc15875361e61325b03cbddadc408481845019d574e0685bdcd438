
import { supabase } from "@/integrations/supabase/client";

export async function getOrCreateYear(year: number) {
  // console.log('📅 Verificando ano:', year);
  
  try {
    const { data: existing, error: searchError } = await supabase
      .from('exam_years')
      .select('*')
      .eq('year', year)
      .single();

    if (searchError && searchError.code !== 'PGRST116') {
      throw searchError;
    }

    if (existing) {
      return existing;
    }

    const { data: created, error: createError } = await supabase
      .from('exam_years')
      .insert({ year })
      .select()
      .single();

    if (createError) throw createError;
    
    return created;
  } catch (error) {
    // console.error('❌ Erro ao processar ano:', error);
    throw error;
  }
}
