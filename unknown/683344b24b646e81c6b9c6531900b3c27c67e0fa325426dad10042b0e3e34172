import HelmetWrapper from "@/components/utils/HelmetWrapper";

export const GrowthCurveMetaTags = () => {
  const pageTitle = "PedBook | Curvas de Crescimento - Acompanhamento do Desenvolvimento Infantil";
  const pageDescription = "Acesse e analise as principais curvas de crescimento infantil. Acompanhe o desenvolvimento físico com gráficos de peso, altura, perímetro cefálico e IMC.";
  const pageUrl = "https://pedb.com.br/puericultura/curva-de-crescimento";
  const imageUrl = "https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/puericultura.webp";
  const keywords = [
    "curvas de crescimento",
    "desenvolvimento físico infantil",
    "peso infantil",
    "altura infantil",
    "perímetro cefálico",
    "IMC infantil",
    "crescimento pediátrico",
    "monitoramento crescimento",
    "gráficos crescimento",
    "percentis crescimento",
    "avaliação antropométrica",
    "desenvolvimento somático"
  ].join(", ");

  return (
    <HelmetWrapper>
      <title>{pageTitle}</title>
      <meta name="description" content={pageDescription} />
      <meta name="keywords" content={keywords} />

      <meta name="robots" content="index, follow, max-image-preview:large" />
      <link rel="canonical" href={pageUrl} />

      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={pageDescription} />
      <meta property="og:type" content="article" />
      <meta property="og:url" content={pageUrl} />
      <meta property="og:image" content={imageUrl} />
      <meta property="og:image:alt" content="Curvas de crescimento e desenvolvimento infantil" />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />

      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={pageDescription} />
      <meta name="twitter:image" content={imageUrl} />

      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "MedicalWebPage",
          "name": pageTitle,
          "description": pageDescription,
          "url": pageUrl,
          "image": imageUrl,
          "keywords": keywords.split(", "),
          "publisher": {
            "@type": "Organization",
            "name": "PedBook",
            "logo": {
              "@type": "ImageObject",
              "url": "https://pedb.com.br/logo.png"
            }
          },
          "specialty": "Pediatria",
          "audience": {
            "@type": "MedicalAudience",
            "audienceType": "Profissionais de Saúde"
          },
          "about": {
            "@type": "MedicalProcedure",
            "name": "Avaliação de Crescimento Infantil",
            "procedureType": "Monitoramento de Crescimento"
          }
        })}
      </script>
    </HelmetWrapper>
  );
};
