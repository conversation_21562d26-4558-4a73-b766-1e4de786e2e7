
import { Progress } from "@/components/ui/progress";
import { Star, Trophy } from "lucide-react";
import { motion } from "framer-motion";

interface UserProgressHeaderProps {
  userProfile: {
    level: number;
    points: number;
  } | null;
}

export const UserProgressHeader = ({ userProfile }: UserProgressHeaderProps) => {
  if (!userProfile) {
    return (
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Seu Progresso</h1>
        <div className="flex items-center gap-4">
          <div className="text-right">
            <p className="text-sm text-gray-600">Carregando...</p>
          </div>
        </div>
      </div>
    );
  }

  const nextLevelThreshold = userProfile.level * 100;
  const progressToNextLevel = (userProfile.points % 100) / nextLevelThreshold * 100;

  return (
    <motion.div 
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="relative rounded-xl overflow-hidden mb-8"
    >
      <div className="absolute inset-0 bg-gradient-to-br from-[#58CC02] via-[#58CC02] to-[#46a302] rounded-lg"></div>
      <div className="relative p-6">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="flex items-center gap-3">
            <div className="bg-white/20 p-2 rounded-full">
              <Star className="h-7 w-7 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white">Seu Progresso</h1>
              <p className="text-white/80">Acompanhe sua evolução</p>
            </div>
          </div>
          
          <div className="flex items-center gap-4 bg-white/20 p-3 rounded-xl">
            <div>
              <p className="text-xs text-white/80">Nível</p>
              <div className="flex items-center">
                <Trophy className="h-4 w-4 mr-1 text-yellow-300" />
                <p className="text-xl font-bold text-white">{userProfile.level}</p>
              </div>
            </div>
            <div className="flex flex-col gap-1 items-center">
              <div className="w-32 bg-white/30 h-2.5 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-white rounded-full"
                  style={{ width: `${progressToNextLevel}%` }}
                ></div>
              </div>
              <p className="text-xs text-white/80">{userProfile.points} pontos</p>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};
