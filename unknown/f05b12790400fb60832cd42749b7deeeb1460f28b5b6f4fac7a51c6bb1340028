import { Search, ChevronDown, ChevronUp } from "lucide-react";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { motion } from "framer-motion";

interface SharedPrescriptionFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  selectedCategory: string | null;
  onCategorySelect: (categoryId: string | null) => void;
  categories: any[];
}

export const SharedPrescriptionFilters = ({
  searchTerm,
  onSearchChange,
  selectedCategory,
  onCategorySelect,
  categories,
}: SharedPrescriptionFiltersProps) => {
  const [isCategoryMenuOpen, setIsCategoryMenuOpen] = useState(false);

  return (
    <motion.div 
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="w-full lg:w-72 space-y-4 md:space-y-6"
    >
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-4 md:p-6 border border-primary/10 shadow-lg shadow-primary/5">
        <h2 className="text-lg font-semibold bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent mb-4 text-center md:text-left">
          Filtros
        </h2>
        
        <div className="space-y-4 md:space-y-6">
          <div className="relative">
            <Input
              type="search"
              placeholder="Pesquisar..."
              className="pl-10 bg-white/50 border-primary/20 focus:border-primary/40 transition-colors text-center md:text-left"
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
            />
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-primary/40" size={18} />
          </div>

          <div className="space-y-3">
            <Button
              variant="outline"
              className="w-full flex items-center justify-between border border-primary/20 hover:border-primary/40 transition-colors"
              onClick={() => setIsCategoryMenuOpen(!isCategoryMenuOpen)}
            >
              <h3 className="text-sm font-medium text-gray-600">Categorias</h3>
              {isCategoryMenuOpen ? (
                <ChevronUp className="h-4 w-4 text-primary" />
              ) : (
                <ChevronDown className="h-4 w-4 text-primary" />
              )}
            </Button>

            {isCategoryMenuOpen && (
              <ScrollArea className="h-[300px] pr-4">
                <motion.div 
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-2"
                >
                  <Button 
                    variant={selectedCategory === null ? "default" : "ghost"} 
                    className={`w-full justify-center md:justify-start transition-all duration-300 ${
                      selectedCategory === null 
                        ? "bg-primary text-white shadow-md shadow-primary/20" 
                        : "hover:bg-primary/5"
                    }`}
                    onClick={() => onCategorySelect(null)}
                  >
                    Todas as categorias
                  </Button>
                  {categories?.map((category) => (
                    <Button
                      key={category.id}
                      variant={selectedCategory === category.id ? "default" : "ghost"}
                      className={`w-full justify-center md:justify-start transition-all duration-300 ${
                        selectedCategory === category.id 
                          ? "bg-primary text-white shadow-md shadow-primary/20" 
                          : "hover:bg-primary/5"
                      }`}
                      onClick={() => onCategorySelect(category.id)}
                    >
                      {category.name}
                    </Button>
                  ))}
                </motion.div>
              </ScrollArea>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};