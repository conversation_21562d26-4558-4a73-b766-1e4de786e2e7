
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { getThemeClasses } from "@/components/ui/theme-utils";
import { Card } from "@/components/ui/card";

interface DengueQuestionProps {
  question: string;
  onAnswer: (answer: boolean) => void;
  selectedAnswer: boolean | null;
}

export const DengueQuestion = ({
  question,
  onAnswer,
  selectedAnswer,
}: DengueQuestionProps) => {
  // Verifica se é a primeira pergunta sobre sinais de gravidade
  const isFirstQuestion = question.includes("sinais de gravidade");
  // Verifica se é a pergunta sobre sangramento
  const isBleedingQuestion = question.includes("sangramento espontâneo");
  // Verifica se é a pergunta sobre choque
  const isShockQuestion = question.includes("sinais de choque");

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-8 animate-fade-in"
    >
      <Card className={getThemeClasses.gradientCard("orange", "p-6 relative overflow-hidden border border-orange-200 dark:border-orange-800/30")}>
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 dark:from-white/5 to-transparent pointer-events-none" />
        
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-6 text-center relative z-10">
          {question}
        </h2>

        {isFirstQuestion && (
          <div className="space-y-6 mb-6 relative z-10">
            <div className="border-2 border-orange-200 dark:border-orange-700/50 rounded-lg p-6 bg-orange-50/50 dark:bg-orange-900/30">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-orange-700 dark:text-orange-300 text-center">Sinais de alarme presentes e sinais de gravidades ausentes</h3>
                <ul className="list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-200">
                  <li>Dor abdominal intensa (referida ou à palpação) e contínua;</li>
                  <li>Vômitos persistentes;</li>
                  <li>Extravasamento de líquidos (ascite, derrame pleural, derrame pericárdico);</li>
                  <li>Hipotensão postural e/ou lipotimia;</li>
                  <li>Hepatomegalia maior do que 2 cm abaixo do rebordo costal;</li>
                  <li>Sangramento de mucosa;</li>
                  <li>Letargia e/ou irritabilidade;</li>
                  <li>Aumento progressivo do hematócrito;</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {isShockQuestion && (
          <div className="space-y-6 mb-6 relative z-10">
            <div className="border-2 border-orange-200 dark:border-orange-700/50 rounded-lg p-6 bg-orange-50/50 dark:bg-orange-900/30">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-red-700 dark:text-red-300 text-center">DENGUE - SINAIS DE CHOQUE</h3>
                <p className="text-gray-700 dark:text-gray-200 italic">• Extravasamento grave de plasma, levando ao choque evidenciado por taquicardia;</p>
                <ul className="list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-200">
                  <li>Extremidades distais frias;</li>
                  <li>Pulso fraco e filiforme;</li>
                  <li>Enchimento capilar lento ({">"}2 segundos);</li>
                  <li>Pressão arterial convergente ({"<"}20 mmHg);</li>
                  <li>Taquipneia;</li>
                  <li>Oligúria ({"<"}1.5 mL/kg/h);</li>
                  <li>Hipotensão arterial (fase tardia do choque);</li>
                  <li>Cianose (fase tardia do choque);</li>
                  <li>Acumulação de líquidos com insuficiência respiratória;</li>
                  <li>Sangramento grave;</li>
                  <li>Comprometimento grave de órgãos;</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {isBleedingQuestion && (
          <div className="space-y-6 mb-6 relative z-10">
            <div className="border-2 border-orange-200 dark:border-orange-700/50 rounded-lg p-6 bg-orange-50/50 dark:bg-orange-900/30">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-orange-700 dark:text-orange-300 text-center">Condições clínicas especiais e/ou risco social ou comorbidades:</h3>
                
                <div className="space-y-6">
                  <div>
                    <ul className="list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-200">
                      <li>Doenças autoimunes;</li>
                      <li>Hepatopatias;</li>
                      <li>Doença ácido péptica;</li>
                      <li>Doença renal crônica;</li>
                      <li>Doenças hematológicas crônicas;</li>
                      <li>Obesidade;</li>
                      <li>Lactentes ({"<"}24 meses);</li>
                      <li>Gestantes</li>
                      <li>Hipertensão arterial ou outras doenças cardiovasculares graves;</li>
                      <li>Diabetes mellitus;</li>
                      <li>Doença pulmonar obstrutiva crônica (DPOC);</li>
                      <li>Asma;</li>
                    </ul>
                  </div>
                  <h3 className="text-lg font-semibold text-orange-700 dark:text-orange-300 text-center">Como realizar a Prova do Laço:</h3>
                  <div>
                  <ol className="list-decimal pl-6 space-y-2 text-gray-700 dark:text-gray-200">
                    <li><b>Aferir a Pressão Arterial:</b> Meça a pressão arterial e calcule a pressão média (PAS + PAD) / 2.</li>
                    <li><b>Insuflar o Manguito:</b> Insufle o manguito do esfigmomanômetro até a pressão média calculada e mantenha por 5 minutos.</li>
                    <li><b>Desinsuflar e Observar:</b> Após os 5 minutos, desinsufle o manguito e aguarde 1-2 minutos.</li>
                    <li><b>Contar Petéquias:</b> Conte as petéquias (pequenos pontos vermelhos) dentro de um quadrado de 2,5 cm x 2,5 cm no braço ou antebraço.</li>
                    <li><b>Interpretar o Resultado:</b>
                      <ul className="list-disc pl-6">
                        <li><b>Positivo:</b> 20 ou mais petéquias no quadrado.</li>
                        <li><b>Negativo:</b> Menos de 20 petéquias.</li>
                     </ul>
                  </li>
                </ol>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        
        <div className="flex flex-col sm:flex-row gap-4 relative z-10">
          <Button
            onClick={() => onAnswer(true)}
            variant={selectedAnswer === true ? "default" : "outline"}
            className={`flex-1 transition-all duration-300 border ${
              selectedAnswer === true
                ? "bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 dark:from-orange-600 dark:to-red-600 dark:hover:from-orange-700 dark:hover:to-red-700"
                : "hover:bg-orange-50 dark:hover:bg-orange-900/30 border-orange-200 dark:border-orange-700/50"
            }`}
          >
            Sim
          </Button>
          <Button
            onClick={() => onAnswer(false)}
            variant={selectedAnswer === false ? "default" : "outline"}
            className={`flex-1 transition-all duration-300 border ${
              selectedAnswer === false
                ? "bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 dark:from-orange-600 dark:to-red-600 dark:hover:from-orange-700 dark:hover:to-red-700"
                : "hover:bg-orange-50 dark:hover:bg-orange-900/30 border-orange-200 dark:border-orange-700/50"
            }`}
          >
            Não
          </Button>
        </div>
      </Card>
    </motion.div>
  );
};
