import React from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Stethoscope, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";

interface PatientMonitoringProps {
  onBiphasicResponse: (hasBiphasicResponse: boolean) => void;
  onResolutionResponse: (hasResolution: boolean) => void;
  showBiphasicQuestion: boolean;
  doseCount?: number;
}

export const PatientMonitoring: React.FC<PatientMonitoringProps> = ({
  onBiphasicResponse,
  onResolutionResponse,
  showBiphasicQuestion,
  doseCount = 1,
}) => {
  const [showNewDoseAlert, setShowNewDoseAlert] = React.useState(false);

  const handleResolutionResponse = (hasResolution: boolean) => {
    if (!hasResolution) {
      setShowNewDoseAlert(true);
      setTimeout(() => setShowNewDoseAlert(false), 5000); // Hide after 5 seconds
    }
    onResolutionResponse(hasResolution);
  };

  return (
    <Card className="w-full animate-fade-in">
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2">
          <Stethoscope className="h-6 w-6 text-red-500" />
          Monitorização do Paciente
          {doseCount > 1 && (
            <span className="ml-2 inline-flex items-center justify-center px-2.5 py-0.5 text-xs font-medium bg-red-100 text-red-800 rounded-full animate-pulse">
              Dose {doseCount}/3
            </span>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {showNewDoseAlert && !showBiphasicQuestion && (
          <Alert variant="destructive" className="animate-fade-in">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Nova Dose Necessária</AlertTitle>
            <AlertDescription>
              Sem melhora após dose {doseCount}. Preparar nova dose de adrenalina.
            </AlertDescription>
          </Alert>
        )}
        <div className="text-center space-y-6 animate-slide-in-up">
          {showBiphasicQuestion ? (
            <div className="space-y-4">
              <p className="font-medium text-gray-800">
                O paciente apresentou novos sintomas de anafilaxia (resposta bifásica)?
              </p>
              <div className="flex justify-center gap-4">
                <Button 
                  onClick={() => onBiphasicResponse(true)}
                  className="bg-red-500 hover:bg-red-600 text-white min-w-[100px]"
                >
                  Sim
                </Button>
                <Button 
                  onClick={() => onBiphasicResponse(false)}
                  variant="outline"
                  className={cn(
                    "border-red-200 text-red-700 hover:bg-red-50 min-w-[100px]",
                    "transition-all duration-300 hover:scale-105"
                  )}
                >
                  Não
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="font-medium text-gray-800">
                Houve resolução da anafilaxia?
              </p>
              <div className="flex justify-center gap-4">
                <Button 
                  onClick={() => handleResolutionResponse(true)}
                  className="bg-red-500 hover:bg-red-600 text-white min-w-[100px]"
                >
                  Sim
                </Button>
                <Button 
                  onClick={() => handleResolutionResponse(false)}
                  variant="outline"
                  className={cn(
                    "border-red-200 text-red-700 hover:bg-red-50 min-w-[100px]",
                    "transition-all duration-300 hover:scale-105"
                  )}
                >
                  Não
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};