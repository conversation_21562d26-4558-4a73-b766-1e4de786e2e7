import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { compressImage, isSupportedImageFormat, calculateCompressionRatio } from "@/utils/imageOptimization";
import { LazyImage } from "@/components/ui/LazyImage";

interface ImageUploadProps {
  imageUrl: string;
  setImageUrl: (url: string) => void;
}

export const ImageUpload = ({ imageUrl, setImageUrl }: ImageUploadProps) => {
  const { toast } = useToast();

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      // Verificar se é um formato suportado
      if (!isSupportedImageFormat(file)) {
        toast({
          variant: "destructive",
          title: "Formato não suportado",
          description: "Por favor, selecione uma imagem JPG, PNG ou WebP.",
        });
        return;
      }

      const originalSize = file.size;

      // Comprimir imagem automaticamente
      const compressedFile = await compressImage(file, {
        maxWidth: 1200,
        maxHeight: 800,
        quality: 0.85,
        format: 'webp'
      });

      const compressionRatio = calculateCompressionRatio(originalSize, compressedFile.size);

      const filePath = `${crypto.randomUUID()}.webp`;

      const { error: uploadError } = await supabase.storage
        .from("dnpm-images")
        .upload(filePath, compressedFile);

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from("dnpm-images")
        .getPublicUrl(filePath);

      setImageUrl(publicUrl);

      // Mostrar sucesso com informações de compressão
      toast({
        title: "Imagem enviada com sucesso!",
        description: `Tamanho reduzido em ${compressionRatio}% (${(originalSize / 1024 / 1024).toFixed(2)}MB → ${(compressedFile.size / 1024 / 1024).toFixed(2)}MB)`,
      });
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao fazer upload da imagem",
        description: error.message,
      });
    }
  };

  return (
    <div>
      <Label htmlFor="image">Imagem</Label>
      <Input
        id="image"
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
      />
      {imageUrl && (
        <div className="mt-2">
          <LazyImage
            src={imageUrl}
            alt="Preview da imagem"
            className="max-w-xs rounded-lg shadow-md"
            priority={true}
          />
        </div>
      )}
    </div>
  );
};