
import { motion } from "framer-motion";
import { ChevronRight, Copy } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";

interface ICDCategory {
  id: string;
  code_range: string;
  name: string;
  description: string | null;
}

interface ICDCategoryCardProps {
  category: ICDCategory;
  onClick: () => void;
}

export const ICDCategoryCard = ({ category, onClick }: ICDCategoryCardProps) => {
  const { toast } = useToast();

  const handleCopyClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Previne o onClick do card
    navigator.clipboard.writeText(category.code_range);
    toast({
      description: "Código CID copiado com sucesso!",
      duration: 2000,
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -4 }}
      onClick={onClick}
      className="group relative bg-gradient-to-br from-white to-primary/5 dark:from-slate-800 dark:to-primary/10 rounded-xl p-6 space-y-3 border border-primary/10 dark:border-primary/20 shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer backdrop-blur-sm"
    >
      <div className="flex justify-between items-start">
        <div className="space-y-2">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold text-primary dark:text-blue-400">
                {category.code_range}
              </h3>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-gray-400 hover:text-primary dark:text-gray-500 dark:hover:text-blue-400"
                onClick={handleCopyClick}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-gray-800 font-medium line-clamp-2 dark:text-gray-300">{category.name}</p>
          </div>
        </div>
        <ChevronRight className="h-5 w-5 text-primary/40 group-hover:text-primary transition-colors dark:text-blue-400/40 dark:group-hover:text-blue-400" />
      </div>
    </motion.div>
  );
};
