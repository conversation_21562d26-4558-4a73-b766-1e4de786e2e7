
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import type { FilteredQuestionsResponse, SelectedFilters } from '@/types/question';
import { useMemo } from 'react';
import { useDomain } from '@/hooks/useDomain';
import { useUserData } from '@/hooks/useUserData';

const INITIAL_COUNTS = {
  specialties: {},
  themes: {},
  focuses: {},
  years: {}
};

export const useFilteredQuestions = (
  filters: SelectedFilters,
  page: number = 1,
  pageSize: number = 50, // Otimizado: reduzido de 1000 para 50 para melhor performance
  enabled: boolean = true // Novo parâmetro para controlar quando executar
) => {
  const { domain, isReady, isResidencia } = useDomain();
  const { user } = useUserData();

  // Removidas funções auxiliares desnecessárias para filtrar questões acertadas
  // Essas funções serão implementadas onde realmente forem necessárias

  // Log domain for debugging
  //console.log(`🔍 [useFilteredQuestions] Using domain: ${domain}, isReady: ${isReady}, isResidencia: ${isResidencia}`);

  const hasFilters = useMemo(() => {
    if (!filters || typeof filters !== 'object') {
      return false;
    }

    return Object.values(filters).some(f =>
      Array.isArray(f) ? f.length > 0 : false
    );
  }, [filters]);

  // Log the actual filter values for debugging
  //console.log(`🔍 [useFilteredQuestions] Current filters:`, JSON.stringify(filters, null, 2));

  const result = useQuery({
    queryKey: ['filtered-questions', filters, page, pageSize, domain, filters?.excludeAnswered, user?.id],
    queryFn: async () => {
      try {
        // Skip fetching if there are no filters or domain isn't ready
        if (!hasFilters || !isReady || !domain) {
         // console.log(`⚠️ [useFilteredQuestions] Skipping fetch - hasFilters: ${hasFilters}, isReady: ${isReady}, domain: ${domain}`);
          return {
            questions: [],
            total_count: 0,
            filtered_counts: INITIAL_COUNTS,
            excluded_questions_count: 0
          };
        }

        // Prepare basic parameters that are always included
        const baseParams = {
          specialty_ids: filters?.specialties || [],
          theme_ids: filters?.themes || [],
          focus_ids: filters?.focuses || [],
          location_ids: filters?.locations || [],
          years: (filters?.years || []).map(Number),
          question_types: filters?.question_types || [], // Always include question_types, even if empty
          question_formats: filters?.question_formats || [], // Always include question_formats, even if empty
          page_number: page,
          items_per_page: pageSize,
          domain_filter: domain
        };

        // Use pediatric-specific functions
        let data, error;
        const rpcFunction = filters?.excludeAnswered && user?.id
          ? 'get_filtered_pediatric_questions_excluding_answered'
          : 'get_filtered_pediatric_questions';

        const params = {
          specialty_ids: filters.specialties || [],
          theme_ids: filters.themes || [],
          focus_ids: filters.focuses || [],
          location_ids: filters.locations || [],
          years: (filters.years || []).map(Number),
          question_types: filters.question_types || [],
          question_formats: filters.question_formats || [],
          page_number: page,
          items_per_page: pageSize,
          ...(filters?.excludeAnswered && user?.id && { user_id: user.id })
        };

        const result = await supabase.rpc(rpcFunction, params);
        data = result.data;
        error = result.error;

        if (error) {
          console.error('❌ [useFilteredQuestions] Error:', error);
          throw error;
        }

        if (!data) {
          console.warn('⚠️ [useFilteredQuestions] No data returned');
          throw new Error('No data returned');
        }

        // The data should have questions and total_count properties
        const typedData = data as {
          questions: any[];
          total_count: number;
        };

        let questions = typedData.questions || [];
        let totalCount = typedData.total_count || 0;
        let excludedQuestionsCount = 0;

        // Filtro de questões acertadas removido para evitar violação das regras dos hooks
        // O filtro será aplicado no useQuestionCount apenas

        //console.log(`✅ [useFilteredQuestions] Found ${totalCount} questions with domain: ${domain}`);

        if (questions.length > 0) {
          // console.log(`📋 [useFilteredQuestions] First question example:`, {
          //   id: questions[0].id,
          //   specialty: questions[0].specialty?.name,
          //   theme: questions[0].theme?.name,
          //   focus: questions[0].focus?.name,
          //   domain: questions[0].domain
          // });
        }


        const filtered_counts = questions.reduce((counts, question) => {
          if (question.specialty?.id) {
            counts.specialties[question.specialty.id] =
              (counts.specialties[question.specialty.id] || 0) + 1;
          }
          if (question.theme?.id) {
            counts.themes[question.theme.id] =
              (counts.themes[question.theme.id] || 0) + 1;
          }
          if (question.focus?.id) {
            counts.focuses[question.focus.id] =
              (counts.focuses[question.focus.id] || 0) + 1;
          }
          if (question.year) {
            counts.years[question.year] =
              (counts.years[question.year] || 0) + 1;
          }
          return counts;
        }, { ...INITIAL_COUNTS });

        return {
          questions,
          total_count: totalCount,
          filtered_counts,
          excluded_questions_count: excludedQuestionsCount
        };
      } catch (error) {
        console.error('❌ [useFilteredQuestions] Failed to fetch questions:', error);
        throw error;
      }
    },
    // Only fetch if there are filters, domain is ready, and explicitly enabled
    enabled: enabled && hasFilters && isReady && !!domain,
    staleTime: 10 * 60 * 1000, // 10 minutes - cache mais agressivo
    cacheTime: 15 * 60 * 1000, // 15 minutes - manter em cache por mais tempo
    refetchOnWindowFocus: false, // Não refetch ao focar na janela
    refetchOnMount: false // Não refetch ao montar se já tem dados em cache
  });

  return {
    ...result,
    isLoading: result.isLoading,
    isFetching: result.isFetching,
    data: result.data || {
      questions: [],
      total_count: 0,
      filtered_counts: INITIAL_COUNTS,
      excluded_questions_count: 0
    }
  };
};
