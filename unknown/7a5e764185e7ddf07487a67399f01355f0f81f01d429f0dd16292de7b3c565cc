
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";

interface GrowthCurveCardProps {
  curve: {
    id: string;
    title: string;
    description?: string;
    gender: string;
    gestational_age: string;
    growth_type: string;
    image_url?: string;
  };
  onViewCurve: (curve: any) => void;
}

export function GrowthCurveCard({ curve, onViewCurve }: GrowthCurveCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      whileHover={{ y: -4 }}
      className="group bg-white dark:bg-slate-800 rounded-xl border border-gray-100 dark:border-gray-700 shadow-md overflow-hidden hover:shadow-lg transition-all duration-300"
    >
      <div className="p-6">
        <h3 className="font-semibold text-lg mb-2 dark:text-gray-100">{curve.title}</h3>
        {curve.description && (
          <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">{curve.description}</p>
        )}
        <div className="flex flex-wrap gap-2 mb-4">
          <span className="px-3 py-1 rounded-full text-xs bg-accent-blue dark:bg-blue-800 dark:text-blue-100 text-blue-900">
            {curve.gender === "male" ? "Menino" : "Menina"}
          </span>
          <span className="px-3 py-1 rounded-full text-xs bg-accent-yellow dark:bg-yellow-800 dark:text-yellow-100 text-yellow-900">
            {curve.gestational_age === "term" ? "A Termo" : "Pré-termo"}
          </span>
          <span className="px-3 py-1 rounded-full text-xs bg-accent-green dark:bg-green-800 dark:text-green-100 text-green-900">
            {curve.growth_type === "healthy" ? "Saudável" : "Transtorno"}
          </span>
        </div>
        <Button 
          onClick={() => onViewCurve(curve)}
          className="w-full relative group dark:bg-slate-700 dark:hover:bg-slate-600 dark:text-white"
        >
          <span>Visualizar Curva</span>
          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
            <Search className="w-6 h-6 text-white" />
          </div>
        </Button>
      </div>
    </motion.div>
  );
}
