import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface BlogPostReactionsProps {
  postId: string;
  userId?: string;
  userReaction?: { reaction_type: string } | null;
  post: any;
}

export function BlogPostReactions({ postId, userId, userReaction, post }: BlogPostReactionsProps) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const reactionMutation = useMutation({
    mutationFn: async (type: "like" | "dislike") => {
      if (!userId) {
        console.error("No user found when trying to react");
        throw new Error("Você deve entrar em sua conta para conseguir reagir ao post!");
      }

  
      try {
        const { error } = await supabase.rpc('handle_blog_reaction', {
          p_user_id: userId,
          p_post_id: postId,
          p_reaction_type: type
        });

        if (error) throw error;

      } catch (error: any) {
        console.error("Error in reaction mutation:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["blog-reaction", postId, userId] });
      queryClient.invalidateQueries({ queryKey: ["blog-post", postId] });
    },
    onError: (error: any) => {
      console.error("Error in reaction mutation:", error);
      toast({
        variant: "destructive",
        title: "Erro ao processar reação",
        description: error.message || "Ocorreu um erro ao processar sua reação.",
      });
    },
  });

  return { reactionMutation };
}