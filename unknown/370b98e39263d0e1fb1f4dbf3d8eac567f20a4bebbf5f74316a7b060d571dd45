import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export const useReactionQueries = (prescriptionId: string, userId?: string) => {
  const { data: prescription } = useQuery({
    queryKey: ["prescription-reactions", prescriptionId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_prescriptions")
        .select("likes_count, dislikes_count")
        .eq("id", prescriptionId)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') {
        console.error("Error fetching prescription:", error);
        throw error;
      }
      return data ?? { likes_count: 0, dislikes_count: 0 };
    },
  });

  const { data: userReaction } = useQuery({
    queryKey: ["user-reaction", prescriptionId, userId],
    queryFn: async () => {
      if (!userId) {
        return null;
      }
      
      const { data, error } = await supabase
        .from("pedbook_prescription_user_reactions")
        .select("reaction_type")
        .eq("prescription_id", prescriptionId)
        .eq("user_id", userId)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') {
        console.error("Error fetching user reaction:", error);
        throw error;
      }
      return data;
    },
    enabled: !!userId,
  });

  return { prescription, userReaction };
};