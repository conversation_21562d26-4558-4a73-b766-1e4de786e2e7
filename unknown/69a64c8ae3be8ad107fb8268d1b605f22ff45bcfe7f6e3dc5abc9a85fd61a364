import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";

interface ReferencesFieldsProps {
  formData: {
    scientific_references?: string;
  };
  onChange: (field: string, value: string) => void;
}

export function ReferencesFields({ formData, onChange }: ReferencesFieldsProps) {
  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="scientific_references">Referências Científicas</Label>
        <Textarea
          id="scientific_references"
          value={formData.scientific_references || ""}
          onChange={(e) => onChange("scientific_references", e.target.value)}
          className="min-h-[200px]"
        />
      </div>
    </div>
  );
}