import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Folder, ChevronRight } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { motion } from "framer-motion";

interface BlogSidebarProps {
  selectedCategory?: string;
  onCategorySelect: (categoryId: string | undefined) => void;
}

export function BlogSidebar({ selectedCategory, onCategorySelect }: BlogSidebarProps) {
  const { data: categories, isLoading } = useQuery({
    queryKey: ['blog-categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_blog_categories')
        .select('*')
        .order('name');
      
      if (error) throw error;
      return data;
    },
  });

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-full" />
        <div className="space-y-2">
          {[1, 2, 3, 4].map((i) => (
            <Skeleton key={i} className="h-10 w-full" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="font-semibold text-lg gradient-text">Categorias</h3>
      <ScrollArea className="h-[calc(100vh-200px)] pr-4">
        <div className="space-y-2">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
          >
            <Button
              variant={!selectedCategory ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => onCategorySelect(undefined)}
            >
              <Folder className="w-4 h-4 mr-2" />
              Todas as categorias
            </Button>
          </motion.div>
          
          {categories?.map((category, index) => (
            <motion.div
              key={category.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Button
                variant={selectedCategory === category.id ? "default" : "ghost"}
                className="w-full justify-start group"
                onClick={() => onCategorySelect(category.id)}
              >
                <Folder className="w-4 h-4 mr-2" />
                {category.name}
                <ChevronRight className="w-4 h-4 ml-auto opacity-0 group-hover:opacity-100 transition-opacity" />
              </Button>
            </motion.div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}