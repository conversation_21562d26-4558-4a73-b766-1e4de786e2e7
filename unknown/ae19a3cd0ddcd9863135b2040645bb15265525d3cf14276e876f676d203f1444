
import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle, AlertTriangle, AlertOctagon } from 'lucide-react';

interface TreatmentCardProps {
  title: string;
  description: string;
  treatment: string;
  onEvolution: () => void;
  onNoEvolution: () => void;
  severity: 'mild' | 'moderate' | 'severe';
  evolutionQuestion: string;
}

export const TreatmentCard: React.FC<TreatmentCardProps> = ({
  title,
  description,
  treatment,
  onEvolution,
  onNoEvolution,
  severity,
  evolutionQuestion,
}) => {
  const getSeverityIcon = () => {
    switch (severity) {
      case 'mild':
        return <AlertCircle className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />;
      case 'moderate':
        return <AlertTriangle className="h-6 w-6 text-amber-600 dark:text-amber-400" />;
      case 'severe':
        return <AlertOctagon className="h-6 w-6 text-rose-600 dark:text-rose-400" />;
    }
  };

  const getSeverityColor = () => {
    switch (severity) {
      case 'mild':
        return 'bg-[#F2FCE2] dark:bg-green-900/30';
      case 'moderate':
        return 'bg-[#FEF7CD] dark:bg-amber-900/30';
      case 'severe':
        return 'bg-[#FFDEE2] dark:bg-red-900/30';
    }
  };

  const getBorderColor = () => {
    switch (severity) {
      case 'mild':
        return 'border-[#E8F7D4] dark:border-green-800/40';
      case 'moderate':
        return 'border-[#FDF2B8] dark:border-amber-800/40';
      case 'severe':
        return 'border-[#FFD4D9] dark:border-red-800/40';
    }
  };

  return (
    <Card className={`p-8 max-w-2xl mx-auto bg-white border-2 ${getBorderColor()} dark:bg-slate-800/90`}>
      <div className="flex items-center gap-3 mb-6">
        {getSeverityIcon()}
        <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100">
          {title}
        </h2>
      </div>
      <div className="space-y-6">
        <div className={`p-6 rounded-lg border-2 ${getBorderColor()} ${getSeverityColor()}`}>
          <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Características:</h3>
          <p className="text-gray-700 dark:text-gray-300">{description}</p>
        </div>
        <div className={`p-6 rounded-lg border-2 ${getBorderColor()} ${getSeverityColor()}`}>
          <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Conduta:</h3>
          <p className="text-gray-700 dark:text-gray-300">{treatment}</p>
        </div>

        {severity !== 'severe' && (
          <div className="space-y-4">
            <p className="font-medium text-gray-800 dark:text-gray-200">{evolutionQuestion}</p>
            <div className="grid grid-cols-2 gap-4">
              <Button
                onClick={onEvolution}
                variant="outline"
                className={`w-full border-2 ${getBorderColor()} ${getSeverityColor()} hover:bg-white/50 text-gray-700 dark:text-gray-200 dark:hover:bg-slate-800/90`}
              >
                Sim
              </Button>
              <Button
                onClick={onNoEvolution}
                variant="outline"
                className={`w-full border-2 ${getBorderColor()} ${getSeverityColor()} hover:bg-white/50 text-gray-700 dark:text-gray-200 dark:hover:bg-slate-800/90`}
              >
                Não
              </Button>
            </div>
          </div>
        )}

        {severity === 'severe' && (
          <Button
            onClick={onNoEvolution}
            variant="outline"
            className={`w-full border-2 ${getBorderColor()} ${getSeverityColor()} hover:bg-white/50 text-gray-700 dark:text-gray-200 dark:hover:bg-slate-800/90`}
          >
            Prosseguir para Alta
          </Button>
        )}
      </div>
    </Card>
  );
};
