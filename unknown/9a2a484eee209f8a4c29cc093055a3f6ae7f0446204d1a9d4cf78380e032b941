import { Scale } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface PatientInfoSectionProps {
  weight: number;
  onWeightChange: (weight: number) => void;
  onWeightCommit: (weight: number) => void;
  age: number;
  onAgeChange: (age: number) => void;
  onAgeCommit: (age: number) => void;
}

export const PatientInfoSection = ({
  weight,
  onWeightChange,
  onWeightCommit,
  age,
  onAgeChange,
  onAgeCommit,
  
}: PatientInfoSectionProps) => {
  return (
    <div className="space-y-6 bg-gradient-to-br from-primary/5 via-primary/10 to-transparent p-6 rounded-xl backdrop-blur-sm border border-primary/10">
      <div className="space-y-2">
        <Label htmlFor="weight" className="text-lg font-medium text-red-700 flex items-center gap-2">
          <Scale className="h-5 w-5" />
          Peso do Paciente
        </Label>
        <div className="flex items-center gap-2">
          <Input
            id="weight"
            type="number"
            min={0}
            max={100}
            value={weight || ""}
            onChange={(e) => {
              const value = parseFloat(e.target.value);
              if (!isNaN(value)) {
                const limitedValue = Math.min(value, 100);
                onWeightChange(limitedValue);
                onWeightCommit(limitedValue);
              }
            }}
            className="flex-1 bg-white/50 border-red-200 focus:border-red-400 transition-colors text-center text-lg"
            placeholder="Digite o peso"
          />
          <span className="text-lg font-medium text-red-700 min-w-[3rem]">kg</span>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="age" className="text-lg font-medium text-primary flex items-center gap-2">
          Idade do Paciente
        </Label>
        <div className="flex items-center gap-2">
          <Input
            id="age"
            type="number"
            min={0}
            max={18}
            value={age || ""}
            onChange={(e) => {
              const value = parseFloat(e.target.value);
              if (!isNaN(value)) {
                const limitedValue = Math.min(value, 18);
                onAgeChange(limitedValue);
                onAgeCommit(limitedValue);
              }
            }}
            className="flex-1 bg-white/50 border-primary/20 focus:border-primary/40 transition-colors text-center text-lg"
            placeholder="Digite a idade"
          />
          <span className="text-lg font-medium text-primary min-w-[3rem]">anos</span>
        </div>
      </div>
    </div>
  );
};