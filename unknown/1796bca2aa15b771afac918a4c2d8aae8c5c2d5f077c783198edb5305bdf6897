import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON><PERSON>cle, Stethoscope } from "lucide-react";

export const DiagnosisCriteria: React.FC = () => {
  return (
    <div className="space-y-6 animate-fade-in">
      <Card className="w-full">
        <CardHeader className="space-y-1">
          <div className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-6 w-6" />
            <CardTitle>Critérios para Anafilaxia (WAO 2020)</CardTitle>
          </div>
          <p className="text-sm text-red-600 font-medium">
            ANAFILAXIA = RECONHECIMENTO PRECOCE + NÃO RETARDAR ADRENALINA IM
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Paciente com alergia conhecida */}
            <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
              <h3 className="font-medium text-purple-800 mb-3">
                Paciente com Alergia Conhecida/Suspeita
              </h3>
              <p className="text-sm text-gray-600 mb-2 italic">
                (alérgeno conhecido ou suspeito)
              </p>
              <ul className="list-disc pl-4 space-y-2 text-gray-700">
                <li>Início súbito de hipotensão</li>
                <li>Broncoespasmo</li>
                <li>Edema de laringe</li>
              </ul>
            </div>

            {/* Paciente sem histórico */}
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <h3 className="font-medium text-green-800 mb-3">
                Paciente sem Alergia Conhecida
              </h3>
              <p className="text-sm text-gray-600 mb-2 italic">
                (alérgeno desconhecido)
              </p>
              <div className="space-y-2 text-gray-700">
                <p className="font-medium text-sm">
                  Início agudo (minutos/algumas horas) com:
                </p>
                <ul className="list-disc pl-4 space-y-2">
                  <li>Comprometimento respiratório (dispneia, sibilos, estridor)</li>
                  <li>Redução de PA ou sinais de choque</li>
                  <li>Dois ou mais sistemas envolvidos:</li>
                  <ul className="list-[circle] pl-6 space-y-1 text-sm">
                    <li>Pele/mucosas</li>
                    <li>Respiratório</li>
                    <li>Cardiovascular</li>
                    <li>Gastrointestinal grave</li>
                  </ul>
                </ul>
              </div>
            </div>
          </div>

          {/* Avaliação Multissistêmica */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h3 className="font-medium text-blue-800 mb-3 flex items-center gap-2">
              <Stethoscope className="h-5 w-5" />
              Avaliação Multissistêmica
            </h3>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <p className="font-medium text-blue-700 mb-1">Via Aérea/Respiratório:</p>
                <ul className="list-disc pl-4 text-sm text-gray-700">
                  <li>Dispneia, sibilos, broncoespasmo</li>
                  <li>Estridor, hipoxemia</li>
                </ul>
              </div>
              <div>
                <p className="font-medium text-blue-700 mb-1">Circulação:</p>
                <ul className="list-disc pl-4 text-sm text-gray-700">
                  <li>Hipotensão</li>
                  <li>Síncope, colapso, incontinência</li>
                </ul>
              </div>
              <div>
                <p className="font-medium text-blue-700 mb-1">Pele/Mucosas:</p>
                <ul className="list-disc pl-4 text-sm text-gray-700">
                  <li>Urticária generalizada</li>
                  <li>Prurido, rubor, angioedema</li>
                </ul>
              </div>
              <div>
                <p className="font-medium text-blue-700 mb-1">Gastrointestinal:</p>
                <ul className="list-disc pl-4 text-sm text-gray-700">
                  <li>Dor abdominal severa</li>
                  <li>Vômitos repetitivos</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Reconhecimento e Intervenção */}
          <div className="bg-red-50 p-4 rounded-lg border border-red-200">
            <h3 className="font-medium text-red-800 mb-3">
              Reconhecimento Precoce e Intervenção
            </h3>
            <div className="space-y-2 text-gray-700">
              <p>
                Garantir o uso imediato de adrenalina intramuscular e posicionar o paciente adequadamente 
                (posição de Trendelenburg com pernas elevadas).
              </p>
              <p className="text-sm text-red-600 font-medium">
                ABCDE primário + posição de Trendelenburg (Elevar Pernas)
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};