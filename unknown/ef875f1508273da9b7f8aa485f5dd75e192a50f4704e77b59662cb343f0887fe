import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

interface BasicInfoSectionProps {
  formData: any;
  setFormData: (data: any) => void;
}

export function BasicInfoSection({ formData, setFormData }: BasicInfoSectionProps) {
  const handleAgeChange = (value: string) => {
    if (value === "") {
      setFormData({ ...formData, age: "", isPediatric: false });
      return;
    }
    
    const numValue = parseInt(value);
    if (!isNaN(numValue)) {
      const isPediatric = numValue < 18;
      setFormData({ ...formData, age: numValue, isPediatric });
    }
  };

  const handleWeightChange = (value: string) => {
    if (value === "") {
      setFormData({ ...formData, weight: "" });
      return;
    }
    
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      setFormData({ ...formData, weight: numValue });
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div className="space-y-3">
        <Label 
          htmlFor="age" 
          className="text-xl font-semibold text-gray-800 bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent"
        >
          Idade
        </Label>
        <Input
          id="age"
          type="number"
          min={0}
          max={120}
          value={formData.age}
          onChange={(e) => handleAgeChange(e.target.value)}
          required
          className="h-12 text-lg transition-all duration-200 focus:ring-2 focus:ring-primary/50 bg-white/50 backdrop-blur-sm"
        />
      </div>

      <div className="space-y-3">
        <Label 
          htmlFor="weight" 
          className="text-xl font-semibold text-gray-800 bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent"
        >
          Peso (kg)
        </Label>
        <Input
          id="weight"
          type="number"
          min={0}
          max={200}
          step={0.1}
          value={formData.weight}
          onChange={(e) => handleWeightChange(e.target.value)}
          required
          className="h-12 text-lg transition-all duration-200 focus:ring-2 focus:ring-primary/50 bg-white/50 backdrop-blur-sm"
        />
      </div>

      <div className="space-y-4">
        <Label className="text-xl font-semibold text-gray-800 bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent">
          Gênero
        </Label>
        <div className="flex gap-3">
          <Button
            type="button"
            variant={formData.gender === "male" ? "default" : "outline"}
            onClick={() => setFormData({ ...formData, gender: "male" })}
            className="flex-1 h-12"
          >
            Masculino
          </Button>
          <Button
            type="button"
            variant={formData.gender === "female" ? "default" : "outline"}
            onClick={() => setFormData({ ...formData, gender: "female" })}
            className="flex-1 h-12"
          >
            Feminino
          </Button>
        </div>

        {formData.gender === "female" && (
          <div className="mt-4 space-y-3 animate-fade-in">
            <Label className="text-xl font-semibold text-gray-800 bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent">
              Está grávida?
            </Label>
            <div className="flex gap-3">
              <Button
                type="button"
                variant={formData.isPregnant ? "default" : "outline"}
                onClick={() => setFormData({ ...formData, isPregnant: true })}
                className="flex-1 h-12"
              >
                Sim
              </Button>
              <Button
                type="button"
                variant={!formData.isPregnant ? "default" : "outline"}
                onClick={() => setFormData({ ...formData, isPregnant: false })}
                className="flex-1 h-12"
              >
                Não
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}