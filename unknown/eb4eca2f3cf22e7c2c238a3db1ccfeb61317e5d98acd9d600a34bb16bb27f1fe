import HelmetWrapper from "@/components/utils/HelmetWrapper";

export const GlasgowMetaTags = () => {
  const pageTitle = "PedBook | Escala de Glasgow Pediátrica - Avaliação Neurológica";
  const description = "Calculadora da Escala de Glasgow adaptada para pediatria, essencial para avaliação do nível de consciência em crianças. Ferramenta fundamental para pediatras e neurologistas na avaliação neurológica pediátrica.";
  const pageUrl = "https://pedb.com.br/calculadoras/glasgow";
  const keywords = [
    "escala de glasgow pediátrica",
    "glasgow infantil",
    "avaliação neurológica",
    "nível de consciência",
    "pediatria calculadora",
    "neurologia pediátrica",
    "trauma craniano",
    "resposta motora",
    "resposta verbal",
    "abertura ocular",
    "avaliação consciência"
  ].join(", ");

  return (
    <HelmetWrapper>
      <title>{pageTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="robots" content="index, follow, max-image-preview:large" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta charSet="UTF-8" />
      <link rel="canonical" href={pageUrl} />

      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content="article" />
      <meta property="og:url" content={pageUrl} />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />
      <meta property="og:image" content="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/calculadora.webp" />
      <meta property="og:image:alt" content="Interface da calculadora da Escala de Glasgow Pediátrica com campos específicos para avaliação de resposta motora, verbal e abertura ocular em crianças" />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />

      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/calculadora.webp" />
      <meta name="twitter:site" content="@PedBook" />

      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "MedicalWebPage",
          "name": pageTitle,
          "description": description,
          "url": pageUrl,
          "keywords": keywords.split(", "),
          "inLanguage": "pt-BR",
          "mainEntity": {
            "@type": "MedicalCalculator",
            "name": "Calculadora da Escala de Glasgow Pediátrica",
            "description": description,
            "medicalSpecialty": {
              "@type": "MedicalSpecialty",
              "name": "Pediatria"
            },
            "relevantSpecialty": [
              {
                "@type": "MedicalSpecialty",
                "name": "Neurologia Pediátrica"
              },
              {
                "@type": "MedicalSpecialty",
                "name": "Pediatria"
              }
            ]
          },
          "about": {
            "@type": "MedicalCondition",
            "name": "Avaliação do Nível de Consciência",
            "description": "Avaliação neurológica padronizada do nível de consciência em pacientes pediátricos"
          },
          "audience": {
            "@type": "MedicalAudience",
            "audienceType": "Profissionais de Saúde"
          },
          "publisher": {
            "@type": "Organization",
            "name": "PedBook",
            "url": "https://pedb.com.br",
            "logo": {
              "@type": "ImageObject",
              "url": "https://pedb.com.br/logo.png"
            }
          }
        })}
      </script>
    </HelmetWrapper>
  );
};