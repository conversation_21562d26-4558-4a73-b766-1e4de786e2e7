import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";

interface MonitoringChecklistProps {
  onComplete: () => void;
}

export const MonitoringChecklist = ({ onComplete }: MonitoringChecklistProps) => {
  const [checkedItems, setCheckedItems] = useState<string[]>([]);

  const monitoringItems = [
    "Gasometria venosa",
    "Função renal",
    "Eletrólitos (Mg, P, Na, K, Cl)",
    "Controle de diurese",
    "Sinais vitais"
  ];

  const warningSignsItems = [
    "Cefaleia",
    "Diminuição do nível de consciência",
    "Pulso lento",
    "Aumento de PA",
    "Diminuição na saturação de oxigênio",
    "Fundo de olho alterado"
  ];

  const handleCheck = (item: string) => {
    setCheckedItems(prev => 
      prev.includes(item) 
        ? prev.filter(i => i !== item)
        : [...prev, item]
    );
  };

  return (
    <Card className="p-6 space-y-6">
      <h3 className="text-lg font-semibold">Checklist de Monitoramento</h3>

      <div className="space-y-4">
        <h4 className="font-medium">Exames e Controles (2-4 horas)</h4>
        <div className="space-y-2">
          {monitoringItems.map((item) => (
            <div key={item} className="flex items-center space-x-2">
              <Checkbox
                id={item}
                checked={checkedItems.includes(item)}
                onCheckedChange={() => handleCheck(item)}
              />
              <label htmlFor={item} className="text-sm">{item}</label>
            </div>
          ))}
        </div>
      </div>

      <Alert className="bg-red-50 border-red-200">
        <AlertDescription className="space-y-2">
          <h4 className="font-medium text-red-800">Sinais de Alerta - Edema Cerebral:</h4>
          <ul className="list-disc pl-4 text-red-700 space-y-1">
            {warningSignsItems.map((item) => (
              <li key={item}>{item}</li>
            ))}
          </ul>
        </AlertDescription>
      </Alert>

      <Button 
        onClick={onComplete}
        className="w-full"
        disabled={checkedItems.length < monitoringItems.length}
      >
        Continuar
      </Button>
    </Card>
  );
};