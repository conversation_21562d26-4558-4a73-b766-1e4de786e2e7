import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";
import { useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

interface DeleteMedicationDialogProps {
  medication: any;
  isOpen: boolean;
  onClose: () => void;
}

export function DeleteMedicationDialog({ medication, isOpen, onClose }: DeleteMedicationDialogProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const handleDelete = async () => {
    try {
      const { error } = await supabase
        .from("pedbook_medications")
        .delete()
        .eq("id", medication?.id);

      if (error) throw error;

      toast({
        title: "Medicamento excluído com sucesso!",
        description: `O medicamento ${medication?.name} foi excluído.`,
      });

      queryClient.invalidateQueries({ queryKey: ["medications"] });
      onClose();
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao excluir medicamento",
        description: error.message || "Ocorreu um erro ao excluir o medicamento.",
      });
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
          <AlertDialogDescription>
            Tem certeza que deseja excluir o medicamento "{medication?.name}"? Esta ação não pode ser desfeita.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
            Excluir
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}