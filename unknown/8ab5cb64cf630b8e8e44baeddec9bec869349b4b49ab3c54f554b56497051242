import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";

const RESEND_API_KEY = Deno.env.get("RESEND_API_KEY");

interface WelcomeEmailRequest {
  email: string;
  name: string;
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { email, name }: WelcomeEmailRequest = await req.json();

    const emailHtml = `
    <!DOCTYPE html>
    <html lang="pt">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Bem-vindo ao PEDBOOK</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                background-color: #f9f9f9;
                margin: 0;
                padding: 0;
                color: #333;
            }
            .container {
                width: 100%;
                max-width: 600px;
                margin: 40px auto;
                padding: 20px;
                background-color: #ffffff;
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
                border-radius: 12px;
            }
            .header {
                text-align: center;
                padding: 20px 0;
                background-color: #007bff;
                color: #ffffff;
                border-radius: 12px 12px 0 0;
            }
            .content {
                padding: 30px;
                text-align: center;
                font-size: 16px;
            }
            .button {
                display: inline-block;
                padding: 12px 28px;
                margin: 20px 0;
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                background-color: #007bff;
                text-decoration: none;
                border-radius: 8px;
                border: none;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            }
            .button:hover {
                background-color: #0056b3;
            }
            .footer {
                text-align: center;
                padding: 20px 0;
                font-size: 12px;
                color: #777;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Bem-vindo ao PEDBOOK!</h1>
            </div>
            <div class="content">
                <p>Olá ${name},</p>
                <p>Obrigado por se cadastrar no PEDBOOK! Estamos felizes em tê-lo conosco. Explore os recursos e aproveite o melhor conteúdo para profissionais de saúde focado em pediatria.</p>
                <a href="https://pedbook.com.br" class="button">Explorar PEDBOOK</a>
                <p>Se você tiver alguma dúvida ou precisar de ajuda, não hesite em nos contatar.</p>
            </div>
            <div class="footer">
                <p>&copy; 2025 PEDBOOK - Plataforma para profissionais de saúde focada em pediatria.</p>
            </div>
        </div>
    </body>
    </html>
    `;

    const res = await fetch("https://api.resend.com/emails", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${RESEND_API_KEY}`,
      },
      body: JSON.stringify({
        from: "PEDBOOK <<EMAIL>>",
        to: [email],
        subject: "Bem-vindo ao PEDBOOK! 🎉",
        html: emailHtml,
      }),
    });

    if (res.ok) {
      const data = await res.json();
      return new Response(JSON.stringify(data), {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    } else {
      const error = await res.text();
      return new Response(JSON.stringify({ error }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }
};

serve(handler);