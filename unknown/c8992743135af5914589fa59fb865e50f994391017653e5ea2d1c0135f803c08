
import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { AlertTriangle, Check, X, AlertCircle, Info } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export const InteractionLegendDialog: React.FC = () => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="link" className="text-xs text-gray-500 dark:text-gray-400 h-auto p-0 underline">
          O que significa cada nível?
        </Button>
      </DialogTrigger>
      <DialogContent className={cn(
        "sm:max-w-md max-h-[85dvh] overflow-y-auto rounded-xl",
        "scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent",
        "w-[calc(100dvw-2rem)] max-w-[90dvw] sm:max-w-md"
      )}>
        <DialogHeader>
          <DialogTitle>Níveis de gravidade das interações</DialogTitle>
          <DialogDescription>
            Entenda o significado de cada classificação de interação medicamentosa
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-5 py-2 pr-1">
          <div className="flex items-start gap-3 p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
            <div className="bg-red-600 p-1.5 rounded-full mt-0.5 shadow-sm">
              <X className="h-4 w-4 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-red-700 dark:text-red-300">Contraindicado</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Combinação não recomendada. O uso conjunto desses medicamentos pode resultar em riscos graves à saúde ou afetar significativamente a eficácia do tratamento.
              </p>
            </div>
          </div>

          <div className="flex items-start gap-3 p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
            <div className="bg-purple-600 p-1.5 rounded-full mt-0.5 shadow-sm">
              <AlertTriangle className="h-4 w-4 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-purple-700 dark:text-purple-300">Grave</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Interação potencialmente perigosa que requer monitoramento rigoroso e pode exigir ajustes na dosagem ou horários de administração. Em alguns casos, alternativas terapêuticas devem ser consideradas.
              </p>
            </div>
          </div>

          <div className="flex items-start gap-3 p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
            <div className="bg-orange-500 p-1.5 rounded-full mt-0.5 shadow-sm">
              <AlertCircle className="h-4 w-4 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-orange-700 dark:text-orange-300">Moderado</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Interação clinicamente significativa que pode requerer monitoramento e possíveis ajustes de tratamento. Os benefícios geralmente superam os riscos com o acompanhamento adequado.
              </p>
            </div>
          </div>

          <div className="flex items-start gap-3 p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
            <div className="bg-green-500 p-1.5 rounded-full mt-0.5 shadow-sm">
              <Check className="h-4 w-4 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-green-700 dark:text-green-300">Leve/Sem interação</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Interação com mínimos efeitos clínicos ou sem interações relevantes documentadas. Geralmente não requer modificações no tratamento.
              </p>
            </div>
          </div>

          <div className="flex items-start gap-3 pt-3 mt-1 border-t border-gray-100 dark:border-gray-800 p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
            <div className="bg-blue-500 p-1.5 rounded-full mt-0.5 shadow-sm">
              <Info className="h-4 w-4 text-white" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Como médico, é seu papel avaliar e discernir essas informações de acordo com a condição específica do paciente e o contexto clínico.
              </p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
