import type { RawStudySession, StudySession, SessionStats } from '@/types/study-session-types';

export const transformRawSession = (rawSession: RawStudySession): StudySession => {
  // Ensure stats has the correct shape before casting
  const rawStats = rawSession.stats as any;
  const stats: SessionStats = {
    total_correct: rawStats.total_correct || 0,
    total_incorrect: rawStats.total_incorrect || 0,
    avg_response_time: rawStats.avg_response_time?.toString() || "0",
    by_specialty: rawStats.by_specialty || {},
    by_theme: rawStats.by_theme || {},
    by_focus: rawStats.by_focus || {}
  };

  return {
    ...rawSession,
    stats
  };
};

export const calculateSessionStats = (sessions: StudySession[]) => {
  const today = new Date().setHours(0, 0, 0, 0);
  let currentStreak = 0;
  let maxStreak = 0;

  if (sessions.length === 0) {
    return { currentStreak: 0, maxStreak: 0 };
  }

  // Obter datas únicas das sessões completadas e ordená-las em ordem decrescente
  const uniqueDates = [...new Set(
    sessions
      .filter(session => session.status === 'completed')
      .map(session => {
        const date = new Date(session.started_at);
        return date.setHours(0, 0, 0, 0);
      })
  )].sort((a, b) => b - a); // Ordenar em ordem decrescente

  if (uniqueDates.length === 0) {
    return { currentStreak: 0, maxStreak: 0 };
  }

  // Verificar se há atividade hoje
  const hasActivityToday = uniqueDates[0] === today;

  // Calcular a sequência atual
  if (hasActivityToday) {
    currentStreak = 1;
    for (let i = 1; i < uniqueDates.length; i++) {
      const expectedPreviousDay = new Date(uniqueDates[i - 1]);
      expectedPreviousDay.setDate(expectedPreviousDay.getDate() - 1);
      expectedPreviousDay.setHours(0, 0, 0, 0);

      if (uniqueDates[i] === expectedPreviousDay.getTime()) {
        currentStreak++;
      } else {
        break;
      }
    }
  } else {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);

    if (uniqueDates[0] === yesterday.getTime()) {
      currentStreak = 1;
      for (let i = 1; i < uniqueDates.length; i++) {
        const expectedPreviousDay = new Date(uniqueDates[i - 1]);
        expectedPreviousDay.setDate(expectedPreviousDay.getDate() - 1);
        expectedPreviousDay.setHours(0, 0, 0, 0);

        if (uniqueDates[i] === expectedPreviousDay.getTime()) {
          currentStreak++;
        } else {
          break;
        }
      }
    } else {
      currentStreak = 0;
    }
  }

  // Calcular a maior sequência histórica
  for (let startIndex = 0; startIndex < uniqueDates.length; startIndex++) {
    let tempStreak = 1;
    let currentDate = uniqueDates[startIndex];

    for (let i = startIndex + 1; i < uniqueDates.length; i++) {
      const nextDate = uniqueDates[i];
      const expectedDate = new Date(currentDate);
      expectedDate.setDate(expectedDate.getDate() - 1);
      expectedDate.setHours(0, 0, 0, 0);

      if (nextDate === expectedDate.getTime()) {
        tempStreak++;
        currentDate = nextDate;
      } else {
        break;
      }
    }

    if (tempStreak > maxStreak) {
      maxStreak = tempStreak;
    }
  }

  return {
    currentStreak,
    maxStreak
  };
};
