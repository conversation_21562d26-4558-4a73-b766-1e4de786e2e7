import React, { createContext, useContext, useState, useEffect } from 'react';

type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [theme, setTheme] = useState<Theme>(() => {
    // Verificar se estamos no navegador antes de acessar o localStorage
    if (typeof window !== 'undefined') {
      // Verificar se há uma preferência salva no localStorage
      const savedTheme = localStorage.getItem('theme');

      // Se não houver preferência salva, usar light como padrão
      return (savedTheme as Theme) || 'light';
    }

    // Padrão para o lado do servidor
    return 'light';
  });

  // Efeito para aplicar o tema ao elemento HTML root e salvar no localStorage
  useEffect(() => {
    // Verificar se estamos no navegador
    if (typeof window !== 'undefined') {
      const root = window.document.documentElement;

      // Aplicar classes CSS para o tema escolhido
      if (theme === 'dark') {
        root.classList.add('dark');
      } else {
        root.classList.remove('dark');
      }

      // Salvar a preferência do usuário no localStorage
      localStorage.setItem('theme', theme);
    }
  }, [theme]);

  // Função para alternar entre os temas
  const toggleTheme = () => {
    setTheme(prevTheme => {
      const newTheme = prevTheme === 'light' ? 'dark' : 'light';
      return newTheme;
    });
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
