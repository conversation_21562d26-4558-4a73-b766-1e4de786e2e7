import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";

interface PrescriptionSearchProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
}

export const PrescriptionSearch = ({ searchTerm, onSearchChange }: PrescriptionSearchProps) => {
  return (
    <div className="relative">
      <Search
        className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"
        size={18}
      />
      <Input
        type="search"
        placeholder="Pesquisar prescrições..."
        className="pl-10 bg-white/50 border-gray-200"
        value={searchTerm}
        onChange={(e) => onSearchChange(e.target.value)}
      />
    </div>
  );
};