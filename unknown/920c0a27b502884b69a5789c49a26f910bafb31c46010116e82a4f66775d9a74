import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface RelatedVaccine {
  id: string;
  doseNumber?: number;
  doseType?: string;
}

interface VaccineRelatedFormProps {
  vaccineId: string;
  vaccineName: string;
  isSelected: boolean;
  relatedVaccine: RelatedVaccine | undefined;
  onSelectionChange: (vaccineId: string, checked: boolean) => void;
  onDoseNumberChange: (vaccineId: string, doseNumber: number) => void;
  onDoseTypeChange: (vaccineId: string, doseType: string) => void;
}

export function VaccineRelatedForm({
  vaccineId,
  vaccineName,
  isSelected,
  relatedVaccine,
  onSelectionChange,
  onDoseNumberChange,
  onDoseTypeChange,
}: VaccineRelatedFormProps) {
  return (
    <div className="space-y-2">
      <div className="flex items-center space-x-2">
        <Checkbox
          id={vaccineId}
          checked={isSelected}
          onCheckedChange={(checked) => onSelectionChange(vaccineId, checked as boolean)}
        />
        <label
          htmlFor={vaccineId}
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          {vaccineName}
        </label>
      </div>
      {isSelected && (
        <div className="ml-6 space-y-4">
          <div>
            <Label htmlFor={`dose-${vaccineId}`}>Número da dose</Label>
            <Input
              id={`dose-${vaccineId}`}
              type="number"
              min="1"
              value={relatedVaccine?.doseNumber || ''}
              onChange={(e) => onDoseNumberChange(vaccineId, parseInt(e.target.value))}
              className="w-24"
              required
            />
          </div>
          <div>
            <Label htmlFor={`type-${vaccineId}`}>Tipo</Label>
            <Select
              value={relatedVaccine?.doseType || 'dose'}
              onValueChange={(value) => onDoseTypeChange(vaccineId, value)}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Selecione o tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="dose">Dose</SelectItem>
                <SelectItem value="reforço">Reforço</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}
    </div>
  );
}