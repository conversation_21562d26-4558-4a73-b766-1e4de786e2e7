import { ScrollArea } from "@/components/ui/scroll-area";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { MessageSquare } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";

interface FeedbackResponse {
  id: string;
  message: string;
  created_at: string;
  user: {
    full_name: string;
    is_admin: boolean;
  };
}

interface FeedbackResponseSectionProps {
  responses: FeedbackResponse[];
  feedbackId: string;
  isResolved: boolean;
  newResponse: string;
  onResponseChange: (value: string) => void;
  onSendResponse: () => void;
}

export function FeedbackResponseSection({
  responses,
  isResolved,
  newResponse,
  onResponseChange,
  onSendResponse
}: FeedbackResponseSectionProps) {
  return (
    <div className="space-y-4">
      <ScrollArea className="h-[300px] rounded-lg border bg-white p-4">
        <div className="space-y-4">
          {responses?.map((response, index) => (
            <motion.div
              key={response.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="group"
            >
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-primary/5 to-transparent rounded-lg transform -skew-x-3 group-hover:skew-x-0 transition-transform duration-300" />
                <div className="relative bg-white p-4 rounded-lg border border-primary/20 shadow-md transform hover:-translate-y-1 transition-all duration-300">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-semibold text-gray-900">{response.user.full_name}</span>
                    {response.user.is_admin && (
                      <Badge variant="default" className="bg-gradient-to-r from-primary to-primary/70 text-white font-medium">
                        Admin
                      </Badge>
                    )}
                  </div>
                  <p className="text-gray-800 leading-relaxed font-normal">{response.message}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </ScrollArea>

      {!isResolved ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-3"
        >
          <Textarea
            placeholder="Digite sua resposta..."
            value={newResponse}
            onChange={(e) => onResponseChange(e.target.value)}
            className="min-h-[100px] bg-white border-primary/20 focus:border-primary/40 focus:ring-primary/40 text-gray-800"
          />
          <Button
            onClick={onSendResponse}
            disabled={!newResponse?.trim()}
            className="w-full bg-gradient-to-r from-primary to-primary/70 hover:from-primary/90 hover:to-primary/60 text-white font-medium shadow-md transform hover:-translate-y-1 transition-all duration-300"
          >
            <MessageSquare className="h-4 w-4 mr-2" />
            Enviar Resposta
          </Button>
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="p-4 rounded-lg bg-gradient-to-r from-gray-100 to-gray-50 border border-gray-200 text-center text-gray-700 font-medium"
        >
          Este feedback foi marcado como resolvido e não aceita mais respostas.
        </motion.div>
      )}
    </div>
  );
}