import React, { useState } from 'react';
import { Progress } from "@/components/ui/progress";
import { ChevronDown, ChevronRight } from "lucide-react";

interface SkillTreeSectionProps {
  title: string;
  data: Record<string, { name: string; correct: number; total: number }>;
  icon: React.ReactNode;
  categories: Record<string, any>;
}

export const SkillTreeSection = ({ title, data, icon, categories }: SkillTreeSectionProps) => {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  if (!data || Object.keys(data).length === 0) return null;

  const calculateAccuracy = (correct: number, total: number) => {
    if (total === 0) return 0;
    const accuracy = (correct / total) * 100;
    console.log('📊 Calculando acurácia:', { correct, total, accuracy });
    return accuracy;
  };

  const toggleExpand = (id: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };

  // Build hierarchical structure
  const buildHierarchy = () => {
    const hierarchy: Record<string, any> = {};
    const rootItems: any[] = [];

    // First pass: create all nodes
    Object.entries(data).forEach(([id, stats]) => {
      const category = categories[id];
      if (!category) return;

      const node = {
        id,
        name: stats.name,
        correct: stats.correct,
        total: stats.total,
        children: [],
        parent_id: category.parent_id
      };

      hierarchy[id] = node;
    });

    // Second pass: build tree structure
    Object.values(hierarchy).forEach(node => {
      if (node.parent_id && hierarchy[node.parent_id]) {
        hierarchy[node.parent_id].children.push(node);
      } else {
        rootItems.push(node);
      }
    });

    return rootItems;
  };

  const renderNode = (node: any, level = 0) => {
    const hasChildren = node.children && node.children.length > 0;
    const isExpanded = expandedItems.has(node.id);
    const paddingLeft = `${level * 1.5}rem`;

    return (
      <div key={node.id} className="space-y-2">
        <div className="bg-secondary/10 p-3 rounded-lg" style={{ marginLeft: paddingLeft }}>
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              {hasChildren && (
                <button
                  onClick={() => toggleExpand(node.id)}
                  className="p-1 hover:bg-secondary/20 rounded-full"
                >
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </button>
              )}
              <span className="font-medium">{node.name}</span>
            </div>
            <span className="text-sm text-gray-600">
              {node.correct} de {node.total} ({calculateAccuracy(node.correct, node.total).toFixed(1)}%)
            </span>
          </div>
          <Progress value={calculateAccuracy(node.correct, node.total)} className="h-2" />
        </div>

        {hasChildren && isExpanded && (
          <div className="space-y-2">
            {node.children.map((child: any) => renderNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  const rootNodes = buildHierarchy();

  return (
    <div className="space-y-2">
      <h3 className="font-semibold text-lg flex items-center gap-2">
        {icon}
        {title}
      </h3>
      <div className="space-y-4">
        {rootNodes.map(node => renderNode(node))}
      </div>
    </div>
  );
};