import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { QuestionSolver } from "@/components/question/QuestionSolver";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import type { Question } from "@/types/question";
import { useDomain } from "@/hooks/useDomain";
import { Loader2 } from "lucide-react";
import PediatricStudy from "./PediatricStudy";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const Questions = () => {
  const { sessionId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { domain } = useDomain();
  const [questions, setQuestions] = useState<Question[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [userId, setUserId] = useState<string | null>(null);

  useEffect(() => {
    // Evitar execuções duplicadas
    if (!sessionId) {
      return;
    }

    // Evitar execução se já carregou questões para esta sessão
    if (questions.length > 0 && sessionId) {
      return;
    }

    const loadData = async () => {
      try {
        setIsLoading(true);

        // Verificar autenticação obrigatória
        const {
          data: { user },
          error: userError
        } = await supabase.auth.getUser();

        if (userError) {
          throw userError;
        }

        if (!user) {
          throw new Error("Usuário não autenticado");
        }

        setUserId(user.id);

        if (!sessionId) {
          setIsLoading(false);
          return;
        }

        // Buscar sessão do banco de dados
        const { data: session, error: sessionError } = await supabase
          .from("study_sessions")
          .select("*, knowledge_domain")
          .eq("id", sessionId)
          .maybeSingle();

        if (sessionError) {
          throw sessionError;
        }

        if (!session) {
          toast({
            title: "Sessão não encontrada",
            description: "A sessão que você está tentando acessar não existe",
            variant: "destructive",
          });
          navigate("/estudos");
          return;
        }



        if (!session.questions || session.questions.length === 0) {
          throw new Error("Nenhuma questão encontrada nesta sessão");
        }

        // Use the domain from the session if available, otherwise use "residencia"
        const queryDomain = session.knowledge_domain || "residencia";

        // Use RPC function to avoid URL length issues with large question lists
        let questionsData;
        let questionsError;

        if (session.questions.length > 100) {
          // Use RPC function for large lists
          const { data: rpcData, error: rpcError } = await supabase.rpc('get_questions_by_ids', {
            question_ids: session.questions,
            domain_filter: queryDomain
          });

          if (rpcError) {
            questionsError = rpcError;
          } else {
            // Transform RPC result to match expected format
            questionsData = rpcData?.map((q: any) => ({
              ...q,
              specialty: q.specialty_name ? { id: q.specialty_id, name: q.specialty_name } : null,
              theme: q.theme_name ? { id: q.theme_id, name: q.theme_name } : null,
              focus: q.focus_name ? { id: q.focus_id, name: q.focus_name } : null,
              location: q.location_name ? { id: q.exam_location, name: q.location_name } : null
            }));
          }
        } else {
          // Use regular query for smaller lists
          const { data, error } = await supabase
            .from("questions")
            .select(`
              *,
              specialty:study_categories!questions_specialty_id_fkey(id, name),
              theme:study_categories!questions_theme_id_fkey(id, name),
              focus:study_categories!questions_focus_id_fkey(id, name),
              location:exam_locations!questions_location_id_fkey(id, name)
            `)
            .in("id", session.questions)
            .eq("knowledge_domain", queryDomain);

          questionsData = data;
          questionsError = error;
        }

        if (questionsError) {
          throw questionsError;
        }

        if (!questionsData?.length) {
          throw new Error("Nenhuma questão encontrada para esta sessão");
        }

        // Transform questions for compatibility with Question type
        const transformedQuestions = questionsData.map((q) => {
          const imagesProcessed = processQuestionImages(q.media_attachments || q.images);

          return {
            id: q.id,
            statement: q.question_content || q.statement,
            question_content: q.question_content || q.statement,
            alternatives: Array.isArray(q.alternatives) && q.alternatives.length > 0
              ? q.alternatives.map((alt) => String(alt))
              : typeof q.alternatives === "object" && q.alternatives !== null
              ? Object.values(q.alternatives).map(String)
              : undefined,
            response_choices: Array.isArray(q.response_choices) && q.response_choices.length > 0
              ? q.response_choices.map((alt) => String(alt))
              : Array.isArray(q.alternatives) && q.alternatives.length > 0
              ? q.alternatives.map((alt) => String(alt))
              : [],
            correct_answer: parseInt(String(q.correct_choice || q.correct_answer)),
            correct_choice: parseInt(String(q.correct_choice || q.correct_answer)),
            answer_type: q.question_format || q.answer_type || 'ALTERNATIVAS',
            question_format: q.question_format || q.answer_type || 'ALTERNATIVAS',
            specialty: q.specialty,
            theme: q.theme,
            focus: q.focus,
            year: q.exam_year || q.year,
            exam_year: q.exam_year || q.year,
            location: q.location,
            institution: q.institutions?.[0]?.institution,
            statistics: Array.isArray(q.statistics) ? q.statistics.map(s => ({
              count: typeof s === 'number' ? s : 0,
              percentage: 0
            })) : [],
            alternativeComments:
              typeof q.alternative_comments === "object" &&
              q.alternative_comments !== null
                ? Object.entries(q.alternative_comments).reduce(
                    (acc, [key, value]) => ({
                      ...acc,
                      [parseInt(key)]: String(value),
                    }),
                    {}
                  )
                : {},
            comments: q.comments || [],
            owner: q.owner,
            likes: q.likes || 0,
            dislikes: q.dislikes || 0,
            liked_by: q.liked_by || [],
            disliked_by: q.disliked_by || [],
            created_at: q.created_at,
            domain: q.knowledge_domain || q.domain,
            images: imagesProcessed,
            media_attachments: imagesProcessed,
            question_type: q.assessment_type || q.question_type,
            assessment_type: q.assessment_type || q.question_type,
            question_number: q.question_number,
            ai_commentary: q.ai_commentary
          };
        });

        setQuestions(transformedQuestions);

      } catch (error: any) {
        toast({
          title: "Erro ao carregar questões",
          description: error.message || "Ocorreu um erro ao carregar as questões",
          variant: "destructive",
        });
        navigate("/estudos");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [sessionId]);

  const processQuestionImages = (images: any): string[] => {
    if (!images) return [];

    if (typeof images === 'string') {
      return [images];
    }

    if (Array.isArray(images)) {
      return images.map(img => typeof img === 'string' ? img : '').filter(Boolean);
    }

    // Se for um objeto JSON, tentar extrair URLs
    if (typeof images === 'object' && images !== null) {
      const values = Object.values(images);
      return values.filter(val => typeof val === 'string' && val.length > 0) as string[];
    }

    return [];
  };

  if (isLoading) {
    return (
      <>
        <Header />
        <div className="container mx-auto px-4 pt-16 md:py-8">
          <div className="flex flex-col items-center justify-center p-8 rounded-lg shadow-sm bg-white/30 backdrop-blur-sm">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <h2 className="text-xl font-semibold mb-2">Carregando questões...</h2>
            <p className="text-muted-foreground text-center">Aguarde enquanto preparamos suas questões de pediatria</p>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  if (!sessionId) {
    return <PediatricStudy />;
  }

  if (!questions.length) {
    return (
      <>
        <Header />
        <div className="container mx-auto px-4 pt-16 md:py-8">
          <h2 className="text-xl font-semibold mb-4">
            Nenhuma questão encontrada
          </h2>
          <Button onClick={() => navigate("/estudos")}>
            Voltar para Filtros
          </Button>
        </div>
        <Footer />
      </>
    );
  }

  return (
    <>
      <Header />
      <div className="pt-8 md:pt-12" style={{paddingRight:0,paddingLeft:0}}>
        {sessionId && userId && (
          <QuestionSolver
            questions={questions}
            sessionId={sessionId}
            userId={userId}
          />
        )}
      </div>
      <Footer />
    </>
  );
};

export default Questions;
