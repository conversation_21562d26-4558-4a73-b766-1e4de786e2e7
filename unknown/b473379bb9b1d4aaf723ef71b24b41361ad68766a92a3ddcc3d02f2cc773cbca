import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useQueryClient } from "@tanstack/react-query";
import { Upload, Loader2 } from "lucide-react";
import { Label } from "@/components/ui/label";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useForm } from "react-hook-form";

interface CategoryFormData {
  name: string;
  description: string;
  slug: string;
  coming_soon: boolean;
}

interface CategoryDialogProps {
  category?: {
    id: string;
    name: string;
    description: string | null;
    slug: string;
    image_url?: string | null;
    coming_soon?: boolean;
  };
  onOpenChange: (open: boolean) => void;
  open: boolean;
}

export function CategoryDialog({ category, onOpenChange, open }: CategoryDialogProps) {
  const queryClient = useQueryClient();
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(category?.image_url || null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<CategoryFormData>({
    defaultValues: {
      name: category?.name || "",
      description: category?.description || "",
      slug: category?.slug || "",
      coming_soon: category?.coming_soon || false,
    },
  });

  useEffect(() => {
    if (category) {
      form.reset({
        name: category.name,
        description: category.description || "",
        slug: category.slug,
        coming_soon: category.coming_soon || false,
      });
      setImagePreview(category.image_url || null);
      setImageFile(null);
    }
  }, [category, form]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadImage = async (categoryId: string): Promise<string | null> => {
    if (!imageFile) return null;

    const fileExt = imageFile.name.split('.').pop();
    const filePath = `${categoryId}.${fileExt}`;

    const { error: uploadError } = await supabase.storage
      .from('category-icons')
      .upload(filePath, imageFile, {
        upsert: true,
      });

    if (uploadError) throw uploadError;

    const { data } = supabase.storage
      .from('category-icons')
      .getPublicUrl(filePath);

    return data.publicUrl;
  };

  const onSubmit = async (data: CategoryFormData) => {
    try {
      setIsSubmitting(true);
      let image_url = category?.image_url;

      if (category?.id) {
        if (imageFile) {
          console.log('Uploading new image for category:', category.id);
          image_url = await uploadImage(category.id);
          console.log('New image URL:', image_url);
        }

        const { error } = await supabase
          .from('pedbook_conducts_categories')
          .update({
            name: data.name,
            description: data.description,
            slug: data.slug,
            image_url,
            coming_soon: data.coming_soon,
            updated_at: new Date().toISOString(),
          })
          .eq('id', category.id);

        if (error) throw error;
        toast({ title: "Categoria atualizada com sucesso" });
      } else {
        const { data: newCategory, error: insertError } = await supabase
          .from('pedbook_conducts_categories')
          .insert([{
            name: data.name,
            description: data.description,
            slug: data.slug,
            coming_soon: data.coming_soon,
          }])
          .select()
          .single();

        if (insertError) throw insertError;

        if (imageFile && newCategory) {
          image_url = await uploadImage(newCategory.id);
          const { error: updateError } = await supabase
            .from('pedbook_conducts_categories')
            .update({ image_url })
            .eq('id', newCategory.id);

          if (updateError) throw updateError;
        }

        toast({ title: "Categoria criada com sucesso" });
      }

      queryClient.invalidateQueries({ queryKey: ['conducts-categories'] });
      onOpenChange(false);
    } catch (error: any) {
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao salvar a categoria",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    try {
      if (!category?.id) return;

      console.log('Iniciando processo de exclusão da categoria:', category.id);

      // Primeiro, buscar todos os tópicos da categoria
      const { data: topics, error: fetchError } = await supabase
        .from('pedbook_conducts_topics')
        .select('id')
        .eq('category_id', category.id);

      if (fetchError) {
        console.error('Erro ao buscar tópicos:', fetchError);
        throw fetchError;
      }

      console.log('Tópicos encontrados:', topics);

      // Se existem tópicos, deletar todos eles primeiro
      if (topics && topics.length > 0) {
        const topicIds = topics.map(topic => topic.id);

        // Deletar os summaries dos tópicos
        const { error: summaryError } = await supabase
          .from('pedbook_conducts_summaries')
          .delete()
          .in('topic_id', topicIds);

        if (summaryError) {
          console.error('Erro ao deletar summaries:', summaryError);
          throw summaryError;
        }

        // Deletar o conteúdo dos tópicos
        const { error: contentError } = await supabase
          .from('pedbook_conducts_content')
          .delete()
          .in('topic_id', topicIds);

        if (contentError) {
          console.error('Erro ao deletar conteúdo:', contentError);
          throw contentError;
        }

        // Deletar os tópicos
        const { error: topicsError } = await supabase
          .from('pedbook_conducts_topics')
          .delete()
          .eq('category_id', category.id);

        if (topicsError) {
          console.error('Erro ao deletar tópicos:', topicsError);
          throw topicsError;
        }
      }

      // Finalmente, deletar a categoria
      const { error: categoryError } = await supabase
        .from('pedbook_conducts_categories')
        .delete()
        .eq('id', category.id);

      if (categoryError) throw categoryError;

      toast({ title: "Categoria removida com sucesso" });
      queryClient.invalidateQueries({ queryKey: ['conducts-categories'] });
      setShowDeleteDialog(false);
      onOpenChange(false);
    } catch (error: any) {
      console.error('Error:', error);
      toast({
        title: "Erro ao remover categoria",
        description: error.message || "Ocorreu um erro ao remover a categoria",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{category ? "Editar Categoria" : "Nova Categoria"}</DialogTitle>
          <DialogDescription>
            {category ? "Edite as informações da categoria abaixo:" : "Crie uma nova categoria"}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="slug"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Slug</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div>
              <Label htmlFor="image">Imagem</Label>
              <div className="mt-1 flex items-center gap-4">
                {imagePreview && (
                  <img
                    src={imagePreview}
                    alt="Category preview"
                    className="h-16 w-16 object-cover rounded-lg"
                  />
                )}
                <Label
                  htmlFor="image-upload"
                  className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Escolher arquivo
                </Label>
                <Input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="hidden"
                />
              </div>
            </div>
            <FormField
              control={form.control}
              name="coming_soon"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Em breve</FormLabel>
                    <p className="text-sm text-muted-foreground">
                      Marque esta opção para indicar que esta categoria estará disponível em breve
                    </p>
                  </div>
                </FormItem>
              )}
            />
            <div className="flex justify-end gap-2">
              {category && (
                <Button
                  type="button"
                  variant="destructive"
                  onClick={() => setShowDeleteDialog(true)}
                >
                  Excluir
                </Button>
              )}
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  category ? "Salvar" : "Criar"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
      {showDeleteDialog && (
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Excluir Categoria</AlertDialogTitle>
              <AlertDialogDescription>
                Tem certeza de que deseja excluir a categoria "{category?.name}"?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancelar</AlertDialogCancel>
              <AlertDialogAction onClick={handleDelete}>Excluir</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </Dialog>
  );
}
