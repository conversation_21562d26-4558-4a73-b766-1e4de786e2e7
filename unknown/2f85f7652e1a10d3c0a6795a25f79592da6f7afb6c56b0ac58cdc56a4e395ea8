import { useState, useEffect } from 'react';
import { useUser } from '@supabase/auth-helpers-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface CinematicPreferences {
  cinematicViewed: boolean | null;
  loading: boolean;
  error: string | null;
}

export const useCinematicPreferences = () => {
  const user = useUser();
  const { toast } = useToast();
  
  const [preferences, setPreferences] = useState<CinematicPreferences>({
    cinematicViewed: null,
    loading: true,
    error: null
  });

  // Buscar preferências do usuário
  const fetchPreferences = async () => {
    if (!user?.id) {
      setPreferences({
        cinematicViewed: null,
        loading: false,
        error: 'Usuário não autenticado'
      });
      return;
    }

    try {
      setPreferences(prev => ({ ...prev, loading: true, error: null }));

      const { data, error } = await supabase
        .from('user_preferences')
        .select('cinematic_viewed')
        .eq('user_id', user.id)
        .single();

      if (error) {
        // Se não existe registro, criar um novo
        if (error.code === 'PGRST116') {
          const { error: insertError } = await supabase
            .from('user_preferences')
            .insert({
              user_id: user.id,
              cinematic_viewed: false
            });

          if (insertError) {
            throw insertError;
          }

          setPreferences({
            cinematicViewed: false,
            loading: false,
            error: null
          });
        } else {
          throw error;
        }
      } else {
        setPreferences({
          cinematicViewed: data.cinematic_viewed ?? false,
          loading: false,
          error: null
        });
      }
    } catch (error) {
      console.error('Erro ao buscar preferências cinematográficas:', error);
      setPreferences({
        cinematicViewed: null,
        loading: false,
        error: 'Erro ao carregar preferências'
      });
    }
  };

  // Marcar como visualizado
  const markCinematicAsViewed = async () => {
    if (!user?.id) {
      toast({
        title: "Erro",
        description: "Usuário não autenticado",
        variant: "destructive"
      });
      return false;
    }

    try {
      const { error } = await supabase
        .from('user_preferences')
        .update({ cinematic_viewed: true })
        .eq('user_id', user.id);

      if (error) {
        throw error;
      }

      setPreferences(prev => ({
        ...prev,
        cinematicViewed: true
      }));

      return true;
    } catch (error) {
      console.error('Erro ao marcar cinematográfica como vista:', error);
      toast({
        title: "Erro",
        description: "Não foi possível salvar a preferência",
        variant: "destructive"
      });
      return false;
    }
  };

  // Resetar para mostrar novamente (útil para testes ou se usuário quiser ver novamente)
  const resetCinematicViewed = async () => {
    if (!user?.id) {
      toast({
        title: "Erro",
        description: "Usuário não autenticado",
        variant: "destructive"
      });
      return false;
    }

    try {
      const { error } = await supabase
        .from('user_preferences')
        .update({ cinematic_viewed: false })
        .eq('user_id', user.id);

      if (error) {
        throw error;
      }

      setPreferences(prev => ({
        ...prev,
        cinematicViewed: false
      }));

      toast({
        title: "Sucesso",
        description: "Experiência cinematográfica será mostrada novamente na próxima visita",
      });

      return true;
    } catch (error) {
      console.error('Erro ao resetar preferência cinematográfica:', error);
      toast({
        title: "Erro",
        description: "Não foi possível resetar a preferência",
        variant: "destructive"
      });
      return false;
    }
  };

  useEffect(() => {
    fetchPreferences();
  }, [user?.id]);

  return {
    ...preferences,
    markCinematicAsViewed,
    resetCinematicViewed,
    refetch: fetchPreferences
  };
};
