import HelmetWrapper from "@/components/utils/HelmetWrapper";

export const ChildcareMetaTags = () => {
  const pageTitle = "PedBook | Puericultura - Acompanhamento do Desenvolvimento Infantil";
  const pageDescription = "Ferramentas essenciais para acompanhamento do crescimento e desenvolvimento infantil. Acesse curvas de crescimento, calendário vacinal, marcos do desenvolvimento e mais.";
  const pageUrl = "https://pedb.com.br/puericultura";
  const imageUrl = "https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/puericultura.webp";
  const keywords = [
    "puericultura",
    "desenvolvimento infantil",
    "pediatria",
    "crescimento infantil",
    "vacinação infantil",
    "marcos do desenvolvimento",
    "saúde da criança",
    "acompanhamento pediátrico",
    "curvas de crescimento",
    "calendário vacinal",
    "nutrição infantil",
    "desenvolvimento neuropsicomotor"
  ].join(", ");

  return (
    <HelmetWrapper>
      <title>{pageTitle}</title>
      <meta name="description" content={pageDescription} />
      <meta name="keywords" content={keywords} />

      <meta name="robots" content="index, follow, max-image-preview:large" />
      <link rel="canonical" href={pageUrl} />

      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={pageDescription} />
      <meta property="og:type" content="article" />
      <meta property="og:url" content={pageUrl} />
      <meta property="og:image" content={imageUrl} />
      <meta property="og:image:alt" content="Ilustração sobre puericultura e desenvolvimento infantil" />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />

      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={pageDescription} />
      <meta name="twitter:image" content={imageUrl} />

      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "MedicalWebPage",
          "name": pageTitle,
          "description": pageDescription,
          "url": pageUrl,
          "image": imageUrl,
          "keywords": keywords.split(", "),
          "publisher": {
            "@type": "Organization",
            "name": "PedBook",
            "logo": {
              "@type": "ImageObject",
              "url": "https://pedb.com.br/logo.png"
            }
          },
          "specialty": "Pediatria",
          "audience": {
            "@type": "MedicalAudience",
            "audienceType": "Profissionais de Saúde"
          }
        })}
      </script>
    </HelmetWrapper>
  );
};
