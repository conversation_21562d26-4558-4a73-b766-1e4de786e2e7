import { Card } from "@/components/ui/card";

interface InstructionsCardProps {
  content: string;
}

export function InstructionsCard({ content }: InstructionsCardProps) {
  const formatMarkdown = (text: string) => {
    return text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\n/g, '<br>')
      .replace(/- /g, '• ');
  };

  return (
    <Card className="p-6 bg-white/80 backdrop-blur-sm border border-primary/20 shadow-lg animate-fade-in">
      <h3 className="text-xl font-semibold mb-4 bg-gradient-to-r from-primary to-primary/70 text-transparent bg-clip-text">
        Observações e Instruções
      </h3>
      <div 
        className="prose prose-blue max-w-none [&>*:first-child]:mt-0 [&>*:last-child]:mb-0"
        dangerouslySetInnerHTML={{ 
          __html: formatMarkdown(content)
        }}
      />
    </Card>
  );
}