import React from 'react';
import { useTheme } from '@/context/ThemeContext';
import { Moon, Sun } from 'lucide-react';
import { cn } from '@/lib/utils';

export const MobileThemeToggle: React.FC = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <div className="fixed left-3 bottom-20 z-50 sm:hidden">
      <button
        onClick={toggleTheme}
        className={cn(
          "w-9 h-9 rounded-full flex items-center justify-center transition-all duration-300",
          "focus:outline-none focus:ring-2 focus:ring-primary/20 dark:focus:ring-blue-500/30",
          theme === 'dark'
            ? "text-yellow-300 bg-slate-800/90 hover:bg-slate-700/90 shadow-md backdrop-blur-sm"
            : "text-primary bg-white/90 hover:bg-gray-100/90 shadow-md backdrop-blur-sm",
        )}
        aria-label={theme === 'dark' ? 'Alternar para modo claro' : 'Alternar para modo escuro'}
        title={theme === 'dark' ? 'Alternar para modo claro' : 'Alternar para modo escuro'}
      >
        {theme === 'dark' ? (
          <Sun className="h-4 w-4" />
        ) : (
          <Moon className="h-4 w-4" />
        )}
      </button>
    </div>
  );
};

export default MobileThemeToggle;
