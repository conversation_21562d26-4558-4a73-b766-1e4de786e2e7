import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

interface VaccineHeaderProps {
  ageInMonths: number;
}

export function VaccineHeader({ ageInMonths }: VaccineHeaderProps) {
  return (
    <Alert className="bg-yellow-50 border-yellow-200">
      <AlertCircle className="h-4 w-4 text-yellow-600" />
      <AlertTitle className="text-yellow-800">Vacinas Recomendadas</AlertTitle>
      <AlertDescription className="text-yellow-700">
        Com base na idade do paciente ({ageInMonths} meses)
      </AlertDescription>
    </Alert>
  );
}