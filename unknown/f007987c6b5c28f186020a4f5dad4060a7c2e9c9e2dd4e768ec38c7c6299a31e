
import { useState, useEffect } from "react";
import { Navigate, useNavigate } from "react-router-dom";
import { useAuth } from "@/hooks/useAuth";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Lock, ArrowLeft } from "lucide-react";
import AuthDialog from "@/components/auth/AuthDialog";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { getThemeClasses } from "@/components/ui/theme-utils";

interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
  showLoginPrompt?: boolean;
}

const ProtectedRoute = ({
  children,
  redirectTo = "/",
  showLoginPrompt = true
}: ProtectedRouteProps) => {
  const { user, isLoading } = useAuth();
  const navigate = useNavigate();
  const [showAuthDialog, setShowAuthDialog] = useState(false);

  // Remove o padding-bottom global quando estamos em uma rota protegida
  // pois o ProtectedRoute tem seu próprio layout completo
  useEffect(() => {
    const appContainer = document.querySelector('.pb-16');
    if (appContainer) {
      appContainer.classList.add('!pb-0');
    }

    // Cleanup: restaurar o padding quando sair da rota protegida
    return () => {
      const appContainer = document.querySelector('.pb-16');
      if (appContainer) {
        appContainer.classList.remove('!pb-0');
      }
    };
  }, []);

  // Mostrar um indicador de carregamento enquanto verificamos a autenticação
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        <p className="ml-4 text-muted-foreground">Verificando autenticação...</p>
      </div>
    );
  }

  // Se não estiver autenticado
  if (!user) {
    // Se não deve mostrar prompt de login, redirecionar diretamente
    if (!showLoginPrompt) {
      return <Navigate to={redirectTo} replace />;
    }

    // Mostrar tela de login amigável
    return (
      <div className={`${getThemeClasses.pageBackground()} min-h-screen flex flex-col`}>
        <Header />
        <main className="flex-1 container mx-auto px-4 py-8 flex items-center justify-center">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center">
                <Lock className="w-8 h-8 text-primary" />
              </div>
              <CardTitle className="text-xl">Acesso Restrito</CardTitle>
              <CardDescription>
                Esta página requer que você esteja logado para continuar.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={() => setShowAuthDialog(true)}
                className="w-full"
                size="lg"
              >
                Fazer Login
              </Button>
              <Button
                variant="outline"
                onClick={() => navigate("/")}
                className="w-full"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar ao Início
              </Button>
            </CardContent>
          </Card>
        </main>
        <Footer />

        <AuthDialog
          open={showAuthDialog}
          onOpenChange={setShowAuthDialog}
          hidden={true}
        />
      </div>
    );
  }

  return <>{children}</>;
};

export default ProtectedRoute;
