
import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Heading2, Heading3 } from "lucide-react";
import { Section, ContentStructure } from './types';
import { FAQBlock } from './FAQBlock';
import { v4 as uuidv4 } from 'uuid';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';

interface ContentEditorProps {
  initialContent?: ContentStructure;
  onChange: (content: ContentStructure) => void;
}

export function ContentEditor({ initialContent, onChange }: ContentEditorProps) {
  const [sections, setSections] = useState<Section[]>(initialContent?.sections || []);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const addSection = (type: 'title' | 'subtitle', parentId?: string) => (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const newSection: Section = {
      id: uuidv4(),
      type,
      content: '',
      textContent: '',
      children: [],
      order: sections.length,
      parentId
    };

    if (parentId) {
      setSections(prev => {
        const updated = [...prev];
        const parentIndex = updated.findIndex(s => s.id === parentId);
        if (parentIndex !== -1) {
          updated[parentIndex].children.push(newSection);
        }
        onChange({ sections: updated });
        return updated;
      });
    } else {
      setSections(prev => {
        const updated = [...prev, newSection];
        onChange({ sections: updated });
        return updated;
      });
    }
  };

  const formatSelectedAsTitle = () => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      return;
    }

    const selectedText = selection.toString().trim();
    if (!selectedText) {
      return;
    }

    const newSection: Section = {
      id: uuidv4(),
      type: 'title',
      content: `<strong>##. ${selectedText}</strong>`,
      textContent: selectedText,
      children: [],
      order: sections.length,
    };

    setSections(prev => {
      const updated = [...prev, newSection];
      onChange({ sections: updated });
      return updated;
    });
  };

  const formatSelectedAsSubtitle = () => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      return;
    }

    const selectedText = selection.toString().trim();
    if (!selectedText) {
      return;
    }

    const newSection: Section = {
      id: uuidv4(),
      type: 'subtitle',
      content: `<strong>##; ${selectedText}</strong>`,
      textContent: selectedText,
      children: [],
      order: sections.length,
    };

    setSections(prev => {
      const updated = [...prev, newSection];
      onChange({ sections: updated });
      return updated;
    });
  };

  const updateSection = (sectionId: string, value: string, field: 'content' | 'textContent') => {
    setSections(prev => {
      const updated = prev.map(section => {
        if (section.id === sectionId) {
          return { ...section, [field]: value };
        }
        return {
          ...section,
          children: section.children.map(child =>
            child.id === sectionId ? { ...child, [field]: value } : child
          )
        };
      });
      onChange({ sections: updated });
      return updated;
    });
  };

  const deleteSection = (sectionId: string) => {
    setSections(prev => {
      const updated = prev.filter(section => section.id !== sectionId).map(section => ({
        ...section,
        children: section.children.filter(child => child.id !== sectionId)
      }));
      onChange({ sections: updated });
      return updated;
    });
  };

  const moveSection = (sectionId: string, direction: 'up' | 'down') => {
    setSections(prev => {
      const index = prev.findIndex(s => s.id === sectionId);
      if (index === -1) return prev;

      const newIndex = direction === 'up' ? index - 1 : index + 1;
      if (newIndex < 0 || newIndex >= prev.length) return prev;

      const updated = arrayMove(prev, index, newIndex);
      onChange({ sections: updated });
      return updated;
    });
  };

  const handleDragEnd = (event: any) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      setSections((prev) => {
        const oldIndex = prev.findIndex((s) => s.id === active.id);
        const newIndex = prev.findIndex((s) => s.id === over.id);

        const updated = arrayMove(prev, oldIndex, newIndex);
        onChange({ sections: updated });
        return updated;
      });
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex gap-2 mb-4">
        <Button
          onClick={addSection('title')}
          variant="outline"
          size="sm"
          type="button"
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Adicionar Título
        </Button>

        <Button
          onClick={formatSelectedAsTitle}
          variant="outline"
          size="sm"
          type="button"
          className="flex items-center gap-2"
        >
          <Heading2 className="w-4 h-4" />
          Formatar como Título
        </Button>

        <Button
          onClick={formatSelectedAsSubtitle}
          variant="outline"
          size="sm"
          type="button"
          className="flex items-center gap-2"
        >
          <Heading3 className="w-4 h-4" />
          Formatar como Subtítulo
        </Button>
      </div>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext
          items={sections.map(s => s.id)}
          strategy={verticalListSortingStrategy}
        >
          <div className="space-y-4">
            {sections.map((section, index) => (
              <FAQBlock
                key={section.id}
                id={section.id}
                type={section.type}
                content={section.content}
                textContent={section.textContent}
                onUpdate={updateSection}
                onDelete={deleteSection}
                onAddSubsection={addSection('subtitle', section.id)}
                onMoveUp={() => moveSection(section.id, 'up')}
                onMoveDown={() => moveSection(section.id, 'down')}
                isFirst={index === 0}
                isLast={index === sections.length - 1}
              />
            ))}
          </div>
        </SortableContext>
      </DndContext>
    </div>
  );
}
