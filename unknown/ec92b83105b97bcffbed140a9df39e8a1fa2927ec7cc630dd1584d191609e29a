import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Message } from '@/types/chat';
import { useToast } from '@/hooks/use-toast';
import { removeMarkdown } from '@/lib/utils';
import { useAuth } from '@/hooks/useAuth';

interface Thread {
  id: string;
  title: string;
  lastMessage: string;
  createdAt: Date;
}

interface ChatMetadata {
  threadId: string;
  [key: string]: any;
}

export const useChatHistory = (threadId?: string) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [threads, setThreads] = useState<Thread[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  const loadThreads = useCallback(async () => {
    try {
      const { data: messagesData, error } = await supabase
        .from('secure_chat_history')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: true });

      if (error) throw error;

      const threadMap = new Map<string, Message[]>();
      messagesData.forEach((msg) => {
        const metadata = msg.metadata as ChatMetadata;
        const threadId = metadata?.threadId;
        if (!threadId) return;
        const threadMessages = threadMap.get(threadId) || [];
        threadMessages.push({
          role: msg.role as 'user' | 'assistant',
          content: msg.content,
          timestamp: new Date(msg.created_at),
          image_url: metadata?.image_url ? metadata.image_url : undefined,
          file_url: typeof metadata?.file_url === "string" ? metadata.file_url : undefined,
          file_type: typeof metadata?.file_type === "string" ? metadata.file_type : undefined,
          threadId: threadId, // Adicionar o threadId
        });
        threadMap.set(threadId, threadMessages);
      });

      const threadList: Thread[] = Array.from(threadMap.entries()).map(([id, messages]) => {
        const lastMessage = messages[messages.length - 1];
        const firstUserMessage = messages.find(m => m.role === 'user');
        const title = firstUserMessage
          ? removeMarkdown(firstUserMessage.content.slice(0, 50))
          : 'Nova conversa';

        return {
          id,
          title,
          lastMessage: lastMessage ? lastMessage.content : '',
          createdAt: messages[0].timestamp,
        };
      });

      threadList.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
      setThreads(threadList);
    } catch (error) {
      toast({
        title: "Erro ao carregar conversas",
        description: "Ocorreu um erro ao carregar o histórico de conversas.",
        variant: "destructive",
      });
    }
  }, [toast, user?.id]);

  const deleteAllThreads = async () => {
    if (!user?.id) return;

    try {
      const { error } = await supabase
        .from('pedbook_chat_history')
        .delete()
        .eq('user_id', user.id);

      if (error) throw error;

      setThreads([]);
      setMessages([]);

      toast({
        title: "Conversas excluídas",
        description: "Todas as conversas foram excluídas com sucesso.",
      });
    } catch (error) {
      toast({
        title: "Erro ao excluir conversas",
        description: "Ocorreu um erro ao excluir as conversas.",
        variant: "destructive",
      });
    }
  };

  const loadMessages = useCallback(async (threadId: string) => {
    try {
      const { data: messagesData, error } = await supabase
        .from('secure_chat_history')
        .select('*')
        .eq('metadata->>threadId', threadId)
        .eq('user_id', user?.id)
        .order('created_at', { ascending: true });

      if (error) throw error;

      const formattedMessages = messagesData.map((msg): Message => {
        const metadata = msg.metadata as ChatMetadata;
        return {
          role: msg.role as 'user' | 'assistant',
          content: msg.content,
          timestamp: new Date(msg.created_at),
          image_url: metadata?.image_url ? metadata.image_url : undefined,
          file_url: typeof metadata?.file_url === "string" ? metadata.file_url : undefined,
          file_type: typeof metadata?.file_type === "string" ? metadata.file_type : undefined,
          threadId: threadId, // Adicionar o threadId
        };
      });

      setMessages(formattedMessages);
    } catch (error) {
      toast({
        title: "Erro ao carregar mensagens",
        description: "Ocorreu um erro ao carregar as mensagens da conversa.",
        variant: "destructive",
      });
    }
  }, [toast, user?.id]);

  const saveMessage = async (message: Message, threadId: string) => {
    // Only set loading for assistant messages (AI responses)
    if (message.role === 'assistant') {
      setIsLoading(true);
    }

    // Criar uma cópia da mensagem para garantir que ela não seja modificada
    const messageCopy = { ...message };

    // Update messages state immediately for better UI responsiveness
    // Usamos uma função para garantir que estamos trabalhando com o estado mais recente
    setMessages(prev => {
      // Verificar se a mensagem já existe no array para evitar duplicatas
      const messageExists = prev.some(
        m => m.role === messageCopy.role &&
             m.content === messageCopy.content &&
             Math.abs(m.timestamp.getTime() - messageCopy.timestamp.getTime()) < 1000
      );

      if (messageExists) {
        return prev;
      }

      return [...prev, messageCopy];
    });

    try {
      const metadata: ChatMetadata & { image_url?: string; file_url?: string; file_type?: string } = { threadId };
      if (message.image_url) metadata.image_url = message.image_url;
      if (message.file_url) metadata.file_url = message.file_url;
      if (message.file_type) metadata.file_type = message.file_type;

      const { error } = await supabase
        .from('pedbook_chat_history')
        .insert({
          user_id: user?.id,
          role: message.role,
          content: message.content,
          metadata: metadata,
        });

      if (error) throw error;

      // Refresh thread list after saving
      await loadThreads();

      // Atualizar a lista de threads, mas não recarregar as mensagens
      // para evitar o efeito de piscar na interface
      // await loadMessages(threadId);
    } catch (error) {
      toast({
        title: "Erro ao salvar mensagem",
        description: "Ocorreu um erro ao salvar sua mensagem.",
        variant: "destructive",
      });
    } finally {
      if (message.role === 'assistant') {
        setIsLoading(false);
      }
    }
  };

  const createNewThread = () => {
    const newThread = {
      id: crypto.randomUUID(),
      title: 'Nova conversa',
      lastMessage: '',
      createdAt: new Date(),
    };

    // Update threads state immediately
    setThreads(prev => [newThread, ...prev]);

    // Clear messages for the new thread
    setMessages([]);

    // Set loading to false to ensure UI is responsive
    setIsLoading(false);

    // Forçar uma atualização do estado para garantir que a UI reflita a mudança
    setTimeout(() => {
      // Este setTimeout garante que a atualização do estado seja processada
      // após a conclusão do ciclo de renderização atual
    }, 0);

    return newThread;
  };

  const renameThread = async (threadId: string, newTitle: string) => {
    setThreads(prev =>
      prev.map(thread =>
        thread.id === threadId
          ? { ...thread, title: newTitle }
          : thread
      )
    );
  };

  const deleteThread = async (threadId: string) => {
    try {
      const { error } = await supabase
        .from('pedbook_chat_history')
        .delete()
        .eq('metadata->>threadId', threadId)
        .eq('user_id', user?.id);

      if (error) throw error;

      setThreads(prev => prev.filter(thread => thread.id !== threadId));
      if (threadId === threadId) {
        setMessages([]);
      }
    } catch (error) {
      toast({
        title: "Erro ao excluir conversa",
        description: "Ocorreu um erro ao excluir a conversa.",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    if (user?.id) {
      loadThreads();
    }
  }, [loadThreads, user?.id]);

  useEffect(() => {
    if (threadId && user?.id) {
      loadMessages(threadId);
    } else {
      setMessages([]);
    }
  }, [threadId, loadMessages, user?.id]);

  return {
    messages,
    setMessages, // Exportando a função setMessages
    threads,
    isLoading,
    setIsLoading, // Exportando a função setIsLoading
    saveMessage,
    createNewThread,
    renameThread,
    deleteThread,
    deleteAllThreads,
  };
};
