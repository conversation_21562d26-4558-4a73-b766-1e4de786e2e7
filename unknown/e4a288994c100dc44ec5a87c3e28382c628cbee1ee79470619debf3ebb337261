import React from "react";
import { Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { NoteDialog } from "./NoteDialog";
import { NoteForm } from "./NoteForm";

interface CreateNoteButtonProps {
  folderId?: string | null;
}

export const CreateNoteButton: React.FC<CreateNoteButtonProps> = ({ folderId }) => {
  const [open, setOpen] = React.useState(false);

  return (
    <>
      <Button onClick={() => setOpen(true)} className="gap-2">
        <Plus className="h-4 w-4" />
        Nova Anotação
      </Button>

      <NoteForm
        isOpen={open}
        onClose={() => setOpen(false)}
        initialFolderId={folderId || ""}
      />
    </>
  );
};