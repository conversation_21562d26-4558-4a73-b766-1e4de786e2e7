
/**
 * Formats age in months to a human readable string (years and months)
 * @param ageInMonths Age in months
 * @returns Formatted age string
 */
export function formatRelativeAge(ageInMonths: number): string {
  console.log(`🕒 Formatando idade: ${ageInMonths} meses`);
  
  const years = Math.floor(ageInMonths / 12);
  const months = ageInMonths % 12;
  
  if (years === 0) {
    return `${months} ${months === 1 ? 'mês' : 'meses'}`;
  } else if (months === 0) {
    return `${years} ${years === 1 ? 'ano' : 'anos'}`;
  } else {
    return `${years} ${years === 1 ? 'ano' : 'anos'} e ${months} ${months === 1 ? 'mês' : 'meses'}`;
  }
}
