
import { supabase } from "@/integrations/supabase/client";
import { formatNumberPtBR } from "./formatNumber";

export interface DosageResult {
  text: string;
  restrictions?: {
    type: "age" | "weight" | "both";
    minAge?: number;
    minWeight?: number;
  };
}

export const calculateDosage = async (
  template: string,
  weight: number,
  age: number,
  medicationId: string,
  providedTags?: any[] // Aceitar tags como parâmetro opcional
): Promise<DosageResult> => {
  if (!template) return { text: "" };
  let result = template;
  let allZeroValues = true;

  try {
    let tags = providedTags;

    // Se tags não foram fornecidas, buscar do banco
    if (!tags) {
      const { data: fetchedTags, error } = await supabase
        .from("pedbook_medication_tags")
        .select("name, multiplier, max_value, type, start_month, end_month, start_weight, end_weight, round_result")
        .eq("medication_id", medicationId)
        .eq("is_user_medication", false);

      if (error) {
        console.error('❌ [dosageCalculator] Error fetching medication tags:', error);
        throw error;
      }
      tags = fetchedTags || [];
    }

    // Detect restrictions
    let minAgeRestriction: number | undefined;
    let minWeightRestriction: number | undefined;

    if (tags && tags.length > 0) {
      // Find the minimum start_month and start_weight across all relevant tags
      const ageBasedTags = tags.filter(tag => 
        (tag.type === 'age' || tag.type === 'multiplier_by_fixed_age') && 
        tag.start_month !== null && 
        tag.start_month > 0
      );
      
      const weightBasedTags = tags.filter(tag => 
        tag.type === 'fixed_by_weight' && 
        tag.start_weight !== null && 
        tag.start_weight > 0
      );
      
      // Find the lowest start_month (minimum age)
      if (ageBasedTags.length > 0) {
        minAgeRestriction = Math.min(...ageBasedTags.map(tag => tag.start_month || Infinity));
        
        // Check if the current age is less than the minimum required
        if (age < minAgeRestriction) {
          allZeroValues = true; // Force restriction message
        }
      }
      
      // Find the lowest start_weight (minimum weight)
      if (weightBasedTags.length > 0) {
        minWeightRestriction = Math.min(...weightBasedTags.map(tag => tag.start_weight || Infinity));
        
        // Check if the current weight is less than the minimum required
        if (weight < minWeightRestriction) {
          allZeroValues = true; // Force restriction message
        }
      }

      // Mapeamento de nomes de tags para seus valores calculados
      const tagValues: Map<string, number> = new Map();
      
      // Primeiro passo: processar todas as tags e calcular seus valores
      for (const tag of tags) {
        // Calcular o valor com base no tipo de tag
        let calculatedValue: number | undefined = undefined;
        
        if (tag.type === 'fixed_by_weight') {
          // Para tags do tipo 'fixed_by_weight', encontramos o valor fixo baseado no range de peso
          const matchingTag = tags.find(t => 
            t.name === tag.name && 
            t.type === 'fixed_by_weight' &&
            weight >= (t.start_weight || 0) && 
            weight <= (t.end_weight || Infinity)
          );
          
          if (matchingTag) {
            calculatedValue = matchingTag.multiplier || 0;
            if (calculatedValue > 0) allZeroValues = false;
            
            if (matchingTag.round_result) {
              calculatedValue = Math.round(calculatedValue);
            }
          } else {
            // Se não houver correspondência, definir como 0
            calculatedValue = 0;
          }
        } else if (tag.type === 'age') {
          // Para tags do tipo 'age', verificamos se a idade está dentro do intervalo
          const matchingTag = tags.find(t => 
            t.name === tag.name && 
            t.type === 'age' &&
            age >= (t.start_month || 0) && 
            age <= (t.end_month || Infinity)
          );
          
          if (matchingTag) {
            calculatedValue = matchingTag.multiplier || 0;
            if (calculatedValue > 0) allZeroValues = false;
            
            if (matchingTag.round_result) {
              calculatedValue = Math.round(calculatedValue);
            }
          } else {
            // Se não houver correspondência, definir como 0
            calculatedValue = 0;
          }
        } else if (tag.type === 'multiplier_by_fixed_age') {
          // Para tags do tipo 'multiplier_by_fixed_age', multiplicamos pelo peso se a idade estiver no intervalo
          const matchingTag = tags.find(t => 
            t.name === tag.name && 
            t.type === 'multiplier_by_fixed_age' &&
            age >= (t.start_month || 0) && 
            age <= (t.end_month || Infinity)
          );
          
          if (matchingTag) {
            calculatedValue = weight * (matchingTag.multiplier || 0);
            if (calculatedValue > 0) allZeroValues = false;
            
            // Aplicar valor máximo se definido
            if (matchingTag.max_value && calculatedValue > matchingTag.max_value) {
              calculatedValue = matchingTag.max_value;
            }
            
            if (matchingTag.round_result) {
              calculatedValue = Math.round(calculatedValue);
            }
          } else {
            // Se não houver correspondência, definir como 0
            calculatedValue = 0;
          }
        } else if (tag.type === 'fixed') {
          // Para tags do tipo 'fixed', simplesmente usamos o valor do multiplicador
          calculatedValue = tag.multiplier || 0;
          if (calculatedValue > 0) allZeroValues = false;
          
          if (tag.round_result) {
            calculatedValue = Math.round(calculatedValue);
          }
        } else {
          // Tipo 'multiplier' padrão
          const multiplier = tag.multiplier || 0;
          calculatedValue = weight * multiplier;
          if (calculatedValue > 0) allZeroValues = false;
          
          if (tag.max_value && calculatedValue > tag.max_value) {
            calculatedValue = tag.max_value;
          }
          
          if (tag.round_result) {
            calculatedValue = Math.round(calculatedValue);
          }
        }
        
        // Armazenar o valor calculado para esta tag
        if (calculatedValue !== undefined) {
          const existingValue = tagValues.get(tag.name);
          
          // Priorizar valores não-zero quando houver conflito de tags
          if (existingValue === undefined || 
              (calculatedValue !== 0 && existingValue === 0) ||
              (existingValue === 0 && calculatedValue === 0)) {
            tagValues.set(tag.name, calculatedValue);
          }
        }
      }
      
      // Segundo passo: substituir todas as tags no template pelos valores calculados
      for (const [tagName, value] of tagValues.entries()) {
        const tagPattern = new RegExp(`\\(\\(${tagName}\\)\\)`, 'g');

        let formattedValue;
        if (value === 0) {
          formattedValue = "0";
        } else if (value > 0 && value < 0.001) {
          formattedValue = value.toFixed(4).replace('.', ',');
        } else if (value > 0 && value < 0.01) {
          formattedValue = value.toFixed(3).replace('.', ',');
        } else {
          formattedValue = formatNumberPtBR(value);
        }

        result = result.replace(tagPattern, formattedValue);
      }

      // Garantir que todas as tags sejam substituídas, mesmo se não tiverem valores calculados
      const remainingTagsPattern = /\(\(([^)]+)\)\)/g;
      const remainingTags = result.match(remainingTagsPattern);
      if (remainingTags) {
        console.warn('⚠️ [dosageCalculator] Found unreplaced tags:', remainingTags, 'in template for medication:', medicationId);
      }
      result = result.replace(remainingTagsPattern, "0");
    }

    const finalResult = result
      .replace("{weight}", formatNumberPtBR(weight))
      .replace("{age}", age.toString());

    const dosageResult: DosageResult = {
      text: finalResult
    };

    // Se todos os valores são zero e temos restrições, adicione-as ao resultado
    if (allZeroValues && (minAgeRestriction || minWeightRestriction)) {
      if (minAgeRestriction && minWeightRestriction) {
        dosageResult.restrictions = {
          type: "both",
          minAge: minAgeRestriction,
          minWeight: minWeightRestriction
        };
      } else if (minAgeRestriction) {
        dosageResult.restrictions = {
          type: "age",
          minAge: minAgeRestriction
        };
      } else if (minWeightRestriction) {
        dosageResult.restrictions = {
          type: "weight",
          minWeight: minWeightRestriction
        };
      }
    }

    return dosageResult;
  } catch (error) {
    return { text: template };
  }
};
