import { <PERSON>, <PERSON>, Target } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

interface FlashcardStatsProps {
  totalCards: number;
  correctAnswers: number;
  timeSpent: number;
}

export const FlashcardStats = ({ totalCards, correctAnswers, timeSpent }: FlashcardStatsProps) => {
  const accuracy = totalCards > 0 ? Math.round((correctAnswers / totalCards) * 100) : 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col items-center text-center space-y-2">
            <div className="p-3 bg-primary/10 rounded-full">
              <Brain className="w-6 h-6 text-primary" />
            </div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Total de Flashcards
            </h3>
            <p className="text-3xl font-bold">{totalCards}</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col items-center text-center space-y-2">
            <div className="p-3 bg-green-100 rounded-full">
              <Target className="w-6 h-6 text-green-600" />
            </div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Taxa de Acerto
            </h3>
            <p className="text-3xl font-bold">{accuracy}%</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col items-center text-center space-y-2">
            <div className="p-3 bg-blue-100 rounded-full">
              <Clock className="w-6 h-6 text-blue-600" />
            </div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Tempo Total de Estudo
            </h3>
            <p className="text-3xl font-bold">{timeSpent} min</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};