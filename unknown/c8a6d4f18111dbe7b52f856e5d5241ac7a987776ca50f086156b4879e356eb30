
import React from 'react';
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Plus, Trash2 } from "lucide-react";
import { Section } from './types';

interface SectionEditorProps {
  section: Section;
  onUpdate: (id: string, content: string) => void;
  onDelete: (id: string) => void;
  onAddSubsection: () => void;
}

export function SectionEditor({ section, onUpdate, onDelete, onAddSubsection }: SectionEditorProps) {
  return (
    <Card className="p-4">
      <div className="space-y-4">
        <div className="flex items-start gap-4">
          <div className="flex-1">
            <Textarea
              value={section.content}
              onChange={(e) => onUpdate(section.id, e.target.value)}
              placeholder={section.type === 'title' ? 'Digite o título...' : 'Digite o subtítulo...'}
              className="resize-none"
            />
          </div>
          <div className="flex gap-2">
            {section.type === 'title' && (
              <Button
                onClick={onAddSubsection}
                variant="outline"
                size="icon"
                className="h-8 w-8"
              >
                <Plus className="h-4 w-4" />
              </Button>
            )}
            <Button
              onClick={() => onDelete(section.id)}
              variant="destructive"
              size="icon"
              className="h-8 w-8"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {section.children && section.children.length > 0 && (
          <div className="pl-6 space-y-4">
            {section.children.map((child) => (
              <SectionEditor
                key={child.id}
                section={child}
                onUpdate={onUpdate}
                onDelete={onDelete}
                onAddSubsection={onAddSubsection}
              />
            ))}
          </div>
        )}
      </div>
    </Card>
  );
}
