import { Button } from "@/components/ui/button";

interface PrescriptionFormActionsProps {
  isEditing: boolean;
  onCancel?: () => void;
}

export function PrescriptionFormActions({ isEditing, onCancel }: PrescriptionFormActionsProps) {
  return (
    <div className="flex justify-end gap-2">
      <Button type="button" variant="outline" onClick={onCancel}>
        Cancelar
      </Button>
      <Button type="submit">
        {isEditing ? "Atualizar Prescrição" : "Criar Prescrição"}
      </Button>
    </div>
  );
}