
import React, { useState, useEffect } from "react";
import { PoisoningPatientInput } from "./PoisoningPatientInput";
import { Card } from "@/components/ui/card";
import { Toxidrome } from "@/data/toxidromes";
import { Button } from "@/components/ui/button";
import { Calculator } from "lucide-react";

interface DoseCalculatorProps {
  toxidrome: Toxidrome;
  onCalculate: (dose: string) => void;
}

export const DoseCalculator: React.FC<DoseCalculatorProps> = ({ toxidrome, onCalculate }) => {
  const [weight, setWeight] = useState<number | null>(null);
  const [ageInMonths, setAgeInMonths] = useState<number | null>(null);
  const [calculatedDose, setCalculatedDose] = useState<string>("");

  const handleCalculate = () => {
    let dose = "";

    switch (toxidrome.id) {
      case "benzodiazepinicos":
        dose = calculateFlumazenilDose(weight);
        break;
      case "opioides":
        dose = calculateNaloxoneDose(weight, ageInMonths);
        break;
      case "anticolinergicos":
        dose = calculatePhysostigmineDose(weight);
        break;
      case "paracetamol":
        dose = calculateNAcetylcysteineDose(weight);
        break;
      case "betabloqueadores":
        dose = calculateGlucagonDose(weight);
        break;
      case "metemoglobinemia":
        dose = calculateMethyleneBlue(weight);
        break;
      case "colinergicos":
        dose = calculateAtropinePralidoxime(weight);
        break;
      case "antidepressivos_triciclicos":
        dose = calculateSodiumBicarbonate(weight);
        break;
      default:
        dose = "Não há cálculo de dose disponível para este antídoto.";
    }

    setCalculatedDose(dose);
    onCalculate(dose);
  };

  // Funções de cálculo para cada antídoto
  const calculateFlumazenilDose = (weight: number | null): string => {
    if (!weight) return "Informe o peso do paciente para calcular a dose.";
    
    return `Flumazenil (Lanexat®):\n\n` +
           `Dose inicial: ${(0.01 * weight).toFixed(2)} mg IV lento (0,01 mg/kg)\n` +
           `Dose máxima inicial: 0,2 mg\n\n` +
           `Se necessário, repetir a cada minuto até dose máxima acumulada de ${(0.05 * weight).toFixed(2)} mg (0,05 mg/kg) ou 1 mg (o que for menor).\n\n` +
           `Manutenção: Infusão de 0,1-0,4 mg/hora se necessário.`;
  };

  const calculateNaloxoneDose = (weight: number | null, ageInMonths: number | null): string => {
    if (!weight) return "Informe o peso do paciente para calcular a dose.";
    
    let doseText = `Naloxona (Narcan®):\n\n`;
    
    // Ajuste para pacientes por idade
    if (ageInMonths !== null) {
      if (ageInMonths < 1) {
        doseText += `Neonato (< 1 mês): 0,01 mg/kg IV/IM/SC/ET\n`;
      } else if (ageInMonths < 60) { // < 5 anos
        doseText += `Criança (1 mês - 5 anos): 0,1 mg/kg IV/IM/SC/ET\n`;
      } else {
        doseText += `Criança (> 5 anos): 2 mg IV/IM/SC/ET\n`;
      }
    }
    
    // Cálculo baseado no peso (usado se idade não estiver disponível ou como informação adicional)
    doseText += `Dose calculada por peso: ${(0.1 * weight).toFixed(2)} mg IV/IM/SC/ET (0,1 mg/kg)\n` +
                `Dose máxima inicial: 2 mg\n\n` +
                `Se necessário, repetir a cada 2-3 minutos.\n` +
                `Em caso de resposta inadequada, considerar aumentar dose para ${(0.2 * weight).toFixed(2)} mg (0,2 mg/kg).`;
    
    return doseText;
  };

  const calculatePhysostigmineDose = (weight: number | null): string => {
    if (!weight) return "Informe o peso do paciente para calcular a dose.";
    
    return `Fisostigmina (Antilirium®):\n\n` +
           `Dose: ${(0.02 * weight).toFixed(2)} mg/kg IV lento (0,02 mg/kg)\n` +
           `Dose máxima: 0,5 mg em crianças menores de 12 anos e 2 mg em maiores de 12 anos\n\n` +
           `Administrar lentamente, não excedendo 0,5 mg/min.\n` +
           `Se necessário, repetir a cada 5-10 minutos até resolução dos sintomas ou desenvolvimento de efeitos colinérgicos.`;
  };

  const calculateNAcetylcysteineDose = (weight: number | null): string => {
    if (!weight) return "Informe o peso do paciente para calcular a dose.";
    
    const loadingDose = (150 * weight).toFixed(0);
    const secondDose = (50 * weight).toFixed(0);
    const thirdDose = (100 * weight).toFixed(0);
    
    return `N-Acetilcisteína (NAC, Fluimucil®):\n\n` +
           `Protocolo IV (21 horas):\n` +
           `1ª dose: ${loadingDose} mg (150 mg/kg) em 200 mL SG5% por 1 hora\n` +
           `2ª dose: ${secondDose} mg (50 mg/kg) em 500 mL SG5% por 4 horas\n` +
           `3ª dose: ${thirdDose} mg (100 mg/kg) em 1000 mL SG5% por 16 horas\n\n` +
           `Protocolo VO (72 horas):\n` +
           `Dose de ataque: ${loadingDose} mg (150 mg/kg)\n` +
           `Manutenção: ${(70 * weight).toFixed(0)} mg (70 mg/kg) a cada 4 horas por 17 doses`;
  };

  const calculateGlucagonDose = (weight: number | null): string => {
    if (!weight) return "Informe o peso do paciente para calcular a dose.";
    
    const bolusMin = (0.03 * weight).toFixed(2);
    const bolusMax = (0.1 * weight).toFixed(2);
    
    return `Glucagon:\n\n` +
           `Dose em bolus: ${bolusMin} - ${bolusMax} mg/kg IV (0,03-0,1 mg/kg)\n` +
           `Dose máxima: 1 mg para crianças ≤ 25 kg, 5 mg para > 25 kg\n\n` +
           `Infusão contínua: ${(0.07 * weight).toFixed(2)} mg/kg/hora\n` +
           `Titular conforme resposta clínica até máximo de 0,1 mg/kg/hora`;
  };

  const calculateMethyleneBlue = (weight: number | null): string => {
    if (!weight) return "Informe o peso do paciente para calcular a dose.";
    
    return `Azul de Metileno:\n\n` +
           `Dose: ${(1 * weight).toFixed(0)} - ${(2 * weight).toFixed(0)} mg IV lento (1-2 mg/kg)\n` +
           `Diluir em 100 mL de SG5% e administrar em 15-30 minutos\n\n` +
           `Pode ser repetido em 1 hora se os sintomas persistirem e a metemoglobinemia permanecer > 30%\n` +
           `Dose máxima acumulada: 7 mg/kg`;
  };

  const calculateAtropinePralidoxime = (weight: number | null): string => {
    if (!weight) return "Informe o peso do paciente para calcular a dose.";
    
    return `Atropina e Pralidoxima:\n\n` +
           `Atropina:\n` +
           `Dose inicial: ${(0.02 * weight).toFixed(2)} - ${(0.05 * weight).toFixed(2)} mg IV (0,02-0,05 mg/kg)\n` +
           `Dose mínima: 0,1 mg, Dose máxima: 5 mg\n` +
           `Repetir a cada 5-10 minutos até atropinização (secreções secas, midríase)\n\n` +
           `Pralidoxima (para organofosforados):\n` +
           `Dose de ataque: ${(25 * weight).toFixed(0)} - ${(50 * weight).toFixed(0)} mg IV lento (25-50 mg/kg)\n` +
           `Infusão contínua: ${(10 * weight).toFixed(0)} - ${(20 * weight).toFixed(0)} mg/kg/hora`;
  };

  const calculateSodiumBicarbonate = (weight: number | null): string => {
    if (!weight) return "Informe o peso do paciente para calcular a dose.";
    
    return `Bicarbonato de Sódio 8,4%:\n\n` +
           `Dose inicial: ${(1 * weight).toFixed(0)} - ${(2 * weight).toFixed(0)} mEq IV (1-2 mEq/kg)\n` +
           `Administrar lentamente (em 30-60 minutos)\n\n` +
           `Manutenção: Ajustar dose para manter pH sanguíneo entre 7,45-7,55\n` +
           `Monitorar pH sanguíneo, eletrólitos e ECG`;
  };

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-blue-900 dark:text-blue-300">Calculadora de Dose</h3>
      
      <PoisoningPatientInput 
        onWeightChange={setWeight}
        onAgeChange={setAgeInMonths}
        showWeight={toxidrome.requiresWeight !== false}
        showAge={toxidrome.requiresAge === true}
      />
      
      <Button 
        onClick={handleCalculate}
        disabled={!weight && toxidrome.requiresWeight !== false} 
        className="w-full py-6 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white flex items-center justify-center gap-2"
      >
        <Calculator className="h-5 w-5" />
        <span>Calcular Dose de {toxidrome.antidote}</span>
      </Button>
    </div>
  );
};
