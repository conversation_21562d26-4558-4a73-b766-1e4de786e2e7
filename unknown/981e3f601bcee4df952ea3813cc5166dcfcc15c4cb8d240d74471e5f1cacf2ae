import HelmetWrapper from "@/components/utils/HelmetWrapper";

export const DNPMMetaTags = () => {
  const pageTitle = "PedBook | DNPM - Desenvolvimento Neuropsicomotor Infantil";
  const pageDescription = "Acompanhe o desenvolvimento neuropsicomotor infantil com marcos detalhados por idade. Avalie aspectos sociais, emocionais, linguagem, cognição e desenvolvimento motor.";
  const pageUrl = "https://pedb.com.br/childcare/dnpm";
  const imageUrl = "https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/dnpm.webp";
  const keywords = [
    "desenvolvimento infantil",
    "marcos do desenvolvimento",
    "desenvolvimento neuropsicomotor",
    "DNPM",
    "pediatria",
    "desenvolvimento motor",
    "desenvolvimento cognitivo",
    "desenvolvimento social",
    "desenvolvimento emocional",
    "linguagem infantil",
    "avaliação desenvolvimento infantil"
  ].join(", ");

  return (
    <HelmetWrapper>
      <title>{pageTitle}</title>
      <meta name="description" content={pageDescription} />
      <meta name="keywords" content={keywords} />

      <meta name="robots" content="index, follow, max-image-preview:large" />
      <link rel="canonical" href={pageUrl} />

      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={pageDescription} />
      <meta property="og:type" content="article" />
      <meta property="og:url" content={pageUrl} />
      <meta property="og:image" content={imageUrl} />
      <meta property="og:image:alt" content="Ilustração sobre desenvolvimento infantil e marcos do desenvolvimento" />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />

      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={pageDescription} />
      <meta name="twitter:image" content={imageUrl} />

      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "MedicalWebPage",
          "name": pageTitle,
          "description": pageDescription,
          "url": pageUrl,
          "image": imageUrl,
          "keywords": keywords.split(", "),
          "publisher": {
            "@type": "Organization",
            "name": "PedBook",
            "logo": {
              "@type": "ImageObject",
              "url": "https://pedb.com.br/logo.png"
            }
          },
          "specialty": "Pediatria",
          "audience": {
            "@type": "MedicalAudience",
            "audienceType": "Profissionais de Saúde"
          },
          "about": {
            "@type": "MedicalCondition",
            "name": "Desenvolvimento Infantil",
            "description": "Avaliação e acompanhamento do desenvolvimento neuropsicomotor infantil"
          }
        })}
      </script>
    </HelmetWrapper>
  );
};