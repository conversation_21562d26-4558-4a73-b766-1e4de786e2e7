export function calculateBetaBlockerDose(weight: number): string {
  if (weight <= 0) {
    throw new Error("Por favor, insira um peso válido.");
  }

  // Calculate doses in mL
  const minDoseML = (weight * 0.02).toFixed(2);
  const maxDoseML = (weight * 0.03).toFixed(2);
  
  // Calculate doses in mg (same as mL since concentration is 1mg/mL)
  const minDoseMg = minDoseML;
  const maxDoseMg = maxDoseML;

  // Apply maximum dose limit of 1 mL (1 mg)
  const finalMinDoseML = Math.min(Number(minDoseML), 1).toFixed(2);
  const finalMaxDoseML = Math.min(Number(maxDoseML), 1).toFixed(2);
  const finalMinDoseMg = Math.min(Number(minDoseMg), 1).toFixed(2);
  const finalMaxDoseMg = Math.min(Number(maxDoseMg), 1).toFixed(2);

  return `Glucagon (1 mg/mL):\nDose: ${finalMinDoseML}-${finalMaxDoseML} mL (${finalMinDoseMg}-${finalMaxDoseMg} mg) EV/IM\nDose máxima: 1 mL por dose`;
}