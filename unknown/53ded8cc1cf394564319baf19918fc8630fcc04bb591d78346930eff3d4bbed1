import { Command } from "@/components/ui/command";
import { useEffect, useState } from "react";
import { calculateLevenshteinDistance } from "@/lib/utils";

interface SearchSuggestionsProps {
  searchTerm: string;
  suggestions: string[];
  onSuggestionSelect: (suggestion: string) => void;
}

export const SearchSuggestions = ({
  searchTerm,
  suggestions,
  onSuggestionSelect
}: SearchSuggestionsProps) => {
  const [didYouMean, setDidYouMean] = useState<string | null>(null);

  useEffect(() => {
    if (searchTerm.length > 2) {
      const closestMatch = suggestions.reduce((closest, current) => {
        const currentDistance = calculateLevenshteinDistance(searchTerm.toLowerCase(), current.toLowerCase());
        const closestDistance = calculateLevenshteinDistance(searchTerm.toLowerCase(), closest.toLowerCase());
        return currentDistance < closestDistance ? current : closest;
      }, suggestions[0]);

      if (calculateLevenshteinDistance(searchTerm.toLowerCase(), closestMatch.toLowerCase()) <= 3) {
        setDidYouMean(closestMatch);
      } else {
        setDidYouMean(null);
      }
    } else {
      setDidYouMean(null);
    }
  }, [searchTerm, suggestions]);

  if (!didYouMean) return null;

  return (
    <Command className="bg-transparent">
      <div className="px-4 py-2 text-sm text-primary">
        Você quis dizer:{" "}
        <button
          onClick={() => onSuggestionSelect(didYouMean)}
          className="font-medium underline hover:text-primary/80 transition-colors"
        >
          {didYouMean}
        </button>
        ?
      </div>
    </Command>
  );
};