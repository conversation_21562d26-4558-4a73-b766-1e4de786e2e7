
import React from 'react';
import { Stage } from './types';
import { LoxoscelicQuestion } from './components/LoxoscelicQuestion';
import { LoxoscelicResult } from './LoxoscelicResult';
import { CutaneousQuestion } from './components/CutaneousQuestion';
import { useLoxoscelicFlow } from './useLoxoscelicFlow';

export const LoxoscelicFlowchart: React.FC = () => {
  const { currentStage, handleAnswer, handleBack } = useLoxoscelicFlow();
  
  console.log('🕷️ Fluxograma Loxoscélico - Estágio atual:', currentStage);

  const renderContent = () => {
    switch (currentStage) {
      case 'initial':
        return <LoxoscelicQuestion onAnswer={handleAnswer} />;
      case 'cutaneous':
        return <CutaneousQuestion onAnswer={handleAnswer} />;
      case 'cutaneous-mild':
      case 'cutaneous-moderate':
      case 'cutaneous-severe':
      case 'cutaneous-hemolytic':
        return (
          <LoxoscelicResult
            stage={currentStage}
            onBack={handleBack}
          />
        );
      default:
        return <LoxoscelicQuestion onAnswer={handleAnswer} />;
    }
  };

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6 animate-slide-in-up">
      {renderContent()}
      
      <div className="mt-6 p-4 bg-gray-50 dark:bg-slate-800/80 border border-gray-200 dark:border-slate-700 rounded-lg">
        <p className="text-sm text-gray-600 dark:text-gray-300 italic">
          Referência: BRASIL. Ministério da Saúde. Secretaria de Vigilância em Saúde e Ambiente. Departamento de Doenças Transmissíveis. Guia de Animais Peçonhentos do Brasil. Brasília: Ministério da Saúde, 2024.
        </p>
      </div>
    </div>
  );
};
