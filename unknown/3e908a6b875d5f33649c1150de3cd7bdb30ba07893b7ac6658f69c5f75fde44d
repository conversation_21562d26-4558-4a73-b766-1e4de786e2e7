
import { motion } from "framer-motion";

export const ICDHeader = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-4 mb-8"
    >
      <h1 className="text-3xl font-bold text-primary dark:text-blue-400">
        Classificação Internacional de Doenças
      </h1>
      <p className="text-muted-foreground dark:text-gray-300 max-w-2xl mx-auto">
        Navegue pela estrutura hierárquica do CID-10 para encontrar códigos e
        descrições específicas. Use a pesquisa para encontrar rapidamente uma
        condição ou código específico.
      </p>
    </motion.div>
  );
};
