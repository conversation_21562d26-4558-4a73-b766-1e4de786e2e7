import { useState, useEffect } from "react";
import { Plus, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { MedicationForm } from "./medication/MedicationForm";
import { DeleteMedicationDialog } from "./medication/DeleteMedicationDialog";
import { DosageForm } from "./DosageForm";
import { DosageList } from "./DosageList";
import { MedicationUseCases } from "./medication/MedicationUseCases";
import { FormPersistence } from "./FormPersistence";

interface MedicationDialogProps {
  medication?: any;
  categories: Array<{ id: string; name: string }>;
  isOpen: boolean;
  onClose: () => void;
}

export function MedicationDialog({ medication, categories, isOpen, onClose }: MedicationDialogProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showDosageForm, setShowDosageForm] = useState(false);
  const [editingDosage, setEditingDosage] = useState<any>(null);





  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl h-[85vh]">
          <DialogHeader className="px-6 pt-6 pb-4">
            <DialogTitle>
              {medication ? "Editar Medicamento" : "Novo Medicamento"}
            </DialogTitle>
            <DialogDescription>
              {medication ? "Edite as informações do medicamento" : "Preencha as informações do novo medicamento"}
            </DialogDescription>
          </DialogHeader>

          <ScrollArea className="h-[calc(85vh-120px)] px-6">
            <div className="space-y-6 pb-6">
              <FormPersistence
                formId="medication-form"
                storageKey={`medication_${medication?.id || 'new'}`}
                debug={true}
              >
                <MedicationForm
                  medication={medication}
                  categories={categories}
                  onClose={onClose}
                />
              </FormPersistence>

              {medication && (
                <div className="space-y-6">
                  <MedicationUseCases medicationId={medication.id} />

                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-semibold">Dosagens</h3>
                    <Button
                      type="button"
                      onClick={() => setShowDosageForm(true)}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Nova Dosagem
                    </Button>
                  </div>

                  <DosageList
                    selectedMedicationId={medication.id}
                    onEdit={(dosage) => {
                      setEditingDosage(dosage);
                      setShowDosageForm(true);
                    }}
                  />

                  <Button
                    type="button"
                    variant="destructive"
                    onClick={() => setShowDeleteDialog(true)}
                    className="w-full"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Excluir Medicamento
                  </Button>
                </div>
              )}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>

      <DeleteMedicationDialog
        medication={medication}
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
      />

      {medication && (
        <Dialog open={showDosageForm} onOpenChange={(open) => {
          setShowDosageForm(open);
          if (!open) setEditingDosage(null);
        }}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>{editingDosage ? "Editar Dosagem" : "Nova Dosagem"}</DialogTitle>
              <DialogDescription>
                {editingDosage ? "Edite as informações da dosagem" : "Preencha as informações da nova dosagem"}
              </DialogDescription>
            </DialogHeader>
            <DosageForm
              medicationId={medication.id}
              dosage={editingDosage}
              onSuccess={() => {
                setShowDosageForm(false);
                setEditingDosage(null);
              }}
              onCancel={() => {
                setShowDosageForm(false);
                setEditingDosage(null);
              }}
            />
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}