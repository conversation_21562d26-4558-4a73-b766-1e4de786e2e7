
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";

interface ICDSearchProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}

export const ICDSearch = ({ searchTerm, setSearchTerm }: ICDSearchProps) => {
  return (
    <div className="relative w-full sm:w-72">
      <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground dark:text-gray-400 h-4 w-4" />
      <Input
        type="search"
        placeholder="Pesquisar por CID, nome ou descrição..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        className="pl-10 bg-white/50 dark:bg-slate-800/50 border-primary/20 dark:border-primary/30 focus:border-primary/40 dark:focus:border-primary/50 transition-colors"
      />
    </div>
  );
};
