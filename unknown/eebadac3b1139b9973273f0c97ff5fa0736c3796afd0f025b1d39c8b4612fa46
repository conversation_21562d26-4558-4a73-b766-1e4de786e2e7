import { Card } from "@/components/ui/card";

interface AnamnesisCardProps {
  formData: {
    age: number;
    weight: number;
    gender: "male" | "female";
    symptoms: string[];
    hasChronicDiseases: boolean;
    chronicDiseases?: string;
    symptomsIntensity: number;
    hasRecentExams: boolean;
    examDetails?: string;
    isPregnant?: boolean;
    isPediatric: boolean;
    manualSymptoms?: string;
  };
}

export function AnamnesisCard({ formData }: AnamnesisCardProps) {
  return (
    <Card className="p-6 bg-white/80 backdrop-blur-sm border border-primary/20 shadow-lg animate-fade-in">
      <h3 className="text-xl font-semibold mb-4 bg-gradient-to-r from-primary to-primary/70 text-transparent bg-clip-text">
        Resu<PERSON> da Anamnese
      </h3>
      <div className="text-sm text-gray-600 space-y-2">
        <p><span className="font-medium">Idade:</span> {formData.age} anos</p>
        <p><span className="font-medium">Peso:</span> {formData.weight} kg</p>
        <p><span className="font-medium">Gênero:</span> {formData.gender === 'male' ? 'Masculino' : 'Feminino'}</p>
        {formData.gender === 'female' && formData.isPregnant !== undefined && (
          <p><span className="font-medium">Gestante:</span> {formData.isPregnant ? 'Sim' : 'Não'}</p>
        )}
        <p><span className="font-medium">Paciente Pediátrico:</span> {formData.isPediatric ? 'Sim' : 'Não'}</p>
        <p><span className="font-medium">Sintomas:</span> {formData.symptoms.join(', ')}</p>
        {formData.manualSymptoms && (
          <p><span className="font-medium">Sintomas adicionais:</span> {formData.manualSymptoms}</p>
        )}
        <p><span className="font-medium">Intensidade:</span> {formData.symptomsIntensity}/10</p>
        {formData.hasChronicDiseases && formData.chronicDiseases && (
          <p><span className="font-medium">Doenças crônicas:</span> {formData.chronicDiseases}</p>
        )}
        {formData.hasRecentExams && formData.examDetails && (
          <p><span className="font-medium">Exames recentes:</span> {formData.examDetails}</p>
        )}
      </div>
    </Card>
  );
}