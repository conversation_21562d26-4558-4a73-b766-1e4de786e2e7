
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, Upload } from "lucide-react";
import { importQuestions, processImportFile } from "@/utils/importUtils";
import { Progress } from "@/components/ui/progress";

export const QuestionImport = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [progress, setProgress] = useState(0);
  const [currentFile, setCurrentFile] = useState("");
  const [fileCount, setFileCount] = useState({ current: 0, total: 0 });

  const processFiles = async (files: FileList) => {
    setIsLoading(true);
    setResults(null);
    setFileCount({ current: 0, total: files.length });

    const combinedResults = {
      success: 0,
      errors: [] as string[],
      created: {
        specialties: new Map(),
        themes: new Map(),
        focuses: new Map(),
        locations: new Map(),
        years: new Set<number>()
      }
    };

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        setCurrentFile(file.name);
        setFileCount(prev => ({ ...prev, current: i + 1 }));
        setProgress(0);

        try {
          // Atualiza o progresso para indicar início do processamento
          setProgress(10);
          
          const fileResults = await processImportFile(file);
          
          // Atualiza o progresso para indicar conclusão do processamento
          setProgress(100);
          
          // Combina os resultados
          combinedResults.success += fileResults.success;
          combinedResults.errors.push(...fileResults.errors.map(err => `[${file.name}] ${err}`));
          
          // Combina as coleções de categorias criadas
          fileResults.created.specialties.forEach((value, key) => {
            combinedResults.created.specialties.set(key, value);
          });
          
          fileResults.created.themes.forEach((value, key) => {
            combinedResults.created.themes.set(key, value);
          });
          
          fileResults.created.focuses.forEach((value, key) => {
            combinedResults.created.focuses.set(key, value);
          });
          
          fileResults.created.locations.forEach((value, key) => {
            combinedResults.created.locations.set(key, value);
          });
          
          fileResults.created.years.forEach(year => {
            combinedResults.created.years.add(year);
          });
          
        } catch (error: any) {
          combinedResults.errors.push(`[${file.name}] ${error.message}`);
        }
      }

      setResults(combinedResults);

      toast({
        title: "Importação concluída!",
        description: `${combinedResults.success} questões importadas com sucesso.${
          combinedResults.errors.length > 0 ? ` ${combinedResults.errors.length} erros encontrados.` : ''
        }`
      });

    } catch (error: any) {
      console.error('Error importing questions:', error);
      toast({
        title: "Erro na importação",
        description: error.message || 'Erro desconhecido ao importar questões',
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
      setCurrentFile("");
      setProgress(0);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;
    
    await processFiles(files);
    
    // Reset file input
    event.target.value = '';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Importar Questões</CardTitle>
        <CardDescription>
          Faça upload de arquivos JSON contendo as questões a serem importadas
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <input
              type="file"
              accept=".json"
              onChange={handleFileUpload}
              disabled={isLoading}
              multiple
              className="file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/90 disabled:opacity-50"
            />
            {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
          </div>

          {isLoading && (
            <div className="mt-4 space-y-2">
              <div className="flex justify-between text-sm text-gray-500">
                <span>Processando arquivo {fileCount.current} de {fileCount.total}</span>
                <span>{currentFile}</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}

          {results && (
            <div className="mt-4 space-y-4">
              <div className="rounded-lg bg-secondary/50 p-4">
                <h3 className="font-semibold mb-2">Resultados da Importação:</h3>
                <ul className="space-y-2 text-sm">
                  <li>✅ {results.success} questões importadas com sucesso</li>
                  {results.errors.length > 0 && (
                    <li>❌ {results.errors.length} erros encontrados</li>
                  )}
                  <li>📚 {results.created.specialties.size} especialidades</li>
                  <li>📝 {results.created.themes.size} temas</li>
                  <li>🎯 {results.created.focuses.size} focos</li>
                  <li>📍 {results.created.locations.size} localizações</li>
                  <li>📅 {results.created.years.size} anos</li>
                </ul>
              </div>

              {results.errors.length > 0 && (
                <div className="rounded-lg bg-destructive/10 p-4 max-h-60 overflow-auto">
                  <h3 className="font-semibold mb-2 text-destructive">Erros:</h3>
                  <ul className="space-y-1 text-sm">
                    {results.errors.map((error: string, index: number) => (
                      <li key={index} className="text-destructive">{error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
