import React from 'react';
import { Helmet } from 'react-helmet-async';

// Este componente serve como um wrapper para facilitar a migração de react-helmet para react-helmet-async
// Ele mantém a mesma API do react-helmet, mas usa o react-helmet-async internamente
const HelmetWrapper: React.FC<React.PropsWithChildren<any>> = (props) => {
  return <Helmet {...props} />;
};

export default HelmetWrapper;
