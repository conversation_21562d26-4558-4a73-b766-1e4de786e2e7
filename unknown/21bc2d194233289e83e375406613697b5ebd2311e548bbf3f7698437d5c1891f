import { WeightInput } from "./WeightInput";
import { AgeInput } from "./AgeInput";
import { User, Activity, CheckCircle } from "lucide-react";

interface PatientInfoSectionProps {
  weight: number;
  onWeightChange: (weight: number) => void;
  onWeightCommit: (weight: number) => void;
  age: number;
  onAgeChange: (age: number) => void;
  onAgeCommit: (age: number) => void;
  requiredMeasures?: string[];
}

export const PatientInfoSection = ({
  weight,
  onWeightChange,
  onWeightCommit,
  age,
  onAgeChange,
  onAgeCommit,
  requiredMeasures = ["weight", "age"]
}: PatientInfoSectionProps) => {

  if (requiredMeasures.length === 0) {
    return null;
  }
  const requiredFieldsCount = requiredMeasures.length;

  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
      <div className="p-3 sm:p-4">
        <div className="flex items-center gap-2 mb-3">
          <User className="w-4 h-4 text-primary" />
          <h3 className="text-sm sm:text-base font-medium text-gray-900 dark:text-gray-100">
            Dados do Paciente
          </h3>
        </div>

        <div className={`grid ${requiredFieldsCount === 1 ? 'place-items-center' : 'grid-cols-2'} gap-2 sm:gap-4`}>
          {requiredMeasures.includes("weight") && (
            <div className={`${requiredFieldsCount === 1 ? 'w-full max-w-md' : 'w-full'}`}>
              <WeightInput
                value={weight}
                onChange={onWeightChange}
                onCommit={onWeightCommit}
              />
            </div>
          )}
          {requiredMeasures.includes("age") && (
            <div className={`${requiredFieldsCount === 1 ? 'w-full max-w-md' : 'w-full'}`}>
              <AgeInput
                ageInMonths={age}
                onChange={onAgeChange}
                onCommit={onAgeCommit}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};