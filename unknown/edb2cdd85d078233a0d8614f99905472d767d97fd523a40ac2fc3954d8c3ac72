import { useState } from "react";
import { PecarnQuestion } from "./PecarnQuestion";
import { PecarnResult } from "./PecarnResult";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { ArrowLef<PERSON>, Calculator } from "lucide-react";
import { Link } from "react-router-dom";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { GlasgowCalculator } from "@/components/flowcharts/pecarn/GlasgowCalculator";

type Step = "age" | "severe" | "moderate" | "result";

export const PecarnContent = () => {
  const [age, setAge] = useState<"under2" | "over2" | null>(null);
  const [step, setStep] = useState<Step>("age");
  const [hasSevereSymptoms, setHasSevereSymptoms] = useState<boolean | null>(null);
  const [hasModerateSymptoms, setHasModerateSymptoms] = useState<boolean | null>(null);
  const [showGlasgow, setShowGlasgow] = useState(false);

  const getSevereQuestion = () => {
    if (age === "under2") {
      return (
        <div className="space-y-4">
          <p>A Escala de Glasgow é ≤ 14, há sinais de alteração mental ou fratura de crânio palpável?</p>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calculator className="h-4 w-4" />
            <button
              onClick={() => setShowGlasgow(true)}
              className="text-primary hover:underline"
            >
              Calcular Escala de Glasgow
            </button>
          </div>
        </div>
      );
    }
    return (
      <div className="space-y-4">
        <p>A Escala de Glasgow é ≤ 14, há sinais de alteração mental ou fratura de base de crânio?</p>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Calculator className="h-4 w-4" />
          <button
            onClick={() => setShowGlasgow(true)}
            className="text-primary hover:underline"
          >
            Calcular Escala de Glasgow
          </button>
        </div>
      </div>
    );
  };

  const getModerateQuestion = () => {
    if (age === "under2") {
      return "Há um dos seguintes critérios?\n- Hematoma subgaleal em região occipital, parietal ou temporal;\n- História de perda de consciência > 5 segundos;\n- Mecanismo de trauma grave;\n- Criança não está agindo normalmente, segundo os pais.";
    }
    return "Há um dos seguintes critérios?\n- História de perda de consciência;\n- Vômitos;\n- Mecanismo de trauma grave;\n- Cefaleia forte.";
  };

  const handleReset = () => {
    setAge(null);
    setStep("age");
    setHasSevereSymptoms(null);
    setHasModerateSymptoms(null);
  };

  const renderContent = () => {
    switch (step) {
      case "age":
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-800 text-center">
              A criança tem menos de 2 anos?
            </h2>
            <div className="flex justify-center gap-4">
              <Button
                onClick={() => {
                  setAge("under2");
                  setStep("severe");
                }}
                variant="outline"
                className="w-32"
              >
                Sim
              </Button>
              <Button
                onClick={() => {
                  setAge("over2");
                  setStep("severe");
                }}
                variant="outline"
                className="w-32"
              >
                Não
              </Button>
            </div>
          </div>
        );

      case "severe":
        return (
          <PecarnQuestion
            question={getSevereQuestion()}
            onAnswer={(answer) => {
              setHasSevereSymptoms(answer);
              if (answer) {
                setStep("result");
              } else {
                setStep("moderate");
              }
            }}
          />
        );

      case "moderate":
        return (
          <PecarnQuestion
            question={getModerateQuestion()}
            onAnswer={(answer) => {
              setHasModerateSymptoms(answer);
              setStep("result");
            }}
          />
        );

      case "result":
        return (
          <PecarnResult
            age={age!}
            hasSevereSymptoms={hasSevereSymptoms!}
            hasModerateSymptoms={hasModerateSymptoms}
          />
        );
    }
  };

  return (
    <div className="space-y-8">
      {step !== "age" && (
        <Button
          variant="ghost"
          className="flex items-center gap-2"
          onClick={handleReset}
        >
          <ArrowLeft className="h-4 w-4" />
          Recomeçar
        </Button>
      )}
      
      <motion.div
        key={step}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className="space-y-6"
      >
        {renderContent()}
      </motion.div>

      <div className="text-sm text-muted-foreground border-t pt-4 mt-8">
        <p className="italic">
        Adaptado de Kuppermann N, Holmes JF, Dayan PS, et al. Identification of children at very low risk of clinicallyimportant brain injuries after head trauma: a prospective cohort study. Lancet 2009; 374 (9696): 1160–70
        </p>
      </div>

      <Dialog open={showGlasgow} onOpenChange={setShowGlasgow}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Calculadora de Glasgow</DialogTitle>
          </DialogHeader>
          <GlasgowCalculator />
        </DialogContent>
      </Dialog>
    </div>
  );
};