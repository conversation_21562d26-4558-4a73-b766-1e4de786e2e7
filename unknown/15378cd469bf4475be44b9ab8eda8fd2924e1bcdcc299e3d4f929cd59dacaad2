
import { supabase } from '@/integrations/supabase/client';

export type Question = {
  id: string;
  statement: string;
  alternatives: string[];
  correct_answer: string | number;
  answer_type: 'MULTIPLE_CHOICE' | 'DISCURSIVE' | 'TRUE_OR_FALSE';
  tags?: Record<string, any>;
  [key: string]: any;
};

export const getUnanalyzedQuestions = async (domain: string, limit: number = 10): Promise<Question[]> => {
  try {
    const { data, error } = await supabase
      .from('questions')
      .select('*')
      .eq('domain', domain)
      .or(`tags->theme_analyzed.is.null, not.tags->theme_analyzed`)
      .limit(limit);

    if (error) {
      throw error;
    }

    return (data || []).map(q => ({
      ...q,
      alternatives: Array.isArray(q.alternatives)
        ? q.alternatives
        : typeof q.alternatives === 'object' && q.alternatives !== null
          ? Object.values(q.alternatives).map(String)
          : [],
      tags: q.tags || {}
    }));
  } catch (error) {
    throw error;
  }
};

export const getThemeAnalysisStats = async (domain: string): Promise<{
  total: number;
  analyzed: number;
  remaining: number;
}> => {
  try {
    // Contar total de questões para o domínio
    const { count: totalCount, error: totalError } = await supabase
      .from('questions')
      .select('*', { count: 'exact', head: true })
      .eq('domain', domain);

    if (totalError) throw totalError;

    // Contar questões já analisadas (marcadas na coluna tags)
    const { count: analyzedCount, error: analyzedError } = await supabase
      .from('questions')
      .select('*', { count: 'exact', head: true })
      .eq('domain', domain)
      .not('tags->theme_analyzed', 'is', null);

    if (analyzedError) {
      throw analyzedError;
    }

    const total = totalCount || 0;
    const analyzed = analyzedCount || 0;
    const remaining = total - analyzed;

    return { total, analyzed, remaining };
  } catch (error) {
    throw error;
  }
};

export const analyzeQuestion = async (questionId: string, domain: string): Promise<any> => {
  try {

    // Fetch the question details
    const { data: question, error: questionError } = await supabase
      .from('questions')
      .select('*')
      .eq('id', questionId)
      .single();

    if (questionError) throw questionError;
    if (!question) throw new Error('Question not found');

    // Prepare the prompt for AI analysis
    const prompt = `Analyze this medical question from the field of ${domain === 'pediatria' ? 'pediatrics' : 'ophthalmology'} and recommend the most appropriate theme and focus categories:

Statement: "${question.statement}"

${question.alternatives ? `
Alternatives:
${Array.isArray(question.alternatives)
  ? question.alternatives.map((alt, i) => `${i+1}. ${alt}`).join('\n')
  : typeof question.alternatives === 'object' && question.alternatives !== null
    ? Object.values(question.alternatives).map((alt, i) => `${i+1}. ${alt}`).join('\n')
    : 'No alternatives provided'
}` : ''}

Current categories:
- Specialty: ${question.specialty?.name || 'Not assigned'}
- Theme: ${question.theme?.name || 'Not assigned'}
- Focus: ${question.focus?.name || 'Not assigned'}

Please recommend the most appropriate theme (broader category) and focus (more specific subcategory) for this question. They must be different from each other.`;

    // Call the Supabase Edge Function
    const { data: aiResult, error: aiError } = await supabase.functions.invoke(
      'analyze-question-theme',
      {
        body: { prompt, domain }
      }
    );

    if (aiError) throw aiError;

    // Update the question tags with the AI analysis result
    const currentTags = question.tags || {};
    const updatedTags = {
      ...currentTags,
      theme_analyzed: true,
      analyzed_at: new Date().toISOString(),
      analyzed_domain: domain,
      recommended_theme: aiResult.recommendedTheme,
      recommended_focus: aiResult.recommendedFocus,
      recommendation_justification: aiResult.justification,
      is_new_theme: aiResult.isNewTheme,
      is_new_focus: aiResult.isNewFocus
    };

    const { error: updateError } = await supabase
      .from('questions')
      .update({ tags: updatedTags })
      .eq('id', questionId);

    if (updateError) {
      throw updateError;
    }

    return {
      ...aiResult,
      questionId,
      tags: updatedTags
    };
  } catch (error) {
    throw error;
  }
};
