import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface AgeSelectorProps {
  ageType: "months" | "years";
  setAgeType: (value: "months" | "years") => void;
  ageYears: number | null;
  setAgeYears: (value: number | null) => void;
  ageMonths: number;
  setAgeMonths: (value: number) => void;
}

export const AgeSelector = ({
  ageType,
  setAgeType,
  ageYears,
  setAgeYears,
  ageMonths,
  setAgeMonths,
}: AgeSelectorProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <Label htmlFor="ageType">Tipo de Idade</Label>
        <Select value={ageType} onValueChange={(value: "months" | "years") => setAgeType(value)}>
          <SelectTrigger>
            <SelectValue placeholder="Selecione o tipo de idade" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="months">Meses</SelectItem>
            <SelectItem value="years">Anos</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {ageType === "years" && (
        <div>
          <Label htmlFor="ageYears">Anos</Label>
          <Input
            id="ageYears"
            type="number"
            min={0}
            max={18}
            value={ageYears || ""}
            onChange={(e) => setAgeYears(parseInt(e.target.value) || null)}
          />
        </div>
      )}

      <div>
        <Label htmlFor="ageMonths">Meses</Label>
        <Input
          id="ageMonths"
          type="number"
          min={0}
          max={11}
          value={ageMonths}
          onChange={(e) => setAgeMonths(parseInt(e.target.value) || 0)}
        />
      </div>
    </div>
  );
};