import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { ImageUpload } from "@/components/admin/blog/ImageUpload";
import { TagSelector } from "@/components/admin/blog/TagSelector";
import { CategorySelector } from "@/components/admin/blog/CategorySelector";
import { RichTextEditor } from "@/components/admin/blog/RichTextEditor";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";

interface BlogPostFormProps {
  postId?: string;
  initialData?: any;
  onSuccess?: () => void;
}

interface FormData {
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  published: boolean;
}

export function BlogPostForm({ postId, initialData, onSuccess }: BlogPostFormProps) {
  const [imageUrl, setImageUrl] = useState(initialData?.featured_image || "");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [categoryId, setCategoryId] = useState<string>(initialData?.category_id || "");
  const [content, setContent] = useState(initialData?.content || "");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  
  const { register, handleSubmit, formState: { errors }, reset, setValue } = useForm<FormData>({
    defaultValues: initialData || {}
  });

  useEffect(() => {
    if (initialData) {
      reset(initialData);
      setImageUrl(initialData.featured_image || "");
      setCategoryId(initialData.category_id || "");
      setContent(initialData.content || "");
    }
  }, [initialData, reset]);

  const onSubmit = async (data: FormData) => {
    try {
      setIsSubmitting(true);
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError) throw authError;
      if (!user) throw new Error("User not authenticated");

      const now = new Date().toISOString();
      
      const postData = {
        title: data.title,
        slug: data.slug,
        content: content,
        excerpt: data.excerpt,
        category_id: categoryId || null,
        featured_image: imageUrl || null,
        published: data.published,
        author_id: user.id,
        published_at: data.published ? now : null,
        updated_at: now,
        ...(postId && { id: postId })
      };

      const { data: savedPost, error: postError } = await supabase
        .from('pedbook_blog_posts')
        .upsert(postData)
        .select()
        .single();

      if (postError) throw postError;
      if (!savedPost) throw new Error("Failed to save post");

      if (selectedTags.length > 0) {
        if (postId) {
          await supabase
            .from('pedbook_blog_posts_tags')
            .delete()
            .eq('post_id', postId);
        }

        const tagRelations = selectedTags.map(tagId => ({
          post_id: savedPost.id,
          tag_id: tagId
        }));

        const { error: tagError } = await supabase
          .from('pedbook_blog_posts_tags')
          .insert(tagRelations);

        if (tagError) throw tagError;
      }

      toast({
        title: "Sucesso!",
        description: "Post salvo com sucesso.",
      });

      onSuccess?.();
    } catch (error: any) {
      console.error('Error saving post:', error);
      toast({
        variant: "destructive",
        title: "Erro ao salvar o post",
        description: error.message,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="title">Título</Label>
        <Input
          id="title"
          {...register("title", { required: "Título é obrigatório" })}
        />
        {errors.title && (
          <p className="text-sm text-red-500">{errors.title.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="slug">Slug</Label>
        <Input
          id="slug"
          {...register("slug", { required: "Slug é obrigatório" })}
        />
        {errors.slug && (
          <p className="text-sm text-red-500">{errors.slug.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="content">Conteúdo</Label>
        <RichTextEditor
          content={content}
          onChange={setContent}
        />
        {errors.content && (
          <p className="text-sm text-red-500">{errors.content.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="excerpt">Resumo</Label>
        <Textarea
          id="excerpt"
          {...register("excerpt")}
        />
      </div>

      <ImageUpload
        imageUrl={imageUrl}
        setImageUrl={setImageUrl}
      />

      <CategorySelector
        value={categoryId}
        onValueChange={setCategoryId}
      />

      <TagSelector
        selectedTags={selectedTags}
        onTagsChange={setSelectedTags}
      />

      <div className="flex items-center space-x-2">
        <Switch
          id="published"
          {...register("published")}
        />
        <Label htmlFor="published">Publicar</Label>
      </div>

      <Button type="submit" disabled={isSubmitting}>
        {isSubmitting ? "Salvando..." : "Salvar Post"}
      </Button>
    </form>
  );
}