import { <PERSON>, ChartLine, Ruler, Scale } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface PatientData {
  age: number;
  gender: 'male' | 'female';
  weight?: number;
  height?: number;
  headCircumference?: number;
  birthWeight?: number;
  maturity?: 'Term' | 'Pre-term';
}

interface PatientDataFormProps {
  data: PatientData;
  onChange: (data: PatientData) => void;
}

export function PatientDataForm({ data, onChange }: PatientDataFormProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {/* <PERSON><PERSON> e <PERSON> (Obrigatórios) */}
      <div className="space-y-2">
        <Label htmlFor="age" className="flex items-center gap-2">
          <Baby className="h-4 w-4" />
          <PERSON><PERSON> (meses) *
        </Label>
        <Input
          id="age"
          type="number"
          min={0}
          max={240}
          value={data.age}
          onChange={(e) => onChange({ ...data, age: Number(e.target.value) })}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="gender" className="flex items-center gap-2">
          Gênero *
        </Label>
        <Select
          value={data.gender}
          onValueChange={(value: 'male' | 'female') => 
            onChange({ ...data, gender: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Selecione o gênero" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="male">Masculino</SelectItem>
            <SelectItem value="female">Feminino</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="maturity" className="flex items-center gap-2">
          <Baby className="h-4 w-4" />
          Maturidade *
        </Label>
        <Select
          value={data.maturity}
          onValueChange={(value: 'Term' | 'Pre-term') => 
            onChange({ ...data, maturity: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Selecione a maturidade" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Term">A termo</SelectItem>
            <SelectItem value="Pre-term">Pré-termo</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Medidas (Opcionais) */}
      <div className="space-y-2">
        <Label htmlFor="birthWeight" className="flex items-center gap-2">
          <Scale className="h-4 w-4" />
          Peso ao Nascer (g)
        </Label>
        <Input
          id="birthWeight"
          type="number"
          step="1"
          min={0}
          value={data.birthWeight || ''}
          onChange={(e) => onChange({ ...data, birthWeight: Number(e.target.value) })}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="weight" className="flex items-center gap-2">
          <Scale className="h-4 w-4" />
          Peso Atual (kg)
        </Label>
        <Input
          id="weight"
          type="number"
          step="0.1"
          min={0}
          value={data.weight || ''}
          onChange={(e) => onChange({ ...data, weight: Number(e.target.value) })}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="height" className="flex items-center gap-2">
          <Ruler className="h-4 w-4" />
          Altura (cm)
        </Label>
        <Input
          id="height"
          type="number"
          step="0.1"
          min={0}
          value={data.height || ''}
          onChange={(e) => onChange({ ...data, height: Number(e.target.value) })}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="headCircumference" className="flex items-center gap-2">
          <ChartLine className="h-4 w-4" />
          Perímetro Cefálico (cm)
        </Label>
        <Input
          id="headCircumference"
          type="number"
          step="0.1"
          min={0}
          value={data.headCircumference || ''}
          onChange={(e) => onChange({ ...data, headCircumference: Number(e.target.value) })}
        />
      </div>
    </div>
  );
}