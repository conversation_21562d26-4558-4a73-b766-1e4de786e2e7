import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

interface MedicalHistorySectionProps {
  formData: any;
  setFormData: (data: any) => void;
}

export function MedicalHistorySection({ formData, setFormData }: MedicalHistorySectionProps) {
  return (
    <>
      <div className="space-y-3">
        <Label className="text-xl font-semibold text-gray-800 bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent">
          Tem doenças crônicas?
        </Label>
        <div className="flex gap-3">
          <Button
            type="button"
            variant={formData.hasChronicDiseases ? "default" : "outline"}
            onClick={() => setFormData({ 
              ...formData, 
              hasChronicDiseases: true 
            })}
            className="flex-1 h-12"
          >
            Sim
          </Button>
          <Button
            type="button"
            variant={!formData.hasChronicDiseases ? "default" : "outline"}
            onClick={() => setFormData({ 
              ...formData, 
              hasChronicDiseases: false,
              chronicDiseases: undefined 
            })}
            className="flex-1 h-12"
          >
            Não
          </Button>
        </div>
        {formData.hasChronicDiseases && (
          <Input
            placeholder="Descreva as doenças crônicas"
            value={formData.chronicDiseases || ""}
            onChange={(e) => setFormData({ ...formData, chronicDiseases: e.target.value })}
            className="mt-2 h-12 text-lg bg-white/50 backdrop-blur-sm"
          />
        )}
      </div>

      <div className="space-y-3">
        <Label className="text-xl font-semibold text-gray-800 bg-gradient-to-r from-primary/80 to-primary bg-clip-text text-transparent">
          Fez exames recentemente?
        </Label>
        <div className="flex gap-3">
          <Button
            type="button"
            variant={formData.hasRecentExams ? "default" : "outline"}
            onClick={() => setFormData({ 
              ...formData, 
              hasRecentExams: true 
            })}
            className="flex-1 h-12"
          >
            Sim
          </Button>
          <Button
            type="button"
            variant={!formData.hasRecentExams ? "default" : "outline"}
            onClick={() => setFormData({ 
              ...formData, 
              hasRecentExams: false,
              examDetails: undefined 
            })}
            className="flex-1 h-12"
          >
            Não
          </Button>
        </div>
        {formData.hasRecentExams && (
          <Input
            placeholder="Descreva os exames e resultados"
            value={formData.examDetails || ""}
            onChange={(e) => setFormData({ ...formData, examDetails: e.target.value })}
            className="mt-2 h-12 text-lg bg-white/50 backdrop-blur-sm"
          />
        )}
      </div>
    </>
  );
}