import React, { useEffect, useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { ThumbsUp, ThumbsDown, <PERSON>h, Heart } from "lucide-react";
import { Card } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";

interface ConductsFeedbackProps {
  summaryId: string;
  summaryTitle: string;
}

const RATING_OPTIONS = [
  {
    value: 'excellent',
    label: 'Excelente',
    icon: Heart,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 hover:bg-blue-100',
  },
  {
    value: 'good',
    label: 'Bom',
    icon: ThumbsUp,
    color: 'text-green-500',
    bgColor: 'bg-green-50 hover:bg-green-100',
  },
  {
    value: 'regular',
    label: 'Regular',
    icon: Meh,
    color: 'text-yellow-500',
    bgColor: 'bg-yellow-50 hover:bg-yellow-100',
  },
  {
    value: 'poor',
    label: 'Ruim',
    icon: ThumbsDown,
    color: 'text-red-500',
    bgColor: 'bg-red-50 hover:bg-red-100',
  },
] as const;

export function ConductsFeedback({ summaryId, summaryTitle }: ConductsFeedbackProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [existingFeedback, setExistingFeedback] = useState<string | null>(null);
  const [showCommentDialog, setShowCommentDialog] = useState(false);
  const [comment, setComment] = useState("");
  const [pendingRating, setPendingRating] = useState<typeof RATING_OPTIONS[number]['value'] | null>(null);

  useEffect(() => {
    const checkExistingFeedback = async () => {
      if (!user) return;

      try {
        // Use maybeSingle() instead of single() to handle when no rows are found
        const { data, error } = await supabase
          .from('pedbook_conducts_feedback')
          .select('rating')
          .eq('summary_id', summaryId)
          .eq('user_id', user.id)
          .maybeSingle();

        if (error) {
          console.error("Error checking existing feedback:", error);
          return;
        }

        if (data) {
          setExistingFeedback(data.rating);
        }
      } catch (error) {
        console.error("Erro ao verificar feedback existente:", error);
      }
    };

    checkExistingFeedback();
  }, [user, summaryId]);

  const askForComment = (rating: typeof RATING_OPTIONS[number]['value']) => {
    if (rating !== 'excellent') {
      setPendingRating(rating);
      const { dismiss } = toast({
        title: "Poderia nos ajudar a melhorar?",
        description: (
          <div className="mt-2 space-y-4">
            <p>Gostaríamos saber sua opinião para melhorarmos nosso conteúdo. Aceitamos qualquer tipo de crítica construtiva ou sugestão.</p>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  dismiss();
                  handleFeedback(rating);
                }}
              >
                Não, obrigado
              </Button>
              <Button
                onClick={() => {
                  dismiss();
                  setShowCommentDialog(true);
                }}
              >
                Sim, quero ajudar
              </Button>
            </div>
          </div>
        ),
        duration: 10000,
        className: "fixed !top-[50%] !left-[50%] !-translate-x-1/2 !-translate-y-1/2 !bottom-auto !right-auto max-w-[90dvw] w-full md:max-w-[400px] !p-6",
      });
    } else {
      handleFeedback(rating);
    }
  };

  const handleFeedback = async (rating: typeof RATING_OPTIONS[number]['value'], userComment?: string) => {
    if (!user) {
      toast({
        title: "Login necessário",
        description: "Você precisa estar logado para enviar feedback.",
        variant: "destructive",
      });
      return;
    }

    if (existingFeedback) {
      toast({
        title: "Feedback já enviado",
        description: "Você já avaliou este resumo anteriormente.",
        variant: "destructive",
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('pedbook_conducts_feedback')
        .insert({
          summary_id: summaryId,
          summary_title: summaryTitle,
          user_id: user.id,
          rating: rating,
          comment: userComment,
        });

      if (error) {
        if (error.code === '23505') {
          toast({
            title: "Feedback já enviado",
            description: "Você já avaliou este resumo anteriormente.",
            variant: "destructive",
          });
          return;
        }
        throw error;
      }

      setExistingFeedback(rating);
      toast({
        title: "Feedback enviado",
        description: "Obrigado por contribuir com sua avaliação!" + (userComment ? " Seu comentário nos ajudará a melhorar." : ""),
      });
    } catch (error: any) {
      toast({
        title: "Erro ao enviar feedback",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleCommentSubmit = () => {
    if (pendingRating && comment.trim()) {
      handleFeedback(pendingRating, comment.trim());
      setShowCommentDialog(false);
      setComment("");
      setPendingRating(null);
    }
  };

  return (
    <>
      <div className="mt-6 border-t pt-6">
        <div className="text-center space-y-2">
          <h3 className="text-base font-medium text-gray-900">
            {existingFeedback ? 'Sua avaliação' : 'Este resumo foi útil para você?'}
          </h3>
        </div>

        <div className="mt-4 flex justify-center gap-3">
          {RATING_OPTIONS.map((option) => {
            const isSelected = existingFeedback === option.value;
            return (
              <Card
                key={option.value}
                className={`flex flex-col items-center justify-center p-2 cursor-${existingFeedback ? 'default' : 'pointer'} transition-all
                  ${isSelected ? `${option.bgColor} ring-2 ring-${option.color}` : 'bg-gray-50'}
                  ${!existingFeedback ? `${option.bgColor} hover:ring-2 hover:ring-${option.color}` : ''}
                  w-16 h-16`}
                onClick={() => !existingFeedback && askForComment(option.value)}
              >
                <option.icon className={`w-6 h-6 ${isSelected ? option.color : 'text-gray-400'}`} />
                <span className={`mt-1 text-xs font-medium ${isSelected ? 'text-gray-900' : 'text-gray-500'}`}>
                  {option.label}
                </span>
              </Card>
            );
          })}
        </div>
      </div>

      <AlertDialog open={showCommentDialog} onOpenChange={setShowCommentDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Deixe seu comentário</AlertDialogTitle>
            <AlertDialogDescription>
              Sua opinião é muito importante para melhorarmos nosso conteúdo. Por favor, compartilhe suas sugestões ou críticas construtivas.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="my-4">
            <Textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Digite seu comentário aqui..."
              className="min-h-[100px]"
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setShowCommentDialog(false);
              if (pendingRating) {
                handleFeedback(pendingRating);
              }
            }}>
              Cancelar
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleCommentSubmit}>
              Enviar comentário
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
