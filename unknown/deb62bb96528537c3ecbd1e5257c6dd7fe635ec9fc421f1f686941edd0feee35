
import { useNavigate } from "react-router-dom";
import { Pill, Loader2 } from "lucide-react";
import { useState } from "react";
import { useLoading } from "@/context/LoadingContext";

interface MedicationCardProps {
  id?: string;
  name: string;
  category: string;
  slug: string;
}

export function MedicationCard({ id, name, category, slug }: MedicationCardProps) {
  const navigate = useNavigate();
  const { startLoading } = useLoading();
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = () => {
    if (isLoading) return;
    setIsLoading(true);
    navigate(`/bulas-profissionais/${slug}`);
    setTimeout(() => setIsLoading(false), 500);
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer border border-blue-100 dark:border-blue-800 dark:bg-slate-800 overflow-hidden flex flex-col h-full transform hover:-translate-y-1 ${isLoading ? 'opacity-75 scale-95 pointer-events-none' : ''}`}
      onClick={handleClick}
    >
      <div className="p-4 flex items-start gap-3">
        <div className="p-2 bg-primary/10 rounded-full dark:bg-blue-800/40 flex-shrink-0">
          {isLoading ? (
            <Loader2 className="h-5 w-5 text-primary dark:text-blue-400 animate-spin" />
          ) : (
            <Pill className="h-5 w-5 text-primary dark:text-blue-400" />
          )}
        </div>
        
        <div className="flex-grow">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1">{name}</h3>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-300">
            {category}
          </span>
        </div>
      </div>
      
      <div className="mt-auto bg-gradient-to-r from-primary/5 to-primary/10 dark:from-blue-900/30 dark:to-blue-800/40 px-4 py-2 text-xs text-primary dark:text-blue-300 text-right">
        <span className="flex items-center justify-end gap-1">
          Ver bula completa
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M5 12h14"></path>
            <path d="m12 5 7 7-7 7"></path>
          </svg>
        </span>
      </div>
    </div>
  );
}
