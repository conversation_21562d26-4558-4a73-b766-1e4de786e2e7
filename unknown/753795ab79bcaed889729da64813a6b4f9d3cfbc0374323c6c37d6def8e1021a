import React from "react";
import { useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { Instagram, ExternalLink, Clock } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { cn } from "@/lib/utils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { format, formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";

export const LatestInstagramPost = () => {
  // Estado para armazenar a largura da janela
  const [windowWidth, setWindowWidth] = React.useState(0);

  // Efeito para atualizar a largura da janela
  React.useEffect(() => {
    // Definir a largura inicial
    setWindowWidth(window.innerWidth);

    // Função para atualizar a largura quando a janela for redimensionada
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    // Adicionar listener para redimensionamento
    window.addEventListener('resize', handleResize);

    // Remover listener quando o componente for desmontado
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  const { data: latestPost, isError } = useQuery({
    queryKey: ['latest-instagram-post'],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from('pedbook_site_instagram_posts')
          .select('title, link, created_at, post_date')
          .order('created_at', { ascending: false })
          .limit(1)
          .single();

        if (error) {
          // Se o erro for "No rows found", não é um erro crítico
          if (error.code === 'PGRST116') {
            return null;
          }
          throw error;
        }

        // Se temos post_date, usamos ele, caso contrário usamos created_at
        const effectiveDate = data.post_date || data.created_at;

        return {
          ...data,
          effective_date: effectiveDate
        };
      } catch (error) {
        throw error;
      }
    },
    retry: 1,
    staleTime: 1000 * 60 * 5, // 5 minutos
  });

  if (!latestPost) return null;

  // Verificar se a data efetiva é válida
  const effectiveDate = new Date(latestPost.effective_date);
  const isValidDate = !isNaN(effectiveDate.getTime());

  // Formatar a data relativa (ex: "há 2 dias")
  const relativeDate = isValidDate
    ? formatDistanceToNow(effectiveDate, { locale: ptBR, addSuffix: true })
    : "recentemente";

  // Formatar a data completa para o tooltip (ex: "15 de junho de 2023")
  const fullDate = isValidDate
    ? format(effectiveDate, "dd 'de' MMMM 'de' yyyy", { locale: ptBR })
    : "Data não disponível";

  // Truncar o título se for muito longo
  const truncateTitle = (title, maxLength = 60) => {
    if (title.length <= maxLength) return title;
    return title.substring(0, maxLength) + '...';
  };

  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <motion.div
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            onClick={() => window.open(latestPost.link, '_blank')}
            className={cn(
              "mx-auto px-1 sm:px-1.5 py-1 md:px-2.5 md:py-1.5",
              // Largura mais compacta para aparência de app
              "max-w-[70%] sm:max-w-[50%] md:max-w-[40%] lg:max-w-[30%] xl:max-w-[25%]",
              // Estilo mais moderno com sombra e fundo de vidro
              "bg-white/70 dark:bg-slate-800/70",
              "backdrop-blur-sm",
              // Cantos mais arredondados para aparência de app
              "rounded hover:shadow-sm transition-all cursor-pointer group",
              "flex items-center gap-0.5 sm:gap-1 overflow-hidden hover:bg-gray-50/70 dark:hover:bg-slate-700/70"
            )}
          >
            {/* Layout responsivo com diferentes configurações para mobile e desktop */}
            <div className="flex items-center w-full gap-0.5 sm:gap-1 md:gap-2">
              {/* Ícone do Instagram com estilo mais moderno */}
              <div className="flex-shrink-0 bg-gradient-to-br from-pink-500 to-purple-600 p-0.5 rounded md:p-1">
                <Instagram className="w-2.5 h-2.5 md:w-3.5 md:h-3.5 text-white" />
              </div>

              {/* Conteúdo principal com layout responsivo */}
              <div className="flex flex-1 items-center overflow-hidden pr-0.5">
                {/* Título com truncamento */}
                <span className="text-[9px] md:text-xs font-medium text-gray-700 dark:text-gray-200 truncate group-hover:text-primary transition-colors flex-1">
                  {truncateTitle(latestPost.title, windowWidth > 768 ? 40 : 35)}
                </span>

                {/* Indicador de tempo em formato compacto */}
                <div className="flex-shrink-0 flex items-center ml-0.5 md:ml-2">
                  <div className="flex items-center gap-0.5 md:gap-1 px-0.5 sm:px-1 py-0.5 bg-gray-100/70 dark:bg-slate-700/70 rounded">
                    <Clock className="w-2 h-2 md:w-3 md:h-3 text-gray-500 dark:text-gray-400" />
                    <span className="text-[8px] md:text-[10px] text-gray-500 dark:text-gray-400 whitespace-nowrap font-medium">
                      {windowWidth > 768 ? relativeDate.replace('há ', '') : relativeDate.replace('há ', '')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </TooltipTrigger>
        <TooltipContent side="bottom" className="max-w-xs p-3 bg-white/95 dark:bg-slate-800/95 backdrop-blur-md shadow-lg border border-gray-200/50 dark:border-gray-700/50">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div className="bg-gradient-to-br from-pink-500 to-purple-600 p-1.5 rounded-lg shadow-sm">
                <Instagram className="w-3.5 h-3.5 text-white" />
              </div>
              <span className="font-medium text-sm">Instagram</span>
            </div>
            <p className="text-sm font-medium">{latestPost.title}</p>
            <div className="text-xs text-gray-500 flex items-center gap-1.5">
              <Clock className="w-3 h-3" />
              <span>Publicado em {fullDate}</span>
            </div>
            <div className="text-xs flex items-center gap-1.5 text-primary">
              <ExternalLink className="w-3 h-3" />
              <span>Clique para ver no Instagram</span>
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};