import React, { useState, useMemo } from "react";
import { Helmet } from "react-helmet-async";
import { motion } from "framer-motion";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Newspaper, Rss, ChevronLeft, Sparkles, Zap } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { useNewsletters, useNewslettersCount } from "@/hooks/useNewsletters";
import { RevolutionaryNewsCard } from "@/components/newsletters/RevolutionaryNewsCard";
import { NewsFilters, FilterState } from "@/components/newsletters/NewsFilters";
import { NewsPagination } from "@/components/newsletters/NewsPagination";
import { useNavigate } from "react-router-dom";

const Newsletters: React.FC = () => {
  const navigate = useNavigate();
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    category: '',
    sortBy: 'recent',
    timeRange: 'all'
  });
  const [layout, setLayout] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Calcular limite dinâmico baseado na página e layout
  const getItemsLimit = () => {
    if (layout === 'list') return itemsPerPage; // Lista sempre 10

    // Grid: primeira página = 1 destaque + 9 outras = 10 total
    // Outras páginas = múltiplos de 3 para preencher linhas completas
    if (currentPage === 1) return 10; // 1 destaque + 9 outras
    return 12; // 4 linhas de 3 = 12 notícias
  };

  const currentLimit = getItemsLimit();
  const currentOffset = currentPage === 1 ? 0 : 10 + (currentPage - 2) * 12;

  // Buscar notícias com paginação
  const {
    data: news,
    isLoading: isLoadingNews,
    isError: isNewsError,
    error: newsError
  } = useNewsletters({
    limit: currentLimit,
    offset: currentOffset,
    category: filters.category || undefined,
    searchTerm: filters.search || undefined
  });

  // Buscar total de notícias para paginação
  const {
    data: totalCount,
    isLoading: isLoadingCount
  } = useNewslettersCount({
    category: filters.category || undefined,
    searchTerm: filters.search || undefined
  });

  // Calcular total de páginas baseado no layout
  const getTotalPages = () => {
    if (!totalCount) return 0;
    if (layout === 'list') return Math.ceil(totalCount / itemsPerPage);

    // Grid: primeira página = 10, outras páginas = 12 cada
    if (totalCount <= 10) return 1;
    const remainingAfterFirstPage = totalCount - 10;
    return 1 + Math.ceil(remainingAfterFirstPage / 12);
  };

  const totalPages = getTotalPages();

  // Função para lidar com mudança de filtros
  const handleFilterChange = (newFilters: FilterState) => {
    setFilters(newFilters);
    setCurrentPage(1); // Resetar para primeira página quando filtros mudarem
  };

  // Função para lidar com mudança de página
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Scroll para o topo quando mudar de página
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Função para lidar com mudança de layout
  const handleLayoutChange = (newLayout: 'grid' | 'list') => {
    setLayout(newLayout);
    setCurrentPage(1); // Resetar para primeira página quando layout mudar
  };

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20 dark:from-slate-900 dark:via-slate-800/50 dark:to-slate-900">
      <Helmet>
        <title>Notícias Diárias | PedBook</title>
        <meta name="description" content="Descubra as últimas atualizações e novidades do mundo da medicina pediátrica com nossa experiência revolucionária de notícias." />
      </Helmet>

      <Header />

      <main className="flex-1 container mx-auto px-4 py-6">
        <div className="max-w-7xl mx-auto">
          {/* Cabeçalho Compacto */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-6 mt-2"
          >
            <div className="flex items-center justify-between">
              {/* Botão Voltar */}
              <Button
                variant="ghost"
                onClick={() => navigate('/')}
                className="flex items-center gap-2 h-9 px-3 hover:bg-blue-50/60 dark:hover:bg-slate-800/60 transition-all duration-300 hover:scale-105 rounded-lg"
                aria-label="Voltar ao menu inicial"
              >
                <ChevronLeft className="h-4 w-4" />
                <span className="hidden sm:inline text-sm font-medium">Voltar</span>
              </Button>

              {/* Título Central */}
              <div className="flex items-center gap-2">
                <div className="relative">
                  <div className="absolute inset-0 bg-blue-500 rounded-full blur-lg opacity-30 animate-pulse"></div>
                  <Newspaper className="relative h-6 w-6 text-blue-500" />
                </div>
                <h1 className="text-xl md:text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-blue-400">
                  Notícias Médicas
                </h1>
              </div>

              {/* Info de Atualização */}
              <div className="flex items-center gap-2 bg-yellow-50 dark:bg-yellow-900/20 px-3 py-1.5 rounded-full">
                <Sparkles className="w-3 h-3 text-yellow-500" />
                <span className="hidden sm:inline text-xs font-medium text-gray-600 dark:text-gray-400">
                  Atualizado às 07:00
                </span>
                <span className="sm:hidden text-xs font-medium text-gray-600 dark:text-gray-400">
                  Atualizado 07:00
                </span>
              </div>
            </div>
          </motion.div>

          {/* Filtros Otimizados */}
          <div>
            <NewsFilters
              onFilterChange={handleFilterChange}
              onLayoutChange={handleLayoutChange}
              currentLayout={layout}
            />
          </div>

          {/* Grid Otimizado de Notícias */}
          <div>
            {isLoadingNews ? (
              // Esqueletos de carregamento otimizados (sem animações pesadas)
              <div className={layout === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
                  >
                    <Skeleton className="h-40" />
                    <div className="p-4 space-y-3">
                      <div className="flex gap-2">
                        <Skeleton className="h-5 w-16 rounded-full" />
                        <Skeleton className="h-5 w-20 rounded-full" />
                      </div>
                      <Skeleton className="h-6 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                      <div className="flex justify-between items-center pt-2">
                        <Skeleton className="h-8 w-20" />
                        <Skeleton className="h-8 w-8 rounded-full" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : isNewsError ? (
              <div className="text-center py-16">
                <div className="bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 p-8 rounded-2xl border border-red-200 dark:border-red-800/50 max-w-md mx-auto">
                  <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Rss className="w-8 h-8 text-red-500" />
                  </div>
                  <h3 className="text-xl font-bold text-red-800 dark:text-red-300 mb-3">
                    Ops! Algo deu errado
                  </h3>
                  <p className="text-red-600 dark:text-red-400 mb-4">
                    Não conseguimos carregar as notícias no momento. Nossa equipe já foi notificada.
                  </p>
                  <Button
                    variant="outline"
                    className="border-red-300 text-red-700 hover:bg-red-50 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/30"
                    onClick={() => window.location.reload()}
                  >
                    <Rss className="h-4 w-4 mr-2" />
                    Tentar Novamente
                  </Button>
                </div>
              </div>
            ) : news && news.length > 0 ? (
              <>
                <div className={
                  layout === 'grid'
                    ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                    : 'space-y-4'
                }>
                  {/* Notícia em Destaque */}
                  {news.length > 0 && layout === 'grid' && currentPage === 1 && (
                    <RevolutionaryNewsCard
                      key={`featured-${news[0].id}`}
                      news={news[0]}
                      index={0}
                      variant="featured"
                    />
                  )}

                  {/* Outras Notícias */}
                  {news.slice(layout === 'grid' && currentPage === 1 ? 1 : 0).map((item, index) => (
                    <RevolutionaryNewsCard
                      key={item.id}
                      news={item}
                      index={layout === 'grid' && currentPage === 1 ? index + 1 : index}
                      variant={layout === 'list' ? 'compact' : 'standard'}
                    />
                  ))}
                </div>

                {/* Paginação */}
                {totalCount && totalPages > 1 && (
                  <NewsPagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    totalItems={totalCount}
                    itemsPerPage={layout === 'grid' ? (currentPage === 1 ? 10 : 12) : itemsPerPage}
                    onPageChange={handlePageChange}
                  />
                )}
              </>
            ) : (
              <div className="text-center py-16">
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-8 rounded-2xl border border-blue-200 dark:border-blue-800/50 max-w-md mx-auto">
                  <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Sparkles className="w-8 h-8 text-blue-500" />
                  </div>
                  <h3 className="text-xl font-bold text-blue-800 dark:text-blue-300 mb-3">
                    Nenhuma notícia encontrada
                  </h3>
                  <p className="text-blue-600 dark:text-blue-400 mb-4">
                    Tente ajustar os filtros ou volte mais tarde para novas atualizações.
                  </p>
                  <Button
                    variant="outline"
                    className="border-blue-300 text-blue-700 hover:bg-blue-50 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-900/30"
                    onClick={() => handleFilterChange({
                      search: '',
                      category: '',
                      sortBy: 'recent',
                      timeRange: 'all'
                    })}
                  >
                    <Sparkles className="h-4 w-4 mr-2" />
                    Limpar Filtros
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Newsletters;
