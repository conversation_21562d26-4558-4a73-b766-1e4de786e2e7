import React, { useState, useEffect, useRef } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { toast as sonnerToast } from "sonner";
import { Brain, Building2, Calendar, FileQuestion, ArrowRight, CheckCircle, Sparkles, Target } from "lucide-react";

type TutorialStep = {
  title: string;
  description: string;
  targetSelector?: string;
  position?: "top" | "bottom" | "left" | "right";
  icon?: React.ComponentType<{ className?: string }>;
  color?: string;
  badge?: string;
};

export const QuestionFilterTutorial = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  // Estados sempre declarados na mesma ordem
  const [currentStep, setCurrentStep] = useState(0);
  const [showTutorial, setShowTutorial] = useState(false);
  const [hasCompletedTutorial, setHasCompletedTutorial] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [targetRect, setTargetRect] = useState<DOMRect | null>(null);
  const [isExpandingHierarchy, setIsExpandingHierarchy] = useState(false);
  const [hierarchyMessage, setHierarchyMessage] = useState("");
  const [canProceed, setCanProceed] = useState(true);

  // Refs sempre declarados na mesma ordem
  const tutorialRef = useRef<HTMLDivElement>(null);

  const tutorialSteps: TutorialStep[] = [
    {
      title: "🎯 Filtros de Pediatria",
      description: "Bem-vindo aos filtros do PedBook! Aqui você pode personalizar sua experiência de estudos em pediatria. Vamos explorar cada seção para você dominar a plataforma.",
      icon: Target,
      color: "from-blue-500 to-indigo-600",
      badge: "INÍCIO"
    },
    {
      title: "🧠 Especialidades → Temas → Focos",
      description: "Esta é a seção mais importante! Aqui você navega pela hierarquia: ESPECIALIDADE → TEMA → FOCO. No PedBook, você terá acesso apenas à pediatria, mas com toda a profundidade de temas e focos específicos.",
      targetSelector: "[data-tutorial='specialty-section']",
      icon: Brain,
      color: "from-yellow-500 to-orange-600",
      badge: "HIERÁRQUICO"
    },
    {
      title: "🏥 Instituições",
      description: "Filtre questões por instituições específicas como UNIFESP, USP, UFMG e outras. Ideal para focar em bancas que você mais estuda ou que caem na sua prova de residência em pediatria.",
      targetSelector: "[data-tutorial='institution-section']",
      icon: Building2,
      color: "from-blue-500 to-cyan-600"
    },
    {
      title: "📅 Anos das Provas",
      description: "Selecione anos específicos das provas. Questões mais recentes podem refletir tendências atuais em pediatria, enquanto questões antigas testam conceitos consolidados.",
      targetSelector: "[data-tutorial='year-section']",
      icon: Calendar,
      color: "from-green-500 to-emerald-600"
    },
    {
      title: "🎯 Formato de Questão",
      description: "Filtre por formato específico: questões discursivas, objetivas, com imagens, casos clínicos pediátricos. Personalize conforme seu estilo de estudo.",
      targetSelector: "[data-tutorial='question-format-section']",
      icon: FileQuestion,
      color: "from-pink-500 to-rose-600"
    },
    {
      title: "🎲 Opções de Estudo",
      description: "Após filtrar, você pode: estudar questões FILTRADAS (seguindo seus critérios) ou um MIX ALEATÓRIO (variado para testar conhecimentos gerais de pediatria). Ambas são estratégias válidas!",
      targetSelector: "[data-tutorial='study-options']",
      icon: Sparkles,
      color: "from-indigo-500 to-purple-600",
      badge: "FINAL"
    }
  ];

  // useEffect 1: Verificar status do tutorial
  useEffect(() => {
    const checkTutorialStatus = async () => {
      if (!user) {
        setIsLoading(false);
        return;
      }

      try {
        const { data, error } = await supabase
          .from('user_preferences')
          .select('question_filter_tutorial_completed')
          .eq('user_id', user.id)
          .limit(1);

        if (error || !data || data.length === 0 || !data[0]?.question_filter_tutorial_completed) {
          setShowTutorial(true);
        } else {
          setHasCompletedTutorial(true);
        }
      } catch (err) {
        setShowTutorial(true);
      } finally {
        setIsLoading(false);
      }
    };

    checkTutorialStatus();
  }, [user]);

  // useEffect 2: Atualizar posição do elemento destacado e fazer scroll automático
  useEffect(() => {
    if (!showTutorial) return;

    const updatePosition = () => {
      const currentStepData = tutorialSteps[currentStep];
      if (currentStepData.targetSelector) {
        const targetElement = document.querySelector(currentStepData.targetSelector);
        if (targetElement) {
          const rect = targetElement.getBoundingClientRect();
          setTargetRect(rect);

          // Scroll automático para garantir que o elemento esteja visível
          const scrollToElement = () => {
            const elementTop = targetElement.offsetTop;
            const elementHeight = targetElement.offsetHeight;
            const windowHeight = window.innerHeight;

            // Calcular posição ideal para centralizar o elemento na tela
            const idealScrollPosition = elementTop - (windowHeight / 2) + (elementHeight / 2);

            // Verificar se o elemento está fora da viewport
            const elementBottom = rect.bottom;
            const elementTopVisible = rect.top;

            const isElementVisible = elementTopVisible >= 0 &&
                                   elementBottom <= windowHeight &&
                                   elementTopVisible < windowHeight;

            if (!isElementVisible) {
              window.scrollTo({
                top: Math.max(0, idealScrollPosition),
                behavior: 'smooth'
              });
            }
          };

          // Delay pequeno para garantir que o DOM foi atualizado
          setTimeout(scrollToElement, 100);
        } else {
          setTargetRect(null);
        }
      } else {
        setTargetRect(null);
      }
    };

    updatePosition();

    const handleResize = () => updatePosition();
    const handleScroll = () => updatePosition();

    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [currentStep, showTutorial]);

  // useEffect 3: Controlar scroll do body
  useEffect(() => {
    const originalStyle = document.body.style.overflow;

    if (showTutorial) {
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.body.style.overflow = originalStyle;
    };
  }, [showTutorial]);

  const markTutorialAsCompleted = async () => {
    if (!user) return;

    try {
      // Remover event listeners se existirem
      // Note: não precisamos referenciar updatePosition aqui pois os listeners
      // são removidos automaticamente no cleanup do useEffect

      const { data, error: fetchError } = await supabase
        .from('user_preferences')
        .select('id')
        .eq('user_id', user.id)
        .limit(1);

      if (fetchError) {
        return;
      }

      let saveError;

      if (data && data.length > 0) {
        const { error } = await supabase
          .from('user_preferences')
          .update({
            question_filter_tutorial_completed: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', data[0].id);

        saveError = error;
      } else {
        const { error } = await supabase
          .from('user_preferences')
          .insert({
            user_id: user.id,
            question_filter_tutorial_completed: true
          });

        saveError = error;
      }

      if (saveError) {
        toast({
          title: "Erro",
          description: "Não foi possível salvar o status do tutorial",
          variant: "destructive",
        });
      } else {
        sonnerToast.success("Tutorial de filtros concluído!", {
          description: "Agora você sabe como usar todos os filtros de pediatria"
        });
      }
    } catch (err) {
      // Error handling
    }
  };

  const expandHierarchyExample = () => {
    setIsExpandingHierarchy(true);
    setCanProceed(false);

    // Passo 1: Expandir especialidade
    setHierarchyMessage("🎯 Abrindo uma especialidade...");
    setTimeout(() => {
      const specialtySection = document.querySelector('[data-tutorial="specialty-section"]');
      if (!specialtySection) {
        setIsExpandingHierarchy(false);
        setCanProceed(true);
        return;
      }

      const specialtyButtons = specialtySection.querySelectorAll('.lucide-chevron-right');

      if (specialtyButtons.length > 0) {
        const specialtyButton = specialtyButtons[0].closest('button');
        if (specialtyButton) {
          (specialtyButton as HTMLElement).click();

          // Passo 2: Expandir tema
          setTimeout(() => {
            setHierarchyMessage("📚 Agora abrindo um tema...");
            setTimeout(() => {
              const themeButtons = specialtySection.querySelectorAll('.lucide-chevron-right');

              if (themeButtons.length > 1) {
                const themeButton = themeButtons[1].closest('button');
                if (themeButton) {
                  (themeButton as HTMLElement).click();

                  // Passo 3: Expandir foco
                  setTimeout(() => {
                    setHierarchyMessage("🎯 E agora um foco específico...");
                    setTimeout(() => {
                      const focusButtons = specialtySection.querySelectorAll('.lucide-chevron-right');

                      if (focusButtons.length > 2) {
                        const focusButton = focusButtons[2].closest('button');
                        if (focusButton) {
                          (focusButton as HTMLElement).click();
                        }
                      }

                      // Finalizar demonstração
                      setTimeout(() => {
                        setHierarchyMessage("✨ Viu como é fácil? Agora você pode prosseguir!");
                        setTimeout(() => {
                          setIsExpandingHierarchy(false);
                          setCanProceed(true);
                          setHierarchyMessage("");
                        }, 1500);
                      }, 1000);
                    }, 800);
                  }, 1000);
                }
              } else {
                // Se não encontrou temas, finalizar
                setTimeout(() => {
                  setHierarchyMessage("✨ Viu como é fácil? Agora você pode prosseguir!");
                  setTimeout(() => {
                    setIsExpandingHierarchy(false);
                    setCanProceed(true);
                    setHierarchyMessage("");
                  }, 1500);
                }, 1000);
              }
            }, 800);
          }, 1000);
        }
      } else {
        // Se não encontrou botões, finalizar
        setTimeout(() => {
          setIsExpandingHierarchy(false);
          setCanProceed(true);
        }, 1000);
      }
    }, 800);
  };

  const handleNext = () => {
    // Não permitir prosseguir se estiver expandindo hierarquia
    if (!canProceed) {
      return;
    }

    if (currentStep < tutorialSteps.length - 1) {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);

      // Se o próximo passo é o da hierarquia (passo 1), expande automaticamente
      if (nextStep === 1) {
        expandHierarchyExample();
      }
    } else {
      setShowTutorial(false);
      setHasCompletedTutorial(true);
      markTutorialAsCompleted();
    }
  };

  // Renderização condicional mais simples
  const shouldRender = !isLoading && !hasCompletedTutorial && showTutorial && user;

  if (!shouldRender) {
    return null;
  }

  const currentStepData = tutorialSteps[currentStep];

  // Calcular posição do tooltip responsivo sem sobrepor o elemento destacado
  const getTooltipStyle = () => {
    const isMobile = window.innerWidth < 768;
    const isTablet = window.innerWidth < 1024;

    // Se não há elemento alvo, centralizar
    if (!targetRect) {
      return {
        position: 'fixed' as const,
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: isMobile ? '90vw' : isTablet ? '80vw' : '450px',
        maxWidth: isMobile ? '350px' : '500px',
        maxHeight: isMobile ? '70vh' : '80vh',
        zIndex: 1003
      };
    }

    const tooltipWidth = isMobile ? Math.min(320, window.innerWidth * 0.9) :
                       isTablet ? Math.min(400, window.innerWidth * 0.8) : 450;
    const tooltipHeight = isMobile ? 280 : 350;
    const padding = 16;

    let top, left, transform = '';

    // Mobile: Priorizar posição acima ou abaixo do elemento
    if (isMobile) {
      // Tentar posicionar acima primeiro
      if (targetRect.top - tooltipHeight - padding > 0) {
        top = targetRect.top - tooltipHeight - padding;
        left = Math.max(padding, Math.min(
          window.innerWidth - tooltipWidth - padding,
          targetRect.left + (targetRect.width / 2) - (tooltipWidth / 2)
        ));
      }
      // Se não cabe acima, posicionar abaixo
      else if (targetRect.bottom + tooltipHeight + padding < window.innerHeight) {
        top = targetRect.bottom + padding;
        left = Math.max(padding, Math.min(
          window.innerWidth - tooltipWidth - padding,
          targetRect.left + (targetRect.width / 2) - (tooltipWidth / 2)
        ));
      }
      // Se não cabe nem acima nem abaixo, posicionar no topo da tela
      else {
        top = padding;
        left = Math.max(padding, Math.min(
          window.innerWidth - tooltipWidth - padding,
          targetRect.left + (targetRect.width / 2) - (tooltipWidth / 2)
        ));
      }
    }
    // Desktop/Tablet: Sempre acima para seção de especialidades
    else {
      // Para seção de especialidades, sempre posicionar acima
      if (currentStep === 1) {
        // Posicionar bem acima da seção de especialidades
        top = Math.max(padding, targetRect.top - tooltipHeight - (padding * 2));
        left = Math.max(padding, Math.min(
          window.innerWidth - tooltipWidth - padding,
          targetRect.left + (targetRect.width / 2) - (tooltipWidth / 2)
        ));
      }
      // Para outros passos, usar lógica normal
      else {
        // Tentar acima primeiro
        if (targetRect.top - tooltipHeight - padding > 0) {
          top = targetRect.top - tooltipHeight - padding;
          left = Math.max(padding, Math.min(
            window.innerWidth - tooltipWidth - padding,
            targetRect.left + (targetRect.width / 2) - (tooltipWidth / 2)
          ));
        }
        // Se não cabe acima, tentar à direita
        else if (targetRect.right + tooltipWidth + padding < window.innerWidth) {
          left = targetRect.right + padding;
          top = Math.max(padding, Math.min(
            window.innerHeight - tooltipHeight - padding,
            targetRect.top + (targetRect.height / 2) - (tooltipHeight / 2)
          ));
        }
        // Se não cabe à direita, tentar à esquerda
        else if (targetRect.left - tooltipWidth - padding > 0) {
          left = targetRect.left - tooltipWidth - padding;
          top = Math.max(padding, Math.min(
            window.innerHeight - tooltipHeight - padding,
            targetRect.top + (targetRect.height / 2) - (tooltipHeight / 2)
          ));
        }
        // Se não cabe nas laterais nem acima, posicionar abaixo
        else {
          top = targetRect.bottom + padding;
          left = Math.max(padding, Math.min(
            window.innerWidth - tooltipWidth - padding,
            targetRect.left + (targetRect.width / 2) - (tooltipWidth / 2)
          ));
        }
      }
    }

    return {
      position: 'fixed' as const,
      top: `${top}px`,
      left: `${left}px`,
      width: `${tooltipWidth}px`,
      maxHeight: isMobile ? '70vh' : '80vh',
      zIndex: 1003,
      transform
    };
  };

  const tooltipStyle = getTooltipStyle();
  const isMobile = window.innerWidth < 768;

  return (
    <div
      ref={tutorialRef}
      className="fixed inset-0 z-[1000] touch-none"
      style={{ overflow: 'hidden', height: '100vh' }}
    >
      <svg className="fixed inset-0 w-full h-full pointer-events-none" style={{ zIndex: 1001 }}>
        <defs>
          <mask id="filter-tutorial-mask">
            <rect width="100%" height="100%" fill="white" />
            {targetRect && (
              <rect
                x={targetRect.left}
                y={targetRect.top}
                width={targetRect.width}
                height={targetRect.height}
                fill="black"
              />
            )}
          </mask>
        </defs>
        <rect
          width="100%"
          height="100%"
          fill="rgba(0, 0, 0, 0.7)"
          mask="url(#filter-tutorial-mask)"
        />
      </svg>

      {/* Highlight ring around target */}
      {targetRect && (
        <div
          className="fixed pointer-events-none z-[1002] border-4 border-yellow-400 rounded-lg animate-pulse"
          style={{
            top: targetRect.top - 4,
            left: targetRect.left - 4,
            width: targetRect.width + 8,
            height: targetRect.height + 8,
          }}
        />
      )}

      <Card
        className={`p-0 bg-white rounded-2xl shadow-2xl z-[1004] animate-in fade-in-50 slide-in-from-bottom-4 fixed border-2 border-gray-100 overflow-hidden ${
          isMobile ? 'text-sm' : ''
        }`}
        style={{
          ...tooltipStyle,
          overflowY: 'auto'
        }}
      >
        {/* Header com gradiente */}
        <div className={`bg-gradient-to-r ${currentStepData.color || 'from-blue-500 to-purple-600'} ${
          isMobile ? 'p-3' : 'p-4'
        } text-white relative`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {currentStepData.icon && (
                <div className={`bg-white/20 ${isMobile ? 'p-1.5' : 'p-2'} rounded-lg`}>
                  {React.createElement(currentStepData.icon, {
                    className: isMobile ? "h-4 w-4" : "h-5 w-5"
                  })}
                </div>
              )}
              <div>
                <h3 className={`${isMobile ? 'text-base' : 'text-lg'} font-bold`}>
                  {currentStepData.title}
                </h3>
                <div className={`${isMobile ? 'text-xs' : 'text-sm'} text-white/80`}>
                  Passo {currentStep + 1} de {tutorialSteps.length}
                </div>
              </div>
            </div>
            {currentStepData.badge && (
              <Badge
                variant="secondary"
                className={`bg-white/20 text-white border-white/30 ${
                  isMobile ? 'text-xs px-2 py-0.5' : ''
                }`}
              >
                {currentStepData.badge}
              </Badge>
            )}
          </div>
        </div>

        {/* Conteúdo */}
        <div className={`${isMobile ? 'p-4' : 'p-6'} space-y-3`}>
          {/* Mensagem de expansão da hierarquia */}
          {isExpandingHierarchy && hierarchyMessage && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
                <p className="text-blue-700 text-sm font-medium">
                  {hierarchyMessage}
                </p>
              </div>
            </div>
          )}

          <p className={`text-gray-700 leading-relaxed ${
            isMobile ? 'text-sm' : 'text-base'
          }`}>
            {currentStepData.description}
          </p>

          {/* Barra de progresso */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`bg-gradient-to-r ${currentStepData.color || 'from-blue-500 to-purple-600'} h-2 rounded-full transition-all duration-500`}
              style={{ width: `${((currentStep + 1) / tutorialSteps.length) * 100}%` }}
            />
          </div>

          {/* Botões */}
          <div className={`flex justify-between items-center ${isMobile ? 'pt-1' : 'pt-2'}`}>
            <div className={`${isMobile ? 'text-xs' : 'text-xs'} text-gray-500`}>
              {currentStep === 0 ? "Vamos começar!" :
               currentStep === tutorialSteps.length - 1 ? "Quase lá!" :
               "Continue explorando"}
            </div>
            <Button
              onClick={handleNext}
              disabled={!canProceed}
              className={`bg-gradient-to-r ${currentStepData.color || 'from-blue-500 to-purple-600'} hover:opacity-90 text-white font-medium ${
                isMobile ? 'px-4 py-1.5 text-sm' : 'px-6 py-2'
              } rounded-lg transition-all duration-200 flex items-center gap-2 ${
                !canProceed ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {!canProceed ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-2 border-white border-t-transparent"></div>
                  Aguarde...
                </>
              ) : currentStep < tutorialSteps.length - 1 ? (
                <>
                  Próximo
                  <ArrowRight className={isMobile ? "h-3 w-3" : "h-4 w-4"} />
                </>
              ) : (
                <>
                  Entendi!
                  <CheckCircle className={isMobile ? "h-3 w-3" : "h-4 w-4"} />
                </>
              )}
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};
