import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus } from "lucide-react";
import { TagNameInput } from "./TagNameInput";
import { FixedValueInput } from "./FixedValueInput";
import { WeightRangeInputs } from "./WeightRangeInputs";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

interface TagInputFormProps {
  tagName: string;
  onTagNameChange: (value: string) => void;
  tagType: 'fixed' | 'multiplier' | 'age' | 'fixed_by_weight' | 'multiplier_by_fixed_age';
  multiplier: number;
  onMultiplierChange: (value: number) => void;
  maxValue?: number;
  onMaxValueChange: (value?: number) => void;
  startMonth: number;
  onStartMonthChange: (value: number) => void;
  endMonth: number;
  onEndMonthChange: (value: number) => void;
  ageValue: number;
  onAgeValueChange: (value: number) => void;
  startWeight: number;
  onStartWeightChange: (value: number) => void;
  endWeight: number;
  onEndWeightChange: (value: number) => void;
  onAddTag: () => void;
  onAddAgeRange: () => void;
  onAddWeightRange: () => void;
  ageRangesCount: number;
  weightRangesCount: number;
  roundResult: boolean;
  onRoundResultChange: (value: boolean) => void;
}

export function TagInputForm({
  tagName,
  onTagNameChange,
  tagType,
  multiplier,
  onMultiplierChange,
  maxValue,
  onMaxValueChange,
  startMonth,
  onStartMonthChange,
  endMonth,
  onEndMonthChange,
  ageValue,
  onAgeValueChange,
  startWeight,
  onStartWeightChange,
  endWeight,
  onEndWeightChange,
  onAddTag,
  onAddAgeRange,
  onAddWeightRange,
  ageRangesCount,
  weightRangesCount,
  roundResult,
  onRoundResultChange,
}: TagInputFormProps) {
  return (
    <div className="space-y-4">
      <div className="flex gap-2 items-start">
        <TagNameInput tagName={tagName} onTagNameChange={onTagNameChange} />

        {tagType === 'fixed_by_weight' ? (
          <>
            <WeightRangeInputs
              startWeight={startWeight}
              onStartWeightChange={onStartWeightChange}
              endWeight={endWeight}
              onEndWeightChange={onEndWeightChange}
              value={multiplier}
              onValueChange={onMultiplierChange}
            />
            <Button 
              type="button" 
              onClick={onAddWeightRange}
              disabled={!tagName || startWeight > endWeight}
            >
              
              <Plus className="h-4 w-4 mr-2" />
              Add Faixa
            </Button>
            <Button 
              type="button" 
              onClick={onAddTag}
              disabled={!tagName || weightRangesCount === 0}
            >
              <Plus className="h-4 w-4 mr-2" />
              Adicionar Tag
            </Button>
          </>
        ) : tagType === 'multiplier_by_fixed_age' ? (
          <>
            <div className="w-24">
              <Input
                type="number"
                placeholder="Mês inicial"
                value={startMonth || ""}
                onChange={(e) => onStartMonthChange(parseInt(e.target.value) || 0)}
                min="0"
              />
            </div>
            <div className="w-24">
              <Input
                type="number"
                placeholder="Mês final"
                value={endMonth || ""}
                onChange={(e) => onEndMonthChange(parseInt(e.target.value) || 0)}
                min="0"
              />
            </div>
            <div className="w-24">
              <Input
                type="number"
                placeholder="Valor"
                value={ageValue || ""}
                onChange={(e) => onAgeValueChange(parseFloat(e.target.value) || 0)}
                step="0.00001"
              />
              
            </div>
            <FixedValueInput
                value={maxValue || 0}
                onChange={(value) => onMaxValueChange(value || undefined)}
              />
            <Button 
              type="button" 
              onClick={onAddAgeRange}
              disabled={!tagName || startMonth > endMonth}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Faixa
            </Button>
            <Button 
              type="button" 
              onClick={onAddTag}
              disabled={!tagName || ageRangesCount === 0}
            >
              <Plus className="h-4 w-4 mr-2" />
              Adicionar Tag
            </Button>
          </>
        ) : tagType === 'age' ? (
          <>
            <div className="w-24">
              <Input
                type="number"
                placeholder="Mês inicial"
                value={startMonth || ""}
                onChange={(e) => onStartMonthChange(parseInt(e.target.value) || 0)}
                min="0"
              />
            </div>
            <div className="w-24">
              <Input
                type="number"
                placeholder="Mês final"
                value={endMonth || ""}
                onChange={(e) => onEndMonthChange(parseInt(e.target.value) || 0)}
                min="0"
              />
            </div>
            <div className="w-24">
              <Input
                type="number"
                placeholder="Valor"
                value={ageValue || ""}
                onChange={(e) => onAgeValueChange(parseFloat(e.target.value) || 0)}
                step="0.00001"
              />
            </div>
            <Button 
              type="button" 
              onClick={onAddAgeRange}
              disabled={!tagName || startMonth > endMonth}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Faixa
            </Button>
            <Button 
              type="button" 
              onClick={onAddTag}
              disabled={!tagName || ageRangesCount === 0}
            >
              <Plus className="h-4 w-4 mr-2" />
              Adicionar Tag
            </Button>
          </>
        ) : (
          <>
            <FixedValueInput
              value={multiplier}
              onChange={onMultiplierChange}
            />
            {tagType === 'multiplier' && (
              <FixedValueInput
                value={maxValue || 0}
                onChange={(value) => onMaxValueChange(value || undefined)}
              />
            )}
            <Button 
              type="button" 
              onClick={onAddTag}
              disabled={!tagName || !multiplier}
            >
              <Plus className="h-4 w-4 mr-2" />
              Adicionar Tag
            </Button>
          </>
        )
        
        }
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="round-result"
          checked={roundResult}
          onCheckedChange={onRoundResultChange}
        />
        <Label htmlFor="round-result">Aproximar resultado para número inteiro</Label>
      </div>
    </div>
  );
}