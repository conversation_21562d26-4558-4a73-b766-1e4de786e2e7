import { Input } from "@/components/ui/input";

interface WeightRangeInputsProps {
  startWeight: number;
  onStartWeightChange: (value: number) => void;
  endWeight: number;
  onEndWeightChange: (value: number) => void;
  value: number;
  onValueChange: (value: number) => void;
}

export function WeightRangeInputs({
  startWeight,
  onStartWeightChange,
  endWeight,
  onEndWeightChange,
  value,
  onValueChange
}: WeightRangeInputsProps) {
  const formatNumber = (value: number): string => {
    return value.toString().replace('.', ',');
  };

  const parseNumber = (value: string): number => {
    const parsedValue = parseFloat(value.replace(',', '.'));
    return isNaN(parsedValue) ? 0 : parsedValue;
  };

  return (
    <>
      <div className="w-24">
        <Input
          type="number"
          placeholder="Peso inicial"
          value={startWeight || ""}
          onChange={(e) => onStartWeightChange(parseFloat(e.target.value) || 0)}
          min="0"
          step="0.1"
        />
      </div>
      <div className="w-24">
        <Input
          type="number"
          placeholder="Peso final"
          value={endWeight || ""}
          onChange={(e) => onEndWeightChange(parseFloat(e.target.value) || 0)}
          min="0"
          step="0.1"
        />
      </div>
      <div className="w-24">
        <Input
          type="text"
          placeholder="Valor"
          value={value ? formatNumber(value) : ""}
          onChange={(e) => onValueChange(parseNumber(e.target.value))}
        />
      </div>
    </>
  );
}