import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { session_id } = await req.json();

    // Atualizar a query para usar COUNT(DISTINCT)
    const query = `
      SELECT 
        ROUND(AVG(response_time)::numeric, 2) as avg_response_time,
        COUNT(DISTINCT question_id) FILTER (WHERE response_status = true) as total_correct,
        COUNT(DISTINCT question_id) FILTER (WHERE response_status = false) as total_incorrect,
        jsonb_object_agg(
          theme_id::text,
          jsonb_build_object(
            'id', theme_id,
            'correct', COUNT(DISTINCT question_id) FILTER (WHERE response_status = true),
            'total', COUNT(DISTINCT question_id)
          )
        ) FILTER (WHERE theme_id IS NOT NULL) as by_theme,
        jsonb_object_agg(
          specialty_id::text,
          jsonb_build_object(
            'id', specialty_id,
            'correct', COUNT(DISTINCT question_id) FILTER (WHERE response_status = true),
            'total', COUNT(DISTINCT question_id)
          )
        ) FILTER (WHERE specialty_id IS NOT NULL) as by_specialty,
        jsonb_object_agg(
          focus_id::text,
          jsonb_build_object(
            'id', focus_id,
            'correct', COUNT(DISTINCT question_id) FILTER (WHERE response_status = true),
            'total', COUNT(DISTINCT question_id)
          )
        ) FILTER (WHERE focus_id IS NOT NULL) as by_focus
      FROM session_events
      WHERE session_id = $1
      GROUP BY session_id
    `;

    const { data, error } = await supabase
      .from('rpc')
      .select('*')
      .rpc('get_session_statistics', { p_session_id: session_id })
      .single();

    if (error) throw error;

    return new Response(
      JSON.stringify(data),
      { 
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );

  } catch (error) {
    console.error('Error:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 400,
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );
  }
});