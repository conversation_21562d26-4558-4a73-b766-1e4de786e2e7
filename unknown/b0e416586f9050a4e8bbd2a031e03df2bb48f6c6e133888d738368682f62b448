import { useNavigate, useLocation } from "react-router-dom";
import AuthDialog from "./auth/AuthDialog";
import { HeaderLogo } from "./header/HeaderLogo";
import { HeaderSearch } from "./header/HeaderSearch";
import { HeaderActions } from "./header/HeaderActions";
import { useAuth } from "@/hooks/useAuth";
import { useNotification } from "@/context/NotificationContext";

interface HeaderProps {
  hideSearch?: boolean;
}

const Header = ({ hideSearch }: HeaderProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, profile, isAdmin, signOut } = useAuth();
  const { showNotification } = useNotification();

  const isIndexPage = location.pathname === "/";

  const handleLogout = async () => {
    await signOut();
    showNotification({
      title: "Logout realizado com sucesso",
      description: "Você foi desconectado da sua conta.",
      type: "success",
      buttonText: "Continuar"
    });
  };

  return (
    <header className="hidden sm:block w-full bg-white/90 dark:bg-slate-800/90 backdrop-blur-md shadow-sm z-50">
      <div className="container mx-auto px-4 py-2">
        <div className="flex items-center">
          <div className="flex items-center w-[180px]">
            <HeaderLogo />
          </div>

          <div className="flex-1 flex justify-center">
            <HeaderSearch hideSearch={hideSearch} isIndexPage={isIndexPage} />
          </div>

          <div className="flex items-center gap-2 w-[180px] justify-end">
            {user ? (
              <HeaderActions
                user={user}
                profile={profile}
                isAdmin={isAdmin}
                onFeedbackClick={() => {}}
                onLogout={handleLogout}
              />
            ) : (
              <AuthDialog />
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
