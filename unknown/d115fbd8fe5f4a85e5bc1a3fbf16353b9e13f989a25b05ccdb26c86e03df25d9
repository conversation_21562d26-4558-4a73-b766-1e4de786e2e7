import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Plus, Search, Pencil } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { CategoryDialog } from "@/components/admin/CategoryDialog";
import { supabase } from "@/integrations/supabase/client";

export default function Categories() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<any>(null);
  const [showDialog, setShowDialog] = useState(false);

  const { data: categories } = useQuery({
    queryKey: ["medication-categories"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_medication_categories")
        .select("*")
        .order("name");
      
      if (error) throw error;
      return data;
    },
  });

  const filteredCategories = categories?.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-2xl font-bold">Categorias de Medicamentos</h1>
        
        <Button onClick={() => {
          setSelectedCategory(null);
          setShowDialog(true);
        }}>
          <Plus className="mr-2 h-4 w-4" />
          Nova Categoria
        </Button>
      </div>

      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={18} />
        <Input
          type="search"
          placeholder="Pesquisar categorias..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCategories?.map((category) => (
          <div
            key={category.id}
            className="bg-white rounded-lg shadow-sm p-6 space-y-2"
          >
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-semibold">{category.name}</h3>
                {category.description && (
                  <p className="text-gray-600 mt-1">{category.description}</p>
                )}
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setSelectedCategory(category);
                  setShowDialog(true);
                }}
              >
                <Pencil className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}
      </div>

      <CategoryDialog
        category={selectedCategory}
        isOpen={showDialog}
        onClose={() => {
          setShowDialog(false);
          setSelectedCategory(null);
        }}
      />
    </div>
  );
}