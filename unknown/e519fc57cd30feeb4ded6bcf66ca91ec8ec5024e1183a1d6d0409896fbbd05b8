import { Button } from "@/components/ui/button";
import { Wand2, Timer, AlertCircle } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useTextEnhancement } from "@/hooks/useTextEnhancement";
import { useEffect, useState } from "react";
import { useToast } from "@/hooks/use-toast";

interface EnhanceTextButtonProps {
  onEnhance: () => Promise<void>;
  isEnhancing: boolean;
}

export const EnhanceTextButton = ({ onEnhance, isEnhancing }: EnhanceTextButtonProps) => {
  const { canEnhance, lastEnhancement } = useTextEnhancement();
  const [timeLeft, setTimeLeft] = useState<string>("");
  const { toast } = useToast();

  useEffect(() => {
    if (!canEnhance && lastEnhancement) {
      const interval = setInterval(() => {
        const lastEnhancementTime = new Date(lastEnhancement.created_at);
        const thirtyMinutesLater = new Date(lastEnhancementTime.getTime() + 30 * 60 * 1000);
        const now = new Date();
        const diff = thirtyMinutesLater.getTime() - now.getTime();

        if (diff <= 0) {
          setTimeLeft("");
          clearInterval(interval);
          return;
        }

        const minutes = Math.floor(diff / 1000 / 60);
        const seconds = Math.floor((diff / 1000) % 60);
        setTimeLeft(`${minutes}:${seconds.toString().padStart(2, '0')}`);
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [canEnhance, lastEnhancement]);

  const showInfoToast = () => {
    const { dismiss } = toast({
      title: "Função de Melhoria de Texto",
      description: (
        <div className="space-y-4">
          <p className="text-sm sm:text-base text-gray-700">
            A função de melhoria de texto pode ser usada a cada 30 minutos.
            {timeLeft && (
              <span className="block mt-2 font-medium">
                Você poderá usar novamente em: {timeLeft}
              </span>
            )}
          </p>
          <div className="flex justify-end">
            <Button variant="outline" onClick={() => dismiss()}>
              Ok, entendi
            </Button>
          </div>
        </div>
      ),
      duration: 5000,
      className: "fixed left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%] max-w-md w-full bg-white shadow-lg rounded-lg border p-6",
    });
  };

  return (
    <div className="flex items-center gap-2 w-full">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              type="button"
              variant="secondary"
              size="sm"
              onClick={onEnhance}
              disabled={isEnhancing || !canEnhance}
              className={`flex-1 flex items-center justify-center gap-2 transition-opacity ${
                isEnhancing ? "animate-pulse" : ""
              }`}
            >
              <Wand2 className={`h-4 w-4 ${isEnhancing ? "animate-spin" : ""}`} />
              {isEnhancing ? "Melhorando texto..." : "Melhorar texto"}
              {!canEnhance && timeLeft && (
                <>
                  <Timer className="h-4 w-4" />
                  <span className="text-xs">{timeLeft}</span>
                </>
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>
              {canEnhance
                ? "Melhora automaticamente a formatação e clareza do seu texto"
                : `Aguarde ${timeLeft} para usar novamente`}
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {!canEnhance && (
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={showInfoToast}
          className="px-2"
        >
          <AlertCircle className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
};