
import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogClose
} from "@/components/ui/dialog";
import { X, Clock, Calendar, BookOpen, Brain, Target, AlertTriangle, Trash2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import type { StudyTopic } from '@/types/study-schedule';
import { RevisionButton } from '@/components/progress/RevisionButton';
import { toast } from '@/hooks/use-toast';

interface TopicDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  topic: StudyTopic | null;
  onMarkStudied?: (topicId: string) => void;
  onDelete?: (topic: StudyTopic) => void;
}

export const TopicDetailsDialog = ({
  open,
  onOpenChange,
  topic,
  onMarkStudied,
  onDelete
}: TopicDetailsDialogProps) => {
  if (!topic) return null;
  
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Fácil': return 'bg-green-100 text-green-600 border-green-200';
      case 'Médio': return 'bg-yellow-100 text-yellow-600 border-yellow-200';
      case 'Difícil': return 'bg-red-100 text-red-600 border-red-200';
      default: return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };
  
  const getDifficultyIcon = (difficulty: string) => {
    switch (difficulty) {
      case 'Fácil': return <Badge className="bg-green-100 text-green-600 border border-green-200">Fácil</Badge>;
      case 'Médio': return <Badge className="bg-yellow-100 text-yellow-600 border border-yellow-200">Médio</Badge>;
      case 'Difícil': return <Badge className="bg-red-100 text-red-600 border border-red-200">Difícil</Badge>;
      default: return <Badge className="bg-gray-100 text-gray-600 border border-gray-200">Normal</Badge>;
    }
  };

  const getActivityIcon = (activity: string | null | undefined) => {
    if (!activity) return <Target className="h-5 w-5 text-green-500" />;
    
    if (activity.includes('Questões')) {
      return <Brain className="h-5 w-5 text-purple-500" />;
    } else if (activity.includes('Revisão')) {
      return <BookOpen className="h-5 w-5 text-blue-500" />;
    } else {
      return <Target className="h-5 w-5 text-green-500" />;
    }
  };

  // Function to handle "Mark as Studied" with dialog closing
  const handleMarkStudied = () => {
    if (topic.id && onMarkStudied) {
      onMarkStudied(topic.id);
      onOpenChange(false); // Close the dialog after marking as studied
      toast({
        title: "Tópico marcado como estudado",
        description: "Você receberá uma revisão em breve para reforçar seu aprendizado.",
      });
    }
  };

  // Handle delete button click
  const handleDelete = () => {
    if (onDelete) {
      onDelete(topic);
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px] p-0 gap-0 overflow-hidden max-w-[95vw] mx-auto">
        <DialogHeader className="p-0">
          <div className="bg-gradient-to-r from-[#58CC02] to-[#46a302] text-white p-4 relative">
            <DialogTitle className="font-bold text-xl mb-1">Detalhes do Estudo</DialogTitle>
            <p className="text-white/90 text-sm">
              {topic.revision_number 
                ? `${topic.revision_number}ª Revisão`
                : "Tópico de Estudo"
              }
            </p>
            <DialogClose className="absolute right-4 top-4 rounded-full bg-white/20 p-1 opacity-70 hover:opacity-100">
              <X className="h-4 w-4" />
            </DialogClose>
          </div>
        </DialogHeader>
        
        <div className="p-4 flex flex-col gap-4">
          {/* Especialidade e Tema */}
          <div className="bg-[#F7F7F7] rounded-lg p-3">
            <h3 className="text-sm text-gray-500 font-medium mb-1">Especialidade</h3>
            <p className="font-semibold text-gray-800 break-words">{topic.specialty}</p>
            
            <h3 className="text-sm text-gray-500 font-medium mt-3 mb-1">Tema</h3>
            <p className="font-semibold text-gray-800 break-words">{topic.theme}</p>
            
            <h3 className="text-sm text-gray-500 font-medium mt-3 mb-1">Foco</h3>
            <p className="font-semibold text-gray-800 break-words">{topic.focus}</p>
          </div>
          
          {/* Atividade */}
          <div className="bg-[#F7F7F7] rounded-lg p-3">
            <div className="flex items-center gap-2 mb-2">
              {getActivityIcon(topic.activity)}
              <h3 className="text-sm text-gray-500 font-medium">Atividade</h3>
            </div>
            <p className="font-semibold text-gray-800 break-words">{topic.activity || "Estudo"}</p>
          </div>
          
          {/* Tempo */}
          <div className="bg-[#F7F7F7] rounded-lg p-3">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Clock className="h-4 w-4 text-gray-500 flex-shrink-0" />
                  <h3 className="text-sm text-gray-500 font-medium">Horário</h3>
                </div>
                <p className="font-semibold text-gray-800">{topic.startTime}</p>
              </div>
              
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Clock className="h-4 w-4 text-gray-500 flex-shrink-0" />
                  <h3 className="text-sm text-gray-500 font-medium">Duração</h3>
                </div>
                <p className="font-semibold text-gray-800">{topic.duration}</p>
              </div>
              
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <AlertTriangle className="h-4 w-4 text-gray-500 flex-shrink-0" />
                  <h3 className="text-sm text-gray-500 font-medium">Dificuldade</h3>
                </div>
                {getDifficultyIcon(topic.difficulty)}
              </div>
            </div>
          </div>
          
          {/* Status */}
          <div className="bg-[#F7F7F7] rounded-lg p-3">
            <div className="flex items-center gap-2 mb-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <h3 className="text-sm text-gray-500 font-medium">Status</h3>
            </div>
            
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
              {topic.study_status === 'completed' ? (
                <Badge className="bg-green-100 text-green-600 px-2 py-1">
                  Concluído
                </Badge>
              ) : (
                <Badge className="bg-yellow-100 text-yellow-600 px-2 py-1">
                  Pendente
                </Badge>
              )}
              
              {topic.next_revision_date && (
                <div className="text-xs sm:text-sm bg-blue-50 text-blue-600 px-2 py-1 rounded-md flex items-center gap-1">
                  <Calendar className="h-3 w-3 sm:h-3.5 sm:w-3.5 flex-shrink-0" />
                  <span className="whitespace-nowrap">
                    Próx: {new Date(topic.next_revision_date).toLocaleDateString()}
                  </span>
                </div>
              )}
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex flex-col gap-2 mt-2">
            {topic.id && onMarkStudied && topic.study_status !== 'completed' && (
              <Button 
                variant="duolingo"
                className="w-full gap-2 py-2"
                onClick={handleMarkStudied}
              >
                <BookOpen className="h-4 w-4 flex-shrink-0" />
                Marcar como Estudado
              </Button>
            )}
            
            <div className="grid grid-cols-1 gap-2">  
              {onDelete && (
                <Button 
                  variant="outline" 
                  className="border-red-300 hover:bg-red-50 hover:text-red-700 text-red-600" 
                  onClick={handleDelete}
                >
                  <Trash2 className="h-4 w-4 mr-1.5 flex-shrink-0" />
                  Excluir
                </Button>
              )}
            </div>
            
            <Button 
              variant="outline" 
              className="w-full border-gray-300" 
              onClick={() => onOpenChange(false)}
            >
              Fechar
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TopicDetailsDialog;
