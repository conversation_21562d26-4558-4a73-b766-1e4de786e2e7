/**
 * Utilitários para otimização de imagens
 * Inclui compressão, redimensionamento e conversão para WebP
 */

export interface ImageOptimizationOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'webp' | 'jpeg' | 'png';
  maintainAspectRatio?: boolean;
}

/**
 * Comprime e otimiza uma imagem
 */
export const compressImage = async (
  file: File,
  options: ImageOptimizationOptions = {}
): Promise<File> => {
  const {
    maxWidth = 1920,
    maxHeight = 1080,
    quality = 0.8,
    format = 'webp',
    maintainAspectRatio = true
  } = options;

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      try {
        // Calcular dimensões otimizadas
        let { width, height } = calculateOptimalDimensions(
          img.width,
          img.height,
          maxWidth,
          maxHeight,
          maintainAspectRatio
        );

        canvas.width = width;
        canvas.height = height;

        // Desenhar imagem redimensionada
        ctx?.drawImage(img, 0, 0, width, height);

        // Converter para blob otimizado
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Falha ao comprimir imagem'));
              return;
            }

            // Criar novo arquivo otimizado
            const optimizedFile = new File(
              [blob],
              `${file.name.split('.')[0]}.${format}`,
              {
                type: `image/${format}`,
                lastModified: Date.now()
              }
            );

            resolve(optimizedFile);
          },
          `image/${format}`,
          quality
        );
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Falha ao carregar imagem'));
    };

    img.src = URL.createObjectURL(file);
  });
};

/**
 * Calcula dimensões otimizadas mantendo proporção
 */
export const calculateOptimalDimensions = (
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight: number,
  maintainAspectRatio: boolean = true
): { width: number; height: number } => {
  if (!maintainAspectRatio) {
    return {
      width: Math.min(originalWidth, maxWidth),
      height: Math.min(originalHeight, maxHeight)
    };
  }

  const aspectRatio = originalWidth / originalHeight;

  let width = originalWidth;
  let height = originalHeight;

  // Redimensionar se exceder limites
  if (width > maxWidth) {
    width = maxWidth;
    height = width / aspectRatio;
  }

  if (height > maxHeight) {
    height = maxHeight;
    width = height * aspectRatio;
  }

  return {
    width: Math.round(width),
    height: Math.round(height)
  };
};

/**
 * Gera uma versão blur/placeholder da imagem
 */
export const generateBlurPlaceholder = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      try {
        // Criar versão muito pequena (blur placeholder)
        canvas.width = 20;
        canvas.height = 20;

        ctx?.drawImage(img, 0, 0, 20, 20);

        // Aplicar blur
        if (ctx) {
          ctx.filter = 'blur(2px)';
          ctx.drawImage(canvas, 0, 0);
        }

        resolve(canvas.toDataURL('image/jpeg', 0.1));
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Falha ao gerar placeholder'));
    };

    img.src = URL.createObjectURL(file);
  });
};

/**
 * Verifica se o formato é suportado para otimização
 */
export const isSupportedImageFormat = (file: File): boolean => {
  const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  return supportedTypes.includes(file.type);
};

/**
 * Calcula redução de tamanho após otimização
 */
export const calculateCompressionRatio = (originalSize: number, compressedSize: number): number => {
  return Math.round(((originalSize - compressedSize) / originalSize) * 100);
};
