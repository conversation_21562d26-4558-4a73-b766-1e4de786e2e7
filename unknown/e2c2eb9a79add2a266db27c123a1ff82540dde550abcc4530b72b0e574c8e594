import { useState, useEffect } from "react";
import { format } from "date-fns";
import { Pencil, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { calculateDosage } from "@/lib/dosageCalculator";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import type { PrescriptionWithMedications } from "./types";
import type { DosageResult } from "@/lib/dosageCalculator";
import { PrescriptionPatientInfo } from "./patient/PrescriptionPatientInfo";
import { PrescriptionMedicationList } from "./medications/PrescriptionMedicationList";
import { PrescriptionForm } from "./PrescriptionForm";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { getThemeClasses } from "@/components/ui/theme-utils";

interface PrescriptionDetailsProps {
  prescription: PrescriptionWithMedications;
  calculatedDosages: Record<string, DosageResult | string>;
  isUpdating: boolean;
  onWeightChange: (weight: string) => void;
  onAgeChange: (age: string) => void;
}

export const PrescriptionDetails = ({
  prescription,
  calculatedDosages,
  isUpdating,
  onWeightChange,
  onAgeChange,
}: PrescriptionDetailsProps) => {
  const [localWeight, setLocalWeight] = useState(prescription.patient_weight?.toString() || "");
  const [dosages, setDosages] = useState<Record<string, DosageResult | string>>(calculatedDosages);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    const updateDosages = async () => {
      const newDosages: Record<string, DosageResult | string> = {};
      const weight = Math.min(parseFloat(localWeight) || 0, 100);
      const age = prescription.patient_age || 0;

      for (const med of prescription.pedbook_prescription_medications || []) {
        const dosageTemplate = med.pedbook_medication_dosages?.dosage_template || med.dosage_template;
        if (dosageTemplate) {
          try {
            const calculatedDosage = await calculateDosage(
              dosageTemplate,
              weight,
              age,
              med.medication_id
            );
            newDosages[med.id] = calculatedDosage;
          } catch (error) {
            newDosages[med.id] = { text: "Erro ao calcular dosagem" };
          }
        }
      }

      setDosages(newDosages);
    };

    updateDosages();
  }, [prescription.pedbook_prescription_medications, localWeight, prescription.patient_age]);

  const handleWeightChange = (value: string) => {
    setLocalWeight(value);
    onWeightChange(value);
  };

  const handleDelete = async () => {
    try {
      const { error: reactionsError } = await supabase
        .from("pedbook_prescription_user_reactions")
        .delete()
        .eq("prescription_id", prescription.id);

      if (reactionsError) throw reactionsError;

      const { error: medsError } = await supabase
        .from("pedbook_prescription_medications")
        .delete()
        .eq("prescription_id", prescription.id);

      if (medsError) throw medsError;

      const { error } = await supabase
        .from("pedbook_prescriptions")
        .delete()
        .eq("id", prescription.id);

      if (error) throw error;

      toast({
        title: "Prescrição removida",
        description: "A prescrição foi removida com sucesso.",
      });

      window.location.href = "/prescriptions";
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao remover prescrição",
        description: error.message,
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className={getThemeClasses.glassContainer("space-y-4 p-6 from-primary/5 via-primary/10 to-transparent backdrop-blur-sm border border-primary/10 dark:border-primary/20")}>
        <div className="flex justify-between items-start">
          <div>
            <h1 className={getThemeClasses.gradientHeading("text-2xl")}>
              {prescription.name}
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Criada em: {prescription.created_at ? format(new Date(prescription.created_at), "dd/MM/yyyy") : ''}
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => setIsEditDialogOpen(true)}
              className="hover:bg-primary/10 dark:hover:bg-blue-900/30"
            >
              <Pencil className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleDelete}
              className="hover:bg-destructive/10 dark:hover:bg-red-900/30"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      <PrescriptionPatientInfo
        weight={localWeight}
        age={prescription.patient_age?.toString() || ""}
        isUpdating={isUpdating}
        onWeightChange={handleWeightChange}
        onAgeChange={onAgeChange}
      />

      <PrescriptionMedicationList
        prescription={prescription}
        calculatedDosages={dosages}
      />

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <PrescriptionForm
            session={{ user: { id: prescription.user_id } }}
            onSuccess={() => {
              setIsEditDialogOpen(false);
              window.location.reload();
            }}
            prescription={prescription}
            onClose={() => setIsEditDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};
