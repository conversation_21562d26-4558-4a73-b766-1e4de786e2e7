
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface CrotalicQuestionProps {
  question: string;
  onAnswer: (answer: boolean) => void;
  selectedAnswer: boolean | null;
}

export const CrotalicQuestion = ({
  question,
  onAnswer,
  selectedAnswer,
}: CrotalicQuestionProps) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const isInitialQuestion = question === "O paciente apresenta clínica de envenenamento crotálico na admissão?";
  const isObservationQuestion = question === "A evolução apresenta clínica de envenenamento?";

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-8"
    >
      <div className="p-6 rounded-xl bg-purple-50 border border-purple-200 glass-card relative overflow-hidden dark:bg-purple-900/20 dark:border-purple-800/40">
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent pointer-events-none" />
        
        {isObservationQuestion && (
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg dark:bg-yellow-900/30 dark:border-yellow-700/40">
            <p className="text-yellow-800 dark:text-yellow-300 font-medium">
              Manter o paciente em observação mínima de 12h
            </p>
          </div>
        )}
        
        <h2 className="text-xl font-semibold text-gray-800 mb-6 text-center relative z-10 dark:text-gray-100">
          {question}
        </h2>
        
        <div className="flex flex-col sm:flex-row gap-4 relative z-10 mb-8">
          <Button
            onClick={() => onAnswer(true)}
            variant={selectedAnswer === true ? "default" : "outline"}
            className={`flex-1 transition-all duration-300 ${
              selectedAnswer === true
                ? "bg-gradient-to-r from-purple-500 to-violet-500 hover:from-purple-600 hover:to-violet-600"
                : "hover:bg-purple-50 border-purple-200 dark:hover:bg-purple-800/50 dark:border-purple-700"
            }`}
          >
            Com clínica de envenenamento
          </Button>
          <Button
            onClick={() => onAnswer(false)}
            variant={selectedAnswer === false ? "default" : "outline"}
            className={`flex-1 transition-all duration-300 ${
              selectedAnswer === false
                ? "bg-gradient-to-r from-purple-500 to-violet-500 hover:from-purple-600 hover:to-violet-600"
                : "hover:bg-purple-50 border-purple-200 dark:hover:bg-purple-800/50 dark:border-purple-700"
            }`}
          >
            Sem clínica de envenenamento
          </Button>
        </div>

        {isInitialQuestion && (
          <div className="space-y-4 bg-white/50 rounded-lg p-4 backdrop-blur-sm border border-purple-100 dark:bg-purple-900/20 dark:border-purple-800/30">
            <h3 className="text-lg font-semibold text-purple-800 dark:text-purple-300">
              Sinais de envenenamento crotálico incluem:
            </h3>
            
            <div className="space-y-3 text-gray-700 dark:text-gray-200">
              <div>
                <p className="font-medium text-purple-700 dark:text-purple-400">Manifestações locais:</p>
                <p>Dor e edema discretos ou ausentes (devido apenas à lesão da mordida).</p>
              </div>
              
              <div>
                <p className="font-medium text-purple-700 dark:text-purple-400">Manifestações sistêmicas leves:</p>
                <p>Ptose palpebral discreta, turvação visual leve.</p>
              </div>
              
              <div>
                <p className="font-medium text-purple-700 dark:text-purple-400">Manifestações graves:</p>
                <p>Insuficiência renal aguda, hemorragias, mioglobinúria.</p>
              </div>
            </div>

            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button 
                  variant="outline" 
                  className="w-full mt-4 bg-purple-100 hover:bg-purple-200 border-purple-300 text-purple-800 dark:bg-purple-900/30 dark:hover:bg-purple-900/50 dark:border-purple-700/50 dark:text-purple-300"
                >
                  Saiba Mais
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Detalhamento dos Sinais de Envenenamento Crotálico</DialogTitle>
                  <DialogDescription className="space-y-4 pt-4">
                    <div>
                      <h4 className="font-semibold text-purple-700 dark:text-purple-400 mb-2">Manifestações Locais</h4>
                      <ul className="list-disc pl-5 space-y-1 dark:text-gray-200">
                        <li>Dor local leve ou ausente</li>
                        <li>Edema discreto ou ausente</li>
                        <li>Parestesia local variável</li>
                      </ul>
                    </div>
                    
                    <div>
                      <h4 className="font-semibold text-purple-700 dark:text-purple-400 mb-2">Manifestações Sistêmicas</h4>
                      <ul className="list-disc pl-5 space-y-1 dark:text-gray-200">
                        <li>Ptose palpebral</li>
                        <li>Turvação visual</li>
                        <li>Oftalmoplegia</li>
                        <li>Urina escura</li>
                      </ul>
                    </div>
                    
                    <div>
                      <h4 className="font-semibold text-purple-700 dark:text-purple-400 mb-2">Manifestações Graves</h4>
                      <ul className="list-disc pl-5 space-y-1 dark:text-gray-200">
                        <li>Insuficiência renal aguda</li>
                        <li>Mioglobinúria</li>
                        <li>Hemorragias</li>
                      </ul>
                    </div>
                  </DialogDescription>
                </DialogHeader>
              </DialogContent>
            </Dialog>
          </div>
        )}
      </div>
    </motion.div>
  );
};
