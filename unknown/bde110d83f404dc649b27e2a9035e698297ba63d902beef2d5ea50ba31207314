
import { useState, useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { calculateDosage, DosageResult } from "@/lib/dosageCalculator";
import { DosageContent } from "./dosage/DosageContent";
import { useMedicationTags } from "@/hooks/useMedicationData";


interface DosageDisplayProps {
  dosage: {
    id: string;
    medication_id: string;
    name: string;
    type: string;
    summary?: string;
    description?: string;
    dosage_template: string;
    max_value?: number;
    multiplier?: number;
  };
  weight: number;
  age: number;
  requiredMeasures?: string[];
}

export const DosageDisplay = ({
  dosage,
  weight,
  age,
  requiredMeasures = ["weight", "age"]
}: DosageDisplayProps) => {
  const [calculatedDosage, setCalculatedDosage] = useState("");
  // Inicializa como true se há tags não calculadas no template
  const [isCalculating, setIsCalculating] = useState(() => {
    return /\(\([^)]+\)\)/.test(dosage.dosage_template);
  });
  const [restriction, setRestriction] = useState<{
    type: "age" | "weight" | "both";
    minAge?: number;
    minWeight?: number;
  } | null>(null);

  // Usar hook centralizado para buscar tags
  const { data: tags = [], isLoading: tagsLoading, error: tagsError } = useMedicationTags(dosage.medication_id);

  // Log only if there are issues
  if (!dosage.medication_id) {
    console.warn('⚠️ [DosageDisplay] Missing medication_id for dosage:', dosage.id);
  }
  if (tagsError) {
    console.error('❌ [DosageDisplay] Tags error:', tagsError);
  }


  useEffect(() => {
    const findDosageRestrictions = () => {
      // Usar tags do hook centralizado
      if (!tags || tags.length === 0) {
        return null;
      }

      // Filtrar as tags relevantes para esta dosagem específica
      const relevantTags = tags.filter(tag =>
        dosage.dosage_template.includes(`((${tag.name}))`)
      );

      // Encontrar restrições específicas desta dosagem
      let minAgeRestriction: number | undefined;
      let minWeightRestriction: number | undefined;

      if (relevantTags && relevantTags.length > 0) {
        // Filtrar tags relacionadas a idade
        const ageBasedTags = relevantTags.filter(tag => 
          (tag.type === 'age' || tag.type === 'multiplier_by_fixed_age') && 
          tag.start_month !== null && 
          tag.start_month > 0
        );
        
        // Filtrar tags relacionadas a peso
        const weightBasedTags = relevantTags.filter(tag => 
          tag.type === 'fixed_by_weight' && 
          tag.start_weight !== null && 
          tag.start_weight > 0
        );
        
        // Encontrar a menor idade mínima
        if (ageBasedTags.length > 0) {
          minAgeRestriction = Math.min(...ageBasedTags.map(tag => tag.start_month || Infinity));
        }
        
        // Encontrar o menor peso mínimo
        if (weightBasedTags.length > 0) {
          minWeightRestriction = Math.min(...weightBasedTags.map(tag => tag.start_weight || Infinity));
        }
      }

      // Determinar o tipo de restrição
      if (minAgeRestriction && minWeightRestriction) {
        return {
          type: "both" as const,
          minAge: minAgeRestriction,
          minWeight: minWeightRestriction
        };
      } else if (minAgeRestriction) {
        return {
          type: "age" as const,
          minAge: minAgeRestriction
        };
      } else if (minWeightRestriction) {
        return {
          type: "weight" as const,
          minWeight: minWeightRestriction
        };
      }

      return null;
    };

    const updateDosage = async () => {
      if (!dosage.dosage_template) {
        setCalculatedDosage("");
        setRestriction(null);
        return;
      }

      // Verifica apenas as medidas necessárias
      const hasRequiredWeight = !requiredMeasures.includes("weight") || (requiredMeasures.includes("weight") && weight > 0);
      const hasRequiredAge = !requiredMeasures.includes("age") || (requiredMeasures.includes("age") && age >= 0);

      if (!hasRequiredWeight || !hasRequiredAge) {
        const missingMeasures = [];
        if (requiredMeasures.includes("weight") && !hasRequiredWeight) missingMeasures.push("peso");
        if (requiredMeasures.includes("age") && !hasRequiredAge) missingMeasures.push("idade");
        setCalculatedDosage(`Preencha o ${missingMeasures.join(" e ")} do paciente`);
        setRestriction(null);
        return;
      }
      
      // Verificar restrições para esta dosagem específica
      const dosageRestrictions = findDosageRestrictions();
      
      // Verificar se o paciente atende às restrições
      let hasDosageRestrictions = false;
      
      if (dosageRestrictions) {
        if (dosageRestrictions.type === "age" && dosageRestrictions.minAge) {
          if (age < dosageRestrictions.minAge) {
            hasDosageRestrictions = true;
            setRestriction(dosageRestrictions);
          }
        } else if (dosageRestrictions.type === "weight" && dosageRestrictions.minWeight) {
          if (weight < dosageRestrictions.minWeight) {
            hasDosageRestrictions = true;
            setRestriction(dosageRestrictions);
          }
        } else if (dosageRestrictions.type === "both" && dosageRestrictions.minAge && dosageRestrictions.minWeight) {
          if (age < dosageRestrictions.minAge || weight < dosageRestrictions.minWeight) {
            hasDosageRestrictions = true;
            setRestriction(dosageRestrictions);
          }
        }
      }
      
      // Se não atende às restrições, não calcular a dosagem
      if (hasDosageRestrictions) {
        setCalculatedDosage("");
        setIsCalculating(false);
        return;
      }

      try {
        // Calcular a dosagem normalmente passando as tags
        setIsCalculating(true);
        const result = await calculateDosage(
          dosage.dosage_template,
          weight,
          age,
          dosage.medication_id,
          tags
        );
        setIsCalculating(false);

        // Verificar se todos os valores calculados são zero
        const allResultsZero = result.text.match(/(\d*[.,]\d+)/g)?.every(match => {
          const num = parseFloat(match.replace(',', '.'));
          return num === 0;
        });

        // Se todos os valores são zero e temos restrições do cálculo, use essas restrições
        if (allResultsZero && result.restrictions) {
          setRestriction(result.restrictions);
        } else if (!hasDosageRestrictions) {
          // Se não temos restrições da dosagem específica, use as do resultado
          setRestriction(result.restrictions || null);
        }

        // Processa o resultado para garantir a formatação correta de números pequenos
        const processedResult = result.text.replace(/(\d*[.,]\d+)/g, (match) => {
          const num = parseFloat(match.replace(',', '.'));
          if (num > 0 && num < 0.001) {
            return num.toFixed(4).replace('.', ',');
          }
          if (num > 0 && num < 0.01) {
            return num.toFixed(3).replace('.', ',');
          }
          return match;
        });

        setCalculatedDosage(processedResult);
      } catch (error) {
        console.error("Erro ao calcular dosagem:", error);
        setCalculatedDosage("");
        setIsCalculating(false);
      }

    };

    updateDosage();
  }, [weight, age, dosage, requiredMeasures, tags]);

  // Converte meses em texto legível
  const formatAgeRestriction = (months: number): string => {
    if (months < 12) {
      return `${months} ${months === 1 ? 'mês' : 'meses'}`;
    } else {
      const years = Math.floor(months / 12);
      const remainingMonths = months % 12;
      
      if (remainingMonths === 0) {
        return `${years} ${years === 1 ? 'ano' : 'anos'}`;
      } else {
        return `${years} ${years === 1 ? 'ano' : 'anos'} e ${remainingMonths} ${remainingMonths === 1 ? 'mês' : 'meses'}`;
      }
    }
  };

  // Monta a mensagem de restrição
  const getRestrictionMessage = (): string | null => {
    if (!restriction) return null;

    if (restriction.type === "age" && restriction.minAge) {
      return `Esta apresentação é indicada para pacientes a partir de ${formatAgeRestriction(restriction.minAge)} de idade.`;
    } else if (restriction.type === "weight" && restriction.minWeight) {
      return `Esta apresentação é indicada para pacientes com peso mínimo de ${restriction.minWeight}kg.`;
    } else if (restriction.type === "both" && restriction.minAge && restriction.minWeight) {
      return `Esta apresentação é indicada para pacientes a partir de ${formatAgeRestriction(restriction.minAge)} de idade e com peso mínimo de ${restriction.minWeight}kg.`;
    }
    
    return null;
  };

  return (
    <DosageContent
      name={dosage.name}
      calculatedDosage={calculatedDosage}
      summary={dosage.summary}
      requiredMeasures={requiredMeasures}
      restrictionMessage={getRestrictionMessage()}
      isCalculating={isCalculating}
    />
  );
};
