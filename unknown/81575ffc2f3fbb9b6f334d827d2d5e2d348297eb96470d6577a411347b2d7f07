
import { useState } from "react";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { ChevronLeft } from "lucide-react";
import { Link } from "react-router-dom";
import { CalculatorSEO } from "@/components/seo/CalculatorSEO";
import { CALCULATOR_SEO_DATA } from "@/data/calculatorSEOData";
import { getThemeClasses } from "@/components/ui/theme-utils";

interface CapurroValues {
  pregas: number | null;
  pele: number | null;
  orelha: number | null;
  glandula: number | null;
  xale: number | null;
  cabecaPesco: number | null;
}

const CapurroNeuroCalculator = () => {
  const seoData = CALCULATOR_SEO_DATA['capurro-neuro'];

  const [values, setValues] = useState<CapurroValues>({
    pregas: null,
    pele: null,
    orelha: null,
    glandula: null,
    xale: null,
    cabecaPesco: null,
  });

  const criteria = {
    pregas: [
      { value: 0, label: "Sem pregas" },
      { value: 5, label: "Discretas anteriores" },
      { value: 10, label: "Anteriores mais definidas" },
      { value: 15, label: "Sulcos anteriores" },
      { value: 20, label: "Sulcos na metade posterior" },
    ],
    pele: [
      { value: 0, label: "Fina e gelatinosa" },
      { value: 10, label: "Mais grossa com descamação fina" },
      { value: 15, label: "Grossa com sulcos, descamação de mãos e pés" },
      { value: 20, label: "Grossa e apergaminhada, sulcos profundos" },
    ],
    orelha: [
      { value: 0, label: "Chata e disforme" },
      { value: 8, label: "Pouco encurvada" },
      { value: 16, label: "Encurvada em toda parte superior" },
      { value: 24, label: "Toda encurvada" },
    ],
    glandula: [
      { value: 0, label: "Não palpável" },
      { value: 5, label: "< 5 mm" },
      { value: 10, label: "5 a 10 mm" },
      { value: 15, label: "> 10 mm" },
    ],
    xale: [
      { value: 0, label: "Cotovelo na axila oposta" },
      { value: 6, label: "Cotovelo ultrapassa linha média" },
      { value: 12, label: "Cotovelo na linha média" },
      { value: 16, label: "Cotovelo não atinge a linha média" },
    ],
    cabecaPesco: [
      { value: 0, label: "Deflexionada a 270°" },
      { value: 4, label: "180° a 270°" },
      { value: 8, label: "180°" },
      { value: 12, label: "< 180°" },
    ],
  };

  const handleChange = (key: keyof CapurroValues, value: string) => {
    setValues((prev) => ({
      ...prev,
      [key]: parseInt(value, 10),
    }));
  };

  const calculateGestationalAge = () => {
    const total = Object.values(values).reduce((sum, value) => sum + (value || 0), 0) + 200;
    const weeks = Math.floor(total / 7);
    const days = total % 7;
    return { total, weeks, days };
  };

  const result = calculateGestationalAge();
  const allFieldsFilled = Object.values(values).every((value) => value !== null);

  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen flex flex-col")}>
      <CalculatorSEO {...seoData} />
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto space-y-8">
          <div className="flex items-center gap-4">
            <Link to="/calculadoras">
              <Button variant="ghost" size="icon" className="hover:bg-primary/10 dark:hover:bg-primary/20">
                <ChevronLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h1 className={getThemeClasses.gradientHeading("text-3xl")}>
              Capurro Neurológico
            </h1>
          </div>
          <p className="text-gray-600 dark:text-gray-300">
            Avaliação da idade gestacional com base em critérios neurológicos e somáticos
          </p>

          <Card className={getThemeClasses.card("p-6 space-y-6")}>
            {(Object.entries(criteria) as [keyof CapurroValues, typeof criteria.pregas][]).map(([key, options]) => (
              <div key={key} className="space-y-2">
                <Label className="text-base font-medium text-gray-800 dark:text-gray-200">
                  {key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1').trim()}
                </Label>
                <Select
                  value={values[key]?.toString() || ""}
                  onValueChange={(value) => handleChange(key, value)}
                >
                  <SelectTrigger className={getThemeClasses.select("w-full")}>
                    <SelectValue placeholder="Selecione uma opção" />
                  </SelectTrigger>
                  <SelectContent>
                    {options.map((option) => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ))}

            {allFieldsFilled && (
              <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
                <div className="text-center space-y-4">
                  <div className="text-4xl font-bold text-primary dark:text-blue-400">
                    {result.weeks} semanas e {result.days} dias
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Total de pontos: {result.total}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400 max-w-md mx-auto">
                    Nota: Este método pode apresentar variações em comparação com outros métodos de avaliação da idade gestacional.
                  </div>
                </div>
              </div>
            )}
          </Card>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default CapurroNeuroCalculator;
