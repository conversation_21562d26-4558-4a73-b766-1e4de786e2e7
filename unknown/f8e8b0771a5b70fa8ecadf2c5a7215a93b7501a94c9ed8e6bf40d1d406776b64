import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Json } from '@/integrations/supabase/types/json';

interface CategoryData {
  name: string;
  correct: number;
  total: number;
}

interface SessionStats {
  total_questions: number;
  total_correct: number;
  total_incorrect: number;
  by_specialty: Record<string, { name: string; correct: number; total: number }>;
  by_theme: Record<string, { name: string; correct: number; total: number }>;
  by_focus: Record<string, { name: string; correct: number; total: number }>;
}

interface StudySession {
  id: string;
  stats: Json;
  updated_at: string;
}

interface SkillTreeData {
  bySpecialty: Record<string, CategoryData>;
  byTheme: Record<string, CategoryData>;
  byFocus: Record<string, CategoryData>;
  totalQuestions: number;
  correctAnswers: number;
  incorrectAnswers: number;
}

export const useSkillTreeData = () => {
  return useQuery({
    queryKey: ['skill-tree-data'],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data: sessions, error: sessionsError } = await supabase
        .from('study_sessions')
        .select('*')
        .eq('user_id', user.id)
        .eq('status', 'completed')
        .order('updated_at', { ascending: false });

      if (sessionsError) {
        console.error('Error fetching sessions:', sessionsError);
        throw sessionsError;
      }

      const { data: categories } = await supabase
        .from('study_categories')
        .select('id, name, type');

      const categoryMap = categories?.reduce((acc, cat) => ({
        ...acc,
        [cat.id]: cat.name
      }), {} as Record<string, string>) || {};

      if (!sessions || sessions.length === 0) {
        return {
          bySpecialty: {},
          byTheme: {},
          byFocus: {},
          totalQuestions: 0,
          correctAnswers: 0,
          incorrectAnswers: 0
        } as SkillTreeData;
      }

      const aggregatedData = (sessions as StudySession[]).reduce((acc, session) => {
        const stats = session.stats as unknown as SessionStats;

        const sessionTotalQuestions = (stats.total_correct || 0) + (stats.total_incorrect || 0);
        const sessionCorrectAnswers = Number(stats.total_correct) || 0;
        const sessionIncorrectAnswers = Number(stats.total_incorrect) || 0;

        acc.totalQuestions += sessionTotalQuestions;
        acc.correctAnswers += sessionCorrectAnswers;
        acc.incorrectAnswers += sessionIncorrectAnswers;

        const processCategory = (categoryData: Record<string, any> | undefined, categoryType: keyof Pick<SkillTreeData, 'bySpecialty' | 'byTheme' | 'byFocus'>) => {
          if (!categoryData) return;

          Object.entries(categoryData).forEach(([id, data]) => {
            if (!acc[categoryType][id]) {
              acc[categoryType][id] = {
                name: categoryMap[id] || 'Categoria não encontrada',
                correct: 0,
                total: 0
              };
            }
            acc[categoryType][id].correct += (data.correct || 0);
            acc[categoryType][id].total += (data.total || 0);
          });
        };

        if (stats.by_specialty) processCategory(stats.by_specialty, 'bySpecialty');
        if (stats.by_theme) processCategory(stats.by_theme, 'byTheme');
        if (stats.by_focus) processCategory(stats.by_focus, 'byFocus');

        return acc;
      }, {
        bySpecialty: {},
        byTheme: {},
        byFocus: {},
        totalQuestions: 0,
        correctAnswers: 0,
        incorrectAnswers: 0
      } as SkillTreeData);

      return aggregatedData;
    },
    retry: false
  });
};
