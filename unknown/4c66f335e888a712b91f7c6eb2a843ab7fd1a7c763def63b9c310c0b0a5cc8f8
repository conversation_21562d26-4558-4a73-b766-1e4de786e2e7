import { useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface GrowthCurveDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function GrowthCurveDialog({ open, onOpenChange }: GrowthCurveDialogProps) {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [gender, setGender] = useState<string>("");
  const [gestationalAge, setGestationalAge] = useState<string>("");
  const [growthType, setGrowthType] = useState<string>("");
  const [file, setFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) {
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Por favor, selecione uma imagem para a curva de crescimento.",
      });
      return;
    }

    setIsLoading(true);
    try {
      // Upload the image
      const fileExt = file.name.split(".").pop();
      const filePath = `${crypto.randomUUID()}.${fileExt}`;
      
      const { error: uploadError } = await supabase.storage
        .from("growth-curves")
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from("growth-curves")
        .getPublicUrl(filePath);

      // Create the growth curve record
      const { error: insertError } = await supabase
        .from("pedbook_growth_curves")
        .insert({
          title,
          description,
          gender,
          gestational_age: gestationalAge,
          growth_type: growthType,
          image_url: publicUrl,
        });

      if (insertError) throw insertError;

      toast({
        title: "Sucesso",
        description: "Curva de crescimento criada com sucesso!",
      });

      queryClient.invalidateQueries({ queryKey: ["growth-curves"] });
      onOpenChange(false);
      resetForm();
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao criar curva de crescimento",
        description: error.message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setTitle("");
    setDescription("");
    setGender("");
    setGestationalAge("");
    setGrowthType("");
    setFile(null);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Nova Curva de Crescimento</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="title">Título</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              required
            />
          </div>

          <div>
            <Label htmlFor="description">Descrição</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="min-h-[100px]"
            />
          </div>

          <div>
            <Label htmlFor="gender">Gênero</Label>
            <Select value={gender} onValueChange={setGender} required>
              <SelectTrigger>
                <SelectValue placeholder="Selecione o gênero" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="male">Menino</SelectItem>
                <SelectItem value="female">Menina</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="gestationalAge">Tempo Gestacional</Label>
            <Select value={gestationalAge} onValueChange={setGestationalAge} required>
              <SelectTrigger>
                <SelectValue placeholder="Selecione o tempo gestacional" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="term">A Termo</SelectItem>
                <SelectItem value="preterm">Pré-termo</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="growthType">Tipo de Crescimento</Label>
            <Select value={growthType} onValueChange={setGrowthType} required>
              <SelectTrigger>
                <SelectValue placeholder="Selecione o tipo de crescimento" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="healthy">Saudável</SelectItem>
                <SelectItem value="disorder">Transtorno</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="image">Imagem</Label>
            <Input
              id="image"
              type="file"
              accept="image/*"
              onChange={(e) => setFile(e.target.files?.[0] || null)}
              required
            />
          </div>

          <div className="flex justify-end gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Salvando..." : "Salvar"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}