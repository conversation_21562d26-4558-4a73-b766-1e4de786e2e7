import { Toggle } from "@/components/ui/toggle";
import { CheckCircle2, Clock } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface StatusToggleProps {
  feedbackId: string;
  currentStatus: string;
  onStatusChange: (newStatus: string) => void;
}

export function StatusToggle({ feedbackId, currentStatus, onStatusChange }: StatusToggleProps) {
  const { toast } = useToast();
  const isResolved = currentStatus === "resolvido";

  const handleToggle = async () => {
    const newStatus = isResolved ? "aguardando" : "resolvido";
    
    try {
      const { error } = await supabase
        .from("pedbook_feedbacks")
        .update({ status: newStatus })
        .eq("id", feedbackId);

      if (error) throw error;

      onStatusChange(newStatus);
      
      toast({
        title: "Status atualizado",
        description: `Feedback marcado como ${newStatus}`,
      });
    } catch (error) {
      console.error("Error updating feedback status:", error);
      toast({
        title: "Erro ao atualizar status",
        description: "Não foi possível atualizar o status do feedback",
        variant: "destructive",
      });
    }
  };

  return (
    <Toggle
      pressed={isResolved}
      onPressedChange={handleToggle}
      className="gap-2"
      variant="outline"
    >
      {isResolved ? (
        <>
          <CheckCircle2 className="h-4 w-4 text-green-500" />
          Resolvido
        </>
      ) : (
        <>
          <Clock className="h-4 w-4 text-yellow-500" />
          Aguardando
        </>
      )}
    </Toggle>
  );
}