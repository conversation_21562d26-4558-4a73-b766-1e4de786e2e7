
import React from "react";
import { Link } from "react-router-dom";
import { ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";

interface TopicCardProps {
  topic: {
    id: string;
    name: string;
    slug: string;
    category_slug?: string;
    summary_count?: number;
  };
  categorySlug: string;
  searchTerm?: string;
}

export const TopicCard = ({ topic, categorySlug, searchTerm }: TopicCardProps) => {
  // Função para destacar o texto pesquisado
  const highlightText = (text: string) => {
    if (!searchTerm || searchTerm.trim() === "") return text;
    
    const parts = text.split(new RegExp(`(${searchTerm})`, 'gi'));
    return (
      <>
        {parts.map((part, i) => 
          part.toLowerCase() === searchTerm.toLowerCase() ? 
            <span key={i} className="bg-yellow-200 dark:bg-yellow-900 font-semibold">{part}</span> : 
            part
        )}
      </>
    );
  };

  return (
    <Link to={`/condutas-e-manejos/${categorySlug}/${topic.slug}`} className="block w-full">
      <div className={cn(
        "p-4 md:p-5 rounded-xl border transition-all duration-300",
        "bg-white dark:bg-slate-800/90 backdrop-blur-sm",
        "border-blue-100 dark:border-slate-700",
        "hover:shadow-md hover:border-blue-200 dark:hover:border-blue-700/50",
        "group"
      )}>
        <div className="flex justify-between items-center gap-3">
          <div className="flex-1 min-w-0">
            <h3 className="text-md md:text-lg font-medium text-blue-800 dark:text-blue-300 group-hover:text-blue-600 dark:group-hover:text-blue-200 transition-colors">
              {highlightText(topic.name)}
            </h3>
            
            {/* Removido o contador de resumos */}
          </div>
          
          <div className="h-8 w-8 md:h-10 md:w-10 flex-shrink-0 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center group-hover:bg-blue-100 dark:group-hover:bg-blue-800/50 transition-colors">
            <ChevronRight className="h-5 w-5 text-blue-400 dark:text-blue-500 group-hover:text-blue-500 dark:group-hover:text-blue-400" />
          </div>
        </div>
      </div>
    </Link>
  );
};
