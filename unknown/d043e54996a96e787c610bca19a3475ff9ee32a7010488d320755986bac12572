import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { DehydrationAssessment } from "./treatment/DehydrationAssessment";

interface TreatmentPlanProps {
  weight: number;
  onComplete: () => void;
}

export const TreatmentPlan = ({ weight, onComplete }: TreatmentPlanProps) => {
  const [step, setStep] = useState<'initial' | 'dehydration'>('initial');
  const [showAssessmentTool, setShowAssessmentTool] = useState(false);
  const minVolume = Math.round(weight * 10);
  const maxVolume = Math.round(weight * 20);

  const handleAssessmentComplete = (hasDehydration: boolean) => {

  };

  if (step === 'initial') {
    return (
      <Card className="p-6 space-y-4">
        <h3 className="text-lg font-semibold"><PERSON><PERSON><PERSON> Inicial</h3>
        <div className="space-y-4">
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <h4 className="font-medium text-green-800">Confirmação do Diagnóstico de CAD</h4>
            <p className="text-green-700">Avaliar sinais de choque, rebaixamento do nível de consciência/coma</p>
          </div>
          
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h4 className="font-medium text-blue-800">Terapia endovenosa:</h4>
            <p className="text-blue-700">
              <li>Parâmetro a utilizar: SF 10-20 mL/KG</li>
              <li>Valor calculado: SF 0,9% {minVolume}-{maxVolume} mL EV em 20-30 min podendo ser repetido.</li>
            </p>
          </div>
        </div>
        <Button onClick={() => setStep('dehydration')} className="w-full">
          Continuar avaliação
        </Button>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card className="p-6 space-y-4">
        <h3 className="text-lg font-semibold">Avaliação da Desidratação</h3>
        <p className="text-gray-600">Após 1 hora, mantém sinais de desidratação?</p>
        
        {!showAssessmentTool && (
          <Button 
            onClick={() => setShowAssessmentTool(true)}
            variant="outline"
            className="w-full"
          >
            Abrir Ferramenta de Avaliação
          </Button>
        )}
        
        <Alert className="bg-blue-50 border-blue-200">
          <AlertDescription className="space-y-2 text-sm text-blue-800">
            <h4 className="font-semibold">Critérios de avaliação de desidrataçãao:</h4>
            <ul className="list-disc pl-4 space-y-1">
            <li>Estado geral: ativo e alerta (sem desidratação), irritado ou intranquilo (com desidratação), comatoso, letárgico, hipotônico ou inconsciente (desidratação grave)</li>
  <li>Olhos: sem alteração (sem desidratação), fundos (com desidratação ou desidratação grave)</li>
  <li>Sede: sem sede (sem desidratação), sedento e bebe rápido (com desidratação), incapaz de beber (desidratação grave)</li>
  <li>Lágrimas: presentes (sem desidratação), ausentes (com desidratação ou desidratação grave)</li>
  <li>Boca/Língua: úmida (sem desidratação), seca ou muito seca (com desidratação ou desidratação grave)</li>
  <li>Sinal da prega abdominal: desaparece imediatamente (sem desidratação), desaparece lentamente (com desidratação), desaparece muito lentamente (&gt; 2 segundos) (desidratação grave)</li>
  <li>Pulso: cheio (sem desidratação ou com desidratação), fraco ou ausente (desidratação grave)</li>
  <li>Perda de peso: sem perda (sem desidratação), até 10% (com desidratação), acima de 10% (desidratação grave)</li>
            </ul>
          </AlertDescription>
        </Alert>
        
        <div className="grid grid-cols-2 gap-4">
          <Button 
            onClick={() => setStep('initial')} 
            variant="outline"
          >
            Sim, repetir expansão
          </Button>
          <Button 
            onClick={onComplete}
            variant="outline"
          >
            Não, prosseguir
          </Button>
        </div>
      </Card>

      {showAssessmentTool && (
        <DehydrationAssessment onAssessmentComplete={handleAssessmentComplete} />
      )}
    </div>
  );
};