import { useEffect, useState } from 'react';

/**
 * Hook adaptado para PedBook - configurado para buscar questões do domínio 'residencia'
 * Foco em fazer funcionar 100% primeiro, filtros específicos depois
 */
export const useDomain = () => {
  const [domain] = useState<string>('residencia'); // Domínio correto no banco
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    // Simular carregamento rápido para manter compatibilidade
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 100);

    return () => clearTimeout(timer);
  }, [domain]);

  return {
    domain,
    isLoading,
    userProfile: null, // Não usado no PedBook
    isResidencia: true, // Agora é residência mesmo
    isReady: !isLoading && !!domain // Flag para indicar que o domínio está pronto
  };
};
