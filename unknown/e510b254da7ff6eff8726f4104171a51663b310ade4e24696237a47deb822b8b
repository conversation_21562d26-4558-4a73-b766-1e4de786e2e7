import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import { Json } from '@/integrations/supabase/types/json';

interface PerformanceData {
  name: string;
  accuracy: number;
  total: number;
}

interface AnalysisResult {
  specialties: PerformanceData[];
  themes: PerformanceData[];
  focuses: PerformanceData[];
  weekly_plan?: Array<{
    day: string;
    topics: Array<{
      name: string;
      duration: string;
      type: string;
    }>;
  }>;
}

interface UserStats {
  total_questions: number;
  correct_answers: number;
  incorrect_answers: number;
  avg_response_time: number;
  total_study_time: number;
  by_specialty: Json;
  by_theme: Json;
  by_focus: Json;
  streak_days: number;
  weekly_stats: Json;
}

export const useGroqAnalysis = () => {
  const { toast } = useToast();

  return useQuery({
    queryKey: ['groq-analysis'],
    queryFn: async () => {


      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not found');

      // Fetch user's study statistics
      const { data: userStatsData, error: statsError } = await supabase
        .rpc('get_user_statistics', { p_user_id: user.id }) as { data: UserStats[] | null, error: Error | null };

      if (statsError) {
        throw statsError;
      }

      if (!userStatsData || userStatsData.length === 0) {
        throw new Error('No user statistics found');
      }

      const userStats = userStatsData[0];

      // Call Groq Edge Function for analysis
      const { data, error } = await supabase.functions.invoke('groq-recommendations', {
        body: {
          userStats: {
            totalQuestions: userStats.total_questions,
            correctAnswers: userStats.correct_answers,
            accuracy: userStats.total_questions > 0
              ? (userStats.correct_answers / userStats.total_questions) * 100
              : 0,
            specialtyAnalysis: Object.entries(userStats.by_specialty || {}).map(([id, data]: [string, any]) => ({
              name: data.name,
              accuracy: (data.correct / data.total) * 100,
              total: data.total,
              correct: data.correct
            })),
            themeAnalysis: Object.entries(userStats.by_theme || {}).map(([id, data]: [string, any]) => ({
              name: data.name,
              accuracy: (data.correct / data.total) * 100,
              total: data.total,
              correct: data.correct
            })),
            focusAnalysis: Object.entries(userStats.by_focus || {}).map(([id, data]: [string, any]) => ({
              name: data.name,
              accuracy: (data.correct / data.total) * 100,
              total: data.total,
              correct: data.correct
            })),
            averageTimePerQuestion: userStats.avg_response_time,
            totalTimeSpent: userStats.total_study_time,
            streakDays: userStats.streak_days,
            weeklyStats: userStats.weekly_stats
          }
        }
      });

      if (error) {
        toast({
          title: "Erro na análise",
          description: "Não foi possível gerar recomendações no momento.",
          variant: "destructive"
        });
        throw error;
      }

      // Transform the data to match the expected format
      const transformedData: AnalysisResult = {
        specialties: data.specialtyAnalysis || [],
        themes: data.themeAnalysis || [],
        focuses: data.focusAnalysis || [],
        weekly_plan: data.weekly_plan
      };

      console.log('✅ [useGroqAnalysis] Analysis completed:', transformedData);
      return transformedData;
    },
    retry: 1,
    staleTime: 1000 * 60 * 5, // Cache for 5 minutes
  });
};