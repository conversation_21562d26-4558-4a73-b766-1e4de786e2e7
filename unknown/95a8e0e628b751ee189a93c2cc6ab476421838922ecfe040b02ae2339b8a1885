import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { AlertTriangle, ChevronUp, Info, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { supabase } from "@/integrations/supabase/client";
import { useWeight } from "@/hooks/useWeight";
import { useAge } from "@/hooks/useAge";
import { useNavigate } from "react-router-dom";
import { MedicationUseCases } from "./medication/MedicationUseCases";
import { PatientInfoSection } from "./patient/PatientInfoSection";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { MedicationInstructionsDialog } from "./medication/MedicationInstructionsDialog";

interface MedicationDetailsProps {
  medicationSlug: string;
  name: string;
  brands: string;
  description: string;
  trigger?: React.ReactNode;
}

export const MedicationDetails = ({
  medicationSlug,
  name,
  brands,
  description,
  trigger
}: MedicationDetailsProps) => {
  const navigate = useNavigate();
  const { weight, setWeight, displayWeight, setTempWeight } = useWeight();
  const { age, setAge } = useAge();
  const [showScrollTop, setShowScrollTop] = useState(false);

  const { data: medication } = useQuery({
    queryKey: ["medication", medicationSlug],
    queryFn: async () => {
      if (!medicationSlug) return null;

      const { data, error } = await supabase
        .from("pedbook_medications")
        .select(`
          *,
          pedbook_medication_use_cases (
            id,
            name,
            description,
            pedbook_medication_dosages (*),
            display_order
          )
        `)
        .eq("slug", medicationSlug)
        .maybeSingle();

      if (error) {
        if (error.code === "PGRST116") {
          return null;
        }
        throw error;
      }
      return data;
    },
    enabled: !!medicationSlug,
  });

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setShowScrollTop(e.currentTarget.scrollTop > 100);
  };

  const scrollToTop = () => {
    const content = document.querySelector('.medication-details');
    content?.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" className="flex-1 gap-2 hover:bg-primary/5">
            Ver detalhes
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] bg-gradient-to-br from-white to-primary/5 lg:h-auto">
        <DialogHeader>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/medicamentos")}
            className="absolute left-4 top-4 text-primary/60 hover:text-primary h-8 px-2 lg:flex hidden"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Voltar ao Menu
          </Button>
          <div className="space-y-2 mt-8">
            <div className="flex items-center justify-between">
              <DialogTitle className="text-2xl bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600">
                {name}
              </DialogTitle>
              {medication?.id && (
                <MedicationInstructionsDialog
                  medicationId={medication.id}
                  medicationName={name}
                  slug={medicationSlug}
                />
              )}
            </div>
            <div className="text-sm text-muted-foreground space-y-1">
              {description && <p>{description}</p>}
              {brands && <p>Marcas comerciais: {brands}</p>}
            </div>
          </div>
        </DialogHeader>

        <div
          className="medication-details space-y-6 max-h-[calc(100vh-16rem)] lg:max-h-[70vh] overflow-y-auto pr-4 scroll-smooth"
          onScroll={handleScroll}
        >
          <PatientInfoSection
            weight={displayWeight}
            onWeightChange={setTempWeight}
            onWeightCommit={setWeight}
            age={age}
            onAgeChange={setAge}
            onAgeCommit={setAge}
          />

          <MedicationUseCases
            useCases={medication?.pedbook_medication_use_cases || []}
            weight={weight}
            age={age}
            medicationId={medication?.id}
          />

          {medication?.contraindications && (
            <div className="border-l-4 border-destructive bg-destructive/5 p-4 rounded-r-lg space-y-2 animate-fade-in">
              <h4 className="font-semibold flex items-center gap-2 text-destructive">
                <AlertTriangle className="h-5 w-5" />
                Contra-indicações
              </h4>
              <div className="ml-7 space-y-2 text-sm whitespace-pre-line">
                {medication.contraindications}
              </div>
            </div>
          )}


          {medication?.guidelines && (
            <div className="border-l-4 border-primary bg-primary/5 p-4 rounded-r-lg space-y-2 animate-fade-in">
              <h4 className="font-semibold flex items-center gap-2 text-primary">
                <Info className="h-5 w-5" />
                Orientações
              </h4>
              <div className="ml-7 space-y-2 text-sm whitespace-pre-line">
                {medication.guidelines}
              </div>
            </div>
          )}
        </div>

        <Button
          variant="outline"
          size="icon"
          className={cn(
            "fixed bottom-4 right-4 rounded-full transition-all duration-300",
            showScrollTop ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4 pointer-events-none"
          )}
          onClick={scrollToTop}
          aria-label="Voltar ao topo da página"
        >
          <ChevronUp className="h-4 w-4" />
        </Button>
      </DialogContent>
    </Dialog>
  );
};
