
import { Json } from "@/integrations/supabase/types/json";

export interface Question {
  id: string;
  question_content: string;
  response_choices: string[];
  correct_choice: number;
  question_format?: 'ALTERNATIVAS' | 'DISSERTATIVA' | 'VERDADEIRO_FALSO';

  // IDs diretos das tabelas relacionadas
  specialty_id?: string;
  theme_id?: string;
  focus_id?: string;
  exam_location?: string;

  // Objetos relacionados (quando incluídos via join)
  location?: {
    id: string;
    name: string;
  };
  institution?: {
    id: string;
    name: string;
  };
  specialty?: {
    id: string;
    name: string;
  };
  theme?: {
    id: string;
    name: string;
  };
  focus?: {
    id: string;
    name: string;
  };
  exam_year?: number;
  statistics?: AlternativeStatistics[];
  alternativeComments?: { [key: number]: string };
  comments: Comment[];
  owner?: string;
  ownerComments?: string;
  likes?: number;
  dislikes?: number;
  liked_by?: string[];
  disliked_by?: string[];
  alternative_comments?: Json;
  created_at?: string;
  final_comment?: string;
  ai_commentary?: Json;
  discursiveAnswer?: string;
  knowledge_domain?: string;
  media_attachments?: string[]; // Campo para armazenar URLs de imagens como array
  assessment_type?: string; // Tipo da questão (ex: teorica-1)
  question_number?: number; // Número da questão
  content_tags?: Json; // Tags de conteúdo

  // Manter compatibilidade com código antigo (deprecated)
  statement?: string;
  alternatives?: string[];
  correct_answer?: number;
  answer_type?: 'ALTERNATIVAS' | 'DISSERTATIVA' | 'VERDADEIRO_FALSO';
  year?: number;
  domain?: string;
  images?: string[];
  question_type?: string;
}

export interface AlternativeStatistics {
  count: number;
  percentage: number;
}

export interface Comment {
  id: string | number;
  text: string;
  user: string;
  timestamp: string;
  replies?: Comment[];
  likes?: number;
  dislikes?: number;
  likedBy?: string[];
  dislikedBy?: string[];
}

export interface Category {
  id: string;
  name: string;
  type?: "specialty" | "theme" | "focus";
  parentId?: string;
}

export interface FilterOption {
  id: string;
  name: string;
  type: "specialty" | "theme" | "focus" | "location" | "year" | "question_type" | "question_format";
  parentId?: string;
  children?: FilterOption[];
}

export interface SelectedFilters {
  specialties: string[];
  themes: string[];
  focuses: string[];
  locations: string[];
  years: string[];
  question_types: string[];
  question_formats: string[];
  excludeAnswered?: boolean;
}

export interface QuestionStats {
  time_spent: number;
  correct_answers: number;
  incorrect_answers: number;
  by_theme: CategoryStats;
  by_specialty: CategoryStats;
  by_focus: CategoryStats;
  currentQuestionIndex?: number;
  specialty?: string;
  theme?: string;
  focus?: string;
}

export interface CategoryStats {
  [key: string]: {
    name: string;
    correct: number;
    total: number;
  };
}

export interface UserAnswer {
  id: string;
  user_id: string;
  question_id: string;
  selected_answer: number;
  is_correct: boolean;
  specialty_id: string;
  theme_id?: string;
  focus_id?: string;
  exam_location?: string;
  institution_id?: string;
  exam_year: number;
  session_id?: string;
  created_at: string;
  time_spent?: number;
  specialty?: Category;
  theme?: Category;
  focus?: Category;

  // Manter compatibilidade (deprecated)
  location_id?: string;
  year?: number;
}

export interface SkillNode {
  id: string;
  name: string;
  progress: number;
  children?: SkillNode[];
}

export interface FilteredQuestionsResponse {
  questions: Question[];
  total_count: number;
  filtered_counts: Record<string, number>;
}

export interface QuestionMetadata {
  specialties: { id: string; name: string; question_count: number; count?: number }[];
  themes: { id: string; name: string; question_count: number; count?: number }[];
  focuses: { id: string; name: string; question_count: number; count?: number }[];
  locations: { id: string; name: string; question_count: number; count?: number }[];
  years: { year: number; question_count: number; count?: number }[];
}

export interface AICommentaryResponse {
  alternativas: {
    texto: string;
    comentario: string;
    correta: boolean;
  }[];
  comentario_final: string;
  possivel_erro_no_gabarito: boolean;
  justificativa_erro_gabarito: string;
}
