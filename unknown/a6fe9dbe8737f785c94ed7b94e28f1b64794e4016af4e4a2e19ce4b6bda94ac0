import { motion } from "framer-motion";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Loader2, Clock } from "lucide-react";
import { Link } from "react-router-dom";
import { calculateReadingTime } from "@/utils/readingTime";

interface Post {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  featured_image: string;
  published_at: string;
  author: {
    full_name: string;
  };
  pedbook_blog_categories: {
    name: string;
  };
}

interface RecentPostsProps {
  posts?: Post[];
  isLoading?: boolean;
}

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 }
};

export const RecentPosts = ({ posts, isLoading }: RecentPostsProps) => {
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!posts?.length) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Nenhum post encontrado.</p>
      </div>
    );
  }

  return (
    <motion.div
      variants={container}
      initial="hidden"
      animate="show"
      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
    >
      {posts.map((post) => (
        <motion.div
          key={post.id}
          variants={item}
          className="group"
        >
          <Link to={`/blog/post/${post.id}`} className="block h-full">
            <div className="rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 h-full bg-white/80 backdrop-blur-sm border border-primary/10 hover:-translate-y-1">
              {post.featured_image && (
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={post.featured_image}
                    alt={post.title}
                    className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <div className="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    {calculateReadingTime(post.content)} min
                  </div>
                </div>
              )}
              
              <div className="p-6 space-y-4">
                <div className="space-y-2">
                  <h3 className="text-xl font-semibold text-gray-800 group-hover:text-primary transition-colors line-clamp-2">
                    {post.title}
                  </h3>
                  <p className="text-gray-600 line-clamp-2">{post.excerpt}</p>
                </div>

                <div className="flex flex-wrap items-center gap-2 text-sm text-gray-500">
                  <span className="bg-primary/10 px-3 py-1 rounded-full text-primary">
                    {post.pedbook_blog_categories?.name}
                  </span>
                  <span>•</span>
                  <span>{post.author?.full_name}</span>
                  <span>•</span>
                  <span>
                    {format(new Date(post.published_at), "dd 'de' MMMM", {
                      locale: ptBR,
                    })}
                  </span>
                </div>
              </div>
            </div>
          </Link>
        </motion.div>
      ))}
    </motion.div>
  );
}