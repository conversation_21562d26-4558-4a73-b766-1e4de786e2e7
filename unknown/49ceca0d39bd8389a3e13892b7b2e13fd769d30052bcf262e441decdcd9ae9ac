
import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { ArrowDown } from "lucide-react";

interface BothropicResultProps {
  group: string;
  color: string;
  instructions: string[];
  onReset: () => void;
  nextQuestion?: string;
  nextStep?: string;
  onContinue?: (step: string) => void;
}

export const BothropicResult = ({
  group,
  color,
  instructions,
  nextQuestion,
  nextStep,
  onContinue,
}: BothropicResultProps) => {
  // Extrair o nome da cor base (green, red, etc.) 
  const baseColor = color.split('-')[1];
  
  // Classes escuras para diferentes cores
  const darkClasses: Record<string, string> = {
    'green': 'dark:bg-green-900/30 dark:border-green-800/30 dark:text-green-300',
    'red': 'dark:bg-red-900/30 dark:border-red-800/30 dark:text-red-300',
    'yellow': 'dark:bg-yellow-900/30 dark:border-yellow-800/30 dark:text-yellow-300',
    'orange': 'dark:bg-orange-900/30 dark:border-orange-800/30 dark:text-orange-300',
    'blue': 'dark:bg-blue-900/30 dark:border-blue-800/30 dark:text-blue-300',
    'purple': 'dark:bg-purple-900/30 dark:border-purple-800/30 dark:text-purple-300',
  };
  
  // Obter classes para modo escuro ou fallback para verde
  const darkClass = darkClasses[baseColor] || darkClasses['green'];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-8"
    >
      <div className={`p-6 rounded-xl ${color} border border-${baseColor}-200 glass-card relative overflow-hidden ${darkClass}`}>
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent pointer-events-none dark:from-slate-800/10" />
        
        <h3 className={`text-xl font-bold text-${baseColor}-800 dark:text-${baseColor}-300 mb-4`}>
          {group}
        </h3>
        
        <div className={`space-y-3 text-${baseColor}-700 dark:text-${baseColor}-200`}>
          {instructions.map((instruction, index) => (
            <p key={index} className="relative z-10">
              {instruction}
            </p>
          ))}
        </div>

        {(nextQuestion || nextStep) && (
          <motion.div 
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="mt-6 flex flex-col items-center"
          >
            <ArrowDown className="w-8 h-8 text-primary dark:text-blue-400 animate-bounce" />
            <p className="text-sm text-primary dark:text-blue-400 mt-2">Continue o manejo abaixo</p>
          </motion.div>
        )}
      </div>

      {nextQuestion && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex justify-center"
        >
          <div 
            onClick={() => onContinue?.(nextQuestion)}
            className="p-4 rounded-lg bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 
                     hover:from-primary/20 hover:to-primary/10 transition-all cursor-pointer backdrop-blur-sm
                     dark:from-blue-900/30 dark:to-blue-900/20 dark:border-blue-800/30 dark:hover:from-blue-900/40 dark:hover:to-blue-900/30"
          >
            <p className="text-primary dark:text-blue-400 font-medium text-center">Próxima Avaliação</p>
          </div>
        </motion.div>
      )}

      {nextStep && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex justify-center"
        >
          <div 
            onClick={() => onContinue?.(nextStep)}
            className="p-4 rounded-lg bg-gradient-to-r from-green-500/10 to-green-500/5 border border-green-500/20 
                     hover:from-green-500/20 hover:to-green-500/10 transition-all cursor-pointer backdrop-blur-sm
                     dark:from-green-900/30 dark:to-green-900/20 dark:border-green-800/30 dark:hover:from-green-900/40 dark:hover:to-green-900/30"
          >
            <p className="text-green-600 dark:text-green-400 font-medium text-center">Próxima Etapa</p>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
};
