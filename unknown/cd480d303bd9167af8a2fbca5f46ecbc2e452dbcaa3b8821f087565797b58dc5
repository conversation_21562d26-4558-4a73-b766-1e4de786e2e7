import React from "react";
import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { NewspaperIcon } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

export const LatestBlogPost = () => {
  const navigate = useNavigate();
  
  const { data: latestPost } = useQuery({
    queryKey: ['latest-blog-post'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_blog_posts')
        .select('id, title')
        .eq('published', true)
        .order('published_at', { ascending: false })
        .limit(1)
        .single();

      if (error) throw error;
      return data;
    },
  });

  if (!latestPost) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      onClick={() => navigate(`/blog/post/${latestPost.id}`)}
      className="mx-auto px-4 py-2 max-w-fit bg-accent-blue/30 backdrop-blur-sm 
        rounded-full shadow-sm hover:shadow-md transition-all cursor-pointer group flex items-center gap-2"
    >
      <NewspaperIcon className="w-4 h-4 text-primary/70" />
      <span className="text-sm text-gray-600 group-hover:text-primary transition-colors">
        Última postagem: {latestPost.title}
      </span>
    </motion.div>
  );
};