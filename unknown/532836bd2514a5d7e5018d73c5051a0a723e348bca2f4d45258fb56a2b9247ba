import { Card } from "@/components/ui/card";

export const ScorpionSpecialConsiderations = () => {
  return (
    <Card className="p-6 space-y-4 bg-blue-50">
      <h2 className="text-xl font-semibold text-blue-800">
        Considerações Especiais
      </h2>
      <ul className="list-disc pl-6 space-y-2 text-gray-700">
        <li>
          Em casos moderados de escorpionismo em crianças de até 7 anos,
          a soroterapia é formalmente indicada
        </li>
        <li>
          Em pacientes com dor persistente após analgesia inicial ou com
          manifestações sistêmicas que não melhorem, considerar iniciar
          soroterapia
        </li>
        <li>
          Todo paciente submetido a tratamento soroterápico deve
          permanecer em observação por no mínimo 24 horas
        </li>
        <li>
          Na falta do Soro Antiescorpiônico (SAEsc), utilizar o Soro Antiaracnídico 
          (SAAR) [Soro Antiaracnídico (Loxosceles, Phoneutria e Thityus)]
        </li>
      </ul>
    </Card>
  );
};