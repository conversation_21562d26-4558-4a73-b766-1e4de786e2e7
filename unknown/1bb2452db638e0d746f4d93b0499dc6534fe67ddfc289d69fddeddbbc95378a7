
import { BookOpen } from "lucide-react";
import { Card } from "@/components/ui/card";
import { getThemeClasses } from "@/components/ui/theme-utils";

export const ReferencesCard = () => {
  return (
    <Card className={getThemeClasses.gradientCard("purple", "p-6")}>
      <div className="flex items-start gap-4">
        <BookOpen className="h-6 w-6 text-primary dark:text-purple-400 mt-1 flex-shrink-0" />
        <div className="space-y-2">
          <h3 className="font-semibold text-gray-900 dark:text-gray-100">Referências Bibliográficas</h3>
          <p className="text-sm text-gray-600 dark:text-gray-300">UpToDate - Maintenance intravenous fluid therapy in children.</p>
        </div>
      </div>
    </Card>
  );
};
