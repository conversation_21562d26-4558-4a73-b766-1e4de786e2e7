import { Loader2 } from "lucide-react";
import { PrescriptionCard } from "./PrescriptionCard";
import type { PrescriptionWithMedications } from "@/components/prescriptions/types";

interface PrescriptionListProps {
  prescriptions: PrescriptionWithMedications[] | undefined;
  userId?: string;
  isLoading: boolean;
  onViewDetails: (prescription: PrescriptionWithMedications) => void;
  onAdd: (prescription: PrescriptionWithMedications) => void;
  onRemove: (prescriptionId: string) => void;
}

export const PrescriptionList = ({
  prescriptions,
  userId,
  isLoading,
  onViewDetails,
  onAdd,
  onRemove,
}: PrescriptionListProps) => {
  if (isLoading) {
    return (
      <div className="col-span-full flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!prescriptions?.length) {
    return (
      <div className="col-span-full text-center py-8 bg-muted/20 rounded-lg backdrop-blur-sm">
        <p className="text-muted-foreground">
          Nenhuma prescrição encontrada
        </p>
      </div>
    );
  }

  return (
    <>
      {prescriptions.map((prescription) => (
        <PrescriptionCard
          key={prescription.id}
          prescription={prescription}
          userId={userId}
          onViewDetails={() => onViewDetails(prescription)}
          onAdd={() => onAdd(prescription)}
          onRemove={() => onRemove(prescription.id)}
        />
      ))}
    </>
  );
};