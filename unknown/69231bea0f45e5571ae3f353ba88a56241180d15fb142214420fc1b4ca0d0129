import { useEffect, useState } from 'react';

type VisibilityState = 'visible' | 'hidden' | 'prerender' | 'unloaded';

/**
 * Hook to track document visibility changes and prevent unwanted refreshes
 * when switching between tabs.
 *
 * @param options Configuration options
 * @returns Current visibility state and last change timestamp
 */
export function useVisibilityChange({
  debug = false,
  onVisibilityChange,
}: {
  debug?: boolean;
  onVisibilityChange?: (state: VisibilityState, timestamp: number) => void;
} = {}) {
  const [visibilityState, setVisibilityState] = useState<VisibilityState>(
    document.visibilityState as VisibilityState
  );
  const [lastChangeTimestamp, setLastChangeTimestamp] = useState<number>(Date.now());

  useEffect(() => {
    const handleVisibilityChange = () => {
      const newState = document.visibilityState as VisibilityState;
      const timestamp = Date.now();



      setVisibilityState(newState);
      setLastChangeTimestamp(timestamp);

      if (onVisibilityChange) {
        onVisibilityChange(newState, timestamp);
      }
    };



    // Add event listener
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [debug, onVisibilityChange, visibilityState]);

  return {
    visibilityState,
    lastChangeTimestamp,
    isVisible: visibilityState === 'visible',
  };
}
