export interface Category {
  id: string;
  name: string;
  type: "specialty" | "theme" | "focus";
  parentId?: string;
}

export interface CategoryFormProps {
  selectedType: "specialty" | "theme" | "focus";
  selectedParentId?: string;
  onAddCategory: (name: string) => void;
  onParentChange: (parentId: string) => void;
  getParentOptions: () => Category[];
}

export interface CategoryListProps {
  categories: Category[];
  selectedType: "specialty" | "theme" | "focus";
}