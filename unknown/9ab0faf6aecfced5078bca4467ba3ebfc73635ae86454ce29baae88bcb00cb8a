import { useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Plus, Search, Pencil } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { FormulaDialog } from "@/components/admin/formula/FormulaDialog";
import { FormulaList } from "@/components/admin/formula/FormulaList";
import { supabase } from "@/integrations/supabase/client";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";

export default function Formulas() {
  const [searchTerm, setSearchTerm] = useState("");
  const [showDialog, setShowDialog] = useState(false);
  const [selectedFormula, setSelectedFormula] = useState<any>(null);
  const [showCategoryDialog, setShowCategoryDialog] = useState(false);
  const [categoryName, setCategoryName] = useState("");
  const [categoryDescription, setCategoryDescription] = useState("");
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: formulas, isLoading } = useQuery({
    queryKey: ['formulas'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_formulas')
        .select(`
          *,
          pedbook_formula_categories (
            id,
            name
          )
        `)
        .order('name');
      
      if (error) throw error;
      return data;
    },
  });

  const handleCreateCategory = async () => {
    try {
      const { error } = await supabase
        .from('pedbook_formula_categories')
        .insert([
          {
            name: categoryName,
            description: categoryDescription,
          },
        ]);

      if (error) throw error;

      toast({
        title: "Categoria criada com sucesso!",
        description: `A categoria ${categoryName} foi adicionada.`,
      });

      // Invalidate both formulas and formula-categories queries
      queryClient.invalidateQueries({ queryKey: ['formula-categories'] });
      queryClient.invalidateQueries({ queryKey: ['formulas'] });

      setCategoryName("");
      setCategoryDescription("");
      setShowCategoryDialog(false);
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao criar categoria",
        description: error.message,
      });
    }
  };

  const filteredFormulas = formulas?.filter(formula =>
    formula.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    formula.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
    formula.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Gerenciamento de Fórmulas</h1>
        <div className="flex gap-4">
          <Button onClick={() => setShowCategoryDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Nova Categoria
          </Button>
          <Button onClick={() => {
            setSelectedFormula(null);
            setShowDialog(true);
          }}>
            <Plus className="h-4 w-4 mr-2" />
            Nova Fórmula
          </Button>
        </div>
      </div>

      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={18} />
        <Input
          type="search"
          placeholder="Pesquisar fórmulas..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {isLoading ? (
        <div className="flex justify-center">
          <p>Carregando fórmulas...</p>
        </div>
      ) : (
        <FormulaList 
          formulas={filteredFormulas || []} 
          onEdit={(formula) => {
            setSelectedFormula(formula);
            setShowDialog(true);
          }}
        />
      )}

      <FormulaDialog
        formula={selectedFormula}
        open={showDialog}
        onOpenChange={setShowDialog}
      />

      <Dialog open={showCategoryDialog} onOpenChange={setShowCategoryDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Nova Categoria de Fórmula</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Nome</Label>
              <Input
                id="name"
                value={categoryName}
                onChange={(e) => setCategoryName(e.target.value)}
                placeholder="Nome da categoria"
              />
            </div>
            <div>
              <Label htmlFor="description">Descrição</Label>
              <Textarea
                id="description"
                value={categoryDescription}
                onChange={(e) => setCategoryDescription(e.target.value)}
                placeholder="Descrição da categoria"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCategoryDialog(false)}>
              Cancelar
            </Button>
            <Button onClick={handleCreateCategory}>
              Criar Categoria
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}