import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { diagnosis, patientData } = await req.json();

    const prompt = `Você é um médico especialista gerando uma prescrição médica detalhada. 
A prescrição deve seguir EXATAMENTE este formato:

**Medicamentos:**

1) **[Nome do Medicamento] [Concentração]** --- [Quantidade]
   - [Instruções detalhadas de uso, incluindo via de administração, frequência e duração]

2) **[Próximo medicamento]** --- [Quantidade]
   - [Instruções detalhadas]

**Observações:**
- [Observações importantes sobre o tratamento]
- [Contraindicações e precauções]
- [Possíveis efeitos adversos]

**Instruções ao Paciente:**
- [Orientações práticas numeradas]
- [Cuidados específicos]
- [Quando retornar ou buscar atendimento de emergência]

Com base nas informações do paciente e diagnóstico abaixo, gere a prescrição médica:

**Dados do Paciente:**
- Idade: ${patientData.age} anos
- Peso: ${patientData.weight} kg
- Gênero: ${patientData.gender === 'male' ? 'Masculino' : 'Feminino'}
${patientData.gender === 'female' && patientData.isPregnant !== undefined ? `- Gestante: ${patientData.isPregnant ? 'Sim' : 'Não'}` : ''}
- Paciente Pediátrico: ${patientData.isPediatric ? 'Sim' : 'Não'}
- Sintomas: ${patientData.symptoms.join(', ')}
${patientData.manualSymptoms ? `- Sintomas adicionais: ${patientData.manualSymptoms}` : ''}
${patientData.hasChronicDiseases ? `- Doenças crônicas: ${patientData.chronicDiseases}` : '- Sem doenças crônicas'}
- Intensidade dos sintomas: ${patientData.symptomsIntensity}/10
${patientData.hasRecentExams ? `- Exames recentes: ${patientData.examDetails}` : '- Sem exames recentes'}

**Diagnóstico Clínico:**
- Condição: ${diagnosis.condition}
- Probabilidade: ${diagnosis.probability}%
- Quadro clínico: ${diagnosis.clinicalPresentation}
- Exames solicitados: ${diagnosis.exams}
- Tratamento recomendado: ${diagnosis.treatment}

Lembre-se:
1. Forneça instruções claras e detalhadas para cada medicamento
2. Você deve seguir o protocolo do Brasil e seus medicamentos;
3. Especifique a quantidade exata de cada medicamento
4. Adicione observações relevantes sobre interações medicamentosas
5. Forneça instruções práticas e claras para o paciente
6. Inclua via de administração, dose, frequência e duração
7. Inclua sinais de alerta para buscar atendimento de emergência`;


    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { 
            role: 'system', 
            content: 'Você é um médico especialista gerando prescrições médicas detalhadas e precisas. Forneça prescrições claras e bem estruturadas, seguindo as melhores práticas médicas e considerando as características específicas do paciente.' 
          },
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    const prescription = data.choices[0].message.content;

    return new Response(JSON.stringify({ prescription }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error in generate-prescription function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});