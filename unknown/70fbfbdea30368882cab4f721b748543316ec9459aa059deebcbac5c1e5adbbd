
import { useState } from "react";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { ChevronLeft } from "lucide-react";
import { Link } from "react-router-dom";
import { CalculatorSEO } from "@/components/seo/CalculatorSEO";
import { CALCULATOR_SEO_DATA } from "@/data/calculatorSEOData";
import { getThemeClasses } from "@/components/ui/theme-utils";

interface FinneganValues {
  choro: number | null;
  sono: number | null;
  moro: number | null;
  tremores: number | null;
  tonus: number | null;
  bocejos: number | null;
  escoriacoes: number | null;
  convulsoes: number | null;
  sudorese: number | null;
  febre: number | null;
  cutis: number | null;
  espirros: number | null;
  prurido: number | null;
  batimentos: number | null;
  respiracao: number | null;
  succao: number | null;
  alimentacao: number | null;
  regurgitacao: number | null;
  vomitos: number | null;
  fezes: number | null;
}

const FinneganCalculator = () => {
  const seoData = CALCULATOR_SEO_DATA['finnegan'];

  const [values, setValues] = useState<FinneganValues>({
    choro: null,
    sono: null,
    moro: null,
    tremores: null,
    tonus: null,
    bocejos: null,
    escoriacoes: null,
    convulsoes: null,
    sudorese: null,
    febre: null,
    cutis: null,
    espirros: null,
    prurido: null,
    batimentos: null,
    respiracao: null,
    succao: null,
    alimentacao: null,
    regurgitacao: null,
    vomitos: null,
    fezes: null,
  });

  const criteria = {
    choro: [
      { value: 3, label: "Contínuo" },
      { value: 2, label: "Excessivo" },
      { value: 0, label: "Nenhum dos acima" },
    ],
    sono: [
      { value: 3, label: "< 1 hora" },
      { value: 2, label: "< 2 horas" },
      { value: 1, label: "< 3 horas" },
      { value: 0, label: "Nenhum dos acima" },
    ],
    moro: [
      { value: 3, label: "Hiperatividade marcante" },
      { value: 2, label: "Hiperatividade" },
      { value: 0, label: "Sem hiperatividade" },
    ],
    tremores: [
      { value: 4, label: "Grave" },
      { value: 3, label: "Moderado a grave" },
      { value: 2, label: "Leve" },
      { value: 1, label: "Sem tremor" },
    ],
    tonus: [
      { value: 2, label: "Aumento do tônus" },
      { value: 0, label: "Sem aumento" },
    ],
    bocejos: [
      { value: 1, label: "Frequentes" },
      { value: 0, label: "Sem bocejos frequentes" },
    ],
    escoriacoes: [
      { value: 1, label: "Escoriação" },
      { value: 0, label: "Sem escoriação" },
    ],
    convulsoes: [
      { value: 5, label: "Convulsões" },
      { value: 0, label: "Sem convulsões" },
    ],
    sudorese: [
      { value: 1, label: "Suor" },
      { value: 0, label: "Sem suor" },
    ],
    febre: [
      { value: 2, label: "≥ 38,3°C" },
      { value: 1, label: "37,8 - 38,3°C" },
      { value: 0, label: "< 37,8ºC" },
    ],
    cutis: [
      { value: 1, label: "Cutis marmorata" },
      { value: 0, label: "Sem cutis marmorata" },
    ],
    espirros: [
      { value: 1, label: "Espirros frequentes" },
      { value: 0, label: "Sem espirros frequentes" },
    ],
    prurido: [
      { value: 1, label: "Prurido nasal" },
      { value: 0, label: "Sem prurido nasal" },
    ],
    batimentos: [
      { value: 2, label: "Com batimentos" },
      { value: 0, label: "Sem batimentos" },
    ],
    respiracao: [
      { value: 2, label: "60 irpm + retração intercostal" },
      { value: 1, label: "60 irpm" },
      { value: 0, label: "≤ 60 irpm" },
    ],
    succao: [
      { value: 1, label: "Sucção excessiva" },
      { value: 0, label: "Sem sucção excessiva" },
    ],
    alimentacao: [
      { value: 2, label: "Pouca alimentação" },
      { value: 0, label: "Alimentação normal" },
    ],
    regurgitacao: [
      { value: 2, label: "Com regurgitação" },
      { value: 0, label: "Sem regurgitação" },
    ],
    vomitos: [
      { value: 3, label: "Vômitos em jato" },
      { value: 0, label: "Sem vômitos em jato" },
    ],
    fezes: [
      { value: 3, label: "Líquidas" },
      { value: 2, label: "Semipastosas" },
      { value: 0, label: "Pastosas" },
    ],
  };

  const handleChange = (key: keyof FinneganValues, value: string) => {
    setValues((prev) => ({
      ...prev,
      [key]: parseInt(value, 10),
    }));
  };

  const calculateScore = () => {
    const total = Object.values(values).reduce((sum, value) => sum + (value || 0), 0);
    return total;
  };

  const getInterpretation = (score: number) => {
    if (score >= 12) {
      return {
        text: "Indica necessidade de tratamento",
        color: "text-red-600 dark:text-red-400",
      };
    } else if (score >= 8) {
      return {
        text: "Monitoramento frequente, considerar tratamento se persistente",
        color: "text-yellow-600 dark:text-yellow-400",
      };
    }
    return {
      text: "Monitoramento contínuo",
      color: "text-green-600 dark:text-green-400",
    };
  };

  const score = calculateScore();
  const interpretation = getInterpretation(score);
  const allFieldsFilled = Object.values(values).every((value) => value !== null);

  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen flex flex-col")}>
      <CalculatorSEO {...seoData} />
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto space-y-8">
          <div className="flex items-center gap-4">
            <Link to="/calculadoras">
              <Button variant="ghost" size="icon" className="hover:bg-primary/10 dark:hover:bg-primary/20">
                <ChevronLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h1 className={getThemeClasses.gradientHeading("text-3xl")}>
              Escala de Finnegan
            </h1>
          </div>
          <p className="text-gray-600 dark:text-gray-300">
            Avaliação da síndrome de abstinência neonatal (SAN) em recém-nascidos
          </p>

          <Card className={getThemeClasses.card("p-6 space-y-6")}>
            {(Object.entries(criteria) as [keyof FinneganValues, typeof criteria.choro][]).map(([key, options]) => (
              <div key={key} className="space-y-2">
                <Label className="text-base font-medium text-gray-800 dark:text-gray-200">
                  {key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1').trim()}
                </Label>
                <Select
                  value={values[key]?.toString() || ""}
                  onValueChange={(value) => handleChange(key, value)}
                >
                  <SelectTrigger className={getThemeClasses.select("w-full")}>
                    <SelectValue placeholder="Selecione uma opção" />
                  </SelectTrigger>
                  <SelectContent>
                    {options.map((option) => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ))}

            {allFieldsFilled && (
              <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
                <div className="text-center space-y-4">
                  <div className="text-4xl font-bold text-primary dark:text-blue-400">
                    {score} pontos
                  </div>
                  <div className={`text-xl font-semibold ${interpretation.color}`}>
                    {interpretation.text}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400 max-w-md mx-auto">
                    Nota: Escores ≥ 8 por 3 vezes consecutivas ou ≥ 12 por 2 vezes consecutivas indicam necessidade de tratamento.
                  </div>
                </div>
              </div>
            )}
          </Card>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default FinneganCalculator;
