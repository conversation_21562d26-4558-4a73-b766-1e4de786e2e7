import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { NewsCategory } from '@/types/newsletter';
import { cn } from '@/lib/utils';

interface CategoryFilterProps {
  categories: NewsCategory[];
  selectedCategory: string | null;
  onSelectCategory: (category: string | null) => void;
}

export const CategoryFilter: React.FC<CategoryFilterProps> = ({
  categories,
  selectedCategory,
  onSelectCategory,
}) => {
  return (
    <div className="flex flex-wrap gap-2 mb-6">
      <Button
        variant={selectedCategory === null ? "default" : "outline"}
        size="sm"
        onClick={() => onSelectCategory(null)}
        className={cn(
          "rounded-full",
          selectedCategory === null 
            ? "bg-blue-600 hover:bg-blue-700 text-white" 
            : "border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800"
        )}
      >
        Todas
      </Button>
      
      {categories.map((category) => (
        <Button
          key={category.name}
          variant={selectedCategory === category.name ? "default" : "outline"}
          size="sm"
          onClick={() => onSelectCategory(category.name)}
          className={cn(
            "rounded-full",
            selectedCategory === category.name 
              ? "bg-blue-600 hover:bg-blue-700 text-white" 
              : "border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800"
          )}
        >
          {category.name}
          <Badge 
            variant="outline" 
            className={cn(
              "ml-2 px-1.5 py-0 text-xs font-normal",
              selectedCategory === category.name 
                ? "bg-blue-700 text-white border-blue-800" 
                : "bg-gray-100 dark:bg-gray-800 border-gray-200 dark:border-gray-700"
            )}
          >
            {category.count}
          </Badge>
        </Button>
      ))}
    </div>
  );
};
