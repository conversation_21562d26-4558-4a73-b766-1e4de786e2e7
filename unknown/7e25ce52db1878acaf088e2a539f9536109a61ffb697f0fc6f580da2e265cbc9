import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { InsulinSelection } from "./maintenance/InsulinSelection";
import { GlucoseAdjustment } from "./maintenance/GlucoseAdjustment";
import { GlucoseMonitoring } from "./maintenance/GlucoseMonitoring";
import { PotassiumAssessment } from "./maintenance/PotassiumAssessment";

interface MaintenancePhaseProps {
  weight: number;
  onComplete: (result: {
    severity: "confirmed" | "mild" | "moderate" | "severe" | null;
    recommendations: string[];
  }) => void;
}

type MaintenanceStage = 
  | 'potassium'
  | 'insulin-selection'
  | 'monitoring'
  | 'adjustment'
  | 'glucose-low'
  | 'glucose-high';

export const MaintenancePhase = ({ weight, onComplete }: MaintenancePhaseProps) => {
  const [stage, setStage] = useState<MaintenanceStage>('potassium');
  const [insulinType, setInsulinType] = useState<"regular" | "ultrafast" | null>(null);
  const [potassiumNormal, setPotassiumNormal] = useState<boolean | null>(null);
  const [glucoseTarget, setGlucoseTarget] = useState<boolean | null>(null);

  const handlePotassiumResponse = (isNormal: boolean) => {
    setPotassiumNormal(isNormal);
    setStage('insulin-selection');
  };

  const handleInsulinSelection = (type: "regular" | "ultrafast") => {
    setInsulinType(type);
    setStage('monitoring');
  };

  const handleGlucoseTargetResponse = (reached: boolean) => {
    setGlucoseTarget(reached);
    if (reached) {
      completeFlow();
    } else {
      setStage('adjustment');
    }
  };

  const handleGlucoseLowResponse = (isLow: boolean) => {
    setStage(isLow ? 'glucose-low' : 'glucose-high');
  };

  const handleReturnToAdjustment = () => {
    setStage('adjustment');
    setGlucoseTarget(null);
  };

  const completeFlow = () => {
    const recommendations = [
      "Manter insulinoterapia e hidratação até preenchimento dos critérios de estabilização:",
      "- Normalização clínica",
      "- pH > 7,3",
      "- Bicarbonato > 15 mEq/L",
      "Realizar transição para insulina subcutânea antes da alta."
    ];
    onComplete({ severity: "confirmed", recommendations });
  };

  if (stage === 'potassium') {
    return (
      <PotassiumAssessment
        weight={weight}
        onPotassiumNormal={handlePotassiumResponse}
      />
    );
  }

  if (stage === 'insulin-selection') {
    return (
      <InsulinSelection
        weight={weight}
        onSelect={handleInsulinSelection}
      />
    );
  }

  if (stage === 'monitoring') {
    return (
      <GlucoseMonitoring
        weight={weight}
        onTargetReached={handleGlucoseTargetResponse}
        showPotassiumInfo={potassiumNormal}
        insulinType={insulinType}
      />
    );
  }

  if (stage === 'adjustment') {
    return (
      <Card className="p-6 space-y-4">
        <h3 className="text-lg font-semibold">Ajuste Glicêmico</h3>
        <div className="bg-green-50 p-4 rounded-lg border border-green-200 mb-4">
          <h4 className="font-medium text-green-800">Alvos glicêmicos:</h4>
          <ul className="list-disc pl-4 text-green-700">
            <li>100-150 mg/dL para escolares/adolescentes</li>
            <li>150-180 mg/dL para crianças menores</li>
          </ul>
        </div>
        
        {glucoseTarget === null && (
          <>
            <p>Após 1 hora, glicemia atingiu o alvo?</p>
            <div className="flex gap-4">
              <Button 
                variant="outline" 
                onClick={() => handleGlucoseTargetResponse(true)}
                className="flex-1"
              >
                Sim
              </Button>
              <Button 
                variant="outline" 
                onClick={() => handleGlucoseTargetResponse(false)}
                className="flex-1"
              >
                Não
              </Button>
            </div>
          </>
        )}

        {!glucoseTarget && (
          <>
            <p>Glicemia está abaixo do alvo?</p>
            <div className="flex gap-4">
              <Button 
                variant="outline" 
                onClick={() => handleGlucoseLowResponse(true)}
                className="flex-1"
              >
                Sim
              </Button>
              <Button 
                variant="outline" 
                onClick={() => handleGlucoseLowResponse(false)}
                className="flex-1"
              >
                Não
              </Button>
            </div>
          </>
        )}
      </Card>
    );
  }

  if (stage === 'glucose-low' || stage === 'glucose-high') {
    return (
      <GlucoseAdjustment
        isLow={stage === 'glucose-low'}
        onReturn={handleReturnToAdjustment}
      />
    );
  }

  return null;
};