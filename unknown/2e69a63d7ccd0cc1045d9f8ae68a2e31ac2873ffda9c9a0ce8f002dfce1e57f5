import { Card } from "@/components/ui/card";
import { Syringe } from "lucide-react";

interface StatusEpilepticusPhaseProps {
  weight: number;
}

export const StatusEpilepticusPhase = ({ weight }: StatusEpilepticusPhaseProps) => {
  const fenobarbitalDose = Math.round(weight * 20);
  const maxFenobarbitalDose = Math.round(weight * 30);
  const levetiracetamDose = Math.round(weight * 40);
  const acidoValproicoDose = Math.round(weight * 40);

  return (
    <div className="space-y-6">
      <Card className="p-6 bg-white/80 backdrop-blur-sm border border-red-200">
        <div className="flex items-start gap-4">
          <div className="mt-1">
            <Syringe className="h-6 w-6 text-red-600" />
          </div>
          <div className="space-y-3">
            <h3 className="font-semibold text-lg text-red-800">
              Medicações de Terceira Linha
            </h3>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>
                Fenobarbital: {fenobarbitalDose}-{maxFenobarbitalDose} mg IV
              </li>
              <li>
                Levetiracetam: {levetiracetamDose} mg IV (infusão a 5 mg/min)
              </li>
              <li>
                Ácido Valpróico: {acidoValproicoDose} mg IV (infusão a 5 mg/min)
              </li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};