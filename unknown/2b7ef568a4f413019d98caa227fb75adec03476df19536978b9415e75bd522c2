import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, CheckCircle2, CalendarClock } from "lucide-react";
import { VaccineDose } from "@/components/admin/vaccine/types";
import { useState } from "react";
import { VaccineFilters } from "./VaccineFilters";
import { VaccineList } from "./VaccineList";
import { VaccineHeader } from "./VaccineHeader";

interface VaccineAnalysisProps {
  ageInMonths: number;
}

interface GroupedDoses {
  [key: string]: VaccineDose[];
}

export function VaccineAnalysis({ ageInMonths }: VaccineAnalysisProps) {
  const [showOnlyCurrent, setShowOnlyCurrent] = useState(false);
  const [showOnlyUpcoming, setShowOnlyUpcoming] = useState(false);
  const [showSUS, setShowSUS] = useState(true);
  const [showPrivate, setShowPrivate] = useState(true);

  const { data: doses, error } = useQuery({
    queryKey: ['vaccine-doses'],
    queryFn: async () => {
      
      const { data, error } = await supabase
        .from('pedbook_vaccine_doses')
        .select(`
          id,
          dose_number,
          age_recommendation,
          type,
          dose_type,
          vaccine:pedbook_vaccines (
            id,
            name,
            description
          )
        `)
        .order('age_recommendation');

      if (error) {
        console.error("Error fetching vaccine doses:", error);
        throw error;
      }

      return data as VaccineDose[];
    },
  });

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Erro</AlertTitle>
        <AlertDescription>
          Não foi possível carregar as informações das vacinas.
        </AlertDescription>
      </Alert>
    );
  }

  if (!doses) {
    return null;
  }

  const groupDosesByAge = (doses: VaccineDose[]): GroupedDoses => {
    return doses.reduce((groups: GroupedDoses, dose) => {
      const ageKey = dose.age_recommendation === "0" ? "Ao nascer" : `${dose.age_recommendation} meses`;
      if (!groups[ageKey]) {
        groups[ageKey] = [];
      }
      groups[ageKey].push(dose);
      return groups;
    }, {});
  };

  const findNextAgeGroup = (groupedDoses: GroupedDoses): string | null => {
    const ages = Object.keys(groupedDoses)
      .map(age => age === "Ao nascer" ? 0 : parseInt(age))
      .filter(age => age > ageInMonths)
      .sort((a, b) => a - b);
    
    if (ages.length === 0) return null;
    return ages[0] === 0 ? "Ao nascer" : `${ages[0]} meses`;
  };

  const filterDosesByType = (doses: VaccineDose[]) => {
    return doses.filter(dose => {
      const isSUS = dose.type === 'SUS';
      return (showSUS && isSUS) || (showPrivate && !isSUS);
    });
  };

  const groupedDoses = groupDosesByAge(doses);
  const nextAgeGroup = findNextAgeGroup(groupedDoses);

  const currentGroups: GroupedDoses = {};
  const upcomingGroups: GroupedDoses = {};

  Object.entries(groupedDoses).forEach(([ageKey, doseGroup]) => {
    const age = ageKey === "Ao nascer" ? 0 : parseInt(ageKey);
    if (age <= ageInMonths) {
      currentGroups[ageKey] = filterDosesByType(doseGroup);
    } else if (nextAgeGroup && ageKey === nextAgeGroup) {
      upcomingGroups[ageKey] = filterDosesByType(doseGroup);
    }
  });

  return (
    <div className="space-y-4">
      <VaccineHeader ageInMonths={ageInMonths} />
      <VaccineFilters 
        showOnlyCurrent={showOnlyCurrent}
        setShowOnlyCurrent={setShowOnlyCurrent}
        showOnlyUpcoming={showOnlyUpcoming}
        setShowOnlyUpcoming={setShowOnlyUpcoming}
        showSUS={showSUS}
        setShowSUS={setShowSUS}
        showPrivate={showPrivate}
        setShowPrivate={setShowPrivate}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {!showOnlyUpcoming && (
          <VaccineList
            title="Vacinas que já deveriam ter sido aplicadas"
            icon={<CheckCircle2 className="h-5 w-5" />}
            doses={currentGroups}
            titleColor="text-green-700"
            bgColor="bg-green-50"
            textColor="text-green-800"
            borderColor="border-green-200"
          />
        )}

        {!showOnlyCurrent && (
          <VaccineList
            title="Próximas vacinas"
            icon={<CalendarClock className="h-5 w-5" />}
            doses={upcomingGroups}
            titleColor="text-blue-700"
            bgColor="bg-blue-50"
            textColor="text-blue-800"
            borderColor="border-blue-200"
          />
        )}
      </div>
    </div>
  );
}