import { useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface DosageEditFormProps {
  dosage: {
    id: string;
    medication_id: string;
    name: string;
    type: string;
    summary?: string;
    description?: string;
    dosage_template: string;
    max_value?: number;
    multiplier?: number;
  };
  onCancel: () => void;
  onSuccess: () => void;
}

export function DosageEditForm({ dosage, onCancel, onSuccess }: DosageEditFormProps) {
  const [editedDosage, setEditedDosage] = useState(dosage);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const { error } = await supabase
        .from("pedbook_medication_dosages")
        .update({
          name: editedDosage.name,
          type: editedDosage.type,
          summary: editedDosage.summary,
          description: editedDosage.description,
          dosage_template: editedDosage.dosage_template,
          max_value: editedDosage.max_value,
          multiplier: editedDosage.multiplier,
        })
        .eq("id", dosage.id);

      if (error) throw error;

      onSuccess();
    } catch (error: any) {
      console.error("Error updating dosage:", error);
      toast({
        variant: "destructive",
        title: "Erro ao atualizar dosagem",
        description: error.message,
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="name">Nome</Label>
        <Input
          id="name"
          value={editedDosage.name}
          onChange={(e) => setEditedDosage({ ...editedDosage, name: e.target.value })}
          required
        />
      </div>

      <div>
        <Label htmlFor="type">Tipo</Label>
        <Select
          value={editedDosage.type}
          onValueChange={(value) => setEditedDosage({ ...editedDosage, type: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Selecione um tipo" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="weight">Baseado no peso</SelectItem>
            <SelectItem value="fixed">Dose fixa</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="dosage_template">Template da Dosagem</Label>
        <Input
          id="dosage_template"
          value={editedDosage.dosage_template}
          onChange={(e) => setEditedDosage({ ...editedDosage, dosage_template: e.target.value })}
          required
        />
      </div>

      <div>
        <Label htmlFor="multiplier">Multiplicador</Label>
        <Input
          id="multiplier"
          type="number"
          value={editedDosage.multiplier || ""}
          onChange={(e) => setEditedDosage({ ...editedDosage, multiplier: parseFloat(e.target.value) || undefined })}
        />
      </div>

      <div>
        <Label htmlFor="max_value">Valor Máximo</Label>
        <Input
          id="max_value"
          type="number"
          value={editedDosage.max_value || ""}
          onChange={(e) => setEditedDosage({ ...editedDosage, max_value: parseFloat(e.target.value) || undefined })}
        />
      </div>

      <div>
        <Label htmlFor="summary">Resumo</Label>
        <Input
          id="summary"
          value={editedDosage.summary || ""}
          onChange={(e) => setEditedDosage({ ...editedDosage, summary: e.target.value })}
        />
      </div>

      <div>
        <Label htmlFor="description">Descrição</Label>
        <Textarea
          id="description"
          value={editedDosage.description || ""}
          onChange={(e) => setEditedDosage({ ...editedDosage, description: e.target.value })}
        />
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancelar
        </Button>
        <Button type="submit">
          Salvar
        </Button>
      </div>
    </form>
  );
}