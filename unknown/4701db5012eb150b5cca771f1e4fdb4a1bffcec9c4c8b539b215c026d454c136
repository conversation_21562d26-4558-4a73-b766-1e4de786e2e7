import { useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface DoseDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  vaccineId: string;
}

export function DoseDialog({ open, onOpenChange, vaccineId }: DoseDialogProps) {
  const [doseNumber, setDoseNumber] = useState("");
  const [ageUnit, setAgeUnit] = useState<"birth" | "months" | "years">("months");
  const [years, setYears] = useState("");
  const [months, setMonths] = useState("");
  const [description, setDescription] = useState("");
  const [type, setType] = useState<"SUS" | "PARTICULAR">("SUS");
  const [doseType, setDoseType] = useState<"dose" | "reforço">("dose");
  
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const formatAgeRecommendation = () => {
    if (ageUnit === "birth") return "0";
    if (ageUnit === "years") {
      const totalMonths = (parseInt(years) * 12) + (parseInt(months) || 0);
      return totalMonths.toString();
    }
    return months;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const formattedAge = formatAgeRecommendation();
      
      const { error } = await supabase
        .from("pedbook_vaccine_doses")
        .insert([{
          vaccine_id: vaccineId,
          dose_number: parseInt(doseNumber),
          age_recommendation: formattedAge,
          description,
          type,
          dose_type: doseType // Make sure this is being sent correctly
        }]);

      if (error) throw error;

      toast({
        title: "Dose adicionada com sucesso!",
        description: `A ${doseNumber}ª ${doseType} foi adicionada.`,
        className: "fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
      });

      queryClient.invalidateQueries({ queryKey: ["vaccines"] });
      onOpenChange(false);
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao adicionar dose",
        description: error.message,
        className: "fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Nova Dose</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label>Tipo</Label>
            <RadioGroup
              value={doseType}
              onValueChange={(value: "dose" | "reforço") => setDoseType(value)}
              className="flex space-x-4 mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="dose" id="dose" />
                <Label htmlFor="dose">Dose</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="reforço" id="reforco" />
                <Label htmlFor="reforco">Reforço</Label>
              </div>
            </RadioGroup>
          </div>
          <div>
            <Label htmlFor="doseNumber">Número da {doseType}</Label>
            <Input
              id="doseNumber"
              type="number"
              value={doseNumber}
              onChange={(e) => setDoseNumber(e.target.value)}
              required
            />
          </div>
          <div className="space-y-2">
            <Label>Idade Recomendada</Label>
            <Select value={ageUnit} onValueChange={(value: "birth" | "months" | "years") => {
              setAgeUnit(value);
              setYears("");
              setMonths("");
            }}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Selecione" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="birth">Ao nascer</SelectItem>
                <SelectItem value="months">Meses</SelectItem>
                <SelectItem value="years">Anos</SelectItem>
              </SelectContent>
            </Select>
            
            {ageUnit === "years" && (
              <div className="flex gap-2">
                <div className="flex-1">
                  <Label>Anos</Label>
                  <Input
                    type="number"
                    value={years}
                    onChange={(e) => setYears(e.target.value)}
                    min="1"
                    max="18"
                    required
                  />
                </div>
                <div className="flex-1">
                  <Label>Meses</Label>
                  <Input
                    type="number"
                    value={months}
                    onChange={(e) => setMonths(e.target.value)}
                    min="0"
                    max="11"
                  />
                </div>
              </div>
            )}
            
            {ageUnit === "months" && (
              <Input
                type="number"
                value={months}
                onChange={(e) => setMonths(e.target.value)}
                min="1"
                max="11"
                placeholder="1-11"
                required
              />
            )}
          </div>
          <div>
            <Label htmlFor="type">Tipo de Serviço</Label>
            <Select value={type} onValueChange={(value: "SUS" | "PARTICULAR") => setType(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione o tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="SUS">SUS</SelectItem>
                <SelectItem value="PARTICULAR">PARTICULAR</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="description">Descrição</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
            />
          </div>
          <Button type="submit" className="w-full">
            Adicionar {doseType === "dose" ? "Dose" : "Reforço"}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
}