import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useEffect } from "react";
import "./ICDSearchForm.css"

interface ICDSearchFormProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  setIsSearching: (isSearching: boolean) => void;
  handleSearch: (e: React.FormEvent) => void;
}

export const ICDSearchForm = ({
  searchTerm,
  setSearchTerm,
  setIsSearching,
  handleSearch,
}: ICDSearchFormProps) => {
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
  };

  // Não precisamos mais do debounce aqui, o React Query já gerencia isso

  return (
    <form onSubmit={handleSearch} className="relative max-w-2xl mx-auto">
      <div className="relative group">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-primary/5 to-transparent rounded-lg blur-lg group-hover:opacity-75 transition-opacity" />
        <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-primary/60 group-hover:text-primary/80 transition-colors" />
        <Input
          type="text"
          value={searchTerm}
          onChange={handleInputChange}
          placeholder="Digite o nome da doença ou código CID (ex: diabetes, A01)..."
          className="pl-7 pr-4 py-6 text-lg bg-white/80 backdrop-blur-sm border-primary/20 focus:border-primary/40 rounded-lg shadow-lg shadow-primary/5 input-search"
        />
        {searchTerm.length > 0 && searchTerm.length < 2 && (
          <div className="absolute top-full left-0 right-0 mt-1 text-sm text-gray-500 text-center">
            Digite pelo menos 2 caracteres para buscar
          </div>
        )}
      </div>
    </form>
  );
};