
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Activity, Heart, Stethoscope, Syringe } from "lucide-react";

interface MonitoringDetailsProps {
  showAdjuvantTherapy?: boolean;
  weight?: number;
  age?: number;
}

export const MonitoringDetails: React.FC<MonitoringDetailsProps> = ({
  showAdjuvantTherapy = false,
  weight = 0,
  age = 0
}) => {
  const isAdult = weight >= 30;

  const calculateAntihistamine = () => {
    if (isAdult) {
      return {
        fexofenadine: "180mg VO 1 cp/dose. Repetir a dose em 20 min s/n",
        diphenhydramine: "25-50mg/dose IM/IV/IO a cada 4-6h (máx 50mg/dose)",
        promethazine: "25-50mg/dose IM a cada 4-6h (máx 50mg/dose)"
      };
    }
    
    const diphenhydramineDose = Math.min(2 * weight, 50);
    const promethazineDose = Math.min(2 * weight, 50);
    let fexofenadine = "";
    
    if (age <= 2) fexofenadine = "5,0ml/dose";
    else if (age <= 5) fexofenadine = "10ml/dose";
    else fexofenadine = "20ml/dose";

    return {
      fexofenadine: `6mg/ml VO - ${fexofenadine} - Repetir a dose em 20 min s/n`,
      diphenhydramine: `${diphenhydramineDose}mg/dose IM/IV/IO a cada 4-6h`,
      promethazine: `${promethazineDose}mg/dose IM a cada 4-6h`
    };
  };

  const calculateCorticosteroids = () => {
    if (isAdult) {
      return {
        prednisolone: "40mg/dose VO",
        hydrocortisone: "100mg/200mg/500mg/dose IM/IV/IO",
        methylprednisolone: "40-80mg/dose IM/IV/IO"
      };
    }

    const prednisoloneDose = Math.min(1 * weight, 40);
    const hydrocortisoneDose = Math.min(5 * weight, 500);
    const methylprednisoloneDose = Math.min(2 * weight, 60);

    return {
      prednisolone: `${prednisoloneDose}mg/dose VO`,
      hydrocortisone: `${hydrocortisoneDose}mg/dose IM/IV/IO`,
      methylprednisolone: `${methylprednisoloneDose}mg/dose IM/IV/IO`
    };
  };

  const calculateFluidReplacement = () => {
    if (isAdult) {
      return "SF 0.9%/Ringer até 2L em bolus IV/IO";
    }
    const volume = Math.min(20 * weight, 60 * weight);
    return `SF 0.9% ${volume}ml em bolus IV/IO`;
  };

  const calculateBronchodilator = () => {
    if (isAdult) {
      return {
        salbutamol: "4-8 jatos a cada 20 minutos (até 3 doses em 1h)",
        fenoterol: "10-20 gotas + Brometo de ipratrópio 40 gotas ou spray 4-8 jatos"
      };
    }
    return {
      salbutamol: "2-4 jatos a cada 20 minutos (até 3 doses em 1h)",
      fenoterol: `${Math.min(weight/3, 10).toFixed(1)} gotas + Brometo de ipratrópio ${Math.min(weight/2, 20)} gotas ou spray 4 jatos`
    };
  };

  const calculateAntiemetic = () => {
    const baseDose = Math.min(0.2 * weight, 16);
    return `Ondansetrona ${baseDose}mg SL/IM/IV\n*até 8mg: IM ou IV em 30s\n**>8mg: IV lento em SF0,9% 50ml em 15min`;
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <Card className="w-full bg-gradient-to-br from-red-50/90 via-white/80 to-red-50/90 border-red-100 dark:from-red-900/20 dark:via-slate-800/70 dark:to-red-900/10 dark:border-red-900/30">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-700 dark:text-red-400">
            <Activity className="h-6 w-6" />
            Monitorização do Paciente
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="bg-white/80 p-4 rounded-lg border border-red-100 dark:bg-slate-800/70 dark:border-red-900/30">
              <h3 className="font-medium text-red-700 dark:text-red-400 mb-2 flex items-center gap-2">
                <Heart className="h-5 w-5" />
                Avaliação ABC
              </h3>
              <ul className="list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-300">
                <li>Monitorização cardíaca contínua (ECG)</li>
                <li>Oximetria de pulso</li>
                <li>PA e nível de consciência</li>
              </ul>
            </div>

            <div className="bg-white/80 p-4 rounded-lg border border-red-100 dark:bg-slate-800/70 dark:border-red-900/30">
              <h3 className="font-medium text-red-700 dark:text-red-400 mb-2 flex items-center gap-2">
                <Stethoscope className="h-5 w-5" />
                Intervenções Imediatas
              </h3>
              <ul className="list-disc pl-6 space-y-2 text-gray-700 dark:text-gray-300">
                <li>Decúbito dorsal com elevação de MMII</li>
                <li>Oxigênio inalatório conforme necessário</li>
                <li>Pesquisar e remover alérgeno (se identificado)</li>
              </ul>
            </div>

            {showAdjuvantTherapy && (
              <div className="bg-white/80 p-4 rounded-lg border border-red-100 dark:bg-slate-800/70 dark:border-red-900/30">
                <h3 className="font-medium text-red-700 dark:text-red-400 mb-2 flex items-center gap-2">
                  <Syringe className="h-5 w-5" />
                  Terapia Adjuvante ({isAdult ? "Adulto >30kg" : "Lactente/Criança <30kg"})
                </h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-red-600 dark:text-red-400 mb-1">Anti-histamínico</h4>
                    <ul className="text-sm text-gray-700 dark:text-gray-300 list-disc pl-4 space-y-1">
                      <li>Fexofenadina: {calculateAntihistamine().fexofenadine}</li>
                      <li>Difenidramina: {calculateAntihistamine().diphenhydramine}</li>
                      <li>Prometazina: {calculateAntihistamine().promethazine}</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-red-600 dark:text-red-400 mb-1">Corticosteroides</h4>
                    <ul className="text-sm text-gray-700 dark:text-gray-300 list-disc pl-4 space-y-1">
                      <li>Prednisolona: {calculateCorticosteroids().prednisolone}</li>
                      <li>Hidrocortisona: {calculateCorticosteroids().hydrocortisone}</li>
                      <li>Metilprednisolona: {calculateCorticosteroids().methylprednisolone}</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-red-600 dark:text-red-400 mb-1">Reposição Volêmica</h4>
                    <p className="text-sm text-gray-700 dark:text-gray-300">{calculateFluidReplacement()}</p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-red-600 dark:text-red-400 mb-1">β2 agonista (broncodilatador)</h4>
                    <ul className="text-sm text-gray-700 dark:text-gray-300 list-disc pl-4 space-y-1">
                      <li>Salbutamol: {calculateBronchodilator().salbutamol}</li>
                      <li>Fenoterol: {calculateBronchodilator().fenoterol}</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-red-600 dark:text-red-400 mb-1">Antiemético</h4>
                    <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-line">{calculateAntiemetic()}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
