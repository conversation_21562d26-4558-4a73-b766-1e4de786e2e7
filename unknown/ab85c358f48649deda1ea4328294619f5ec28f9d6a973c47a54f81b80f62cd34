
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { AnamneseData, TemplateType } from "./AnamneseForm";
import { FileText, Info, ChevronDown, AlertCircle, Zap } from "lucide-react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Switch } from "@/components/ui/switch";

interface AnamneseBlocksProps {
  data: AnamneseData;
  onChange: (data: Partial<AnamneseData>) => void;
  templateType: TemplateType;
}

export const AnamneseBlocks: React.FC<AnamneseBlocksProps> = ({ data, onChange, templateType }) => {
  const [quickMode, setQuickMode] = useState(false);
  const [expandedBlocks, setExpandedBlocks] = useState<string[]>([]);
  
  const handleChange = (field: keyof AnamneseData, value: string) => {
    onChange({ [field]: value } as Partial<AnamneseData>);
  };

  const toggleBlockExpansion = (blockId: string) => {
    setExpandedBlocks(prev => 
      prev.includes(blockId) 
        ? prev.filter(id => id !== blockId) 
        : [...prev, blockId]
    );
  };

  const getPlaceholderText = (blockId: string) => {
    if (templateType === "traditional") {
      switch (blockId) {
        case "hda":
          return quickMode 
            ? "Apenas descreva alterações relevantes. Ex: 'febre há 3 dias, tosse produtiva'" 
            : "O acompanhante relata que o paciente era previamente hígido e, há cerca de X dias, iniciou um quadro de (sintomas principais), acompanhado de (sintomas secundários)...";
        case "perinatais":
          return quickMode
            ? "Apenas alterações relevantes. Ex: 'prematuridade (32 sem)'"
            : "Gestação sem intercorrências, parto tipo (normal/cesárea), a termo, peso ao nascer...";
        case "patologicos":
          return quickMode
            ? "Apenas alterações relevantes. Ex: 'asma, epilepsia'"
            : "Comorbidades, doenças prévias, internações...";
        case "cirurgicos":
          return quickMode
            ? "Apenas alterações relevantes. Ex: 'apendicectomia há 2 anos'"
            : "Procedimentos cirúrgicos prévios...";
        case "alergicos":
          return quickMode
            ? "Apenas alterações relevantes. Ex: 'alergia à dipirona'"
            : "Alergias medicamentosas, alimentares ou outras...";
        case "medicamentosos":
          return quickMode
            ? "Apenas alterações relevantes. Ex: 'prednisolona 20mg/dia'"
            : "Medicamentos em uso regular...";
        case "vacinacao":
          return quickMode
            ? "Apenas alterações relevantes. Ex: 'atrasada para idade'"
            : "Caderneta de vacinação em dia? Qual a próxima vacina programada?";
        case "desenvolvimento":
          return quickMode
            ? "Apenas alterações relevantes. Ex: 'atraso na fala'"
            : "Neuropsicológico: linguagem, interação social. Motor: habilidades motoras grossas e finas...";
        case "alimentacao":
          return quickMode
            ? "Apenas alterações relevantes. Ex: 'seletividade alimentar'"
            : "Tipo de aleitamento durante o 1º ano, introdução de alimentos sólidos, padrão alimentar atual...";
        case "ambiente":
          return quickMode
            ? "Apenas alterações relevantes. Ex: 'mora com avós'"
            : "Condições da habitação, saneamento básico, presença de animais, exposição a tabagismo, rotina de sono...";
        default:
          return "Descreva os achados...";
      }
    } else {
      // Default placeholders for simple template
      return quickMode 
        ? "Apenas descreva alterações relevantes." 
        : "Descreva os achados...";
    }
  };

  const blocks = [
    {
      id: "hda",
      title: "História da Doença Atual",
      placeholder: getPlaceholderText("hda"),
      value: data.hda,
    },
    {
      id: "perinatais",
      title: "Antecedentes Perinatais",
      placeholder: getPlaceholderText("perinatais"),
      value: data.perinatais,
    },
    {
      id: "patologicos",
      title: "Antecedentes Patológicos",
      placeholder: getPlaceholderText("patologicos"),
      value: data.patologicos,
    },
    {
      id: "cirurgicos",
      title: "Antecedentes Cirúrgicos",
      placeholder: getPlaceholderText("cirurgicos"),
      value: data.cirurgicos,
    },
    {
      id: "alergicos",
      title: "Antecedentes Alérgicos",
      placeholder: getPlaceholderText("alergicos"),
      value: data.alergicos,
    },
    {
      id: "medicamentosos",
      title: "Medicamentos em uso",
      placeholder: getPlaceholderText("medicamentosos"),
      value: data.medicamentosos,
    },
    {
      id: "vacinacao",
      title: "Vacinação",
      placeholder: getPlaceholderText("vacinacao"),
      value: data.vacinacao,
    },
    {
      id: "desenvolvimento",
      title: "Desenvolvimento Neuropsicomotor",
      placeholder: getPlaceholderText("desenvolvimento"),
      value: data.desenvolvimento,
    },
    {
      id: "alimentacao",
      title: "Alimentação",
      placeholder: getPlaceholderText("alimentacao"),
      value: data.alimentacao,
    },
    {
      id: "ambiente",
      title: "Ambiente Familiar/Social",
      placeholder: getPlaceholderText("ambiente"),
      value: data.ambiente,
    },
  ];

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-primary" />
            Anamnese {templateType === "traditional" ? "Tradicional" : "Simplificada"}
          </CardTitle>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">Apenas alterações</span>
            <Switch 
              checked={quickMode} 
              onCheckedChange={setQuickMode} 
              className="data-[state=checked]:bg-primary"
            />
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-4 w-4 text-gray-400 cursor-help" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="w-[220px] text-xs">
                    No modo "Apenas alterações", você só precisa descrever o que está alterado. 
                    Os campos vazios serão preenchidos com valores normais para a idade e gênero.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {quickMode ? (
          <div className="p-4 bg-amber-50 border border-amber-200 rounded-md flex gap-3 items-start">
            <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5" />
            <div>
              <h3 className="font-medium text-amber-800">Modo rápido ativado</h3>
              <p className="text-sm text-amber-700">
                Preencha apenas os achados alterados. Os campos vazios serão automaticamente 
                preenchidos com valores normais para a idade e gênero do paciente.
              </p>
            </div>
          </div>
        ) : null}

        {blocks.map((block) => (
          <Collapsible 
            key={block.id} 
            className="border rounded-md"
            open={expandedBlocks.includes(block.id)}
            onOpenChange={() => toggleBlockExpansion(block.id)}
          >
            <div className="flex items-center justify-between p-4">
              <Label htmlFor={block.id} className="font-medium">
                {block.title}
                {block.value && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary/10 text-primary">
                    <Zap className="h-3 w-3 mr-1" />
                    Preenchido
                  </span>
                )}
              </Label>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm">
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </CollapsibleTrigger>
            </div>
            <CollapsibleContent className="px-4 pb-4">
              <Textarea
                id={block.id}
                value={block.value}
                onChange={(e) => handleChange(block.id as keyof AnamneseData, e.target.value)}
                placeholder={block.placeholder}
                className="min-h-[60px]"
              />
            </CollapsibleContent>
          </Collapsible>
        ))}
      </CardContent>
    </Card>
  );
};
