import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { QuestionStats } from "@/types/question";

interface StudySessionStatsProps {
  session: {
    stats: QuestionStats;
    created_at: string;
  };
}

export const StudySessionStats: React.FC<StudySessionStatsProps> = ({ session }) => {
  const { stats } = session;
  const totalQuestions = stats.correct_answers + stats.incorrect_answers;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Última Sessão de Estudo</CardTitle>
      </CardHeader>
      <CardContent>
        <p>Data: {new Date(session.created_at).toLocaleDateString()}</p>
        <p>Total de questões: {totalQuestions}</p>
        <p>Respostas corretas: {stats.correct_answers}</p>
        <p>Respostas incorretas: {stats.incorrect_answers}</p>
        <p>Tempo gasto: {Math.round(stats.time_spent / 60)} minutos</p>
      </CardContent>
    </Card>
  );
};