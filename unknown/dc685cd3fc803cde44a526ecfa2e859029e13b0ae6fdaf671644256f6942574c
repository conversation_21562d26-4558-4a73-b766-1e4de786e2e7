
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import "https://deno.land/x/xhr@0.1.0/mod.ts";

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }
  
  try {
    const { prompt, model = 'gpt-4o-mini', domain = 'pediatria' } = await req.json();
    
    if (!prompt) {
      return new Response(
        JSON.stringify({ error: 'Prompt is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' }}
      );
    }
    
    if (!openAIApiKey) {
      return new Response(
        JSON.stringify({ error: 'OpenAI API key is not configured on the server' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' }}
      );
    }
    
    console.log(`Analyzing with ${model} for domain ${domain}: ${prompt.substring(0, 50)}...`);
    
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        messages: [
          {
            role: 'system',
            content: `You are a medical specialist assistant in ${domain === 'pediatria' ? 'pediatrics' : 'ophthalmology'} who analyzes medical questions and categorizes them into appropriate themes and focuses. 

IMPORTANT RULES:
1. Theme and focus names MUST NEVER be identical. They must be different.
2. Themes should be broader categories, while focuses should be more specific subcategories.
3. If the current theme and focus are identical, you must recommend a more specific focus.
4. Always follow the hierarchy: Specialty > Theme > Focus

Always respond in valid JSON format with these exact fields:
- recommendedTheme: the broader category
- recommendedFocus: a more specific subcategory (NEVER the same as the theme)
- isNewTheme: boolean indicating if this is a new theme not in the provided list
- isNewFocus: boolean indicating if this is a new focus not in the provided list
- justification: brief explanation of your categorization`
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.2,
      }),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      console.error('OpenAI API error:', errorData);
      throw new Error(`OpenAI API error: ${errorData.error?.message || 'Unknown error'}`);
    }
    
    const data = await response.json();
    let result;
    
    try {
      // Extract the content from the first choice
      const content = data.choices[0].message.content;
      console.log("Raw AI response:", content);
      
      // Try to parse the JSON response
      // The AI might wrap the JSON in markdown code blocks, so we clean that up
      const cleanedContent = content.replace(/```json/g, '').replace(/```/g, '').trim();
      result = JSON.parse(cleanedContent);
      
      // Validar que tema e foco não são idênticos
      if (result.recommendedTheme.toLowerCase() === result.recommendedFocus.toLowerCase()) {
        console.log('Warning: Theme and focus are identical. Adjusting recommendation...');
        
        // Se o tema for mais genérico, manter e especificar o foco
        if (domain === 'pediatria') {
          // Para pediatria
          result.recommendedFocus = `${result.recommendedFocus} - Aspectos Clínicos`;
        } else {
          // Para oftalmologia
          result.recommendedFocus = `${result.recommendedFocus} - Diagnóstico`;
        }
        
        result.justification += " (Ajustado para evitar duplicação entre tema e foco)";
      }
      
      // Mark this question as analyzed in the result
      result.reviewed = true;
      result.reviewedAt = new Date().toISOString();
      result.reviewedDomain = domain;
      
      console.log('Successfully parsed AI response:', result);
    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      console.log('Raw AI response:', data.choices[0].message.content);
      
      // Return a fallback result
      result = {
        recommendedTheme: "Não categorizado",
        recommendedFocus: "Não categorizado - Geral",
        isNewTheme: false,
        isNewFocus: false,
        justification: "Não foi possível analisar a questão adequadamente.",
        reviewed: true,
        reviewedAt: new Date().toISOString(),
        reviewedDomain: domain
      };
    }
    
    return new Response(
      JSON.stringify(result),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }}
    );
    
  } catch (error) {
    console.error('Error in analyze-question-theme function:', error);
    
    return new Response(
      JSON.stringify({ error: error.message || 'An unexpected error occurred' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' }}
    );
  }
});
