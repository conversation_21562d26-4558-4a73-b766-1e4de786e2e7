import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Trash2 } from "lucide-react";

interface Milestone {
  id: string;
  age_type: string;
  age_years: number | null;
  age_months: number;
  social_emotional: string | null;
  language_communication: string | null;
  cognition: string | null;
  motor_physical: string | null;
  image_url: string | null;
}

interface DNPMListProps {
  milestones: Milestone[];
  isLoading: boolean;
  onDelete: (id: string) => void;
}

export const DNPMList = ({ milestones, isLoading, onDelete }: DNPMListProps) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="w-full">
            <CardHeader>
              <Skeleton className="h-4 w-3/4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-20 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (milestones.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">Nenhum marco DNPM cadastrado.</p>
      </div>
    );
  }

  const formatAge = (milestone: Milestone) => {
    if (milestone.age_type === "years") {
      const years = Math.floor(milestone.age_months / 12);
      const months = milestone.age_months % 12;
      return `${years} ano${years !== 1 ? 's' : ''}${months > 0 ? ` e ${months} ${months === 1 ? 'mês' : 'meses'}` : ''}`;
    }
    return `${milestone.age_months} ${milestone.age_months === 1 ? 'mês' : 'meses'}`;
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {milestones.map((milestone) => (
        <Card key={milestone.id}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {formatAge(milestone)}
            </CardTitle>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Trash2 className="h-4 w-4" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
                  <AlertDialogDescription>
                    Tem certeza que deseja excluir este marco DNPM? Esta ação não pode ser desfeita.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancelar</AlertDialogCancel>
                  <AlertDialogAction onClick={() => onDelete(milestone.id)}>
                    Confirmar
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </CardHeader>
          <CardContent>
            {milestone.social_emotional && (
              <p className="text-sm text-muted-foreground line-clamp-2">
                {milestone.social_emotional}
              </p>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
};