
import React from "react";
import { Link } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { getThemeClasses } from "@/components/ui/theme-utils";

const VenomousAnimalsFlowchart = () => {
  const flowcharts = [
    {
      id: "scorpion",
      title: "Acidente Escorpiônico",
      description: "Fluxograma para manejo de acidentes escorpiônicos em pediatria",
      path: "/flowcharts/venomous/scorpion",
      color: "bg-yellow-50 dark:bg-yellow-900/30",
      borderColor: "border-yellow-200/50 dark:border-yellow-700/50",
      hoverColor: "hover:bg-yellow-100/70 dark:hover:bg-yellow-800/40",
      textColor: "text-gray-800 dark:text-gray-100",
      descriptionColor: "text-gray-600 dark:text-gray-300",
      iconBg: "bg-white/90 dark:bg-slate-800/90",
      icon: "🦂"
    },
    {
      id: "bothropic",
      title: "Acidente <PERSON>ópic<PERSON>",
      description: "Fluxograma para manejo de acidentes botrópicos em pediatria",
      path: "/flowcharts/venomous/bothropic",
      color: "bg-green-50 dark:bg-green-900/30",
      borderColor: "border-green-200/50 dark:border-green-700/50",
      hoverColor: "hover:bg-green-100/70 dark:hover:bg-green-800/40",
      textColor: "text-gray-800 dark:text-gray-100",
      descriptionColor: "text-gray-600 dark:text-gray-300",
      iconBg: "bg-white/90 dark:bg-slate-800/90",
      icon: "🐍"
    },
    {
      id: "crotalic",
      title: "Acidente Crotálico",
      description: "Fluxograma para manejo de acidentes crotálicos em pediatria",
      path: "/flowcharts/venomous/crotalic",
      color: "bg-purple-50 dark:bg-purple-900/30",
      borderColor: "border-purple-200/50 dark:border-purple-700/50",
      hoverColor: "hover:bg-purple-100/70 dark:hover:bg-purple-800/40",
      textColor: "text-gray-800 dark:text-gray-100",
      descriptionColor: "text-gray-600 dark:text-gray-300",
      iconBg: "bg-white/90 dark:bg-slate-800/90",
      icon: "🐍"
    },
    {
      id: "elapidic",
      title: "Acidente Elapídico",
      description: "Fluxograma para manejo de acidentes elapídicos (coral verdadeira) em pediatria",
      path: "/flowcharts/venomous/elapidic",
      color: "bg-red-50 dark:bg-red-900/30",
      borderColor: "border-red-200/50 dark:border-red-700/50",
      hoverColor: "hover:bg-red-100/70 dark:hover:bg-red-800/40",
      textColor: "text-gray-800 dark:text-gray-100",
      descriptionColor: "text-gray-600 dark:text-gray-300",
      iconBg: "bg-white/90 dark:bg-slate-800/90",
      icon: "🐍"
    },
    {
      id: "phoneutria",
      title: "Acidente Fonêutrico",
      description: "Fluxograma para manejo de acidentes com aranha armadeira em pediatria",
      path: "/flowcharts/venomous/phoneutria",
      color: "bg-orange-50 dark:bg-orange-900/30",
      borderColor: "border-orange-200/50 dark:border-orange-700/50",
      hoverColor: "hover:bg-orange-100/70 dark:hover:bg-orange-800/40",
      textColor: "text-gray-800 dark:text-gray-100",
      descriptionColor: "text-gray-600 dark:text-gray-300",
      iconBg: "bg-white/90 dark:bg-slate-800/90",
      icon: "🕷️"
    },
    {
      id: "loxoscelic",
      title: "Acidente Loxoscélico",
      description: "Fluxograma para manejo de acidentes com aranha marrom em pediatria",
      path: "/flowcharts/venomous/loxoscelic",
      color: "bg-amber-50 dark:bg-amber-900/30",
      borderColor: "border-amber-200/50 dark:border-amber-700/50",
      hoverColor: "hover:bg-amber-100/70 dark:hover:bg-amber-800/40",
      textColor: "text-gray-800 dark:text-gray-100",
      descriptionColor: "text-gray-600 dark:text-gray-300",
      iconBg: "bg-white/90 dark:bg-slate-800/90",
      icon: "🕷️"
    }
  ];

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800">
      <HelmetWrapper>
        <title>PedBook | Animais Peçonhentos</title>
        <meta name="description" content="Fluxogramas para manejo de acidentes com animais peçonhentos" />
      </HelmetWrapper>
      
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto space-y-8">
          <div className="flex items-center gap-4">
            <Link 
              to="/flowcharts" 
              className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Voltar para Fluxogramas</span>
            </Link>
          </div>

          <div className="text-center space-y-4">
            <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400">
              Animais Peçonhentos
            </h1>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Fluxogramas para manejo de acidentes com animais peçonhentos em pediatria
            </p>
          </div>

          <div className="grid grid-cols-2 xs:grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-6">
            {flowcharts.map((flowchart) => (
              <Link
                key={flowchart.id}
                to={flowchart.path}
                className="block group transform transition-all duration-500 hover:scale-[1.02]"
              >
                <div className={`h-full p-6 rounded-2xl ${flowchart.color} border ${flowchart.borderColor} transition-all duration-300 ${flowchart.hoverColor} hover:shadow-lg`}>
                  <div className="flex flex-col items-center text-center h-full">
                    <div className={`${flowchart.iconBg} w-16 h-16 rounded-xl flex items-center justify-center mb-4 shadow-sm transition-transform group-hover:scale-110`}>
                      <span className="text-2xl">{flowchart.icon}</span>
                    </div>
                    <div className="space-y-3 flex-1">
                      <h3 className={`text-xl font-semibold ${flowchart.textColor}`}>
                        {flowchart.title}
                      </h3>
                      <p className={`text-sm ${flowchart.descriptionColor} mt-2`}>
                        {flowchart.description}
                      </p>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default VenomousAnimalsFlowchart;
