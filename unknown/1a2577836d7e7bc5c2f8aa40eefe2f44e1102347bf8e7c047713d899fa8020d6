
import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { ArrowDown } from "lucide-react";
import { calculateHydration, formatMedication } from "./utils/hydrationCalculator";
import { getThemeClasses } from "@/components/ui/theme-utils";
import { Card } from "@/components/ui/card";

interface DengueResultProps {
  group: string;
  color: string;
  instructions: string[];
  onReset: () => void;
  volume?: number;
  nextQuestion?: string;
  nextStep?: string;
  onContinue?: (step: string) => void;
  weight?: number;
  age?: number;
  showHydration?: boolean;
}

export const DengueResult = ({
  group,
  color,
  instructions,
  nextQuestion,
  nextStep,
  onContinue,
  weight = 0,
  age = 0,
  showHydration
}: DengueResultProps) => {
  const hydration = showHydration ? calculateHydration(age, weight) : null;
  const medication = showHydration ? formatMedication(weight, age) : null;

  // Extract color name from the Tailwind class for custom styling
  const colorName = color.split('-')[1] || 'blue';
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-8"
    >
      <Card className={getThemeClasses.gradientCard(colorName, "p-6 relative overflow-hidden")}>
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 dark:from-white/5 to-transparent pointer-events-none" />
        
        <h3 className="text-xl font-bold text-gray-800 dark:text-gray-100 mb-4">
          {group}
        </h3>
        
        <div className="space-y-3 text-gray-700 dark:text-gray-300">
          {instructions.map((instruction, index) => (
            <p key={index} className="relative z-10">
              {instruction}
            </p>
          ))}

          {showHydration && hydration && (
            <div className="mt-6 space-y-4 bg-white/50 dark:bg-slate-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <h4 className="font-semibold text-gray-800 dark:text-gray-200">Hidratação Oral Calculada:</h4>
              <div className="space-y-2">
                <p>Cálculo utilizado para a Taxa hídrica: {hydration.formula}</p>
                <p>Volume total em 24h: {hydration.totalVolume} mL</p>
                <p>• Soro de Reidratação Oral (1/3): {hydration.sro} mL</p>
                <p>• Líquidos caseiros (2/3): {hydration.caseiros} mL</p>
                <p className="text-sm italic mt-2">
                </p>
              </div>

              <div className="mt-4">
                <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">Medicações para dor ou febre:</h4>
                <div className="space-y-2">
                  <p>Paracetamol Solução oral 200 mg/mL - Gotas:</p>
                  <p className="ml-4">• {medication?.paracetamol.drops} gotas/dose a cada 6 horas</p>
                  <p className="ml-4 text-sm">• Máximo: {medication?.paracetamol.maxDaily}</p>
                  <p>ou</p>
                  <p className="mt-2">Dipirona Solução oral 500 mg/mL - Gotas:</p>
                  <p className="ml-4">• {medication?.dipirona.drops} gotas/dose a cada 6 horas</p>
                  <p className="ml-4 text-sm">• Máximo: 40 gotas por dose</p>
                  <p>ou</p>
                  <p className="mt-2">Dipirona Solução oral 50 mg/mL - Xarope:</p>
                  <p className="ml-4">• {medication?.dipirona.solution} mL/dose a cada 6 horas</p>
                  <p className="ml-4 text-sm">• Máximo: 15 mL/dose</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {(nextQuestion || nextStep) && (
          <motion.div 
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="mt-6 flex flex-col items-center"
          >
            <ArrowDown className="w-8 h-8 text-primary dark:text-blue-400 animate-bounce" />
            <p className="text-sm text-primary dark:text-blue-400 mt-2">Continue o manejo abaixo</p>
          </motion.div>
        )}
      </Card>

      {nextQuestion && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex justify-center"
        >
          <Card 
            onClick={() => onContinue?.(nextQuestion)}
            className={getThemeClasses.gradientCard("blue", "p-4 hover:from-primary/20 hover:to-primary/10 dark:hover:from-primary/30 dark:hover:to-primary/20 transition-all cursor-pointer backdrop-blur-sm")}
          >
            <p className="text-primary dark:text-blue-400 font-medium text-center">Próxima Avaliação</p>
          </Card>
        </motion.div>
      )}

      {nextStep && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex justify-center"
        >
          <Card 
            onClick={() => onContinue?.(nextStep)}
            className={getThemeClasses.gradientCard("green", "p-4 hover:from-green-500/20 hover:to-green-500/10 dark:hover:from-green-500/30 dark:hover:to-green-500/20 transition-all cursor-pointer backdrop-blur-sm")}
          >
            <p className="text-green-600 dark:text-green-400 font-medium text-center">Próxima Etapa</p>
          </Card>
        </motion.div>
      )}
    </motion.div>
  );
};
