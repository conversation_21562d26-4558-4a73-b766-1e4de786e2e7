import React from 'react';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CustomBadgeProps {
  icon?: LucideIcon;
  text: string;
  variant?: 'default' | 'news' | 'feature' | 'highlight';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  className?: string;
}

export const CustomBadge: React.FC<CustomBadgeProps> = ({
  icon: Icon,
  text,
  variant = 'default',
  size = 'md',
  onClick,
  className
}) => {
  const baseClasses = cn(
    "inline-flex items-center gap-1.5 rounded-lg transition-all duration-200",
    "border backdrop-blur-sm cursor-pointer group",
    onClick && "hover:shadow-sm"
  );

  const variantClasses = {
    default: "bg-white/60 dark:bg-slate-800/60 border-gray-100/50 dark:border-gray-700/30 text-gray-700 dark:text-gray-300 hover:bg-white/80 dark:hover:bg-slate-800/80",
    news: "bg-blue-50/80 dark:bg-blue-900/30 border-blue-100/50 dark:border-blue-800/30 text-blue-700 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/50",
    feature: "bg-gradient-to-r from-blue-50/80 to-indigo-50/80 dark:from-blue-900/30 dark:to-indigo-900/30 border-blue-100/50 dark:border-blue-800/30 text-blue-700 dark:text-blue-400 hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/50 dark:hover:to-indigo-900/50",
    highlight: "bg-gradient-to-r from-amber-50/80 to-orange-50/80 dark:from-amber-900/30 dark:to-orange-900/30 border-amber-100/50 dark:border-amber-800/30 text-amber-700 dark:text-amber-400 hover:from-amber-50 hover:to-orange-50 dark:hover:from-amber-900/50 dark:hover:to-orange-900/50"
  };

  const sizeClasses = {
    sm: "px-2 py-1 text-[10px] font-medium",
    md: "px-3 py-1.5 text-xs font-medium",
    lg: "px-4 py-2 text-sm font-medium"
  };

  const iconSizeClasses = {
    sm: "h-2.5 w-2.5",
    md: "h-3 w-3",
    lg: "h-3.5 w-3.5"
  };

  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
      onClick={onClick}
    >
      {Icon && (
        <Icon className={cn(
          iconSizeClasses[size],
          "transition-transform duration-200",
          onClick && "group-hover:scale-110"
        )} />
      )}
      <span className="truncate">{text}</span>
    </div>
  );
};

// Componente específico para notícias recentes (similar ao card de notícias)
export const NewsBadge: React.FC<{
  text: string;
  onClick?: () => void;
  className?: string;
}> = ({ text, onClick, className }) => {
  return (
    <div
      className={cn(
        "inline-flex items-center gap-1.5 px-3 py-1.5 rounded-lg",
        "bg-white/40 dark:bg-slate-800/40 backdrop-blur-sm",
        "border border-gray-100/40 dark:border-gray-700/10",
        "text-xs font-medium text-gray-700 dark:text-gray-300",
        "transition-all duration-200 cursor-pointer group",
        "hover:bg-white/60 dark:hover:bg-slate-800/60",
        "hover:border-blue-200/50 dark:hover:border-blue-700/30",
        "hover:text-blue-600 dark:hover:text-blue-400",
        className
      )}
      onClick={onClick}
    >
      <div className="flex items-center gap-1">
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          width="24" 
          height="24" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          className="h-3 w-3 text-blue-500 dark:text-blue-400 transition-transform duration-200 group-hover:scale-110"
        >
          <path d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-2 2Zm0 0a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2"></path>
          <path d="M18 14h-8"></path>
          <path d="M15 18h-5"></path>
          <path d="M10 6h8v4h-8V6Z"></path>
        </svg>
        <span className="truncate">{text}</span>
      </div>
    </div>
  );
};

export default CustomBadge;
