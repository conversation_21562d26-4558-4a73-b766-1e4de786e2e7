import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import type { PrescriptionWithMedications } from "@/components/prescriptions/types";

interface UseSharedPrescriptionsProps {
  selectedCategory: string | null;
  searchTerm: string;
  page: number;
  itemsPerPage: number;
}

export const useSharedPrescriptions = ({
  selectedCategory,
  searchTerm,
  page,
  itemsPerPage,
}: UseSharedPrescriptionsProps) => {
  return useQuery({
    queryKey: ["shared-prescriptions", selectedCategory, searchTerm, page],
    queryFn: async () => {
      let query = supabase
        .from("pedbook_prescriptions")
        .select(`
          *,
          pedbook_prescription_medications (
            id,
            medication_id,
            dosage_id,
            prescription_id,
            notes,
            pedbook_medications (
              name,
              brands,
              description,
              pedbook_medication_categories (
                name
              )
            ),
            pedbook_medication_dosages (
              name,
              dosage_template,
              summary
            )
          ),
          secure_profiles (
            full_name,
            formation_area
          )
        `)
        .eq("is_public", true)
        .order("created_at", { ascending: false })
        .order("id");

      if (selectedCategory) {
        query = query.eq("category_id", selectedCategory);
      }

      if (searchTerm) {
        query = query.ilike("name", `%${searchTerm}%`);
      }

      query = query.range(page * itemsPerPage, (page + 1) * itemsPerPage - 1);

      const { data, error } = await query;

      if (error) {
        console.error("Error fetching shared prescriptions:", error);
        throw error;
      }

      return data as PrescriptionWithMedications[];
    },
    staleTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });
};