import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle, AlertTriangle, AlertOctagon } from 'lucide-react';
import { ClinicalPicture } from '../types';

interface ClinicalPicturesGridProps {
  onSelectPicture: (severity: 'mild' | 'moderate' | 'severe') => void;
}

const clinicalPictures: ClinicalPicture[] = [
  {
    title: 'Quadro Leve',
    description: 'Parestesia e dor de intensidade variável SEM clínica de miastenia',
    treatment: 'Manter sintomáticos e observação por 24 horas',
    severity: 'mild'
  },
  {
    title: 'Quadro Moderado',
    description: 'Miastenia aguda SEM paralisia',
    treatment: 'Administrar SAEL IV (5 ampolas)',
    severity: 'moderate'
  },
  {
    title: 'Quadro Grave',
    description: 'Miastenia aguda COM paralisia',
    treatment: 'Administrar SAEL IV (10 ampolas) e terapia de suporte',
    severity: 'severe'
  }
];

export const ClinicalPicturesGrid: React.FC<ClinicalPicturesGridProps> = ({ onSelectPicture }) => {
  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'mild':
        return <AlertCircle className="h-6 w-6 text-emerald-600" />;
      case 'moderate':
        return <AlertTriangle className="h-6 w-6 text-amber-600" />;
      case 'severe':
        return <AlertOctagon className="h-6 w-6 text-rose-600" />;
      default:
        return null;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'mild':
        return 'bg-[#F2FCE2] hover:bg-[#E8F7D4] border-[#E8F7D4]';
      case 'moderate':
        return 'bg-[#FEF7CD] hover:bg-[#FDF2B8] border-[#FDF2B8]';
      case 'severe':
        return 'bg-[#FFDEE2] hover:bg-[#FFD4D9] border-[#FFD4D9]';
      default:
        return '';
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto p-4">
      {clinicalPictures.map((picture) => (
        <Card 
          key={picture.severity}
          className={`p-6 transition-all duration-300 hover:scale-105 border-2 ${getSeverityColor(picture.severity)}`}
        >
          <div className="flex items-center gap-3 mb-4">
            {getSeverityIcon(picture.severity)}
            <h3 className="text-xl font-semibold text-gray-800">
              {picture.title}
            </h3>
          </div>
          <p className="text-gray-600 mb-6 min-h-[60px]">{picture.description}</p>
          <Button
            variant="outline"
            className="w-full justify-between border-2 hover:bg-white/50 text-gray-700"
            onClick={() => onSelectPicture(picture.severity)}
          >
            Ver tratamento
          </Button>
        </Card>
      ))}
    </div>
  );
};