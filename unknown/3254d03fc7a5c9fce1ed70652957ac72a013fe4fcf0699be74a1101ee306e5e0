import { motion } from "framer-motion";

export const BothropicSpecialConsiderations = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-4"
    >
      <h2 className="text-2xl font-bold text-gray-800">Observação e Reavaliação</h2>
      
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <ul className="space-y-4 text-gray-700">
          <li className="flex gap-2">
            <span className="text-blue-600">•</span>
            <span>Após administração inicial de soro em qualquer quadro clínico:</span>
          </li>
          <li className="flex gap-2">
            <span className="text-blue-600">•</span>
            <span>Monitorar continuamente a evolução clínica do paciente.</span>
          </li>
          <li className="flex gap-2">
            <span className="text-blue-600">•</span>
            <span>Detectar e tratar precocemente complicações como insuficiência renal ou distúrbios de coagulação.</span>
          </li>
          <li className="flex gap-2">
            <span className="text-blue-600">•</span>
            <span>Reclassificar o quadro clínico, se necessário, e ajustar o tratamento.</span>
          </li>
        </ul>
      </div>

      <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <p className="text-sm text-gray-600 italic">
          Referência: BRASIL. Ministério da Saúde. Secretaria de Vigilância em Saúde e Ambiente. Departamento de Doenças Transmissíveis. Guia de Animais Peçonhentos do Brasil. Brasília: Ministério da Saúde, 2024.
        </p>
      </div>
    </motion.div>
  );
};