import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { PrescriptionList } from "./PrescriptionList";
import { PrescriptionDetails } from "./PrescriptionDetails";
import { SharedPrescriptionFilters } from "./SharedPrescriptionFilters";
import { useSharedPrescriptions } from "./SharedPrescriptionQuery";
import { usePrescriptionMutations } from "./usePrescriptionMutations";
import type { PrescriptionWithMedications } from "@/components/prescriptions/types";
import { motion, AnimatePresence } from "framer-motion";

const ITEMS_PER_PAGE = 10;

export function SharedPrescriptionContent({ userId }: { userId?: string }) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedPrescription, setSelectedPrescription] = useState<PrescriptionWithMedications | null>(null);
  const [page, setPage] = useState(0);

  const { data: categories } = useQuery({
    queryKey: ["prescription-categories"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_prescription_categories")
        .select("*")
        .order("name");
      
      if (error) throw error;
      return data;
    },
  });

  const { data: sharedPrescriptions, isLoading } = useSharedPrescriptions({
    selectedCategory,
    searchTerm,
    page,
    itemsPerPage: ITEMS_PER_PAGE,
  });

  const { addPrescriptionMutation, removePrescriptionMutation } = usePrescriptionMutations(userId);

  const handleAddPrescription = async (prescription: PrescriptionWithMedications) => {
    if (!userId) return;
    await addPrescriptionMutation.mutateAsync(prescription);
  };

  const handleRemovePrescription = async (prescriptionId: string) => {
    if (!userId) return;
    await removePrescriptionMutation.mutateAsync(prescriptionId);
  };

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="relative min-h-screen">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-light/20 via-transparent to-accent-pink/20 -z-10" />
        <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))] -z-10" />

        <div className="container mx-auto px-2 md:px-4 py-4 md:py-8">
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="flex flex-col lg:flex-row gap-4 md:gap-8"
          >
            <SharedPrescriptionFilters
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              selectedCategory={selectedCategory}
              onCategorySelect={setSelectedCategory}
              categories={categories || []}
            />

            <div className="flex-1">
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="space-y-4 md:space-y-6"
              >
                <div className="flex items-center justify-center md:justify-between">
                  <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent text-center md:text-left">
                    Prescrições Compartilhadas
                  </h1>
                </div>
                
                <AnimatePresence mode="wait">
                  <motion.div
                    key={`prescriptions-${searchTerm}-${selectedCategory}-${page}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                    className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6"
                  >
                    <PrescriptionList
                      prescriptions={sharedPrescriptions}
                      userId={userId}
                      isLoading={isLoading}
                      onViewDetails={setSelectedPrescription}
                      onAdd={handleAddPrescription}
                      onRemove={handleRemovePrescription}
                    />
                  </motion.div>
                </AnimatePresence>

                {sharedPrescriptions && sharedPrescriptions.length >= ITEMS_PER_PAGE && (
                  <motion.div 
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.6 }}
                    className="flex justify-center gap-3 md:gap-4 mt-4 md:mt-8"
                  >
                    <button
                      className="px-4 md:px-6 py-2 text-sm font-medium text-primary border border-primary/20 rounded-full hover:bg-primary/5 transition-colors disabled:opacity-50"
                      onClick={() => setPage(prev => Math.max(0, prev - 1))}
                      disabled={page === 0}
                    >
                      Anterior
                    </button>
                    <span className="flex items-center px-3 md:px-4 bg-white/50 backdrop-blur-sm rounded-full border border-primary/10 text-sm">
                      Página {page + 1}
                    </span>
                    <button
                      className="px-4 md:px-6 py-2 text-sm font-medium text-primary border border-primary/20 rounded-full hover:bg-primary/5 transition-colors disabled:opacity-50"
                      onClick={() => setPage(prev => prev + 1)}
                      disabled={sharedPrescriptions.length < ITEMS_PER_PAGE}
                    >
                      Próxima
                    </button>
                  </motion.div>
                )}
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>

      <PrescriptionDetails
        prescription={selectedPrescription}
        onClose={() => setSelectedPrescription(null)}
      />
    </motion.div>
  );
}