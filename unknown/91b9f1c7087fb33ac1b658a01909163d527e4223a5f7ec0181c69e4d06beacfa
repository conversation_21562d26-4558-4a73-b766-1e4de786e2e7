import { useState } from "react";
import { Dialog } from "@/components/ui/dialog";
import { useNotes } from "@/hooks/useNotes";
import { NotesFilter } from "./NotesFilter";
import { NoteDialog } from "./NoteDialog";
import { NoteCard } from "./NoteCard";
import { NoteForm } from "./NoteForm";
import { toast } from "sonner";
import { Note } from "@/types/notes";

interface NotesListProps {
  searchTerm?: string;
  folderId?: string;
}

export const NotesList = ({ searchTerm, folderId }: NotesListProps) => {
  const { notes, isLoading, toggleFavorite, deleteNote } = useNotes(searchTerm, folderId);
  const [selectedNote, setSelectedNote] = useState<Note | null>(null);
  const [isViewMode, setIsViewMode] = useState(true);
  const [selectedTag, setSelectedTag] = useState<string | null>(null);
  const [showFavorites, setShowFavorites] = useState(false);

  const availableTags = Array.from(
    new Set(notes?.flatMap((note) => note.tags || []) || [])
  );

  const filteredNotes = notes?.filter((note) => {
    const matchesTag = !selectedTag || note.tags?.includes(selectedTag);
    const matchesFavorite = !showFavorites || note.is_favorite;
    return matchesTag && matchesFavorite;
  });

  const handleDelete = async (noteId: string) => {
    try {
      await deleteNote.mutateAsync(noteId);
      setSelectedNote(null);
      toast.success("Nota excluída com sucesso");
    } catch (error) {
      console.error("Error deleting note:", error);
      toast.error("Erro ao excluir nota");
    }
  };

  return (
    <>
      <NotesFilter
        selectedTag={selectedTag}
        onTagChange={setSelectedTag}
        showFavorites={showFavorites}
        onFavoritesChange={setShowFavorites}
        availableTags={availableTags}
      />

      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
        {filteredNotes?.map((note) => (
          <NoteCard
            key={note.id}
            note={note}
            onNoteClick={() => {
              setSelectedNote(note);
              setIsViewMode(true);
            }}
            onToggleFavorite={toggleFavorite.mutate}
          />
        ))}
      </div>

      {selectedNote && isViewMode && (
        <NoteDialog
          isOpen={!!selectedNote && isViewMode}
          onClose={() => {
            setSelectedNote(null);
            setIsViewMode(true);
          }}
          note={selectedNote}
          onEdit={() => setIsViewMode(false)}
          onDelete={handleDelete}
          onToggleFavorite={toggleFavorite.mutate}
        />
      )}

      {selectedNote && !isViewMode && (
        <NoteForm
          isOpen={!!selectedNote && !isViewMode}
          onClose={() => {
            setSelectedNote(null);
            setIsViewMode(true);
          }}
          noteId={selectedNote.id}
          initialTitle={selectedNote.title}
          initialContent={selectedNote.content}
          initialFolderId={selectedNote.folder_id}
          initialTags={selectedNote.tags}
        />
      )}
    </>
  );
};