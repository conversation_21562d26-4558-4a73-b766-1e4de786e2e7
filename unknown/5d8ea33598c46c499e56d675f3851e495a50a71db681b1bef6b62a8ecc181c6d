import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import { Button } from "@/components/ui/button";
import { Bold, Italic, Underline as UnderlineIcon } from 'lucide-react';
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { useState } from 'react';
import { EnhanceTextButton } from './EnhanceTextButton';
import { useTextEnhancement } from '@/hooks/useTextEnhancement';

interface NoteEditorProps {
  content: string;
  onChange: (content: string) => void;
}

export const NoteEditor = ({ content, onChange }: NoteEditorProps) => {
  const [isEnhancing, setIsEnhancing] = useState(false);
  const { recordEnhancement } = useTextEnhancement();

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: false,
        bulletList: false,
        orderedList: false,
      }),
      Underline,
    ],
    content,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    editorProps: {
      attributes: {
        class: 'h-full min-h-[250px] p-4 focus:outline-none prose-sm sm:prose max-w-none overflow-x-auto break-words whitespace-pre-wrap',
      },
    },
  });

  const enhanceText = async () => {
    if (!editor) return;

    const currentContent = editor.getHTML();
    
    try {
      setIsEnhancing(true);
      toast.loading("Melhorando o texto...");
      
      const { data, error } = await supabase.functions.invoke('enhance-text', {
        body: { text: currentContent },
      });

      if (error) throw error;

      if (data.enhancedText) {
        editor.commands.setContent(data.enhancedText);
        // Importante: Chamar o onChange explicitamente após a atualização do conteúdo pela IA
        onChange(data.enhancedText);
        await recordEnhancement();
        toast.success("Texto melhorado com sucesso!");
      }
    } catch (error) {
      console.error('Error enhancing text:', error);
      toast.error("Erro ao melhorar o texto. Tente novamente.");
    } finally {
      setIsEnhancing(false);
    }
  };

  if (!editor) {
    return null;
  }

  return (
    <div className="border rounded-lg flex flex-col h-full overflow-hidden space-y-4">
      <div className="flex gap-2 p-2 border-b overflow-x-auto">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={editor.isActive('bold') ? 'bg-secondary' : ''}
        >
          <Bold className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={editor.isActive('italic') ? 'bg-secondary' : ''}
        >
          <Italic className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleUnderline().run()}
          className={editor.isActive('underline') ? 'bg-secondary' : ''}
        >
          <UnderlineIcon className="h-4 w-4" />
        </Button>
      </div>

      <div className="flex-1 overflow-auto">
        <EditorContent editor={editor} className="h-full" />
      </div>

      <div className="p-2 border-t">
        <EnhanceTextButton onEnhance={enhanceText} isEnhancing={isEnhancing} />
      </div>
    </div>
  );
};