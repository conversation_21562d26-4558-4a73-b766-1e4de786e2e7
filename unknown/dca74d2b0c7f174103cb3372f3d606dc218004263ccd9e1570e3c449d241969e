import { Pencil, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";

interface DosageActionsProps {
  onEdit: () => void;
  onDelete: () => void;
}

export const DosageActions = ({ onEdit, onDelete }: DosageActionsProps) => {
  return (
    <div className="flex gap-2">
      <Button
        variant="outline"
        size="icon"
        onClick={onEdit}
        className="hover:bg-primary/10"
      >
        <Pencil className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        onClick={onDelete}
        className="hover:bg-destructive/10"
      >
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  );
};