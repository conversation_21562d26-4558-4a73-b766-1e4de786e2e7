import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface InitialDiagnosisProps {
  onStart: () => void;
}

export const InitialDiagnosis = ({ onStart }: InitialDiagnosisProps) => {
  return (
    <Card className="p-6 space-y-4">
      <h3 className="text-lg font-semibold">Manejo de Cetoacidose Diabética</h3>
      
      <div className="space-y-2">
        <h4 className="font-medium">Quadro clínico:</h4>
        <ul className="list-disc list-inside space-y-1 text-gray-600">
          <li>Hiperglicemia: Poliúria, polidipsia, fadiga, perda de peso</li>
          <li>Acidose metabólica: Náuseas, vômitos, dor abdominal, respiração de Kussmaul, hálito cetônico</li>
          <li>Desidratação: Mucosas secas, taquicardia, hipotensão.</li>
          <li>Alterações neurológicas: <PERSON><PERSON>são, letargia, podendo evoluir para coma.</li>
        </ul>
      </div>
      <div className="space-y-2">
        <h4 className="font-medium">Exames necessários para investigação:</h4>
        <ul className="list-disc list-inside space-y-1 text-gray-600">
          <li>Glicemia capilar e venosa</li>
          <li>Gasometria arterial ou venosa</li>
          <li>Eletrólitos séricos (sódio, potássio, cloreto)</li>
          <li>Bicarbonato</li>
          <li>Corpos cetônicos no sangue e urina</li>
        </ul>
      </div>
      <div className="space-y-2">
        <h4 className="font-medium">Já tem os resultados dos exames? Inicie o fluxo.</h4>
        <ul className="list-disc list-inside space-y-1 text-gray-600">
        </ul>
      </div>

      <Button onClick={onStart} className="w-full">
        Iniciar avaliação
      </Button>
    </Card>
  );
};