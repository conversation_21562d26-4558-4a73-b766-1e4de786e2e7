import HelmetWrapper from "@/components/utils/HelmetWrapper";

export const HydrationMetaTags = () => {
  const pageTitle = "PedBook | Calculadora de Hidratação Pediátrica - Holliday-Segar";
  const pageDescription = "Calculadora para determinar a taxa de hidratação de manutenção em pacientes pediátricos baseada na regra de Holliday-Segar. Ferramenta essencial para pediatras e emergencistas.";
  const pageUrl = "https://pedb.com.br/calculadoras/hidratacao";
  const keywords = [
    "hidratação pediátrica",
    "calculadora holliday segar",
    "hidratação de manutenção",
    "pediatria calculadora",
    "emergência pediátrica",
    "fluidoterapia",
    "taxa de hidratação",
    "balanço hídrico",
    "eletrólitos",
    "hidratação venosa",
    "terapia intravenosa"
  ].join(", ");

  return (
    <HelmetWrapper>
      <title>{pageTitle}</title>
      <meta name="description" content={pageDescription} />
      <meta name="keywords" content={keywords} />
      <meta name="robots" content="index, follow, max-image-preview:large" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta charSet="UTF-8" />
      <link rel="canonical" href={pageUrl} />

      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={pageDescription} />
      <meta property="og:type" content="article" />
      <meta property="og:url" content={pageUrl} />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />
      <meta property="og:image" content="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/calculadora.webp" />
      <meta property="og:image:alt" content="Interface da calculadora de hidratação pediátrica mostrando campos para peso do paciente e cálculos baseados na regra de Holliday-Segar" />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />

      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={pageDescription} />
      <meta name="twitter:image" content="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/calculadora.webp" />
      <meta name="twitter:site" content="@PedBook" />

      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "MedicalWebPage",
          "name": pageTitle,
          "description": pageDescription,
          "url": pageUrl,
          "keywords": keywords.split(", "),
          "inLanguage": "pt-BR",
          "mainEntity": {
            "@type": "MedicalCalculator",
            "name": "Calculadora de Hidratação Pediátrica",
            "description": pageDescription,
            "medicalSpecialty": {
              "@type": "MedicalSpecialty",
              "name": "Pediatria"
            },
            "relevantSpecialty": [
              {
                "@type": "MedicalSpecialty",
                "name": "Emergência Pediátrica"
              },
              {
                "@type": "MedicalSpecialty",
                "name": "Pediatria"
              }
            ]
          },
          "about": {
            "@type": "MedicalCondition",
            "name": "Hidratação Pediátrica",
            "description": "Cálculo da taxa de hidratação de manutenção baseado na regra de Holliday-Segar"
          },
          "audience": {
            "@type": "MedicalAudience",
            "audienceType": "Profissionais de Saúde"
          },
          "publisher": {
            "@type": "Organization",
            "name": "PedBook",
            "url": "https://pedb.com.br",
            "logo": {
              "@type": "ImageObject",
              "url": "https://pedb.com.br/logo.png"
            }
          }
        })}
      </script>
    </HelmetWrapper>
  );
};