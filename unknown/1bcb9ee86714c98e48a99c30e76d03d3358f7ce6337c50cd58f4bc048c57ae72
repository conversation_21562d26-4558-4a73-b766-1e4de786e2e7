import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface DosageFormFieldsProps {
  formData: {
    dosageName: string;
    dosageType: string;
    dosageTemplate: string;
    summary: string;
  };
  onChange: (field: string, value: string) => void;
}

export function DosageFormFields({ formData, onChange }: DosageFormFieldsProps) {
  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="dosageName">Nome da Dosagem</Label>
        <Input
          id="dosageName"
          value={formData.dosageName}
          onChange={(e) => onChange("dosageName", e.target.value)}
          required
        />
      </div>

      <div>
        <Label htmlFor="dosageType">Tipo de Dosagem</Label>
        <Select
          value={formData.dosageType}
          onValueChange={(value) => onChange("dosageType", value)}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="weight">Baseado no peso</SelectItem>
            <SelectItem value="fixed">Dose fixa</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="dosageTemplate">Template da Dosagem</Label>
        <Input
          id="dosageTemplate"
          value={formData.dosageTemplate}
          onChange={(e) => onChange("dosageTemplate", e.target.value)}
          required
        />
      </div>

      <div>
        <Label htmlFor="summary">Resumo da Dosagem</Label>
        <Input
          id="summary"
          value={formData.summary}
          onChange={(e) => onChange("summary", e.target.value)}
        />
      </div>
    </div>
  );
}