import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { supabase } from "@/integrations/supabase/client";
import { SignUpFormFields } from "./SignUpFormFields";
import { SignUpFormCheckboxes } from "./SignUpFormCheckboxes";
import { useNotification } from "@/context/NotificationContext";

const formSchema = z.object({
  fullName: z.string().min(2, "Nome muito curto"),
  email: z.string().email("Email inválido"),
  password: z.string().min(6, "A senha deve ter pelo menos 6 caracteres"),
  formationArea: z.string().min(2, "Área de formação muito curta"),
  graduationYear: z.string().min(4, "Ano de formação inválido"),
  type: z.enum(["student", "professional"]),
  isAdult: z.boolean().refine((val) => val === true, "Você deve ter mais de 17 anos"),
  acceptTerms: z.boolean().refine((val) => val === true, "Você deve aceitar os termos"),
});

interface SignUpFormProps {
  onModeChange: () => void;
  onSuccess: () => void;
}

const SignUpForm = ({ onModeChange, onSuccess }: SignUpFormProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const { showNotification } = useNotification();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: "",
      email: "",
      password: "",
      formationArea: "",
      graduationYear: "",
      type: "professional",
      isAdult: false,
      acceptTerms: false,
    },
  });

  const sendWelcomeEmail = async (email: string, name: string) => {
    try {
      const response = await fetch(
        "https://bxedpdmgvgatjdfxgxij.supabase.co/functions/v1/welcome-email",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ email, name }),
        }
      );

      // Verificar se a resposta foi bem-sucedida
      if (!response.ok) {
        // Silenciosamente falhar - não mostrar erro ao usuário
        return;
      }
    } catch (error) {
      // Silenciosamente falhar - não mostrar erro ao usuário
      return;
    }
  };

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsLoading(true);
      const { error } = await supabase.auth.signUp({
        email: values.email,
        password: values.password,
        options: {
          data: {
            full_name: values.fullName,
            formation_area: values.formationArea,
            graduation_year: values.graduationYear,
            is_student: values.type === "student",
            is_professional: values.type === "professional",
          },
        },
      });

      if (error) {
        if (error.message === "User already registered") {
          showNotification({
            title: "Email já cadastrado",
            description: "Este email já está em uso. Por favor, faça login ou use outro email.",
            type: "error",
            buttonText: "Entendi"
          });
          return;
        }
        throw error;
      }

      // Enviar email de boas-vindas
      await sendWelcomeEmail(values.email, values.fullName);

      showNotification({
        title: "Conta criada com sucesso!",
        description: "Seja bem-vindo.",
        type: "success",
        buttonText: "Continuar",
        onButtonClick: onSuccess
      });
    } catch (error) {
      showNotification({
        title: "Erro ao criar conta",
        description: "Verifique os dados e tente novamente.",
        type: "error",
        buttonText: "Tentar novamente"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4 py-2 pb-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <SignUpFormFields form={form} />
          <SignUpFormCheckboxes form={form} />
          <div className="flex flex-col gap-2">
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Criando conta..." : "Criar conta"}
            </Button>
            <Button
              type="button"
              variant="ghost"
              className="w-full"
              onClick={onModeChange}
            >
              Já tem uma conta? Entre
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default SignUpForm;