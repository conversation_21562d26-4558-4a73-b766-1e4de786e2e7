import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Diretórios a serem verificados
const directories = [
  'src/components',
  'src/pages'
];

// Função para verificar se um arquivo contém importações do react-helmet
function checkFileForHelmet(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return {
      hasHelmetWrapper: content.includes('import HelmetWrapper from "@/components/utils/HelmetWrapper"'),
      hasHelmetAsync: content.includes('import { Helmet } from "react-helmet-async"'),
      hasHelmet: content.includes('import { Helmet } from "react-helmet"') ||
                 content.includes("import { Helmet } from 'react-helmet'") ||
                 content.includes('import Helmet from "react-helmet"') ||
                 content.includes("import Helmet from 'react-helmet'"),
      content
    };
  } catch (error) {
    console.error(`Erro ao ler o arquivo ${filePath}:`, error);
    return { hasHelmetWrapper: false, hasHelmetAsync: false, hasHelmet: false, content: '' };
  }
}

// Função para percorrer diretórios recursivamente
function walkDir(dir, callback) {
  fs.readdirSync(dir).forEach(f => {
    const dirPath = path.join(dir, f);
    const isDirectory = fs.statSync(dirPath).isDirectory();
    isDirectory ? walkDir(dirPath, callback) : callback(path.join(dir, f));
  });
}

// Lista de arquivos com problemas
const filesWithProblems = [];

// Percorrer diretórios e verificar arquivos
directories.forEach(dir => {
  walkDir(dir, (filePath) => {
    if (filePath.endsWith('.tsx') || filePath.endsWith('.jsx')) {
      const { hasHelmetWrapper, hasHelmetAsync, hasHelmet, content } = checkFileForHelmet(filePath);

      // Verificar se há problemas
      if (hasHelmet) {
        filesWithProblems.push({ filePath, problem: 'Ainda usa react-helmet', content });
      } else if (!hasHelmetWrapper && !hasHelmetAsync && content.includes('<Helmet')) {
        filesWithProblems.push({ filePath, problem: 'Usa tag Helmet sem importação correta', content });
      } else if (hasHelmetWrapper && content.includes('<Helmet')) {
        filesWithProblems.push({ filePath, problem: 'Usa HelmetWrapper mas ainda tem tag Helmet', content });
      }
    }
  });
});

// Função para corrigir um arquivo
function fixFile(file) {
  try {
    let content = file.content;

    // Substituir tags
    content = content.replace(/<Helmet(\s|>)/g, '<HelmetWrapper$1');
    content = content.replace(/<\/Helmet>/g, '</HelmetWrapper>');

    // Salvar arquivo
    fs.writeFileSync(file.filePath, content, 'utf8');
    return true;
  } catch (error) {
    console.error(`Erro ao corrigir o arquivo ${file.filePath}:`, error);
    return false;
  }
}

// Corrigir o arquivo HelmetWrapper.tsx
const helmetWrapperPath = path.join(__dirname, 'src/components/utils/HelmetWrapper.tsx');
if (fs.existsSync(helmetWrapperPath)) {
  const content = fs.readFileSync(helmetWrapperPath, 'utf8');
  const fixedContent = content.replace(
    /import React from 'react';\nimport { Helmet } from 'react-helmet-async';/,
    "import React from 'react';\nimport { Helmet } from 'react-helmet-async';"
  );
  fs.writeFileSync(helmetWrapperPath, fixedContent, 'utf8');
}

// Corrigir arquivos com problemas
console.log('Corrigindo arquivos com problemas...');
let successCount = 0;
let failCount = 0;

filesWithProblems.forEach(file => {
  const success = fixFile(file);
  if (success) {
    console.log(`✅ Corrigido: ${file.filePath}`);
    successCount++;
  } else {
    console.log(`❌ Falha: ${file.filePath}`);
    failCount++;
  }
});

console.log(`\nCorreção concluída: ${successCount} arquivos corrigidos com sucesso, ${failCount} falhas.`);
