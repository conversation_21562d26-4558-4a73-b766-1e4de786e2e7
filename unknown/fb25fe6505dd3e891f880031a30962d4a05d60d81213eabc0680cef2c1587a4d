import React from 'react';
import { Link } from 'react-router-dom';

// Importar o logo como arquivo
const logoPath = "/faviconx.webp";

// Componente de logo otimizado para carregamento rápido
const PedBookLogo: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <Link to="/" className={`flex-shrink-0 ${className || ''}`}>
      <div className="flex items-center justify-center h-10 w-10 sm:h-11 sm:w-11 rounded-md overflow-hidden shadow-sm">
        <img
          src={logoPath}
          alt="PedBook Logo"
          className="h-full w-full object-cover transition-transform hover:scale-105"
          width="48"
          height="48"
          // Adicionar preload para carregar a imagem com prioridade alta
          fetchpriority="high"
          // Adicionar preconnect para o domínio do Supabase
          // Isso é feito no HTML principal, mas adicionamos aqui como lembrete
          // <link rel="preconnect" href="https://bxedpdmgvgatjdfxgxij.supabase.co" />
        />
      </div>
    </Link>
  );
};

export default PedBookLogo;
