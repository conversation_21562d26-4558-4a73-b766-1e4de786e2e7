import HelmetWrapper from "@/components/utils/HelmetWrapper";

export const FormulaMetaTags = () => {
  const pageTitle = "PedBook | Fórmulas Infantis - Guia Completo de Alimentação";
  const pageDescription = "Explore nosso guia completo sobre fórmulas infantis, com informações detalhadas sobre tipos, indicações e recomendações para cada faixa etária.";
  const pageUrl = "https://pedb.com.br/childcare/formulas";
  const imageUrl = "https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/formulas.webp";
  const keywords = [
    "fórmulas infantis",
    "alimentação infantil",
    "nutrição pediátrica",
    "leite em pó",
    "fórmula láctea",
    "fórmula hipoalergênica",
    "fórmula anti-refluxo",
    "fórmula elementar",
    "fórmula extensamente hidrolisada",
    "alimentação complementar",
    "nutrição bebê",
    "guia alimentação infantil"
  ].join(", ");

  return (
    <HelmetWrapper>
      <title>{pageTitle}</title>
      <meta name="description" content={pageDescription} />
      <meta name="keywords" content={keywords} />

      <meta name="robots" content="index, follow, max-image-preview:large" />
      <link rel="canonical" href={pageUrl} />

      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={pageDescription} />
      <meta property="og:type" content="article" />
      <meta property="og:url" content={pageUrl} />
      <meta property="og:image" content={imageUrl} />
      <meta property="og:image:alt" content="Ilustração sobre fórmulas infantis e alimentação" />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />

      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={pageDescription} />
      <meta name="twitter:image" content={imageUrl} />

      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "MedicalWebPage",
          "name": pageTitle,
          "description": pageDescription,
          "url": pageUrl,
          "image": imageUrl,
          "keywords": keywords.split(", "),
          "publisher": {
            "@type": "Organization",
            "name": "PedBook",
            "logo": {
              "@type": "ImageObject",
              "url": "https://pedb.com.br/logo.png"
            }
          },
          "specialty": "Pediatria",
          "audience": {
            "@type": "MedicalAudience",
            "audienceType": "Profissionais de Saúde"
          },
          "about": {
            "@type": "MedicalCondition",
            "name": "Nutrição Infantil",
            "description": "Guia completo sobre fórmulas infantis e alimentação complementar"
          }
        })}
      </script>
    </HelmetWrapper>
  );
};