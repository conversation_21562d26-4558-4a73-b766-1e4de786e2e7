
import { Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";
import type { Session } from "@supabase/supabase-js";
import { useToast } from "@/hooks/use-toast";
import { motion } from "framer-motion";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { WelcomeHeader } from "./empty-state/WelcomeHeader";
import { RecentPrescriptions } from "./empty-state/RecentPrescriptions";
import { CreatePrescriptionDialog } from "./empty-state/CreatePrescriptionDialog";

interface EmptyStateProps {
  prescriptionsCount: number;
  session: Session | null;
  onSelectPrescription: (prescriptionId: string) => void;
}

export const EmptyState = ({
  prescriptionsCount,
  session,
  onSelectPrescription,
}: EmptyStateProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const { toast } = useToast();

  const { data: recentPrescriptions } = useQuery({
    queryKey: ["recent-prescriptions", session?.user?.id],
    queryFn: async () => {
      if (!session?.user?.id) return [];
      
      const { data, error } = await supabase
        .from("pedbook_prescriptions")
        .select(`
          id,
          name,
          created_at
        `)
        .eq("user_id", session.user.id)
        .order("created_at", { ascending: false })
        .limit(5);

      if (error) throw error;
      return data;
    },
    enabled: !!session?.user?.id,
  });

  const handleSuccess = () => {
    setIsOpen(false);
    toast({
      title: "Prescrição criada com sucesso!",
      description: "A prescrição foi adicionada à sua lista.",
    });

    setTimeout(() => {
      window.location.reload();
    }, 500);
  };

  if (prescriptionsCount === 0) {
    return (
      <>
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="w-full max-w-2xl mx-auto space-y-4 text-center px-4 pt-2"
        >
          <WelcomeHeader />
          
          <div className="flex flex-col sm:flex-row gap-3 justify-center pt-2">
            <Button
              size="lg"
              onClick={() => setIsOpen(true)}
              className="w-full sm:w-auto bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 transition-all duration-300 shadow-lg shadow-primary/20 hover:shadow-xl hover:shadow-primary/30 hover:-translate-y-0.5 dark:shadow-primary/10 dark:hover:shadow-primary/20"
            >
              <Plus className="h-5 w-5 mr-2" />
              Nova Prescrição
            </Button>
          </div>

          <RecentPrescriptions 
            prescriptions={recentPrescriptions || []}
            onSelectPrescription={onSelectPrescription}
          />
        </motion.div>

        <CreatePrescriptionDialog
          isOpen={isOpen}
          onOpenChange={setIsOpen}
          session={session}
          onSuccess={handleSuccess}
        />
      </>
    );
  }

  return (
    <>
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-2xl mx-auto space-y-4 text-center pt-2"
      >
        <h2 className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent dark:from-blue-400 dark:to-blue-600">
          Selecione uma Prescrição
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Escolha uma prescrição no menu lateral para visualizar ou editar seus detalhes.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            variant="outline"
            onClick={() => setIsOpen(true)}
            className="border-primary/20 hover:bg-primary/5 transition-all duration-300 dark:border-primary/30 dark:hover:bg-primary/10"
          >
            <Plus className="h-4 w-4 mr-2" />
            Nova Prescrição
          </Button>
        </div>
      </motion.div>

      <CreatePrescriptionDialog
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        session={session}
        onSuccess={handleSuccess}
      />
    </>
  );
};
