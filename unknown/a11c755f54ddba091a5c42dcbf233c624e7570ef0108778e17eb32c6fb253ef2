
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Clock } from "lucide-react";

interface AgeInputProps {
  value: number;
  onChange: (age: number) => void;
  onCommit: (age: number) => void;
}

export const AgeInput = ({ value, onChange, onCommit }: AgeInputProps) => {
  return (
    <div className="space-y-2">
      <Label htmlFor="age" className="text-lg font-medium text-red-700 dark:text-red-400 flex items-center gap-2">
        <Clock className="h-5 w-5" />
        Idade do Paciente
      </Label>
      <div className="flex items-center gap-2">
        <Input
          id="age"
          type="number"
          min={0}
          max={100}
          value={value || ""}
          onChange={(e) => {
            const newValue = parseFloat(e.target.value);
            if (!isNaN(newValue)) {
              const limitedValue = Math.min(Math.max(0, newValue), 100);
              onChange(limitedValue);
              onCommit(limitedValue);
            }
          }}
          className="flex-1 bg-white/50 dark:bg-slate-800/50 border-red-200 dark:border-red-900/30 focus:border-red-400 dark:focus:border-red-500 transition-colors text-center text-lg"
          placeholder="Digite a idade"
        />
        <span className="text-lg font-medium text-red-700 dark:text-red-400 min-w-[3rem]">anos</span>
      </div>
    </div>
  );
};
