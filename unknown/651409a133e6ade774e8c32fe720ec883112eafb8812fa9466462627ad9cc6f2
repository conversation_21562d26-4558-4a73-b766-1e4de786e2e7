
-- Update the execute_consolidation function to ensure name updating and proper deletion
CREATE OR REPLACE FUNCTION public.execute_consolidation(p_group_id uuid)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_main_focus_id UUID;
    v_theme_id UUID;
    v_similar_focus_ids UUID[];
    v_group_name TEXT;
BEGIN
    -- Log the start of execution
    RAISE NOTICE 'Starting consolidation for group: %', p_group_id;
    
    -- Obter informações do grupo
    SELECT 
        fcg.main_focus_id,
        fcg.theme_id,
        fcg.name,
        ARRAY_AGG(fcm.focus_id)
    INTO 
        v_main_focus_id,
        v_theme_id,
        v_group_name,
        v_similar_focus_ids
    FROM focus_consolidation_groups fcg
    JOIN focus_consolidation_mappings fcm ON fcg.id = fcm.group_id
    WHERE fcg.id = p_group_id
    GROUP BY fcg.main_focus_id, fcg.theme_id, fcg.name;
    
    -- Log the consolidation details
    RAISE NOTICE 'Consolidation details: Main focus ID: %, Theme ID: %, Similar focus IDs: %, Group name: %', 
                v_main_focus_id, v_theme_id, v_similar_focus_ids, v_group_name;
    
    -- 0. Update main focus name if provided in the group
    IF v_group_name IS NOT NULL AND v_group_name != '' THEN
        RAISE NOTICE 'Updating main focus name to: %', v_group_name;
        
        UPDATE study_categories
        SET name = v_group_name
        WHERE id = v_main_focus_id;
        
        RAISE NOTICE 'Updated main focus name to: %', v_group_name;
    END IF;

    -- 1. Migrar todas as questões para o foco principal
    UPDATE questions 
    SET focus_id = v_main_focus_id
    WHERE focus_id = ANY(v_similar_focus_ids);
    
    RAISE NOTICE 'Migrated questions to main focus';

    -- 2. Atualizar referências em outras tabelas
    UPDATE planner_study_items
    SET focus_id = v_main_focus_id
    WHERE focus_id = ANY(v_similar_focus_ids);
    
    RAISE NOTICE 'Updated planner_study_items references';

    UPDATE planner_events
    SET focus_id = v_main_focus_id
    WHERE focus_id = ANY(v_similar_focus_ids);
    
    RAISE NOTICE 'Updated planner_events references';

    UPDATE study_sessions
    SET focus_id = v_main_focus_id
    WHERE focus_id = ANY(v_similar_focus_ids);
    
    RAISE NOTICE 'Updated study_sessions references';

    UPDATE session_events
    SET focus_id = v_main_focus_id
    WHERE focus_id = ANY(v_similar_focus_ids);
    
    RAISE NOTICE 'Updated session_events references';

    -- 3. Atualizar métricas de performance
    WITH performance_updates AS (
        SELECT 
            user_id,
            theme_id,
            specialty_id,
            SUM(total_questions) as total_questions,
            SUM(correct_answers) as correct_answers,
            AVG(avg_response_time) as avg_response_time,
            MAX(last_study_date) as last_study_date
        FROM study_performance_metrics
        WHERE focus_id = ANY(v_similar_focus_ids || array[v_main_focus_id])
        GROUP BY user_id, theme_id, specialty_id
    )
    INSERT INTO study_performance_metrics (
        user_id,
        theme_id,
        specialty_id,
        focus_id,
        total_questions,
        correct_answers,
        avg_response_time,
        last_study_date
    )
    SELECT 
        pu.user_id,
        pu.theme_id,
        pu.specialty_id,
        v_main_focus_id,
        pu.total_questions,
        pu.correct_answers,
        pu.avg_response_time,
        pu.last_study_date
    FROM performance_updates pu
    ON CONFLICT (user_id, specialty_id, theme_id, focus_id)
    DO UPDATE SET
        total_questions = EXCLUDED.total_questions,
        correct_answers = EXCLUDED.correct_answers,
        avg_response_time = EXCLUDED.avg_response_time,
        last_study_date = GREATEST(study_performance_metrics.last_study_date, EXCLUDED.last_study_date),
        updated_at = NOW();
        
    RAISE NOTICE 'Updated performance metrics';

    -- 4. Deletar métricas antigas
    DELETE FROM study_performance_metrics
    WHERE focus_id = ANY(v_similar_focus_ids);
    
    RAISE NOTICE 'Deleted old performance metrics';

    -- 5. Limpar TODOS os mapeamentos de consolidação que referenciam os focos similares
    DELETE FROM focus_consolidation_mappings
    WHERE focus_id = ANY(v_similar_focus_ids);
    
    RAISE NOTICE 'Deleted mappings for similar focuses';

    -- 6. Limpar TODOS os mapeamentos de consolidação para grupos dos focos similares
    DELETE FROM focus_consolidation_mappings
    WHERE group_id IN (
        SELECT id 
        FROM focus_consolidation_groups 
        WHERE main_focus_id = ANY(v_similar_focus_ids)
    );
    
    RAISE NOTICE 'Deleted mappings for groups with similar focuses as main';

    -- 7. Limpar grupos antigos
    DELETE FROM focus_consolidation_groups
    WHERE main_focus_id = ANY(v_similar_focus_ids)
    AND id != p_group_id;
    
    RAISE NOTICE 'Deleted old consolidation groups';

    -- 8. Limpar mapeamentos do grupo atual
    DELETE FROM focus_consolidation_mappings
    WHERE group_id = p_group_id;
    
    RAISE NOTICE 'Deleted mappings for current group';

    -- 9. Agora sim podemos deletar os focos antigos com segurança
    DELETE FROM study_categories
    WHERE id = ANY(v_similar_focus_ids);
    
    RAISE NOTICE 'Deleted similar focuses: %', v_similar_focus_ids;

    -- 10. Atualizar status do grupo
    UPDATE focus_consolidation_groups
    SET status = 'approved', updated_at = NOW()
    WHERE id = p_group_id;
    
    RAISE NOTICE 'Updated group status to approved';

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error in execute_consolidation: %', SQLERRM;
        -- Em caso de erro, tentar reverter o status para pending
        UPDATE focus_consolidation_groups
        SET 
            status = 'pending',
            updated_at = NOW()
        WHERE id = p_group_id;
        
        RAISE;
END;
$function$;
