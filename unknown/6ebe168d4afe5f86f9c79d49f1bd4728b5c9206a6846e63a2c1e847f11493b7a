import { motion } from "framer-motion";
import { Star, Clock, User, ThumbsUp } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";

interface FeaturedPostCardProps {
  post: {
    id: string;
    title: string;
    excerpt: string | null;
    featured_image: string | null;
    published_at: string;
    likes_count?: number;
    author: {
      full_name: string;
    };
    pedbook_blog_categories: {
      name: string;
    } | null;
  };
  delay?: number;
}

export function FeaturedPostCard({ post, delay = 0 }: FeaturedPostCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      className="group h-full"
    >
      <Card className="overflow-hidden h-full bg-gradient-to-br from-white via-primary/5 to-white backdrop-blur-sm border border-white/20 hover:shadow-lg transition-all duration-300">
        <div className="relative">
          {post.featured_image ? (
            <div className="relative h-48 overflow-hidden">
              <img
                src={post.featured_image}
                alt={post.title}
                className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent" />
            </div>
          ) : (
            <div className="h-48 bg-gradient-to-br from-primary/10 via-primary/5 to-transparent" />
          )}
          <div className="absolute top-4 right-4">
            <Star className="w-6 h-6 text-yellow-400 drop-shadow-lg" />
          </div>
          {typeof post.likes_count === 'number' && (
            <div className="absolute bottom-4 right-4 flex items-center gap-1 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1">
              <ThumbsUp className="w-4 h-4 text-primary" />
              <span className="text-sm font-medium text-gray-700">{post.likes_count}</span>
            </div>
          )}
        </div>

        <div className="p-6 space-y-4">
          {post.pedbook_blog_categories && (
            <Badge variant="secondary" className="bg-primary/10 text-primary">
              {post.pedbook_blog_categories.name}
            </Badge>
          )}

          <h3 className="text-xl font-semibold line-clamp-2 group-hover:text-primary transition-colors">
            {post.title}
          </h3>

          <p className="text-gray-600 line-clamp-2">
            {post.excerpt || post.title}
          </p>

          <div className="flex flex-col space-y-2 text-sm text-gray-500">
            <div className="flex items-center gap-2">
              <User className="w-4 h-4" />
              <span>{post.author.full_name}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              <time>
                {format(new Date(post.published_at), "dd 'de' MMMM 'de' yyyy", {
                  locale: ptBR,
                })}
              </time>
            </div>
          </div>
        </div>
      </Card>
    </motion.div>
  );
}