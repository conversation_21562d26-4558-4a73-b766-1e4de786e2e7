
import React, { useState } from 'react';
import { cn } from "@/lib/utils";
import type { Question } from "@/types/question";
import { ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface QuestionMetadataProps {
  question: Question;
  className?: string;
}

export const QuestionMetadata: React.FC<QuestionMetadataProps> = ({ 
  question, 
  className 
}) => {
  const [showDetails, setShowDetails] = useState(false);
  
  // Função auxiliar para formatar o tipo de questão para exibição
  const formatQuestionType = (type: string | undefined) => {
    if (!type) return '';
    
    // Mapeia os tipos de questão para formatos mais legíveis
    const typeMap: Record<string, string> = {
      'teorica-1': 'Teórica I',
      'teorica-2': 'Teórica II',
      'teorico-pratica': 'Teórico-Prática'
    };
    
    return typeMap[type] || type;
  }

  const toggleDetails = () => {
    setShowDetails(prev => !prev);
  };

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex justify-between items-center">
        <div className="flex flex-wrap items-center gap-2">
          {/* Sempre mostrar número da questão e tipo */}
          {question.question_number && (
            <div className="flex items-center gap-1">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
                #{question.question_number}
              </span>
              
              {question.question_type && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-100">
                  {formatQuestionType(question.question_type)}
                </span>
              )}
            </div>
          )}
        </div>
        
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={toggleDetails} 
          className="text-xs px-2 h-7"
        >
          {showDetails ? (
            <span className="flex items-center gap-1">
              Ocultar detalhes <ChevronUp size={14} />
            </span>
          ) : (
            <span className="flex items-center gap-1">
              Mostrar detalhes <ChevronDown size={14} />
            </span>
          )}
        </Button>
      </div>
      
      {showDetails && (
        <div className="flex flex-wrap gap-2 text-sm text-muted-foreground">
          {/* Especialidade */}
          {question.specialty && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {question.specialty.name}
            </span>
          )}
          
          {/* Tema */}
          {question.theme && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              {question.theme.name}
            </span>
          )}
          
          {/* Foco */}
          {question.focus && question.focus.name !== question.theme?.name && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              {question.focus.name}
            </span>
          )}
          
          {/* Origem/Local */}
          {question.location && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
              {question.location.name}
            </span>
          )}
          
          {/* Ano */}
          {question.year && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              {question.year}
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default QuestionMetadata;
