
import React from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { FlowchartSEO } from "@/components/seo/FlowchartSEO";
import { FLOWCHART_SEO_DATA } from "@/data/flowchartSEOData";
import { Scroll<PERSON><PERSON> } from "@/components/ui/scroll-area";
import { ScorpionQuestion } from "@/components/flowcharts/scorpion/ScorpionQuestion";
import { ScorpionResult } from "@/components/flowcharts/scorpion/ScorpionResult";
import { ScorpionSpecialConsiderations } from "@/components/flowcharts/scorpion/ScorpionSpecialConsiderations";
import { ScorpionReferences } from "@/components/flowcharts/scorpion/ScorpionReferences";
import { useScorpionFlow } from "@/components/flowcharts/scorpion/useScorpionFlow";
import { ScorpionHeader } from "@/components/flowcharts/scorpion/ScorpionHeader";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

const ScorpionFlowchart = () => {
  const {
    currentStep,
    answers,
    handleAnswer,
    handleContinue,
    resetFlow,
    getCurrentQuestion,
    getCurrentResult,
  } = useScorpionFlow();

  const seoData = FLOWCHART_SEO_DATA['scorpion'];

  const renderContent = () => {
    const question = getCurrentQuestion();
    const result = getCurrentResult();

    if (question) {
      if (currentStep === "severity") {
        return (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-center mb-6 text-gray-800 dark:text-gray-100">
              {question}
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <Dialog>
                <DialogTrigger asChild>
                  <Button
                    onClick={() => handleAnswer("mild")}
                    variant="outline"
                    className="p-6 h-auto flex flex-col gap-2 hover:bg-green-50 dark:hover:bg-green-900/40 border-green-200 dark:border-green-700/50"
                  >
                    <span className="font-semibold">Leve</span>
                    <span className="text-sm text-gray-600 dark:text-gray-300">
                      Manifestações apenas locais
                    </span>
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl bg-white dark:bg-slate-900">
                  <DialogHeader>
                    <DialogTitle className="dark:text-white">Quadro Leve - Manifestações Detalhadas</DialogTitle>
                    <DialogDescription className="space-y-4 pt-4">
                      <div>
                        <h4 className="font-semibold text-green-700 dark:text-green-400 mb-2">Manifestações Locais</h4>
                        <ul className="list-disc pl-5 space-y-1 text-gray-600 dark:text-gray-300">
                          <li>Dor local intensa</li>
                          <li>Edema discreto ou ausente</li>
                          <li>Parestesia local</li>
                          <li>Eritema</li>
                          <li>Sudorese local</li>
                        </ul>
                      </div>
                      <div>
                        <h4 className="font-semibold text-green-700 dark:text-green-400 mb-2">Manifestações Sistêmicas</h4>
                        <ul className="list-disc pl-5 space-y-1 text-gray-600 dark:text-gray-300">
                          <li>Ocasionalmente náuseas</li>
                          <li>Agitação discreta</li>
                          <li>Taquicardia leve</li>
                          <li>Vômitos ocasionais</li>
                        </ul>
                      </div>
                    </DialogDescription>
                  </DialogHeader>
                </DialogContent>
              </Dialog>

              <Dialog>
                <DialogTrigger asChild>
                  <Button
                    onClick={() => handleAnswer("moderate")}
                    variant="outline"
                    className="p-6 h-auto flex flex-col gap-2 hover:bg-orange-50 dark:hover:bg-orange-900/40 border-orange-200 dark:border-orange-700/50"
                  >
                    <span className="font-semibold">Moderado</span>
                    <span className="text-sm text-gray-600 dark:text-gray-300">
                      Manifestações sistêmicas leves
                    </span>
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl bg-white dark:bg-slate-900">
                  <DialogHeader>
                    <DialogTitle className="dark:text-white">Quadro Moderado - Manifestações Detalhadas</DialogTitle>
                    <DialogDescription className="space-y-4 pt-4">
                      <div>
                        <h4 className="font-semibold text-orange-700 dark:text-orange-400 mb-2">Manifestações Locais</h4>
                        <ul className="list-disc pl-5 space-y-1 text-gray-600 dark:text-gray-300">
                          <li>Dor local intensa</li>
                          <li>Edema moderado</li>
                          <li>Sudorese local intensa</li>
                        </ul>
                      </div>
                      <div>
                        <h4 className="font-semibold text-orange-700 dark:text-orange-400 mb-2">Manifestações Sistêmicas</h4>
                        <ul className="list-disc pl-5 space-y-1 text-gray-600 dark:text-gray-300">
                          <li>Sudorese profusa</li>
                          <li>Náuseas frequentes</li>
                          <li>Vômitos ocasionais</li>
                          <li>Taquicardia</li>
                          <li>Hipertensão ou hipotensão leve</li>
                          <li>Agitação</li>
                          <li>Taquipneia</li>
                        </ul>
                      </div>
                    </DialogDescription>
                  </DialogHeader>
                </DialogContent>
              </Dialog>

              <Dialog>
                <DialogTrigger asChild>
                  <Button
                    onClick={() => handleAnswer("severe")}
                    variant="outline"
                    className="p-6 h-auto flex flex-col gap-2 hover:bg-red-50 dark:hover:bg-red-900/40 border-red-200 dark:border-red-700/50"
                  >
                    <span className="font-semibold">Grave</span>
                    <span className="text-sm text-gray-600 dark:text-gray-300">
                      Manifestações sistêmicas graves
                    </span>
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl bg-white dark:bg-slate-900">
                  <DialogHeader>
                    <DialogTitle className="dark:text-white">Quadro Grave - Manifestações Detalhadas</DialogTitle>
                    <DialogDescription className="space-y-4 pt-4">
                      <div>
                        <h4 className="font-semibold text-red-700 dark:text-red-400 mb-2">Manifestações Sistêmicas Graves</h4>
                        <ul className="list-disc pl-5 space-y-1 text-gray-600 dark:text-gray-300">
                          <li>Sudorese profusa</li>
                          <li>Vômitos incoercíveis</li>
                          <li>Salivação excessiva</li>
                          <li>Alternância de agitação e sonolência</li>
                          <li>Priapismo</li>
                          <li>Arritmias cardíacas</li>
                          <li>Taquidispneia grave</li>
                          <li>Insuficiência cardíaca</li>
                          <li>Edema pulmonar agudo</li>
                          <li>Choque</li>
                          <li>Convulsões</li>
                        </ul>
                      </div>
                    </DialogDescription>
                  </DialogHeader>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        );
      }

      return (
        <ScorpionQuestion
          question={question}
          onAnswer={handleAnswer}
          selectedAnswer={answers[currentStep] as boolean}
          onReset={resetFlow}
        />
      );
    }

    if (result) {
      return (
        <ScorpionResult
          {...result}
          onReset={resetFlow}
          nextQuestion={currentStep === "observation" ? "initial" : undefined}
          onContinue={handleContinue}
        />
      );
    }

    return null;
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-yellow-50 via-white to-yellow-50 dark:from-yellow-900/20 dark:via-slate-900 dark:to-yellow-900/10">
      <FlowchartSEO {...seoData} />

      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto space-y-8">
          <ScorpionHeader />

          <ScrollArea className="h-[calc(100vh-300px)] pr-4">
            <div className="space-y-6">
              {renderContent()}
              <ScorpionSpecialConsiderations />
              <ScorpionReferences />
            </div>
          </ScrollArea>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default ScorpionFlowchart;
