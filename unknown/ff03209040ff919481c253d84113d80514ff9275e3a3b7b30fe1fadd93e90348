
import { Card } from "@/components/ui/card";
import type { Toxidrome } from "@/data/toxidromes";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

interface ToxidromeDetailsProps {
  toxidrome: Toxidrome;
}

export function ToxidromeDetails({ toxidrome }: ToxidromeDetailsProps) {
  // Extrair nome da cor para bordas consistentes
  const getColorClass = (baseColor: string) => {
    const colorMap = {
      "blue": {
        bg: "bg-blue-50 dark:bg-blue-900/30",
        text: "text-blue-900 dark:text-blue-300",
        content: "text-blue-800 dark:text-blue-200",
        border: "border-blue-200 dark:border-blue-800"
      },
      "purple": {
        bg: "bg-purple-50 dark:bg-purple-900/30",
        text: "text-purple-900 dark:text-purple-300",
        content: "text-purple-800 dark:text-purple-200",
        border: "border-purple-200 dark:border-purple-800"
      },
      "emerald": {
        bg: "bg-emerald-50 dark:bg-emerald-900/30",
        text: "text-emerald-900 dark:text-emerald-300",
        content: "text-emerald-800 dark:text-emerald-200",
        border: "border-emerald-200 dark:border-emerald-800"
      },
      "amber": {
        bg: "bg-amber-50 dark:bg-amber-900/30",
        text: "text-amber-900 dark:text-amber-300",
        content: "text-amber-800 dark:text-amber-200",
        border: "border-amber-200 dark:border-amber-800"
      },
      "red": {
        bg: "bg-red-50 dark:bg-red-900/30",
        text: "text-red-900 dark:text-red-300",
        content: "text-red-800 dark:text-red-200",
        border: "border-red-200 dark:border-red-800"
      },
      "gray": {
        bg: "bg-gray-50 dark:bg-gray-800",
        text: "text-gray-900 dark:text-gray-100",
        content: "text-gray-700 dark:text-gray-300",
        border: "border-gray-200 dark:border-gray-700"
      }
    };
    
    return colorMap[baseColor as keyof typeof colorMap] || colorMap.gray;
  };
  
  const blueColors = getColorClass("blue");
  const purpleColors = getColorClass("purple");
  const emeraldColors = getColorClass("emerald");
  const amberColors = getColorClass("amber");
  const redColors = getColorClass("red");
  const grayColors = getColorClass("gray");
  
  return (
    <div className="grid gap-6">
      <div className={`${blueColors.bg} border ${blueColors.border} rounded-lg p-6 animate-fade-in`}>
        <h3 className={`font-semibold text-xl mb-3 ${blueColors.text}`}>Antídoto</h3>
        <p className={blueColors.content}>{toxidrome.antidote}</p>
      </div>

      <div className={`${purpleColors.bg} border ${purpleColors.border} rounded-lg p-6 animate-fade-in delay-100`}>
        <h3 className={`font-semibold text-xl mb-3 ${purpleColors.text}`}>Uso</h3>
        <p className={purpleColors.content}>{toxidrome.usage}</p>
      </div>

      {toxidrome.examples && toxidrome.examples.length > 0 && (
        <div className={`${emeraldColors.bg} border ${emeraldColors.border} rounded-lg p-6 animate-fade-in delay-150`}>
          <h3 className={`font-semibold text-xl mb-3 ${emeraldColors.text}`}>Exemplos</h3>
          <ul className={`list-disc list-inside ${emeraldColors.content}`}>
            {toxidrome.examples.map((example, index) => (
              <li key={index}>{example}</li>
            ))}
          </ul>
        </div>
      )}

      {toxidrome.management && toxidrome.management.length > 0 && (
        <div className={`${amberColors.bg} border ${amberColors.border} rounded-lg p-6 animate-fade-in delay-200`}>
          <h3 className={`font-semibold text-xl mb-3 ${amberColors.text}`}>Manejo</h3>
          <ul className={`list-disc list-inside ${amberColors.content}`}>
            {toxidrome.management.map((step, index) => (
              <li key={index}>{step}</li>
            ))}
          </ul>
        </div>
      )}

      <div className={`${redColors.bg} border ${redColors.border} rounded-lg p-6 animate-fade-in delay-300`}>
        <h3 className={`font-semibold text-xl mb-3 ${redColors.text}`}>Cuidados</h3>
        <ul className="list-disc pl-5 space-y-2">
          {toxidrome.precautions.map((precaution, index) => (
            <li key={index} className={redColors.content}>
              {precaution}
            </li>
          ))}
        </ul>
      </div>

      {toxidrome.reference && (
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="reference" className="border-none">
            <AccordionTrigger className={`${grayColors.bg} border ${grayColors.border} rounded-t-lg px-6 py-4 hover:no-underline hover:bg-gray-100 dark:hover:bg-gray-700`}>
              <span className={`font-semibold ${grayColors.text}`}>Referência Bibliográfica:</span>
            </AccordionTrigger>
            <AccordionContent className={`${grayColors.bg} border-x border-b ${grayColors.border} rounded-b-lg px-6 pb-4`}>
              <div className="pt-2">
                <h4 className={`font-medium ${grayColors.text} mb-2`}> </h4>
                <p className={`${grayColors.content} text-sm italic`}>{toxidrome.reference}</p>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      )}
    </div>
  );
}
