
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { PediatricExameData } from "./AnamneseForm";
import { Stethoscope, Info, ChevronDown, AlertCircle, Zap } from "lucide-react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Switch } from "@/components/ui/switch";

interface PediatricExameBlocksProps {
  data: PediatricExameData;
  onChange: (data: Partial<PediatricExameData>) => void;
}

export const PediatricExameBlocks: React.FC<PediatricExameBlocksProps> = ({ data, onChange }) => {
  const [quickMode, setQuickMode] = useState(false);
  const [expandedBlocks, setExpandedBlocks] = useState<string[]>([]);
  
  const handleChange = (field: keyof PediatricExameData, value: string) => {
    onChange({ [field]: value } as Partial<PediatricExameData>);
  };

  const toggleBlockExpansion = (blockId: string) => {
    setExpandedBlocks(prev => 
      prev.includes(blockId) 
        ? prev.filter(id => id !== blockId) 
        : [...prev, blockId]
    );
  };

  const getPlaceholderText = (blockId: string) => {
    if (quickMode) {
      return "Apenas descreva alterações relevantes.";
    }
    
    switch (blockId) {
      case "weight":
        return "Peso (g)/ganho por dia (g/dia) e escore Z";
      case "height":
        return "Estatura (cm) e escore Z";
      case "headCircumference":
        return "Perímetro cefálico (cm) e escore Z";
      case "anteriorFontanelle":
        return "Fontanela anterior (cm x cm) - avaliar tensão, forma losangular";
      case "posteriorFontanelle":
        return "Fontanela posterior (cm x cm) - avaliar tensão, fechamento";
      case "sutures":
        return "Suturas (quais estão abertas e se há particularidades)";
      case "heartRate":
        return "Frequência cardíaca (bpm) - contar em 1 minuto";
      case "respiratoryRate":
        return "Frequência respiratória (irpm) - contar em 1 minuto";
      case "temperature":
        return "Temperatura (°C)";
      case "headNeck":
        return "Cabeça e pescoço - descreva achados";
      case "redReflex":
        return "Reflexo vermelho - presente bilateralmente?";
      case "oropharynx":
        return "Orofaringe - descreva achados";
      case "otoscopy":
        return "Otoscopia - descreva achados";
      case "cardiovascular":
        return "Aparelho cardiovascular - descreva achados";
      case "respiratory":
        return "Aparelho respiratório - descreva achados";
      case "abdomen":
        return "Abdome - descreva achados";
      case "peripheralPulses":
        return "Pulsos periféricos - descreva achados";
      case "genitalia":
        return "Genitália - descreva achados";
      case "bcgScar":
        return "Cicatriz de BCG - descreva";
      case "ortolaniManeuver":
        return "Manobra de Ortolani - positiva ou negativa";
      case "reflexWalking":
        return "Marcha reflexa - presente ou ausente";
      case "moro":
        return "Reflexo de Moro - simétrico? Completo ou incompleto?";
      case "asimetricTonicNeck":
        return "Reflexo tônico cervical assimétrico - presente ou ausente";
      case "followsObjects":
        return "Segue objeto com o olhar - sim ou não";
      case "socialSmile":
        return "Sorriso social - presente ou ausente";
      case "screeningTests":
        return "Resultados dos testes do pezinho, orelhinha, linguinha e coraçãozinho";
      default:
        return "Descreva os achados...";
    }
  };

  const blocks = [
    {
      id: "weight",
      title: "Peso",
      placeholder: getPlaceholderText("weight"),
      value: data.weight,
    },
    {
      id: "height",
      title: "Estatura",
      placeholder: getPlaceholderText("height"),
      value: data.height,
    },
    {
      id: "headCircumference",
      title: "Perímetro Cefálico",
      placeholder: getPlaceholderText("headCircumference"),
      value: data.headCircumference,
    },
    {
      id: "anteriorFontanelle",
      title: "Fontanela Anterior",
      placeholder: getPlaceholderText("anteriorFontanelle"),
      value: data.anteriorFontanelle,
    },
    {
      id: "posteriorFontanelle",
      title: "Fontanela Posterior",
      placeholder: getPlaceholderText("posteriorFontanelle"),
      value: data.posteriorFontanelle,
    },
    {
      id: "sutures",
      title: "Suturas",
      placeholder: getPlaceholderText("sutures"),
      value: data.sutures,
    },
    {
      id: "heartRate",
      title: "Frequência Cardíaca",
      placeholder: getPlaceholderText("heartRate"),
      value: data.heartRate,
    },
    {
      id: "respiratoryRate",
      title: "Frequência Respiratória",
      placeholder: getPlaceholderText("respiratoryRate"),
      value: data.respiratoryRate,
    },
    {
      id: "temperature",
      title: "Temperatura",
      placeholder: getPlaceholderText("temperature"),
      value: data.temperature,
    },
    {
      id: "headNeck",
      title: "Cabeça e Pescoço",
      placeholder: getPlaceholderText("headNeck"),
      value: data.headNeck,
    },
    {
      id: "redReflex",
      title: "Reflexo Vermelho",
      placeholder: getPlaceholderText("redReflex"),
      value: data.redReflex,
    },
    {
      id: "oropharynx",
      title: "Orofaringe",
      placeholder: getPlaceholderText("oropharynx"),
      value: data.oropharynx,
    },
    {
      id: "otoscopy",
      title: "Otoscopia",
      placeholder: getPlaceholderText("otoscopy"),
      value: data.otoscopy,
    },
    {
      id: "cardiovascular",
      title: "Aparelho Cardiovascular",
      placeholder: getPlaceholderText("cardiovascular"),
      value: data.cardiovascular,
    },
    {
      id: "respiratory",
      title: "Aparelho Respiratório",
      placeholder: getPlaceholderText("respiratory"),
      value: data.respiratory,
    },
    {
      id: "abdomen",
      title: "Abdome",
      placeholder: getPlaceholderText("abdomen"),
      value: data.abdomen,
    },
    {
      id: "peripheralPulses",
      title: "Pulsos Periféricos",
      placeholder: getPlaceholderText("peripheralPulses"),
      value: data.peripheralPulses,
    },
    {
      id: "genitalia",
      title: "Genitália",
      placeholder: getPlaceholderText("genitalia"),
      value: data.genitalia,
    },
    {
      id: "bcgScar",
      title: "Cicatriz de BCG",
      placeholder: getPlaceholderText("bcgScar"),
      value: data.bcgScar,
    },
    {
      id: "ortolaniManeuver",
      title: "Manobra de Ortolani",
      placeholder: getPlaceholderText("ortolaniManeuver"),
      value: data.ortolaniManeuver,
    },
    {
      id: "reflexWalking",
      title: "Marcha Reflexa",
      placeholder: getPlaceholderText("reflexWalking"),
      value: data.reflexWalking,
    },
    {
      id: "moro",
      title: "Reflexo de Moro",
      placeholder: getPlaceholderText("moro"),
      value: data.moro,
    },
    {
      id: "asimetricTonicNeck",
      title: "Reflexo Tônico Cervical Assimétrico",
      placeholder: getPlaceholderText("asimetricTonicNeck"),
      value: data.asimetricTonicNeck,
    },
    {
      id: "followsObjects",
      title: "Segue Objetos com o Olhar",
      placeholder: getPlaceholderText("followsObjects"),
      value: data.followsObjects,
    },
    {
      id: "socialSmile",
      title: "Sorriso Social",
      placeholder: getPlaceholderText("socialSmile"),
      value: data.socialSmile,
    },
    {
      id: "screeningTests",
      title: "Testes de Triagem",
      placeholder: getPlaceholderText("screeningTests"),
      value: data.screeningTests,
    },
  ];

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Stethoscope className="h-5 w-5 text-primary" />
            Exame Físico - Puericultura 1-2 meses
          </CardTitle>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">Apenas alterações</span>
            <Switch 
              checked={quickMode} 
              onCheckedChange={setQuickMode} 
              className="data-[state=checked]:bg-primary"
            />
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-4 w-4 text-gray-400 cursor-help" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="w-[220px] text-xs">
                    No modo "Apenas alterações", você só precisa descrever o que está alterado. 
                    Os campos vazios serão preenchidos com valores normais para a idade e gênero.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {quickMode ? (
          <div className="p-4 bg-amber-50 border border-amber-200 rounded-md flex gap-3 items-start">
            <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5" />
            <div>
              <h3 className="font-medium text-amber-800">Modo rápido ativado</h3>
              <p className="text-sm text-amber-700">
                Preencha apenas os achados alterados. Os campos vazios serão automaticamente 
                preenchidos com valores normais para a idade e gênero do paciente.
              </p>
            </div>
          </div>
        ) : null}

        {blocks.map((block) => (
          <Collapsible 
            key={block.id} 
            className="border rounded-md"
            open={expandedBlocks.includes(block.id)}
            onOpenChange={() => toggleBlockExpansion(block.id)}
          >
            <div className="flex items-center justify-between p-4">
              <Label htmlFor={block.id} className="font-medium">
                {block.title}
                {block.value && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary/10 text-primary">
                    <Zap className="h-3 w-3 mr-1" />
                    Preenchido
                  </span>
                )}
              </Label>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm">
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </CollapsibleTrigger>
            </div>
            <CollapsibleContent className="px-4 pb-4">
              <Textarea
                id={block.id}
                value={block.value}
                onChange={(e) => handleChange(block.id as keyof PediatricExameData, e.target.value)}
                placeholder={block.placeholder}
                className="min-h-[60px]"
              />
            </CollapsibleContent>
          </Collapsible>
        ))}
      </CardContent>
    </Card>
  );
};
