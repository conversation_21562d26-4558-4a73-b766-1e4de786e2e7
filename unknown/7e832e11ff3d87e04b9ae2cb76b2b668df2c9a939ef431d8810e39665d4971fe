
import React from "react";
import { ExternalLink, Alert<PERSON>riangle, AlertCircle, X, Check, Info } from "lucide-react";
import { cn } from "@/lib/utils";

interface InteractionProps {
  drugPair?: string;
  severity?: "Contraindicado" | "Grave" | "Moderado" | "Leve" | "Sem interação relevante";
  mechanism?: string;
  recommendation?: string;
  reference?: string;
  content?: string;
}

interface InteractionDisplayProps {
  interaction: InteractionProps;
  isLast?: boolean;
}

export const InteractionDisplay: React.FC<InteractionDisplayProps> = ({ interaction, isLast = false }) => {
  // Se não houver par de medicamentos, simplesmente mostra o conteúdo
  if (!interaction.drugPair && interaction.content) {
    return (
      <div className="text-gray-600 dark:text-gray-400">
        {interaction.content}
      </div>
    );
  }

  // Função para limpar o texto da recomendação, removendo o resumo se existir
  const cleanRecommendation = (text?: string): string => {
    if (!text) return '';

    // Verifica se existe o padrão "### Resumo" e separa apenas a parte da conduta
    const resumePattern = /#{1,3}\s*Resumo/i;
    if (resumePattern.test(text)) {
      const parts = text.split(resumePattern);
      return parts[0].trim();
    }

    return text;
  };

  // Função para determinar o ícone baseado na severidade da interação
  const getSeverityIcon = () => {
    switch (interaction.severity) {
      case "Contraindicado":
        return <X className="h-5 w-5 text-red-600" />;
      case "Grave":
        return <AlertTriangle className="h-5 w-5 text-purple-600" />;
      case "Moderado":
        return <AlertCircle className="h-5 w-5 text-yellow-600" />;
      case "Leve":
      case "Sem interação relevante":
        return <Check className="h-5 w-5 text-green-600" />;
      default:
        return <Info className="h-5 w-5 text-blue-600" />;
    }
  };

  // Limpar o texto da recomendação
  const cleanedRecommendation = cleanRecommendation(interaction.recommendation);



  // Determinar a classe de cor baseada na severidade
  const getSeverityColorClass = () => {
    switch (interaction.severity) {
      case "Contraindicado":
        return "border-red-500 bg-red-50 dark:bg-red-950/30";
      case "Grave":
        return "border-purple-500 bg-purple-50 dark:bg-purple-950/30";
      case "Moderado":
        return "border-yellow-500 bg-yellow-50 dark:bg-yellow-950/30";
      case "Leve":
      case "Sem interação relevante":
        return "border-green-500 bg-green-50 dark:bg-green-950/30";
      default:
        return "border-blue-500 bg-blue-50 dark:bg-blue-950/30";
    }
  };

  // Determinar a classe para o badge de severidade
  const getSeverityBadgeClass = () => {
    switch (interaction.severity) {
      case "Contraindicado":
        return "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300";
      case "Grave":
        return "bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300";
      case "Moderado":
        return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300";
      case "Leve":
      case "Sem interação relevante":
        return "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300";
      default:
        return "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300";
    }
  };

  return (
    <div
      className={cn(
        "p-4 rounded-md",
        !isLast && "mb-4",
        getSeverityColorClass(),
        "hover:shadow-md transition-all border-l-4"
      )}
    >
      {/* Layout otimizado para maximizar o espaço em telas pequenas */}
      <div className="space-y-3">
        {/* Cabeçalho da interação - simplificado para mobile */}
        <div className="bg-white/80 dark:bg-gray-800/40 p-3 rounded-md border border-gray-200 dark:border-gray-700">
          {/* Ícone em mobile aparece junto ao status para economizar espaço */}
          <div className="flex items-center justify-between mb-2 sm:mb-0">
            <span className="text-sm font-semibold text-gray-600 dark:text-gray-400">Interação:</span>
            <div className={cn(
              "text-xs font-normal px-2 py-0.5 rounded-full flex items-center gap-1",
              getSeverityBadgeClass()
            )}>
              {/* Em mobile, mostramos o ícone ao lado do texto da severidade */}
              <span className="md:hidden">{getSeverityIcon()}</span>
              <span>{interaction.severity || "Desconhecido"}</span>
            </div>
          </div>

          {/* Nome dos medicamentos em destaque e sem divisão em uma linha separada */}
          <div className="font-semibold text-gray-800 dark:text-gray-200 break-words text-base">
            {interaction.drugPair}
          </div>
        </div>

        {/* Mecanismo */}
        <div>
          <h5 className="font-semibold text-gray-700 dark:text-gray-300 mb-2 text-sm">
            Mecanismo farmacológico
          </h5>
          <p className="text-gray-600 dark:text-gray-400 text-sm sm:text-base leading-relaxed">{interaction.mechanism}</p>
        </div>

        {/* Conduta clínica recomendada */}
        <div className="bg-white dark:bg-gray-800/50 p-3 rounded-md border border-gray-100 dark:border-gray-800">
          <h5 className="font-semibold text-gray-700 dark:text-gray-300 mb-2 text-sm">
            Conduta clínica recomendada
          </h5>
          <p className="text-gray-600 dark:text-gray-400 text-sm sm:text-base leading-relaxed">
            {cleanedRecommendation}
          </p>
        </div>
      </div>
    </div>
  );
};
