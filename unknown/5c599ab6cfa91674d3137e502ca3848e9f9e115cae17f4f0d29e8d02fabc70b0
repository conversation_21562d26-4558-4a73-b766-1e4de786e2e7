
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface AIResponse {
  alternativas: {
    texto: string;
    comentario: string;
    correta: boolean;
  }[];
  comentario_final: string;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openAIApiKey) {
      console.error('[Edge Function] Missing OPENAI_API_KEY');
      throw new Error('OpenAI API key not configured');
    }

    const { statement, alternatives, correctAnswer } = await req.json();
    console.log('[Edge Function] Received request:', { 
      statementPreview: statement.substring(0, 100) + '...', 
      alternativesCount: alternatives.length,
      correctAnswer 
    });

    const prompt = `Você é um professor experiente de medicina que está comentando uma questão de residência médica. Seu objetivo é ensinar o raciocínio médico com clareza, segurança e empatia, como um especialista humano faria. Ao comentar cada alternativa, adote um tom didático, humanizado e objetivo — como se estivesse explicando para seus alunos em uma aula. Use expressões naturais como "Segura essa dica que vale ouro", "Tem uma armadilha escondida aqui", "Presta atenção nesse detalhe", "Se vacilar aqui, dança", "Essa é a típica casca de banana", "Essa questão quer te pegar pelo automatismo", "Agora vem o truque que muita gente esquece", "Não cai nessa pegadinha, é cilada", "Essa banca adora cobrar isso", "Chegou a hora de mostrar que você sabe o caminho das pedras!"

Analise a seguinte questão e forneça explicações didáticas:

Enunciado: ${statement}

Alternativas:
${alternatives.map((alt: string, index: number) => `${index + 1}. ${alt}`).join('\n')}

Alternativa correta: ${correctAnswer + 1}

Formate a resposta como um objeto JSON com a seguinte estrutura:
{
  "alternativas": [
    {
      "texto": "texto da alternativa",
      "comentario": "explicação detalhada sobre esta alternativa",
      "correta": boolean
    }
  ],
  "comentario_final": "explicação geral sobre o tema da questão"
}

DIRETRIZES IMPORTANTES:
1. Seja DIRETO e TÉCNICO, como um professor de medicina explicando a residentes.
2. Use linguagem técnica médica apropriada.
3. Estruture suas explicações em PARÁGRAFOS CURTOS com ESPAÇAMENTO entre eles - não faça textos longos sem quebras.
4. Para cada alternativa:
   - Explique claramente por que está correta ou incorreta
   - Na alternativa correta, mostre a linha de raciocínio que leva à resposta — ajude o aluno a enxergar o caminho.
   - Destaque por que uma alternativa parece certa, mas está errada (pegadinha).
   - Cite evidências científicas relevantes
   - Mencione princípios fisiopatológicos quando pertinente
   - Use exemplos clínicos ilustrativos quando apropriado
5. Organize o texto com estrutura clara:
   - Não copie ou repita o enunciado.
   - Contexto/princípio inicial
   - Explicação técnica
   - Conclusão/aplicação clínica
6. RESPONDA EM PORTUGUÊS usando terminologia médica adequada
7. MANTENHA EXPLICAÇÕES CONCISAS mas informativas
8. RETORNE APENAS O JSON, SEM MARKDOWN
9. ESTRUTURE O TEXTO com parágrafos separados e quebras de linha adequadas`;

    console.log('[Edge Function] Sending prompt to OpenAI with length:', prompt.length);
    
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: 'Você é um professor médico experiente explicando questões a residentes.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.5,
        max_tokens: 2048,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('[Edge Function] OpenAI API error:', errorData);
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const completion = await response.json();
    const content = completion.choices[0]?.message?.content;
    
    if (!content) {
      console.error('[Edge Function] Empty response from OpenAI');
      throw new Error('Empty response from OpenAI');
    }

    console.log('[Edge Function] Raw response:', content);

    // Remove any potential formatting characters that might break JSON parsing
    const cleanContent = content
      .replace(/```json\n?/g, '')  // Remove ```json
      .replace(/```\n?/g, '')      // Remove closing ```
      .replace(/\\n/g, '\n')       // Preserve line breaks in the content
      .replace(/\\r/g, '')         // Replace carriage returns with spaces
      .replace(/\\t/g, ' ')        // Replace tabs with spaces
      .trim();                     // Remove extra whitespace
    
    let parsedResponse;
    try {
      parsedResponse = JSON.parse(cleanContent);
      console.log('[Edge Function] Successfully parsed JSON response');
    } catch (e) {
      console.error('[Edge Function] Error parsing OpenAI response:', e);
      console.log('[Edge Function] Content that failed to parse:', cleanContent);
      throw new Error('Invalid JSON response from OpenAI');
    }

    // Normalize response structure
    const normalizedResponse: AIResponse = {
      alternativas: parsedResponse.alternativas?.map((alt: any) => ({
        texto: String(alt.texto || '').trim(),
        comentario: String(alt.comentario || '').trim(),
        correta: Boolean(alt.correta)
      })) || [],
      comentario_final: String(parsedResponse.comentario_final || '').trim()
    };

    // Validate response structure
    if (!Array.isArray(normalizedResponse.alternativas) || 
        normalizedResponse.alternativas.length === 0 || 
        !normalizedResponse.comentario_final) {
      console.error('[Edge Function] Invalid response structure:', normalizedResponse);
      throw new Error('Invalid response structure: missing required fields');
    }

    // Validate each alternative
    const isValidResponse = normalizedResponse.alternativas.every(alt => 
      typeof alt.texto === 'string' &&
      typeof alt.comentario === 'string' &&
      typeof alt.correta === 'boolean' &&
      alt.texto.length > 0 &&
      alt.comentario.length > 0
    );

    if (!isValidResponse) {
      console.error('[Edge Function] Response validation failed:', normalizedResponse);
      throw new Error('Invalid response structure: invalid alternative format');
    }

    console.log('[Edge Function] Sending normalized response');

    return new Response(JSON.stringify(normalizedResponse), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error('[Edge Function] Error:', error);
    return new Response(
      JSON.stringify({ 
        error: error.message || "Erro interno do servidor",
        details: error.stack
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
});
