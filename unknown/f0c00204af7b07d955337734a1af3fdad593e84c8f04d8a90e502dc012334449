import { useState } from "react";
import { crotalicQuestions, crotalicResults } from "./constants";

type Step = "initial" | "observation" | "severity" | "discharge" | "mild" | "moderate" | "severe";

export const useCrotalicFlow = () => {
  const [currentStep, setCurrentStep] = useState<Step>("initial");
  const [answers, setAnswers] = useState<Record<string, boolean | string>>({});

  const handleAnswer = (answer: boolean | string) => {
    const newAnswers = { ...answers, [currentStep]: answer };
    setAnswers(newAnswers);

    if (currentStep === "initial") {
      if (answer === false) {
        setCurrentStep("observation");
      } else {
        setCurrentStep("severity");
      }
    } else if (currentStep === "observation") {
      if (answer === false) {
        setCurrentStep("discharge");
      } else {
        setCurrentStep("severity");
      }
    } else if (currentStep === "severity") {
      setCurrentStep(answer as Step);
    }
  };

  const handleContinue = (nextStep: string) => {
    setCurrentStep(nextStep as Step);
  };

  const resetFlow = () => {
    setCurrentStep("initial");
    setAnswers({});
  };

  const getCurrentQuestion = () => {
    return crotalicQuestions[currentStep as keyof typeof crotalicQuestions];
  };

  const getCurrentResult = () => {
    if (currentStep === "observation" && answers.initial === false) {
      return crotalicResults.noSigns;
    }

    if (currentStep === "discharge" && answers.observation === false) {
      return crotalicResults.discharge;
    }

    return crotalicResults[currentStep as keyof typeof crotalicResults];
  };

  return {
    currentStep,
    answers,
    handleAnswer,
    handleContinue,
    resetFlow,
    getCurrentQuestion,
    getCurrentResult,
  };
};