
import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Flag, Check } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { QuestionTimer } from "./QuestionTimer";
import { QuestionReportDialog } from "./QuestionReportDialog";
import { useNavigationLock } from "@/contexts/NavigationLockContext";

interface QuestionNavigationProps {
  currentIndex: number;
  totalQuestions: number;
  onSelectQuestion: (index: number) => void;
  onFinishSession: () => void;
  elapsedTime: number;
  onTimeUpdate?: (time: number) => void;
  questionId: string;
  userId: string;
  answeredStatuses?: Record<number, boolean | null>;
}

export const QuestionNavigation = ({
  currentIndex,
  totalQuestions,
  onSelectQuestion,
  onFinishSession,
  elapsedTime,
  onTimeUpdate,
  questionId,
  userId,
  answeredStatuses = {}
}: QuestionNavigationProps) => {
  const QUESTIONS_PER_PAGE = 10;
  const { isNavigationLocked } = useNavigationLock();

  const currentPage = Math.floor(currentIndex / QUESTIONS_PER_PAGE);

  const [visibleRange, setVisibleRange] = useState({
    start: currentPage * QUESTIONS_PER_PAGE,
    end: Math.min((currentPage + 1) * QUESTIONS_PER_PAGE - 1, totalQuestions - 1)
  });

  useEffect(() => {
    const newPage = Math.floor(currentIndex / QUESTIONS_PER_PAGE);
    const newStart = newPage * QUESTIONS_PER_PAGE;
    const newEnd = Math.min((newPage + 1) * QUESTIONS_PER_PAGE - 1, totalQuestions - 1);

    setVisibleRange({ start: newStart, end: newEnd });
  }, [currentIndex, totalQuestions]);

  const handleNext = () => {
    if (isNavigationLocked) return;

    const nextPage = Math.floor(visibleRange.start / QUESTIONS_PER_PAGE) + 1;
    const newStart = nextPage * QUESTIONS_PER_PAGE;
    const newEnd = Math.min((nextPage + 1) * QUESTIONS_PER_PAGE - 1, totalQuestions - 1);

    setVisibleRange({ start: newStart, end: newEnd });
    onSelectQuestion(newStart);
  };

  const handlePrevious = () => {
    if (isNavigationLocked) return;

    const prevPage = Math.floor(visibleRange.start / QUESTIONS_PER_PAGE) - 1;
    const newStart = prevPage * QUESTIONS_PER_PAGE;
    const newEnd = Math.min((prevPage + 1) * QUESTIONS_PER_PAGE - 1, totalQuestions - 1);

    setVisibleRange({ start: newStart, end: newEnd });
    onSelectQuestion(newStart);
  };

  const totalPages = Math.ceil(totalQuestions / QUESTIONS_PER_PAGE);
  const currentPageNumber = Math.floor(currentIndex / QUESTIONS_PER_PAGE) + 1;

  //console.log("QuestionNavigation - answeredStatuses:", answeredStatuses);

  return (
    <div className="sticky top-0 z-50 bg-white/95 backdrop-blur-sm shadow-md rounded-xl mb-6">
      <div className="flex items-center justify-between gap-2 max-w-full px-4 py-3">
        {/* Mobile View */}
        <div className="md:hidden flex items-center justify-center w-full max-w-md mx-auto gap-4">
          <Select
            value={currentIndex.toString()}
            onValueChange={(value) => !isNavigationLocked && onSelectQuestion(parseInt(value))}
            disabled={isNavigationLocked}
          >
            <SelectTrigger className="w-12 h-10 rounded-lg">
              <SelectValue>
                {currentIndex + 1}
              </SelectValue>
            </SelectTrigger>
            <SelectContent className="max-h-60">
              <div className="grid grid-cols-5 gap-1 p-1">
                {Array.from({ length: totalQuestions }, (_, i) => {
                  const status = answeredStatuses[i];
                  let itemClass = "flex items-center justify-center h-8 w-8 rounded-md text-sm ";

                  if (i === currentIndex) {
                    itemClass += "bg-primary text-primary-foreground";
                  } else if (status === true) {
                    itemClass += "bg-green-500 text-white";
                  } else if (status === false) {
                    itemClass += "bg-destructive text-destructive-foreground";
                  } else {
                    itemClass += "bg-gray-100";
                  }

                  return (
                    <SelectItem
                      key={i}
                      value={i.toString()}
                      className={cn(itemClass)}
                    >
                      <span className="flex items-center justify-center">
                        {status === true ? <Check className="h-3 w-3" /> : (i + 1)}
                      </span>
                    </SelectItem>
                  );
                })}
              </div>
            </SelectContent>
          </Select>

          <QuestionTimer
            elapsedTime={elapsedTime}
            onTimeUpdate={onTimeUpdate}
            isActive={true}
          />

          <div className="flex items-center gap-2">
            <QuestionReportDialog questionId={questionId} userId={userId}>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-full"
              >
                <Flag className="h-4 w-4" />
              </Button>
            </QuestionReportDialog>

            <Button
              variant="default"
              size="sm"
              onClick={onFinishSession}
              disabled={isNavigationLocked}
              className="rounded-full px-4"
            >
              Finalizar
            </Button>
          </div>
        </div>

        {/* Desktop View */}
        <div className="hidden md:flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={handlePrevious}
            disabled={visibleRange.start === 0 || isNavigationLocked}
            className="h-8 w-8 rounded-full"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <div className="flex gap-2 overflow-x-auto">
            {Array.from({ length: visibleRange.end - visibleRange.start + 1 }, (_, i) => {
              const questionIndex = visibleRange.start + i;

              if (currentIndex === questionIndex) {
                return (
                  <Button
                    key={questionIndex}
                    variant="default"
                    size="sm"
                    onClick={() => !isNavigationLocked && onSelectQuestion(questionIndex)}
                    disabled={isNavigationLocked}
                    className="h-8 w-8 p-0 rounded-full"
                  >
                    {questionIndex + 1}
                  </Button>
                );
              }

              const answerStatus = answeredStatuses[questionIndex];
              //console.log(`Question ${questionIndex + 1}: status=${answerStatus}`);

              if (answerStatus === true) {
                return (
                  <Button
                    key={questionIndex}
                    variant="outline"
                    size="sm"
                    onClick={() => !isNavigationLocked && onSelectQuestion(questionIndex)}
                    disabled={isNavigationLocked}
                    className="h-8 w-8 p-0 rounded-full bg-green-50 border-green-200 text-green-700"
                  >
                    <Check className="h-4 w-4" />
                  </Button>
                );
              } else if (answerStatus === false) {
                return (
                  <Button
                    key={questionIndex}
                    variant="outline"
                    size="sm"
                    onClick={() => !isNavigationLocked && onSelectQuestion(questionIndex)}
                    disabled={isNavigationLocked}
                    className="h-8 w-8 p-0 rounded-full bg-red-50 border-red-200 text-red-700"
                  >
                    {questionIndex + 1}
                  </Button>
                );
              } else {
                return (
                  <Button
                    key={questionIndex}
                    variant="outline"
                    size="sm"
                    onClick={() => !isNavigationLocked && onSelectQuestion(questionIndex)}
                    disabled={isNavigationLocked}
                    className="h-8 w-8 p-0 rounded-full"
                  >
                    {questionIndex + 1}
                  </Button>
                );
              }
            })}
          </div>

          <Button
            variant="ghost"
            size="icon"
            onClick={handleNext}
            disabled={visibleRange.end >= totalQuestions - 1 || isNavigationLocked}
            className="h-8 w-8 rounded-full"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>

          {totalPages > 1 && (
            <span className="text-xs text-muted-foreground ml-2">
              Página {currentPageNumber} de {totalPages}
            </span>
          )}
        </div>

        <div className="hidden md:flex items-center gap-4">
          <QuestionTimer
            elapsedTime={elapsedTime}
            onTimeUpdate={onTimeUpdate}
            isActive={true}
          />
          <QuestionReportDialog questionId={questionId} userId={userId}>
            <Button
              variant="outline"
              size="sm"
              className="hidden md:flex gap-2 rounded-full"
            >
              <Flag className="h-4 w-4" />
              Reportar
            </Button>
          </QuestionReportDialog>
          <Button
            variant="default"
            size="sm"
            onClick={onFinishSession}
            disabled={isNavigationLocked}
            className="flex items-center gap-2 rounded-full"
          >
            Finalizar
          </Button>
        </div>
      </div>
    </div>
  );
};
