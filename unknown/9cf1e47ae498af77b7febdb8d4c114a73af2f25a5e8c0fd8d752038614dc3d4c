
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { EmergencyActions } from "./EmergencyActions";

interface TreatmentFlowProps {
  ageGroup: "under6" | "over6";
  weight: number;
  selectedFlow: "mild" | "severe" | null;
  improved: boolean | null;
  onSeveritySelect: (severity: "mild" | "severe") => void;
  onImprovement: (hasImproved: boolean) => void;
}

export const TreatmentFlow = ({ 
  ageGroup, 
  weight, 
  selectedFlow, 
  improved, 
  onSeveritySelect,
  onImprovement 
}: TreatmentFlowProps) => {
  const renderInitialTreatment = () => {
    if (ageGroup === "over6") {
      return (
        <div className="bg-white/80 p-4 rounded-lg border border-green-100 dark:bg-green-900/20 dark:border-green-800/30 dark:text-gray-100">
          <h4 className="font-semibold text-green-700 dark:text-green-300 mb-2">Medicação Inicial:</h4>
          <ul className="list-disc list-inside space-y-2 text-gray-700 dark:text-gray-200">
            <li>Salbutamol: 4–10 puffs por dose, usando espaçador</li>
            <li>Frequência: Repetir a cada 20 minutos por 1 hora</li>
            <li>Prednisolona : 1 a 2 mg/kg, max de 40 mg;</li>
            <li>Oxigênio: Administrar para manter saturação entre 93–95%</li>
            <h4 className="font-semibold text-green-700 dark:text-green-300 mb-2">Com melhora:</h4>
            <li>Continuar o tratamento com saba conforme necessário</li>
            <li>Avaliar a resposta em 1 hora</li>
          </ul>
        </div>
      );
    }

    return (
      <div className="bg-white/80 p-4 rounded-lg border border-green-100 dark:bg-green-900/20 dark:border-green-800/30 dark:text-gray-100">
        <h4 className="font-semibold text-green-700 dark:text-green-300 mb-2">Medicação Inicial:</h4>
        <ul className="list-disc list-inside space-y-2 text-gray-700 dark:text-gray-200">
          <li>Salbutamol: 100 mcg/dose, 2–4 puffs com espaçador e máscara</li>
          <li>Repetição: A cada 20 minutos por 1 hora</li>
          <li>Oxigênio: Manter saturação entre 94–98%</li>
          <h4 className="font-semibold text-green-700 dark:text-green-300 mb-2">Continue o tratamento se necessário:</h4>
          <li>Se os sintomas recorrerem entre de 3 – 4 hs: Administre salbutamol extra: 2 – 3 puffs por hora, até 10 puffs conforme tolerância.</li>
          <li>Administre prednisolona 2 mg/ kg (máx 20 mg para ≤ 2 anos; max de 30 mg -para 2 – 5 anos, por via oral)</li>
        </ul>
      </div>
    );
  };

  const renderDischargeInstructions = () => {
    if (ageGroup === "over6") {
      return (
        <div className="mt-6 bg-white/80 p-4 rounded-lg border border-green-100 dark:bg-green-900/20 dark:border-green-800/30 dark:text-gray-100">
          <h4 className="font-semibold text-green-700 dark:text-green-300 mb-2">Planejamento de Alta/Seguimento:</h4>
          <ul className="list-disc list-inside space-y-2 text-gray-700 dark:text-gray-200">
            <li>Sintomas melhorando;</li>
            <li>Sat O2 maior que 94% em ar ambiente;</li>
            <li>Medicação de alivio: conforme necessário;</li>
            <li>Tratamento de manutenção: iniciar ou intensificar;</li>
            <li>Corticoide continuar, geralmente por 5 a 7 dias (crianças de 3 a 5 dias);</li>
            <li>Acompanhamento: de 2 a 7 dias na atenção básica;</li>
          </ul>
        </div>
      );
    }

    return (
      <div className="mt-6 bg-white/80 p-4 rounded-lg border border-green-100 dark:bg-green-900/20 dark:border-green-800/30 dark:text-gray-100">
        <h4 className="font-semibold text-green-700 dark:text-green-300 mb-2">Planejamento de Alta:</h4>
        <ul className="list-disc list-inside space-y-2 text-gray-700 dark:text-gray-200">
          <li>Broncodilatador: Salbutamol inalatório a cada 4/4 horas ou 6/6 horas por 5 dias</li>
          <li>Verificar técnica e adesão ao inalador</li>
          <li>Acompanhamento médico: Em 1–2 dias úteis</li>
          <li>Prednisolona: 2mg/kg por 5 dias</li>
        </ul>
      </div>
    );
  };

  return (
    <Card className="p-6 mt-8 bg-gradient-to-br from-white to-green-50/50 border-green-100 dark:from-green-900/30 dark:to-slate-800/80 dark:border-green-800/30">
      <h3 className="text-xl font-semibold text-green-800 dark:text-green-300 mb-4">
        {selectedFlow === "mild" ? "Tratamento Inicial - Leve/Moderada" : "Tratamento - Caso Severo"}
      </h3>
      
      {selectedFlow === "mild" && !improved && (
        <div className="space-y-4">
          {renderInitialTreatment()}

          <div className="mt-6">
            <h4 className="font-semibold text-purple-700 dark:text-purple-300 mb-4">Houve melhora com o tratamento inicial?</h4>
            <div className="flex gap-4 justify-center">
              <Button
                onClick={() => onImprovement(true)}
                variant="outline"
                className="w-32 border-green-200 hover:bg-green-50 dark:border-green-700 dark:hover:bg-green-900/40 dark:text-green-300"
              >
                Sim
              </Button>
              <Button
                onClick={() => onImprovement(false)}
                variant="outline"
                className="w-32 border-red-200 hover:bg-red-50 dark:border-red-700 dark:hover:bg-red-900/40 dark:text-red-300"
              >
                Não
              </Button>
            </div>
          </div>
        </div>
      )}

      {improved === true && renderDischargeInstructions()}
      {(improved === false || selectedFlow === "severe") && <EmergencyActions />}
    </Card>    
  );
};
