import { Button } from "@/components/ui/button";
import { FileText, FileSearch } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface DiagnosisActionsProps {
  selectedDiagnosis: {
    condition: string;
    probability: number;
    clinicalPresentation: string;
    exams: string;
    treatment: string;
  } | null;
  patientData: {
    age: number;
    gender: "male" | "female";
    symptoms: string[];
    hasChronicDiseases: boolean;
    chronicDiseases?: string;
    symptomsIntensity: number;
    hasRecentExams: boolean;
    examDetails?: string;
    isPregnant?: boolean;
    manualSymptoms?: string;
  };
  onPrescriptionGenerated: (prescription: any) => void;
  onDetailedAnalysis: (analysis: string) => void;
  setIsLoadingPrescription: (loading: boolean) => void;
  setIsLoadingAnalysis: (loading: boolean) => void;
}

export function DiagnosisActions({
  selectedDiagnosis,
  patientData,
  onPrescriptionGenerated,
  onDetailedAnalysis,
  setIsLoadingPrescription,
  setIsLoadingAnalysis,
}: DiagnosisActionsProps) {
  const { toast } = useToast();

  const handleGeneratePrescription = async () => {
    if (!selectedDiagnosis) {
      toast({
        title: "Selecione um diagnóstico",
        description: "Por favor, selecione um diagnóstico para gerar a prescrição.",
        variant: "destructive",
      });
      return;
    }

    setIsLoadingPrescription(true);
    try {
      const { data, error } = await supabase.functions.invoke("generate-prescription", {
        body: {
          diagnosis: selectedDiagnosis,
          patientData,
        },
      });

      if (error) throw error;

      onPrescriptionGenerated(data);
      
      toast({
        title: "Prescrição gerada com sucesso!",
        description: "A prescrição foi gerada baseada no diagnóstico selecionado.",
      });
    } catch (error) {
      console.error("Error generating prescription:", error);
      toast({
        title: "Erro ao gerar prescrição",
        description: "Ocorreu um erro ao tentar gerar a prescrição. Tente novamente.",
        variant: "destructive",
      });
      setIsLoadingPrescription(false);
    }
  };

  const handleDetailedAnalysis = async () => {
    if (!selectedDiagnosis) {
      toast({
        title: "Selecione um diagnóstico",
        description: "Por favor, selecione um diagnóstico para gerar a análise detalhada.",
        variant: "destructive",
      });
      return;
    }

    setIsLoadingAnalysis(true);
    try {
      const { data, error } = await supabase.functions.invoke("generate-detailed-analysis", {
        body: {
          diagnosis: selectedDiagnosis,
          patientData,
        },
      });

      if (error) throw error;

      onDetailedAnalysis(data.analysis);
      
      toast({
        title: "Análise detalhada gerada!",
        description: "A análise detalhada do caso foi gerada com sucesso.",
      });
    } catch (error) {
      console.error("Error generating detailed analysis:", error);
      toast({
        title: "Erro ao gerar análise",
        description: "Ocorreu um erro ao tentar gerar a análise detalhada. Tente novamente.",
        variant: "destructive",
      });
      setIsLoadingAnalysis(false);
    }
  };

  return (
    <div className="flex flex-col sm:flex-row gap-4 mt-6">
      <Button
        onClick={handleGeneratePrescription}
        className="flex-1 gap-2 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
      >
        <FileText className="h-4 w-4" />
        Gerar Prescrição
      </Button>
      <Button
        onClick={handleDetailedAnalysis}
        variant="outline"
        className="flex-1 gap-2 border-primary/20 hover:bg-primary/5"
      >
        <FileSearch className="h-4 w-4" />
        Análise Detalhada
      </Button>
    </div>
  );
}