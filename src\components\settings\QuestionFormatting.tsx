
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>Triangle, RefreshCw, Check, CheckCircle2, ArrowDown, Edit2 } from "lucide-react";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";
import { supabase } from "@/integrations/supabase/client";
import { useSuccessDialog, useErrorDialog } from "@/hooks/use-feedback-dialog";
import { Separator } from "@/components/ui/separator";
import { useTextEnhancement } from "@/hooks/useTextEnhancement";
import { processHtmlContent } from "@/utils/htmlProcessor";

type Question = {
  id: string;
  statement: string;
  original_statement: string | null;
};

type Category = {
  id: string;
  name: string;
  type: string;
};

export const QuestionFormatting = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [specialties, setSpecialties] = useState<Category[]>([]);
  const [themes, setThemes] = useState<Category[]>([]);
  const [selectedSpecialty, setSelectedSpecialty] = useState<string>("");
  const [selectedTheme, setSelectedTheme] = useState<string>("");
  const [questions, setQuestions] = useState<Question[]>([]);
  const [formattedQuestions, setFormattedQuestions] = useState<{
    id: string;
    originalStatement: string;
    formattedStatement: string;
    isApproved: boolean;
    isEditing: boolean;
  }[]>([]);
  const [batchSize, setBatchSize] = useState<number>(10);
  const [loadedCount, setLoadedCount] = useState<number>(0);
  const [processedCount, setProcessedCount] = useState<number>(0);
  const [totalQuestions, setTotalQuestions] = useState<number>(0);
  const [formattedCount, setFormattedCount] = useState<number>(0);
  const showSuccessDialog = useSuccessDialog();
  const showErrorDialog = useErrorDialog();

  // Load specialties on component mount
  useEffect(() => {
    loadSpecialties();
  }, []);

  // Load themes when specialty is selected
  useEffect(() => {
    if (selectedSpecialty) {
      loadThemes(selectedSpecialty);
    } else {
      setThemes([]);
      setSelectedTheme("");
    }
  }, [selectedSpecialty]);

  // Load question stats when theme is selected
  useEffect(() => {
    if (selectedTheme) {
      loadQuestionStats();
    } else {
      setQuestions([]);
      setFormattedQuestions([]);
      setTotalQuestions(0);
      setFormattedCount(0);
    }
  }, [selectedTheme]);

  const loadSpecialties = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from("study_categories")
        .select("*")
        .eq("type", "specialty")
        .order("name");

      if (error) throw error;
      console.log("📚 Loaded specialties:", data.length);
      setSpecialties(data || []);
    } catch (error) {
      console.error("Error loading specialties:", error);
      toast({
        title: "Erro ao carregar especialidades",
        description: "Não foi possível carregar as especialidades. Tente novamente mais tarde.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadThemes = async (specialtyId: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from("study_categories")
        .select("*")
        .eq("type", "theme")
        .eq("parent_id", specialtyId)
        .order("name");

      if (error) throw error;
      console.log(`📚 Loaded ${data.length} themes for specialty ${specialtyId}`);
      setThemes(data || []);
    } catch (error) {
      console.error("Error loading themes:", error);
      toast({
        title: "Erro ao carregar temas",
        description: "Não foi possível carregar os temas. Tente novamente mais tarde.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadQuestionStats = async () => {
    setIsLoading(true);
    try {
      // Count total questions for this theme
      const { count: totalCount, error: totalError } = await supabase
        .from("questions")
        .select("*", { count: "exact", head: true })
        .eq("theme_id", selectedTheme);

      // Count questions that already have original_statement (already formatted)
      const { count: formattedCount, error: formattedError } = await supabase
        .from("questions")
        .select("*", { count: "exact", head: true })
        .eq("theme_id", selectedTheme)
        .not("original_statement", "is", null);

      if (totalError || formattedError) throw totalError || formattedError;

      console.log(`📊 Theme stats: ${formattedCount}/${totalCount} questions formatted`);
      setTotalQuestions(totalCount || 0);
      setFormattedCount(formattedCount || 0);
    } catch (error) {
      console.error("Error loading question stats:", error);
      showErrorDialog(
        "Erro ao carregar estatísticas",
        "Não foi possível carregar as estatísticas das questões."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const loadQuestions = async () => {
    if (!selectedTheme) {
      showErrorDialog(
        "Tema não selecionado",
        "Por favor, selecione um tema para carregar questões."
      );
      return;
    }
    
    setIsLoading(true);
    setQuestions([]);
    setFormattedQuestions([]);
    
    try {
      console.log(`⏳ Carregando até ${batchSize} questões do tema ${selectedTheme}`);
      const { data, error } = await supabase
        .from("questions")
        .select("id, statement, original_statement")
        .eq("theme_id", selectedTheme)
        .is("original_statement", null)
        .order("created_at", { ascending: false })
        .limit(batchSize);

      if (error) throw error;

      console.log(`📝 Loaded ${data?.length} questions for formatting`);
      setQuestions(data || []);
      setLoadedCount(data?.length || 0);
      
      if (data?.length === 0) {
        toast({
          title: "Sem questões para formatar",
          description: "Todas as questões deste tema já foram formatadas.",
        });
      }
    } catch (error: any) {
      console.error("Error loading questions:", error);
      toast({
        title: "Erro ao carregar questões",
        description: "Não foi possível carregar as questões para formatação.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatQuestions = async () => {
    if (questions.length === 0) {
      toast({
        title: "Nenhuma questão para formatar",
        description: "Carregue questões primeiro antes de formatar.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    setFormattedQuestions([]);
    setProcessedCount(0);
    
    try {
      // Process questions one by one to avoid timeout issues
      for (let i = 0; i < questions.length; i++) {
        const question = questions[i];
        console.log(`🔄 Formatando questão ${i + 1}/${questions.length} (ID: ${question.id})`);
        
        // Call Supabase Edge Function to get AI-formatted statement
        const { data, error } = await supabase.functions.invoke('enhance-text', {
          body: { text: question.statement }
        });

        if (error) {
          console.error("Error from edge function:", error);
          throw error;
        }
        
        console.log(`✅ Questão ${i + 1} formatada com sucesso`);
        
        setFormattedQuestions(prev => [
          ...prev,
          {
            id: question.id,
            originalStatement: question.statement,
            formattedStatement: data.enhancedText,
            isApproved: false,
            isEditing: false
          }
        ]);
        
        setProcessedCount(i + 1);
      }
      
      showSuccessDialog(
        "Formatação concluída",
        `${questions.length} questões foram formatadas.`
      );
    } catch (error: any) {
      console.error("Error formatting questions:", error);
      showErrorDialog(
        "Erro ao formatar questões",
        error.message || "Ocorreu um erro durante a formatação das questões."
      );
    } finally {
      setIsProcessing(false);
    }
  };

  const toggleApproval = (id: string) => {
    setFormattedQuestions(prev => 
      prev.map(q => 
        q.id === id ? { ...q, isApproved: !q.isApproved } : q
      )
    );
  };

  const toggleEditing = (id: string) => {
    setFormattedQuestions(prev => 
      prev.map(q => 
        q.id === id ? { ...q, isEditing: !q.isEditing } : q
      )
    );
  };

  const updateFormattedText = (id: string, newText: string) => {
    setFormattedQuestions(prev =>
      prev.map(q =>
        q.id === id ? { ...q, formattedStatement: newText } : q
      )
    );
  };

  const approveAll = () => {
    setFormattedQuestions(prev => 
      prev.map(q => ({ ...q, isApproved: true }))
    );
  };

  const saveApprovedQuestions = async () => {
    const approvedQuestions = formattedQuestions.filter(q => q.isApproved);
    
    if (approvedQuestions.length === 0) {
      toast({
        title: "Nenhuma questão aprovada",
        description: "Aprove pelo menos uma questão para salvar.",
        variant: "destructive",
      });
      return;
    }
    
    setIsProcessing(true);
    let successCount = 0;
    
    try {
      console.log(`💾 Salvando ${approvedQuestions.length} questões aprovadas`);
      
      for (const q of approvedQuestions) {
        const { error } = await supabase
          .from("questions")
          .update({
            statement: q.formattedStatement,
            original_statement: q.originalStatement
          })
          .eq("id", q.id);
        
        if (error) {
          console.error("Error updating question:", error);
          continue;
        }
        
        successCount++;
      }
      
      toast({
        title: "Questões atualizadas",
        description: `${successCount} questões foram atualizadas com sucesso.`,
      });
      
      // Refresh stats and clear formatted questions
      loadQuestionStats();
      setFormattedQuestions([]);
      setQuestions([]);
      loadQuestions(); // Recarregar novas questões automaticamente
    } catch (error: any) {
      console.error("Error saving approved questions:", error);
      toast({
        title: "Erro ao salvar questões",
        description: "Ocorreu um erro ao salvar as questões aprovadas.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      <Alert variant="default" className="bg-amber-50 border-amber-200">
        <AlertTriangle className="h-5 w-5 text-amber-500" />
        <AlertDescription className="text-amber-700">
          Esta ferramenta permite melhorar a formatação visual dos enunciados de questões, sem alterar o conteúdo ou significado.
        </AlertDescription>
      </Alert>
      
      <Card>
        <CardHeader>
          <CardTitle>Selecionar Questões</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="specialty">Especialidade</Label>
              <Select
                value={selectedSpecialty}
                onValueChange={setSelectedSpecialty}
                disabled={isLoading}
              >
                <SelectTrigger id="specialty">
                  <SelectValue placeholder="Selecione uma especialidade" />
                </SelectTrigger>
                <SelectContent>
                  {specialties.map((specialty) => (
                    <SelectItem key={specialty.id} value={specialty.id}>
                      {specialty.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="theme">Tema</Label>
              <Select
                value={selectedTheme}
                onValueChange={setSelectedTheme}
                disabled={isLoading || !selectedSpecialty}
              >
                <SelectTrigger id="theme">
                  <SelectValue placeholder="Selecione um tema" />
                </SelectTrigger>
                <SelectContent>
                  {themes.map((theme) => (
                    <SelectItem key={theme.id} value={theme.id}>
                      {theme.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {selectedTheme && (
            <>
              <div className="flex flex-col space-y-2">
                <div className="flex justify-between items-center">
                  <Label>Status das Questões</Label>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={loadQuestionStats}
                    disabled={isLoading}
                  >
                    <RefreshCw className="h-4 w-4 mr-1" />
                    Atualizar
                  </Button>
                </div>
                <Progress value={(formattedCount / totalQuestions) * 100} className="h-2" />
                <div className="text-sm text-gray-500">
                  {formattedCount} de {totalQuestions} questões formatadas ({Math.round((formattedCount / totalQuestions) * 100) || 0}%)
                </div>
              </div>
            
              <div className="flex flex-col md:flex-row items-center gap-4">
                <div className="w-full md:w-1/3">
                  <Label htmlFor="batchSize">Quantidade para formatar</Label>
                  <Input
                    id="batchSize"
                    type="number"
                    min="1"
                    max="50"
                    value={batchSize}
                    onChange={(e) => setBatchSize(parseInt(e.target.value) || 10)}
                    className="mt-1"
                  />
                </div>
                
                <div className="flex items-end gap-2 w-full md:w-2/3 mt-4 md:mt-0">
                  <Button 
                    onClick={loadQuestions}
                    disabled={isLoading || isProcessing || !selectedTheme}
                    className="flex-1"
                  >
                    Carregar Questões
                  </Button>
                  
                  <Button 
                    onClick={formatQuestions}
                    disabled={isLoading || isProcessing || questions.length === 0}
                    className="flex-1"
                  >
                    Formatar com IA
                  </Button>
                </div>
              </div>
              
              {questions.length > 0 && (
                <div className="text-sm text-gray-500">
                  {loadedCount} questões carregadas para formatação
                </div>
              )}
              
              {isProcessing && (
                <div className="text-center py-4">
                  <div className="flex flex-col items-center space-y-3">
                    <RefreshCw className="animate-spin h-8 w-8 text-primary" />
                    <div>
                      Processando {processedCount} de {questions.length} questões...
                    </div>
                    <Progress value={(processedCount / questions.length) * 100} className="h-2 w-64" />
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
      
      {formattedQuestions.length > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Revisar e Aprovar</CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={approveAll}
                disabled={isProcessing}
              >
                <CheckCircle2 className="h-4 w-4 mr-1" />
                Aprovar Todos
              </Button>
              <Button
                onClick={saveApprovedQuestions}
                disabled={isProcessing || formattedQuestions.filter(q => q.isApproved).length === 0}
              >
                <Check className="h-4 w-4 mr-1" />
                Salvar Aprovados
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {formattedQuestions.map((q, i) => (
              <div key={q.id} className="border rounded-md p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">Questão {i + 1}</h3>
                  <Button
                    variant={q.isApproved ? "default" : "outline"}
                    size="sm"
                    onClick={() => toggleApproval(q.id)}
                    disabled={isProcessing}
                  >
                    <Check className="h-4 w-4 mr-1" />
                    {q.isApproved ? "Aprovado" : "Aprovar"}
                  </Button>
                </div>

                <div className="space-y-4">
                  <div className="bg-gray-50 p-3 rounded-md">
                    <h4 className="text-sm font-medium mb-1 text-gray-500">Original:</h4>
                    <pre className="text-sm whitespace-pre-wrap">{q.originalStatement}</pre>
                  </div>
                  
                  <div className="flex justify-center">
                    <ArrowDown className="h-6 w-6 text-gray-400" />
                  </div>
                  
                  <div className="bg-green-50 p-3 rounded-md">
                    <div className="flex justify-between items-center mb-1">
                      <h4 className="text-sm font-medium text-green-700">Formatado:</h4>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => toggleEditing(q.id)}
                        disabled={isProcessing}
                      >
                        <Edit2 className="h-4 w-4 mr-1" />
                        {q.isEditing ? "Concluir" : "Editar"}
                      </Button>
                    </div>
                    {q.isEditing ? (
                      <Textarea
                        value={q.formattedStatement}
                        onChange={(e) => updateFormattedText(q.id, e.target.value)}
                        className="min-h-[200px] font-mono text-sm"
                      />
                    ) : (
                      <pre className="text-sm whitespace-pre-wrap">{q.formattedStatement}</pre>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
