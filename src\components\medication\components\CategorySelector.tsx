import { ChevronDown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

interface CategorySelectorProps {
  categories: any[];
  selectedCategory: string | null;
  onSelectCategory: (categoryId: string) => void;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

export const CategorySelector = ({
  categories,
  selectedCategory,
  onSelectCategory,
  isOpen,
  setIsOpen,
}: CategorySelectorProps) => {
  const getMedicationCount = (count: number) => {
    return `${count} medicamento${count !== 1 ? 's' : ''}`;
  };

  return (
    <Collapsible
      open={isOpen}
      onOpenChange={setIsOpen}
      className="lg:hidden w-full space-y-2"
    >
      <CollapsibleTrigger asChild>
        <Button
          variant="outline"
          className="w-full flex items-center justify-between p-4"
        >
          <span className="font-medium">
            {selectedCategory 
              ? categories.find(cat => cat.id === selectedCategory)?.name 
              : "Selecione uma categoria"}
          </span>
          <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? "transform rotate-180" : ""}`} />
        </Button>
      </CollapsibleTrigger>
      <CollapsibleContent className="space-y-2">
        <Card className="p-2">
          <ScrollArea className="h-[300px]">
            <div className="grid grid-cols-2 gap-2 p-2">
              {categories?.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "ghost"}
                  className="w-full justify-start text-left h-auto py-2"
                  onClick={() => {
                    onSelectCategory(category.id);
                    setIsOpen(false);
                  }}
                >
                  <div className="flex flex-col items-start">
                    <span className="truncate text-sm">{category.name}</span>
                    <span className="text-xs text-muted-foreground">
                      {getMedicationCount(category.pedbook_medications?.length || 0)}
                    </span>
                  </div>
                </Button>
              ))}
            </div>
          </ScrollArea>
        </Card>
      </CollapsibleContent>
    </Collapsible>
  );
};