import { useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface ImportICD10DialogProps {
  isOpen: boolean;
  onClose: () => void;
  parentId: string | null;
}

export function ImportICD10Dialog({ isOpen, onClose, parentId }: ImportICD10DialogProps) {
  const [codesJson, setCodesJson] = useState("");
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const handleImport = async () => {
    try {
      const codes = JSON.parse(codesJson);
      
      // Insert all codes as categories with the current parentId
      const { error } = await supabase
        .from("pedbook_icd10_categories")
        .insert(
          codes.map((code: any) => ({
            code_range: code.codigocid,
            name: code.nome,
            level: parentId ? 2 : 1,
            parent_id: parentId,
          }))
        );

      if (error) throw error;

      toast({
        title: "Códigos importados com sucesso!",
        description: `${codes.length} códigos foram importados.`,
      });
      
      queryClient.invalidateQueries({ queryKey: ["icd10-categories"] });
      onClose();
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao importar códigos",
        description: error.message,
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Importar Códigos CID-10</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <ScrollArea className="h-[400px] w-full rounded-md border">
            <Textarea
              value={codesJson}
              onChange={(e) => setCodesJson(e.target.value)}
              placeholder='[{"codigocid": "A00-A09", "nome": "Doenças infecciosas intestinais"}]'
              className="min-h-[400px] border-none"
            />
          </ScrollArea>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button onClick={handleImport}>Importar</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}