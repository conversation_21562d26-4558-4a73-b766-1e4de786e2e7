import React from "react";
import { motion, AnimatePresence } from "framer-motion";

interface TransitionEffectsProps {
  isActive: boolean;
  type: 'fade' | 'slide' | 'zoom' | 'wipe' | 'spiral';
  duration?: number;
  onComplete?: () => void;
}

export const TransitionEffects: React.FC<TransitionEffectsProps> = ({
  isActive,
  type,
  duration = 1,
  onComplete
}) => {
  if (!isActive) return null;

  const variants = {
    fade: {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 }
    },
    slide: {
      initial: { x: '100%' },
      animate: { x: 0 },
      exit: { x: '-100%' }
    },
    zoom: {
      initial: { scale: 0, opacity: 0 },
      animate: { scale: 1, opacity: 1 },
      exit: { scale: 2, opacity: 0 }
    },
    wipe: {
      initial: { clipPath: 'circle(0% at 50% 50%)' },
      animate: { clipPath: 'circle(150% at 50% 50%)' },
      exit: { clipPath: 'circle(0% at 50% 50%)' }
    },
    spiral: {
      initial: { rotate: -180, scale: 0, opacity: 0 },
      animate: { rotate: 0, scale: 1, opacity: 1 },
      exit: { rotate: 180, scale: 0, opacity: 0 }
    }
  };

  return (
    <AnimatePresence onExitComplete={onComplete}>
      <motion.div
        className="fixed inset-0 z-40 bg-gradient-to-br from-blue-900 via-purple-900 to-black"
        variants={variants[type]}
        initial="initial"
        animate="animate"
        exit="exit"
        transition={{ duration, ease: "easeInOut" }}
      >
        {/* Efeitos de partículas durante a transição */}
        <div className="absolute inset-0">
          {Array.from({ length: 20 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                scale: [0, 1, 0],
                opacity: [0, 1, 0],
                rotate: [0, 360],
              }}
              transition={{
                duration: duration * 0.8,
                delay: Math.random() * duration * 0.5,
                ease: "easeOut",
              }}
            />
          ))}
        </div>

        {/* Overlay com gradiente animado */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent"
          animate={{
            x: ['-100%', '100%'],
          }}
          transition={{
            duration: duration * 0.6,
            ease: "easeInOut",
          }}
        />
      </motion.div>
    </AnimatePresence>
  );
};

// Componente para efeito de cortina
export const CurtainTransition: React.FC<{ isActive: boolean; onComplete?: () => void }> = ({
  isActive,
  onComplete
}) => {
  if (!isActive) return null;

  return (
    <AnimatePresence onExitComplete={onComplete}>
      <div className="fixed inset-0 z-40">
        {/* Cortina esquerda */}
        <motion.div
          className="absolute top-0 left-0 w-1/2 h-full bg-black"
          initial={{ x: '-100%' }}
          animate={{ x: 0 }}
          exit={{ x: '-100%' }}
          transition={{ duration: 0.8, ease: "easeInOut" }}
        />
        
        {/* Cortina direita */}
        <motion.div
          className="absolute top-0 right-0 w-1/2 h-full bg-black"
          initial={{ x: '100%' }}
          animate={{ x: 0 }}
          exit={{ x: '100%' }}
          transition={{ duration: 0.8, ease: "easeInOut" }}
        />

        {/* Efeito de brilho no centro */}
        <motion.div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1 h-full bg-gradient-to-b from-transparent via-white to-transparent"
          initial={{ opacity: 0, scaleY: 0 }}
          animate={{ opacity: 1, scaleY: 1 }}
          exit={{ opacity: 0, scaleY: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        />
      </div>
    </AnimatePresence>
  );
};

// Componente para efeito de ondas
export const WaveTransition: React.FC<{ isActive: boolean; onComplete?: () => void }> = ({
  isActive,
  onComplete
}) => {
  if (!isActive) return null;

  return (
    <AnimatePresence onExitComplete={onComplete}>
      <div className="fixed inset-0 z-40 overflow-hidden">
        {Array.from({ length: 5 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600"
            style={{
              clipPath: `circle(0% at 50% 50%)`,
            }}
            animate={{
              clipPath: [
                'circle(0% at 50% 50%)',
                'circle(150% at 50% 50%)',
                'circle(0% at 50% 50%)'
              ],
            }}
            transition={{
              duration: 2,
              delay: i * 0.2,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>
    </AnimatePresence>
  );
};

// Componente para efeito de portal
export const PortalTransition: React.FC<{ isActive: boolean; onComplete?: () => void }> = ({
  isActive,
  onComplete
}) => {
  if (!isActive) return null;

  return (
    <AnimatePresence onExitComplete={onComplete}>
      <motion.div
        className="fixed inset-0 z-40 flex items-center justify-center bg-black"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Portal circular */}
        <motion.div
          className="relative"
          initial={{ scale: 0, rotate: 0 }}
          animate={{ scale: 1, rotate: 360 }}
          exit={{ scale: 0, rotate: 720 }}
          transition={{ duration: 1.5, ease: "easeInOut" }}
        >
          {/* Anéis do portal */}
          {Array.from({ length: 3 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute border-4 border-blue-500 rounded-full"
              style={{
                width: `${(i + 1) * 100}px`,
                height: `${(i + 1) * 100}px`,
                left: `${-((i + 1) * 50)}px`,
                top: `${-((i + 1) * 50)}px`,
              }}
              animate={{
                rotate: [0, 360],
                borderColor: [
                  '#3b82f6',
                  '#8b5cf6',
                  '#ec4899',
                  '#10b981',
                  '#3b82f6'
                ],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: i * 0.2,
                ease: "linear",
              }}
            />
          ))}

          {/* Centro do portal */}
          <motion.div
            className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
            animate={{
              scale: [1, 1.2, 1],
              boxShadow: [
                '0 0 20px rgba(59, 130, 246, 0.5)',
                '0 0 40px rgba(139, 92, 246, 0.8)',
                '0 0 20px rgba(59, 130, 246, 0.5)',
              ],
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
            }}
          />
        </motion.div>

        {/* Partículas ao redor do portal */}
        <div className="absolute inset-0">
          {Array.from({ length: 30 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                x: [0, (Math.random() - 0.5) * 200],
                y: [0, (Math.random() - 0.5) * 200],
                opacity: [0, 1, 0],
                scale: [0, 1, 0],
              }}
              transition={{
                duration: 2,
                delay: Math.random() * 1,
                ease: "easeOut",
              }}
            />
          ))}
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
