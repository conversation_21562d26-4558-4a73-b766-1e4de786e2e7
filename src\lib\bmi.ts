export const calculateZScore = (bmi: number, coefficients: [number, number, number]): number => {
  const [L, M, S] = coefficients;
  return ((Math.pow(bmi / M, L) - 1) / (L * S));
};

export const getClassification = (ageInYears: number, zScore: number): string => {
  if (zScore < -3) return "Magreza grave";
  if (zScore < -2) return "Magreza";
  if (zScore <= 1) return "Eutrófico";
  if (zScore <= 2) return ageInYears < 5 ? "Risco de sobrepeso" : "Sobrepeso";
  if (zScore <= 3) return ageInYears < 5 ? "Sobrepeso" : "Obesidade";
  return ageInYears < 5 ? "Obesidade" : "Obesidade grave";
};

export const getCoeficientesLMS = (gender: string, ageInMonths: number): [number, number, number] => {
  const coeficientesMasculinos = [
    [-0.3053, 13.4069, 0.0956],
    [-0.2708, 14.9441, 0.09027],
    [0.1118, 16.3195, 0.08677],
    [0.0068, 16.8987, 0.08495],
    [-0.0727, 17.1579, 0.08378],
    [-0.137, 17.2919, 0.08296],
    [-0.1913, 17.3422, 0.08234],
    [-0.2385, 17.3288, 0.08183],
    [-0.2802, 17.2647, 0.0814],
    [-0.3176, 17.1662, 0.08102],
    [-0.3516, 17.0488, 0.08068],
    [-0.3828, 16.9239, 0.08037],
    [-0.4115, 16.7981, 0.08009],
    [-0.4382, 16.6743, 0.07982],
    [-0.463, 16.5548, 0.07958],
    [-0.4863, 16.4409, 0.07935],
    [-0.5082, 16.3335, 0.07913],
    [-0.5289, 16.2329, 0.07892],
    [-0.5484, 16.1392, 0.07873],
    [-0.5669, 16.0528, 0.07854],
    [-0.5846, 15.9743, 0.07836],
    [-0.6014, 15.9039, 0.07818],
    [-0.6174, 15.8412, 0.07802],
    [-0.6328, 15.7852, 0.07786],
    [-0.6187, 16.0189, 0.07785],
    [-0.584, 15.98, 0.07792],
    [-0.5497, 15.9414, 0.078],
    [-0.5166, 15.9036, 0.07808],
    [-0.485, 15.8667, 0.07818],
    [-0.4552, 15.8306, 0.07829],
    [-0.4274, 15.7953, 0.07841],
    [-0.4016, 15.7606, 0.07854],
    [-0.3782, 15.7267, 0.07867],
    [-0.3572, 15.6934, 0.07882],
    [-0.3388, 15.661, 0.07897],
    [-0.3231, 15.6294, 0.07914],
    [-0.3101, 15.5988, 0.07931],
    [-0.3, 15.5693, 0.0795],
    [-0.2927, 15.541, 0.07969],
    [-0.2884, 15.514, 0.0799],
    [-0.2869, 15.4885, 0.08012],
    [-0.2881, 15.4645, 0.08036],
    [-0.2919, 15.442, 0.08061],
    [-0.2981, 15.421, 0.08087],
    [-0.3067, 15.4013, 0.08115],
    [-0.3174, 15.3827, 0.08144],
    [-0.3303, 15.3652, 0.08174],
    [-0.3452, 15.3485, 0.08205],
    [-0.3622, 15.3326, 0.08238],
    [-0.3811, 15.3174, 0.08272],
    [-0.4019, 15.3029, 0.08307],
    [-0.4245, 15.2891, 0.08343],
    [-0.4488, 15.2759, 0.0838],
    [-0.4747, 15.2633, 0.08418],
    [-0.5019, 15.2514, 0.08457],
    [-0.5303, 15.24, 0.08496],
    [-0.5599, 15.2291, 0.08536],
    [-0.5905, 15.2188, 0.08577],
    [-0.6223, 15.2091, 0.08617],
    [-0.6552, 15.2, 0.08659],
    [-0.6892, 15.1916, 0.087],
    [-0.8886, 15.2441, 0.09692],
    [-0.7621, 15.2616, 0.08414],
    [-0.7856, 15.2604, 0.08439],
    [-0.8089, 15.2605, 0.08464],
    [-0.8322, 15.2619, 0.08490],
    [-0.8554, 15.2645, 0.08516],
    [-0.8785, 15.2684, 0.08543],
    [-0.9015, 15.2737, 0.08570],
    [-0.9243, 15.2801, 0.08597],
    [-0.9471, 15.2877, 0.08625],
    [-0.9697, 15.2965, 0.08653],
    [-0.9921, 15.3062, 0.08682],
    [-1.0144, 15.3169, 0.08711],
    [-1.0365, 15.3285, 0.08741],
    [-1.0584, 15.3408, 0.08771],
    [-1.0801, 15.3540, 0.08802],
    [-1.1017, 15.3679, 0.08833],
    [-1.1230, 15.3825, 0.08865],
    [-1.1441, 15.3978, 0.08898],
    [-1.1649, 15.4137, 0.08931],
    [-1.1856, 15.4302, 0.08964],
    [-1.2060, 15.4473, 0.08998],
    [-1.2261, 15.4650, 0.09033],
    [-1.2460, 15.4832, 0.09068],
    [-1.2656, 15.5019, 0.09103],
    [-1.2849, 15.5210, 0.09139],
    [-1.3040, 15.5407, 0.09176],
    [-1.3228, 15.5608, 0.09213],
    [-1.3414, 15.5814, 0.09251],
    [-1.3596, 15.6023, 0.09289],
    [-1.3776, 15.6237, 0.09327],
    [-1.3953, 15.6455, 0.09366],
    [-1.4126, 15.6677, 0.09406],
    [-1.4297, 15.6903, 0.09445],
    [-1.4464, 15.7133, 0.09486],
    [-1.4629, 15.7368, 0.09526],
    [-1.4790, 15.7606, 0.09567],
    [-1.4947, 15.7848, 0.09609],
    [-1.5101, 15.8094, 0.09651],
    [-1.5252, 15.8344, 0.09693],
    [-1.5399, 15.8597, 0.09735],
    [-1.5542, 15.8855, 0.09778],
    [-1.5681, 15.9116, 0.09821],
    [-1.5817, 15.9381, 0.09864],
    [-1.5948, 15.9651, 0.09907],
    [-1.6076, 15.9925, 0.09951],
    [-1.6199, 16.0205, 0.09994],
    [-1.6318, 16.0490, 0.10038],
    [-1.6433, 16.0781, 0.10082],
    [-1.6544, 16.1078, 0.10126],
    [-1.6651, 16.1381, 0.10170],
    [-1.6753, 16.1692, 0.10214],
    [-1.6851, 16.2009, 0.10259],
    [-1.6944, 16.2333, 0.10303],
    [-1.7032, 16.2665, 0.10347],
    [-1.7116, 16.3004, 0.10391],
    [-1.7196, 16.3351, 0.10435],
    [-1.7271, 16.3704, 0.10478],
    [-1.7341, 16.4065, 0.10522],
    [-1.7407, 16.4433, 0.10566],
    [-1.7468, 16.4807, 0.10609],
    [-1.7525, 16.5189, 0.10652],
    [-1.7578, 16.5578, 0.10695],
    [-1.7626, 16.5974, 0.10738],
    [-1.7670, 16.6376, 0.10780],
    [-1.7710, 16.6786, 0.10823],
    [-1.7745, 16.7203, 0.10865],
    [-1.7777, 16.7628, 0.10906],
    [-1.7804, 16.8059, 0.10948],
    [-1.7828, 16.8497, 0.10989],
    [-1.7847, 16.8941, 0.11030],
    [-1.7862, 16.9392, 0.11070],
    [-1.7873, 16.9850, 0.11110],
    [-1.7881, 17.0314, 0.11150],
    [-1.7884, 17.0784, 0.11189],
    [-1.7884, 17.1262, 0.11228],
    [-1.7880, 17.1746, 0.11266],
    [-1.7873, 17.2236, 0.11304],
    [-1.7861, 17.2734, 0.11342],
    [-1.7846, 17.3240, 0.11379],
    [-1.7828, 17.3752, 0.11415],
    [-1.7806, 17.4272, 0.11451],
    [-1.7780, 17.4799, 0.11487],
    [-1.7751, 17.5334, 0.11522],
    [-1.7719, 17.5877, 0.11556],
    [-1.7684, 17.6427, 0.11590],
    [-1.7645, 17.6985, 0.11623],
    [-1.7604, 17.7551, 0.11656],
    [-1.7559, 17.8124, 0.11688],
    [-1.7511, 17.8704, 0.11720],
    [-1.7461, 17.9292, 0.11751],
    [-1.7408, 17.9887, 0.11781],
    [-1.7352, 18.0488, 0.11811],
    [-1.7293, 18.1096, 0.11841],
    [-1.7232, 18.1710, 0.11869],
    [-1.7168, 18.2330, 0.11898],
    [-1.7102, 18.2955, 0.11925],
    [-1.7033, 18.3586, 0.11952],
    [-1.6962, 18.4221, 0.11979],
    [-1.6888, 18.4860, 0.12005],
    [-1.6811, 18.5502, 0.12030],
    [-1.6732, 18.6148, 0.12055],
    [-1.6651, 18.6795, 0.12079],
    [-1.6568, 18.7445, 0.12102],
    [-1.6482, 18.8095, 0.12125],
    [-1.6394, 18.8746, 0.12148],
    [-1.6304, 18.9398, 0.12170],
    [-1.6211, 19.0050, 0.12191],
    [-1.6116, 19.0701, 0.12212],
    [-1.6020, 19.1351, 0.12233],
    [-1.5921, 19.2000, 0.12253],
    [-1.5821, 19.2648, 0.12272],
    [-1.5719, 19.3294, 0.12291],
    [-1.5615, 19.3937, 0.12310],
    [-1.5510, 19.4578, 0.12328],
    [-1.5403, 19.5217, 0.12346],
    [-1.5294, 19.5853, 0.12363],
    [-1.5185, 19.6486, 0.12380],
    [-1.5074, 19.7117, 0.12396],
    [-1.4961, 19.7744, 0.12412],
    [-1.4848, 19.8367, 0.12428],
    [-1.4733, 19.8987, 0.12443],
    [-1.4617, 19.9603, 0.12458],
    [-1.4500, 20.0215, 0.12473],
    [-1.4382, 20.0823, 0.12487],
    [-1.4263, 20.1427, 0.12501],
    [-1.4143, 20.2026, 0.12514],
    [-1.4022, 20.2621, 0.12528],
    [-1.3900, 20.3211, 0.12541],
    [-1.3777, 20.3796, 0.12554],
    [-1.3653, 20.4376, 0.12567],
    [-1.3529, 20.4951, 0.12579],
    [-1.3403, 20.5521, 0.12591],
    [-1.3277, 20.6085, 0.12603],
    [-1.3149, 20.6644, 0.12615],
    [-1.3021, 20.7197, 0.12627],
    [-1.2892, 20.7745, 0.12638],
    [-1.2762, 20.8287, 0.12650],
    [-1.2631, 20.8824, 0.12661],
    [-1.2499, 20.9355, 0.12672],
    [-1.2366, 20.9881, 0.12683],
    [-1.2233, 21.0400, 0.12694],
    [-1.2098, 21.0914, 0.12704],
    [-1.1962, 21.1423, 0.12715],
    [-1.1826, 21.1925, 0.12726],
    [-1.1688, 21.2423, 0.12736],
    [-1.1550, 21.2914, 0.12746],
    [-1.1410, 21.3400, 0.12756],
    [-1.1270, 21.3880, 0.12767],
    [-1.1129, 21.4354, 0.12777],
    [-1.0986, 21.4822, 0.12787],
    [-1.0843, 21.5285, 0.12797],
    [-1.0699, 21.5742, 0.12807],
    [-1.0553, 21.6193, 0.12816],
    [-1.0407, 21.6638, 0.12826],
    [-1.0260, 21.7077, 0.12836],
    [-1.0112, 21.7510, 0.12845],
    [-0.9962, 21.7937, 0.12855],
    [-0.9812, 21.8358, 0.12864],
    [-0.9661, 21.8773, 0.12874],
    [-0.9509, 21.9182, 0.12883],
    [-0.9356, 21.9585, 0.12893],
    [-0.9202, 21.9982, 0.12902],
    [-0.9048, 22.0374, 0.12911],
    [-0.8892, 22.0760, 0.12920],
    [-0.8735, 22.1140, 0.12930],
    [-0.8578, 22.1514, 0.12939],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948]
  ] as [number, number, number][];

  const coeficientesFemininos = [
    [-0.0631, 13.3363, 0.09272],
    [0.3448, 14.5679, 0.09556],
    [0.1749, 15.7679, 0.09371],
    [0.0643, 16.3574, 0.09254],
    [-0.0191, 16.6703, 0.09166],
    [-0.0864, 16.8386, 0.09096],
    [-0.1429, 16.9083, 0.09036],
    [-0.1916, 16.902, 0.08984],
    [-0.2344, 16.8404, 0.08939],
    [-0.2725, 16.7406, 0.08898],
    [-0.3068, 16.6184, 0.08861],
    [-0.3381, 16.4875, 0.08828],
    [-0.3667, 16.3568, 0.08797],
    [-0.3932, 16.2311, 0.08768],
    [-0.4177, 16.1128, 0.08741],
    [-0.4407, 16.0028, 0.08716],
    [-0.4623, 15.9017, 0.08693],
    [-0.4825, 15.8096, 0.08671],
    [-0.5017, 15.7263, 0.0865],
    [-0.5199, 15.6517, 0.0863],
    [-0.5372, 15.5855, 0.08612],
    [-0.5537, 15.5278, 0.08594],
    [-0.5695, 15.4787, 0.08577],
    [-0.5846, 15.438, 0.0856],
    [-0.5684, 15.6881, 0.08454],
    [-0.5684, 15.659, 0.08452],
    [-0.5684, 15.6308, 0.08449],
    [-0.5684, 15.6037, 0.08446],
    [-0.5684, 15.5777, 0.08444],
    [-0.5684, 15.5523, 0.08443],
    [-0.5684, 15.5276, 0.08444],
    [-0.5684, 15.5034, 0.08448],
    [-0.5684, 15.4798, 0.08455],
    [-0.5684, 15.4572, 0.08467],
    [-0.5684, 15.4356, 0.08484],
    [-0.5684, 15.4155, 0.08506],
    [-0.5684, 15.3968, 0.08535],
    [-0.5684, 15.3796, 0.08569],
    [-0.5684, 15.3638, 0.08609],
    [-0.5684, 15.3493, 0.08654],
    [-0.5684, 15.3358, 0.08704],
    [-0.5684, 15.3233, 0.08757],
    [-0.5684, 15.3116, 0.08813],
    [-0.5684, 15.3007, 0.08872],
    [-0.5684, 15.2905, 0.08931],
    [-0.5684, 15.2814, 0.08991],
    [-0.5684, 15.2732, 0.09051],
    [-0.5684, 15.2661, 0.0911],
    [-0.5684, 15.2602, 0.09168],
    [-0.5684, 15.2556, 0.09227],
    [-0.5684, 15.2523, 0.09286],
    [-0.5684, 15.2503, 0.09345],
    [-0.5684, 15.2496, 0.09403],
    [-0.5684, 15.2502, 0.0946],
    [-0.5684, 15.2519, 0.09515],
    [-0.5684, 15.2544, 0.09568],
    [-0.5684, 15.2575, 0.09618],
    [-0.5684, 15.2612, 0.09665],
    [-0.5684, 15.2653, 0.09709],
    [-0.5684, 15.2698, 0.0975],
    [-0.5684, 15.2747, 0.09789],
    [-0.8886, 15.2441, 0.09692],
    [-0.9068, 15.2434, 0.09738],
    [-0.9248, 15.2433, 0.09783],
    [-0.9427, 15.2438, 0.09829],
    [-0.9605, 15.2448, 0.09875],
    [-0.9780, 15.2464, 0.09920],
    [-0.9954, 15.2487, 0.09966],
    [-1.0126, 15.2516, 0.10012],
    [-1.0296, 15.2551, 0.10058],
    [-1.0464, 15.2592, 0.10104],
    [-1.0630, 15.2641, 0.10149],
    [-1.0794, 15.2697, 0.10195],
    [-1.0956, 15.2760, 0.10241],
    [-1.1115, 15.2831, 0.10287],
    [-1.1272, 15.2911, 0.10333],
    [-1.1427, 15.2998, 0.10379],
    [-1.1579, 15.3095, 0.10425],
    [-1.1728, 15.3200, 0.10471],
    [-1.1875, 15.3314, 0.10517],
    [-1.2019, 15.3439, 0.10562],
    [-1.2160, 15.3572, 0.10608],
    [-1.2298, 15.3717, 0.10654],
    [-1.2433, 15.3871, 0.10700],
    [-1.2565, 15.4036, 0.10746],
    [-1.2693, 15.4211, 0.10792],
    [-1.2819, 15.4397, 0.10837],
    [-1.2941, 15.4593, 0.10883],
    [-1.3060, 15.4798, 0.10929],
    [-1.3175, 15.5014, 0.10974],
    [-1.3287, 15.5240, 0.11020],
    [-1.3395, 15.5476, 0.11065],
    [-1.3499, 15.5723, 0.11110],
    [-1.3600, 15.5979, 0.11156],
    [-1.3697, 15.6246, 0.11201],
    [-1.3790, 15.6523, 0.11246],
    [-1.3880, 15.6810, 0.11291],
    [-1.3966, 15.7107, 0.11335],
    [-1.4047, 15.7415, 0.11380],
    [-1.4125, 15.7732, 0.11424],
    [-1.4199, 15.8058, 0.11469],
    [-1.4270, 15.8394, 0.11513],
    [-1.4336, 15.8738, 0.11557],
    [-1.4398, 15.9090, 0.11601],
    [-1.4456, 15.9451, 0.11644],
    [-1.4511, 15.9818, 0.11688],
    [-1.4561, 16.0194, 0.11731],
    [-1.4607, 16.0575, 0.11774],
    [-1.4650, 16.0964, 0.11816],
    [-1.4688, 16.1358, 0.11859],
    [-1.4723, 16.1759, 0.11901],
    [-1.4753, 16.2166, 0.11943],
    [-1.4780, 16.2580, 0.11985],
    [-1.4803, 16.2999, 0.12026],
    [-1.4823, 16.3425, 0.12067],
    [-1.4838, 16.3858, 0.12108],
    [-1.4850, 16.4298, 0.12148],
    [-1.4859, 16.4746, 0.12188],
    [-1.4864, 16.5200, 0.12228],
    [-1.4866, 16.5663, 0.12268],
    [-1.4864, 16.6133, 0.12307],
    [-1.4859, 16.6612, 0.12346],
    [-1.4851, 16.7100, 0.12384],
    [-1.4839, 16.7595, 0.12422],
    [-1.4825, 16.8100, 0.12460],
    [-1.4807, 16.8614, 0.12497],
    [-1.4787, 16.9136, 0.12534],
    [-1.4763, 16.9667, 0.12571],
    [-1.4737, 17.0208, 0.12607],
    [-1.4708, 17.0757, 0.12643],
    [-1.4677, 17.1316, 0.12678],
    [-1.4642, 17.1883, 0.12713],
    [-1.4606, 17.2459, 0.12748],
    [-1.4567, 17.3044, 0.12782],
    [-1.4526, 17.3637, 0.12816],
    [-1.4482, 17.4238, 0.12849],
    [-1.4436, 17.4847, 0.12882],
    [-1.4389, 17.5464, 0.12914],
    [-1.4339, 17.6088, 0.12946],
    [-1.4288, 17.6719, 0.12978],
    [-1.4235, 17.7357, 0.13009],
    [-1.4180, 17.8001, 0.13040],
    [-1.4123, 17.8651, 0.13070],
    [-1.4065, 17.9306, 0.13099],
    [-1.4006, 17.9966, 0.13129],
    [-1.3945, 18.0630, 0.13158],
    [-1.3883, 18.1297, 0.13186],
    [-1.3819, 18.1967, 0.13214],
    [-1.3755, 18.2639, 0.13241],
    [-1.3689, 18.3312, 0.13268],
    [-1.3621, 18.3986, 0.13295],
    [-1.3553, 18.4660, 0.13321],
    [-1.3483, 18.5333, 0.13347],
    [-1.3413, 18.6006, 0.13372],
    [-1.3341, 18.6677, 0.13397],
    [-1.3269, 18.7346, 0.13421],
    [-1.3195, 18.8012, 0.13445],
    [-1.3121, 18.8675, 0.13469],
    [-1.3046, 18.9335, 0.13492],
    [-1.2970, 18.9991, 0.13514],
    [-1.2894, 19.0642, 0.13537],
    [-1.2816, 19.1289, 0.13559],
    [-1.2739, 19.1931, 0.13580],
    [-1.2661, 19.2567, 0.13601],
    [-1.2583, 19.3197, 0.13622],
    [-1.2504, 19.3820, 0.13642],
    [-1.2425, 19.4437, 0.13662],
    [-1.2345, 19.5045, 0.13681],
    [-1.2266, 19.5647, 0.13700],
    [-1.2186, 19.6240, 0.13719],
    [-1.2107, 19.6824, 0.13738],
    [-1.2027, 19.7400, 0.13756],
    [-1.1947, 19.7966, 0.13774],
    [-1.1867, 19.8523, 0.13791],
    [-1.1788, 19.9070, 0.13808],
    [-1.1708, 19.9607, 0.13825],
    [-1.1629, 20.0133, 0.13841],
    [-1.1549, 20.0648, 0.13858],
    [-1.1470, 20.1152, 0.13873],
    [-1.1390, 20.1644, 0.13889],
    [-1.1311, 20.2125, 0.13904],
    [-1.1232, 20.2595, 0.13920],
    [-1.1153, 20.3053, 0.13934],
    [-1.1074, 20.3499, 0.13949],
    [-1.0996, 20.3934, 0.13963],
    [-1.0917, 20.4357, 0.13977],
    [-1.0838, 20.4769, 0.13991],
    [-1.0760, 20.5170, 0.14005],
    [-1.0681, 20.5560, 0.14018],
    [-1.0603, 20.5938, 0.14031],
    [-1.0525, 20.6306, 0.14044],
    [-1.0447, 20.6663, 0.14057],
    [-1.0368, 20.7008, 0.14070],
    [-1.0290, 20.7344, 0.14082],
    [-1.0212, 20.7668, 0.14094],
    [-1.0134, 20.7982, 0.14106],
    [-1.0055, 20.8286, 0.14118],
    [-0.9962, 21.7937, 0.12855],
    [-0.9812, 21.8358, 0.12864],
    [-0.9661, 21.8773, 0.12874],
    [-0.9509, 21.9182, 0.12883],
    [-0.9356, 21.9585, 0.12893],
    [-0.9202, 21.9982, 0.12902],
    [-0.9048, 22.0374, 0.12911],
    [-0.8892, 22.0760, 0.12920],
    [-0.8735, 22.1140, 0.12930],
    [-0.8578, 22.1514, 0.12939],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948],
    [-0.8419, 22.1883, 0.12948]
  ] as [number, number, number][];

  const coefficients = gender === 'male' ? coeficientesMasculinos : coeficientesFemininos;
  const index = Math.min(Math.floor(ageInMonths), coefficients.length - 1);
  
  const result = coefficients[index];
  if (!result || result.length !== 3) {
    console.error('Invalid coefficients for age:', ageInMonths);
    return [0, 16, 0.1];
  }
  
  return result;
};

export const getClassificationColor = (classification: string): string => {
  switch (classification) {
    case "Magreza grave":
    case "Magreza":
      return "text-red-600";
    case "Eutrófico":
      return "text-green-600";
    case "Risco de sobrepeso":
      return "text-yellow-600";
    case "Sobrepeso":
      return "text-orange-600";
    case "Obesidade":
    case "Obesidade grave":
      return "text-red-600";
    default:
      return "text-gray-600";
  }
};
