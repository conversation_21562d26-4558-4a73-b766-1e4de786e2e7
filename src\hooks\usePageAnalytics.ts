import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { useSiteAnalytics } from './useMedicationAnalytics';
import { createThrottledFunction, useCircuitBreaker } from '@/utils/circuitBreaker';

/**
 * Hook para tracking automático de page views em todo o site
 * DESABILITADO PARA TESTE - Rastreamento temporariamente removido
 */
export const usePageAnalytics = () => {
  // ANALYTICS DESABILITADO PARA TESTE
  console.log('📊 [Analytics] Sistema de analytics próprio DESABILITADO para teste');

  // Hook vazio - não faz nenhum tracking
  return;

  // Tracking de tempo na página (quando o usuário sai) - DESABILITADO para evitar duplicação
  // useEffect(() => {
  //   const startTime = Date.now();

  //   return () => {
  //     const timeSpent = Date.now() - startTime;

  //     // Só trackear se ficou mais de 3 segundos na página
  //     if (timeSpent > 3000) {
  //       // TODO: Implementar tracking de tempo de permanência como evento separado
  //     }
  //   };
  // }, [location.pathname]);
};

/**
 * Hook para tracking de eventos específicos de páginas
 * DESABILITADO PARA TESTE
 */
export const usePageEventTracking = () => {
  console.log('📊 [Analytics] Event tracking DESABILITADO para teste');

  // Retornar funções vazias para manter compatibilidade
  return {
    trackScrollDepth: () => {},
    trackExternalLink: () => {},
    trackDownload: () => {}
  };
};
