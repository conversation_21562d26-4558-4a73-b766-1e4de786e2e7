import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Flag } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface QuestionReportDialogProps {
  questionId: string;
  userId: string;
  children?: React.ReactNode;
}

export const QuestionReportDialog = ({ questionId, userId, children }: QuestionReportDialogProps) => {
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async () => {
    if (!message.trim()) {
      toast({
        title: "Erro",
        description: "Por favor, descreva o problema encontrado.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const { error } = await supabase
        .from("question_reports")
        .insert([
          {
            question_id: questionId,
            user_id: userId,
            message: message.trim(),
          },
        ]);

      if (error) throw error;

      toast({
        title: "Report enviado",
        description: "Obrigado por nos ajudar a melhorar a plataforma!",
      });

      setMessage("");
      setOpen(false);
    } catch (error) {
      toast({
        title: "Erro ao enviar report",
        description: "Não foi possível enviar seu report. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button
            variant="outline"
            size="sm"
            className="flex gap-2"
          >
            <Flag className="h-4 w-4" />
            Reportar
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Reportar Questão</DialogTitle>
          <DialogDescription>
            Descreva o problema encontrado nesta questão. Sua contribuição nos ajuda a melhorar a plataforma.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <Textarea
            placeholder="Descreva o problema encontrado..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="min-h-[100px]"
          />
        </div>
        <DialogFooter>
          <Button
            type="submit"
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? "Enviando..." : "Enviar Report"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};