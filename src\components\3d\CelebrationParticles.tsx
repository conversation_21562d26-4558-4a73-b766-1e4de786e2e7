import React, { useCallback } from "react";
import Particles from "@tsparticles/react";
import { loadBasic } from "@tsparticles/basic";

interface CelebrationParticlesProps {
  isActive: boolean;
  intensity?: 'low' | 'medium' | 'high';
  colors?: string[];
}

export const CelebrationParticles: React.FC<CelebrationParticlesProps> = ({
  isActive,
  intensity = 'medium',
  colors = ['#3b82f6', '#8b5cf6', '#ec4899', '#10b981', '#f59e0b']
}) => {
  const particlesInit = useCallback(async (engine: any) => {
    await loadBasic(engine);
  }, []);

  if (!isActive) return null;

  const getParticleCount = () => {
    switch (intensity) {
      case 'low': return 50;
      case 'medium': return 100;
      case 'high': return 200;
      default: return 100;
    }
  };

  return (
    <Particles
      id="celebration"
      init={particlesInit}
      className="absolute inset-0 w-full h-full pointer-events-none"
      options={{
        background: {
          color: {
            value: "transparent",
          },
        },
        fpsLimit: 120,
        particles: {
          color: {
            value: colors,
          },
          move: {
            direction: "top",
            enable: true,
            outModes: {
              default: "out",
            },
            random: true,
            speed: { min: 2, max: 6 },
            straight: false,
          },
          number: {
            density: {
              enable: true,
              area: 800,
            },
            value: getParticleCount(),
          },
          opacity: {
            value: { min: 0.3, max: 1 },
            animation: {
              enable: true,
              speed: 2,
              sync: false,
            },
          },
          shape: {
            type: ["circle", "triangle", "square"],
          },
          size: {
            value: { min: 2, max: 8 },
            animation: {
              enable: true,
              speed: 3,
              sync: false,
            },
          },
          rotate: {
            value: { min: 0, max: 360 },
            animation: {
              enable: true,
              speed: 5,
              sync: false,
            },
          },
          life: {
            duration: {
              sync: false,
              value: { min: 1, max: 3 },
            },
            count: 1,
          },
        },
        emitters: [
          {
            direction: "top",
            life: {
              count: 0,
              duration: 0.1,
              delay: 0,
            },
            rate: {
              delay: 0.1,
              quantity: 10,
            },
            position: {
              x: 50,
              y: 100,
            },
          },
          {
            direction: "top-right",
            life: {
              count: 0,
              duration: 0.1,
              delay: 0.2,
            },
            rate: {
              delay: 0.1,
              quantity: 8,
            },
            position: {
              x: 30,
              y: 100,
            },
          },
          {
            direction: "top-left",
            life: {
              count: 0,
              duration: 0.1,
              delay: 0.2,
            },
            rate: {
              delay: 0.1,
              quantity: 8,
            },
            position: {
              x: 70,
              y: 100,
            },
          },
        ],
        detectRetina: true,
      }}
    />
  );
};

// Componente para confetti específico
export const ConfettiExplosion: React.FC<{ isActive: boolean }> = ({ isActive }) => {
  const particlesInit = useCallback(async (engine: any) => {
    await loadBasic(engine);
  }, []);

  if (!isActive) return null;

  return (
    <Particles
      id="confetti"
      init={particlesInit}
      className="absolute inset-0 w-full h-full pointer-events-none"
      options={{
        background: {
          color: {
            value: "transparent",
          },
        },
        fpsLimit: 120,
        particles: {
          color: {
            value: ["#ff6b6b", "#4ecdc4", "#45b7d1", "#96ceb4", "#feca57", "#ff9ff3", "#54a0ff"],
          },
          move: {
            direction: "bottom",
            enable: true,
            outModes: {
              default: "out",
            },
            random: true,
            speed: { min: 3, max: 8 },
            straight: false,
            gravity: {
              enable: true,
              acceleration: 9.81,
            },
          },
          number: {
            value: 150,
          },
          opacity: {
            value: { min: 0.5, max: 1 },
          },
          shape: {
            type: ["square", "triangle"],
          },
          size: {
            value: { min: 3, max: 8 },
          },
          rotate: {
            value: { min: 0, max: 360 },
            animation: {
              enable: true,
              speed: 10,
              sync: false,
            },
          },
          life: {
            duration: {
              sync: false,
              value: { min: 2, max: 4 },
            },
            count: 1,
          },
        },
        emitters: {
          direction: "top",
          life: {
            count: 1,
            duration: 0.1,
            delay: 0,
          },
          rate: {
            delay: 0.05,
            quantity: 50,
          },
          position: {
            x: 50,
            y: 0,
          },
          size: {
            width: 100,
            height: 0,
          },
        },
        detectRetina: true,
      }}
    />
  );
};

// Componente para estrelas cadentes
export const ShootingStars: React.FC<{ isActive: boolean }> = ({ isActive }) => {
  const particlesInit = useCallback(async (engine: any) => {
    await loadBasic(engine);
  }, []);

  if (!isActive) return null;

  return (
    <Particles
      id="shooting-stars"
      init={particlesInit}
      className="absolute inset-0 w-full h-full pointer-events-none"
      options={{
        background: {
          color: {
            value: "transparent",
          },
        },
        fpsLimit: 60,
        particles: {
          color: {
            value: ["#ffffff", "#ffd700", "#87ceeb"],
          },
          move: {
            direction: "bottom-right",
            enable: true,
            outModes: {
              default: "out",
            },
            speed: { min: 10, max: 20 },
            straight: true,
          },
          number: {
            value: 5,
          },
          opacity: {
            value: { min: 0.3, max: 1 },
            animation: {
              enable: true,
              speed: 3,
              sync: false,
            },
          },
          shape: {
            type: "circle",
          },
          size: {
            value: { min: 1, max: 3 },
          },
          trail: {
            enable: true,
            length: 20,
            fillColor: {
              value: "#ffffff",
            },
          },
          life: {
            duration: {
              sync: false,
              value: { min: 1, max: 2 },
            },
            count: 1,
          },
        },
        emitters: {
          direction: "bottom-right",
          life: {
            count: 0,
            duration: 0.1,
            delay: 2,
          },
          rate: {
            delay: 3,
            quantity: 1,
          },
          position: {
            x: 0,
            y: 0,
          },
        },
        detectRetina: true,
      }}
    />
  );
};
