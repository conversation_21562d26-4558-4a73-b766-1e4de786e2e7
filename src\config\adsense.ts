/**
 * Configuração do Google AdSense
 * Centralize todos os slots de anúncio aqui
 */

export const ADSENSE_CONFIG = {
  // ID do cliente AdSense
  CLIENT_ID: 'ca-pub-4018898302361000',
  
  // Slots de anúncio por posição
  // IMPORTANTE: Substitua pelos slots reais criados no painel do AdSense
  AD_SLOTS: {
    // Página inicial
    HOME_HEADER: '1234567890',
    HOME_CONTENT: '1234567891',
    HOME_SIDEBAR: '1234567892',
    HOME_FOOTER: '1234567893',
    
    // Páginas de medicamentos
    MEDICATION_LIST: '1234567894',
    MEDICATION_DETAIL: '1234567895',
    MEDICATION_SIDEBAR: '1234567896',
    
    // Calculadoras
    CALCULATOR_HEADER: '1234567897',
    CALCULATOR_RESULT: '1234567898',
    
    // Puericultura
    CHILDCARE_CONTENT: '1234567899',
    CHILDCARE_SIDEBAR: '1234567900',
    
    // Condutas e manejos
    CONDUCTS_LIST: '1234567901',
    CONDUCTS_DETAIL: '1234567902',
    
    // Fluxogramas
    FLOWCHART_HEADER: '1234567903',
    FLOWCHART_FOOTER: '1234567904',
    
    // PediDrop
    PEDIDROP_CONTENT: '1234567905',
    PEDIDROP_SIDEBAR: '1234567906',
    
    // Genérico para outras páginas
    GENERIC_CONTENT: '1234567907',
    GENERIC_SIDEBAR: '1234567908',
    GENERIC_FOOTER: '1234567909',
  },
  
  // Configurações por tipo de anúncio
  AD_FORMATS: {
    RESPONSIVE: 'auto',
    BANNER: 'horizontal',
    SQUARE: 'rectangle',
    VERTICAL: 'vertical',
  },
  
  // Configurações de performance
  PERFORMANCE: {
    // Carregar AdSense após interação do usuário
    LOAD_ON_INTERACTION: true,
    // Tempo de fallback para carregar (ms)
    FALLBACK_LOAD_TIME: 10000,
    // Mostrar anúncios apenas após tempo mínimo na página (ms)
    MIN_TIME_ON_PAGE: 3000,
  },
  
  // Configurações por página
  PAGE_CONFIGS: {
    home: {
      showHeader: true,
      showContent: true,
      showSidebar: false, // Não tem sidebar na home
      showFooter: true,
      adFrequency: 8, // A cada 8 cards
    },
    medications: {
      showHeader: true,
      showContent: true,
      showSidebar: true,
      showFooter: true,
      adFrequency: 6, // A cada 6 medicamentos
    },
    calculators: {
      showHeader: false, // Não atrapalhar cálculos
      showContent: true,
      showSidebar: false,
      showFooter: true,
      adFrequency: 3, // A cada 3 calculadoras
    },
    childcare: {
      showHeader: true,
      showContent: true,
      showSidebar: true,
      showFooter: true,
      adFrequency: 5, // A cada 5 itens
    },
    conducts: {
      showHeader: true,
      showContent: true,
      showSidebar: true,
      showFooter: true,
      adFrequency: 4, // A cada 4 condutas
    },
    flowcharts: {
      showHeader: false, // Não atrapalhar visualização
      showContent: false,
      showSidebar: false,
      showFooter: true,
      adFrequency: 0, // Sem anúncios entre fluxogramas
    },
    pedidrop: {
      showHeader: true,
      showContent: true,
      showSidebar: true,
      showFooter: true,
      adFrequency: 2, // A cada 2 posts
    },
  },
  
  // URLs onde NÃO mostrar anúncios
  EXCLUDED_PATHS: [
    '/admin',
    '/login',
    '/register',
    '/reset-password',
    '/privacy-policy',
    '/terms',
    '/maintenance',
  ],
  
  // Configurações de desenvolvimento
  DEVELOPMENT: {
    // Mostrar anúncios em desenvolvimento
    SHOW_IN_DEV: false,
    // Logs detalhados
    VERBOSE_LOGGING: true,
    // Simular carregamento de anúncios
    SIMULATE_ADS: true,
  },
};

/**
 * Função para obter slot de anúncio baseado na página e posição
 */
export const getAdSlot = (page: string, position: string): string => {
  const key = `${page.toUpperCase()}_${position.toUpperCase()}` as keyof typeof ADSENSE_CONFIG.AD_SLOTS;
  return ADSENSE_CONFIG.AD_SLOTS[key] || ADSENSE_CONFIG.AD_SLOTS.GENERIC_CONTENT;
};

/**
 * Função para verificar se deve mostrar anúncios na página atual
 */
export const shouldShowAdsOnPage = (pathname: string): boolean => {
  // Verificar se está em uma página excluída
  return !ADSENSE_CONFIG.EXCLUDED_PATHS.some(path => pathname.startsWith(path));
};

/**
 * Função para obter configuração da página
 */
export const getPageAdConfig = (pageName: string) => {
  return ADSENSE_CONFIG.PAGE_CONFIGS[pageName as keyof typeof ADSENSE_CONFIG.PAGE_CONFIGS] || {
    showHeader: true,
    showContent: true,
    showSidebar: true,
    showFooter: true,
    adFrequency: 5,
  };
};
