
import { AgeInput } from "./AgeInput";
import { WeightInput } from "./WeightInput";

interface PatientInfoProps {
  weight: number;
  onWeightChange: (weight: number) => void;
  onWeightCommit: (weight: number) => void;
  age: number;
  onAgeChange: (age: number) => void;
  onAgeCommit: (age: number) => void;
}

export const PatientInfo = ({
  weight,
  onWeightChange,
  onWeightCommit,
  age,
  onAgeChange,
  onAgeCommit,
}: PatientInfoProps) => {
  return (
    <div className="space-y-6 bg-gradient-to-br from-red-50/90 via-white/80 to-red-50/90 dark:from-red-900/20 dark:via-slate-800/70 dark:to-red-900/10 p-6 rounded-xl backdrop-blur-sm border border-red-100 dark:border-red-900/30">
      <WeightInput
        value={weight}
        onChange={onWeightChange}
        onCommit={onWeightCommit}
      />
      <AgeInput
        value={age}
        onChange={onAgeChange}
        onCommit={onAgeCommit}
      />
    </div>
  );
};
