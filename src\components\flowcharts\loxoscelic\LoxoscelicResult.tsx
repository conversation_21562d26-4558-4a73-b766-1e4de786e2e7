import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Stage } from "./types";

interface LoxoscelicResultProps {
  stage: Stage;
  onBack: () => void;
}

export function LoxoscelicResult({ stage, onBack }: LoxoscelicResultProps) {
  const getResultContent = () => {
    switch (stage) {
      case "cutaneous-mild":
        return {
          title: "Forma Cutânea - Grau Leve",
          characteristics: "Lesão incaracterística: eritema, prurido, bolha de conteúdo seroso com ou sem enduração e dor de pequena intensidade.",
          treatment: [
            "Tratamento sintomático (analgésico, anti-histamínico, corticóide tópico)",
            "Orientar retorno para reavaliação a cada 12 horas"
          ]
        };
      case "cutaneous-moderate":
        return {
          title: "Forma Cutânea - Grau Moderado",
          characteristics: "Lesão provável ou característica com placa marmórea < 3 cm, com ou sem comprometimento do estado geral e sem sinal de hemólise.\n\nLesão provável: presença de eritema, equimose com ou sem enduração, exantema.\n\nPode apresentar alteração do estado geral: cefaléia, febre nas primeiras 24h, mialgia, náusea, vômito, exantema (rash).",
          treatment: [
            "Prednisona por 5 dias:",
            "- Adultos: 40 mg/dia",
            "- Crianças: 0,5 a 1 mg/kg/dia (máximo 40 mg/dia)",
            "Tratamento sintomático (analgésico, anti-histamínico, corticóide tópico)"
          ]
        };
      case "cutaneous-severe":
        return {
          title: "Forma Cutânea - Grau Grave",
          characteristics: "Lesão característica com placa marmórea > 3 cm, com ou sem comprometimento do estado geral e sem sinal de hemólise.\n\nLesão característica: eritema, enduração, palidez ou placa marmórea, bolha, necrose.",
          treatment: [
            "Administrar SALox/SAAR IV (5 ampolas)",
            "SALox/SAAR IV: Soro antiloxoscélico OU antiaracnídico intravenoso",
            "Prednisona por 7 dias:",
            "- Adultos: 40 mg/dia",
            "- Crianças: 0,5 a 1 mg/kg/dia (máximo 40 mg/dia)",
            "Tratamento sintomático (analgésico, anti-histamínico, corticóide tópico)"
          ]
        };
      case "cutaneous-hemolytic":
        return {
          title: "Forma Cutâneo-Hemolítica",
          characteristics: "Lesões cutâneas com sinais sistêmicos de hemólise. Presença ou não de lesão significativa e dor.\n\nSinal de hemólise (anemia aguda): palidez cutâneo-mucosa decorrente da anemia, icterícia, urina escura (hemoglobinúria), confirmada na análise laboratorial (no hemograma diminuição da série vermelha, aumento dos reticulócitos, aumento da bilirrubina indireta, DHL, diminuição da haptoglobina).",
          treatment: [
            "Administrar SALox/SAAR IV (10 ampolas)",
            "SALox/SAAR IV: Soro antiloxoscélico OU antiaracnídico intravenoso",
            "Prednisona por 7 dias:",
            "- Adultos: 40 mg/dia",
            "- Crianças: 0,5 a 1 mg/kg/dia (máximo 40 mg/dia)",
            "Tratamento sintomático (analgésico, anti-histamínico, corticóide tópico)",
            "Hidratação adequada para manutenção da perfusão renal"
          ]
        };
      default:
        return {
          title: "Erro",
          characteristics: "Estágio não reconhecido",
          treatment: []
        };
    }
  };

  const content = getResultContent();

  return (
    <div className="space-y-6">
      <Card className="p-6 bg-amber-50/50 border-amber-200">
        <h2 className="text-2xl font-bold text-amber-800 mb-4">{content.title}</h2>
        
        <div className="space-y-4">
          <div>
            <h3 className="font-semibold text-amber-700 mb-2">Características</h3>
            <p className="text-amber-900 whitespace-pre-line">{content.characteristics}</p>
          </div>

          <div>
            <h3 className="font-semibold text-amber-700 mb-2">Conduta</h3>
            <ul className="list-disc list-inside space-y-2">
              {content.treatment.map((item, index) => (
                <li key={index} className="text-amber-900">{item}</li>
              ))}
            </ul>
          </div>
        </div>
      </Card>

      <div className="flex justify-between items-center">
        <Button
          variant="outline"
          onClick={onBack}
          className="text-amber-600 border-amber-600 hover:bg-amber-50"
        >
          Voltar
        </Button>
      </div>
    </div>
  );
}