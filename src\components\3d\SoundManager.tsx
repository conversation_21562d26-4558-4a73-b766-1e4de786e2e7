import { useEffect, useRef } from "react";

interface SoundManagerProps {
  isEnabled: boolean;
  currentStage: string;
}

export const SoundManager: React.FC<SoundManagerProps> = ({ isEnabled, currentStage }) => {
  const audioContextRef = useRef<AudioContext | null>(null);
  const hasPlayedRef = useRef<Set<string>>(new Set());

  useEffect(() => {
    if (!isEnabled) return;

    // Verificar se já tocou este som
    if (hasPlayedRef.current.has(currentStage)) return;
    hasPlayedRef.current.add(currentStage);

    // Inicializar Web Audio API
    const initAudio = async () => {
      try {
        if (!audioContextRef.current) {
          audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
        }

        // Resumir contexto se estiver suspenso
        if (audioContextRef.current.state === 'suspended') {
          await audioContextRef.current.resume();
        }
      } catch (error) {
        console.warn('Audio context initialization failed:', error);
        return;
      }
    };

    initAudio();

    const playTone = (frequency: number, duration: number, type: OscillatorType = 'sine') => {
      if (!audioContextRef.current || audioContextRef.current.state !== 'running') return;

      try {
        const oscillator = audioContextRef.current.createOscillator();
        const gainNode = audioContextRef.current.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContextRef.current.destination);

        oscillator.frequency.setValueAtTime(frequency, audioContextRef.current.currentTime);
        oscillator.type = type;

        gainNode.gain.setValueAtTime(0, audioContextRef.current.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.05, audioContextRef.current.currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.001, audioContextRef.current.currentTime + duration);

        oscillator.start(audioContextRef.current.currentTime);
        oscillator.stop(audioContextRef.current.currentTime + duration);
      } catch (error) {
        console.warn('Audio playback failed:', error);
      }
    };

    const playChord = (frequencies: number[], duration: number) => {
      frequencies.forEach(freq => playTone(freq, duration));
    };

    // Sons baseados no estágio
    switch (currentStage) {
      case 'starfield':
        // Som ambiente espacial
        playTone(220, 2, 'sine');
        setTimeout(() => playTone(330, 1.5, 'sine'), 500);
        break;

      case 'envelope-arrival':
        // Som de chegada mágica
        playChord([523, 659, 784], 1);
        setTimeout(() => playChord([659, 784, 988], 0.8), 300);
        break;

      case 'envelope-opening':
        // Som de abertura
        playTone(440, 0.3);
        setTimeout(() => playTone(554, 0.3), 100);
        setTimeout(() => playTone(659, 0.5), 200);
        break;

      case 'welcome-message':
        // Fanfarra de celebração
        const celebrationNotes = [523, 659, 784, 1047];
        celebrationNotes.forEach((note, index) => {
          setTimeout(() => playTone(note, 0.4), index * 150);
        });
        break;

      case 'counter-animation':
        // Som de contador
        const interval = setInterval(() => {
          playTone(800, 0.1, 'square');
        }, 100);
        
        setTimeout(() => clearInterval(interval), 3000);
        break;

      case 'feature-showcase':
        // Sons de apresentação
        setTimeout(() => playChord([440, 554, 659], 0.6), 0);
        setTimeout(() => playChord([494, 622, 740], 0.6), 1000);
        setTimeout(() => playChord([523, 659, 784], 0.6), 2000);
        break;

      case 'final-cta':
        // Som final épico
        playChord([262, 330, 392, 523], 2);
        setTimeout(() => playChord([330, 415, 494, 659], 1.5), 500);
        break;
    }

    return () => {
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        audioContextRef.current.close();
      }
    };
  }, [currentStage, isEnabled]);

  return null;
};

// Hook para efeitos sonoros rápidos
export const useSoundEffects = (isEnabled: boolean) => {
  const audioContextRef = useRef<AudioContext | null>(null);

  useEffect(() => {
    if (!isEnabled) return;

    if (!audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
    }
  }, [isEnabled]);

  const playClick = () => {
    if (!isEnabled || !audioContextRef.current) return;

    const oscillator = audioContextRef.current.createOscillator();
    const gainNode = audioContextRef.current.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContextRef.current.destination);

    oscillator.frequency.setValueAtTime(800, audioContextRef.current.currentTime);
    oscillator.type = 'square';

    gainNode.gain.setValueAtTime(0, audioContextRef.current.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.1, audioContextRef.current.currentTime + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.001, audioContextRef.current.currentTime + 0.1);

    oscillator.start(audioContextRef.current.currentTime);
    oscillator.stop(audioContextRef.current.currentTime + 0.1);
  };

  const playHover = () => {
    if (!isEnabled || !audioContextRef.current) return;

    const oscillator = audioContextRef.current.createOscillator();
    const gainNode = audioContextRef.current.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContextRef.current.destination);

    oscillator.frequency.setValueAtTime(600, audioContextRef.current.currentTime);
    oscillator.type = 'sine';

    gainNode.gain.setValueAtTime(0, audioContextRef.current.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.05, audioContextRef.current.currentTime + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.001, audioContextRef.current.currentTime + 0.2);

    oscillator.start(audioContextRef.current.currentTime);
    oscillator.stop(audioContextRef.current.currentTime + 0.2);
  };

  const playSuccess = () => {
    if (!isEnabled || !audioContextRef.current) return;

    const notes = [523, 659, 784];
    notes.forEach((note, index) => {
      setTimeout(() => {
        const oscillator = audioContextRef.current!.createOscillator();
        const gainNode = audioContextRef.current!.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContextRef.current!.destination);

        oscillator.frequency.setValueAtTime(note, audioContextRef.current!.currentTime);
        oscillator.type = 'sine';

        gainNode.gain.setValueAtTime(0, audioContextRef.current!.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.1, audioContextRef.current!.currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.001, audioContextRef.current!.currentTime + 0.3);

        oscillator.start(audioContextRef.current!.currentTime);
        oscillator.stop(audioContextRef.current!.currentTime + 0.3);
      }, index * 100);
    });
  };

  return { playClick, playHover, playSuccess };
};
