import HelmetWrapper from "@/components/utils/HelmetWrapper";

interface HomepageSEOProps {
  categories?: string[];
}

export const HomepageSEO = ({
  categories = []
}: HomepageSEOProps) => {

  // Gerar keywords dinâmicas baseadas nos dados
  const generateDynamicKeywords = () => {
    const baseKeywords = [
      "calculadora pediátrica",
      "doses pediátricas",
      "medicamentos pediátricos",
      "prescrição pediátrica",
      "pediatria",
      "cálculo de dose",
      "posologia pediátrica",
      "farmacologia pediátrica",
      "medicina pediátrica",
      "dosagem infantil",
      "bulas profissionais",
      "condutas pediátricas",
      "fluxogramas clínicos",
      "calculadoras médicas",
      "puericultura",
      "intoxicações pediátricas"
    ];

    // Adicionar categorias
    const categoryKeywords = categories.slice(0, 8).map(cat =>
      `${cat.toLowerCase()} pediátrico`
    );

    return [...baseKeywords, ...categoryKeywords].join(", ");
  };

  // Descrição focada no conteúdo médico
  const dynamicDescription = `Calculadora pediátrica completa para profissionais da saúde. Calcule doses de medicamentos pediátricos, acesse prescrições digitais e fluxogramas clínicos com precisão e segurança baseada em evidências científicas.`;

  // Título focado na funcionalidade
  const dynamicTitle = `PedBook - Calculadora de Doses Pediátricas | Medicamentos e Prescrições`;

  // Schema.org enriquecido
  const enrichedSchema = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "PedBook",
    "description": dynamicDescription,
    "url": "https://pedb.com.br",
    "applicationCategory": "MedicalApplication",
    "operatingSystem": "Web",
    "author": {
      "@type": "Organization",
      "name": "MedUnity",
      "url": "https://medunity.com.br"
    },

    "audience": {
      "@type": "MedicalAudience",
      "audienceType": "Pediatras e profissionais da saúde"
    },
    "featureList": [
      "Cálculo automático de doses pediátricas",
      "Prescrições médicas digitais",
      "Fluxogramas clínicos interativos",
      "Curvas de crescimento infantil",
      "Calendário vacinal completo",
      "DNPM (Desenvolvimento Neuropsicomotor)",
      "Medicamentos para amamentação",
      "Calculadoras médicas especializadas"
    ],
    "applicationSubCategory": "Calculadora Médica",
    "downloadUrl": "https://pedb.com.br",
    "screenshot": "https://pedb.com.br/faviconx.webp",
    "softwareVersion": "2.0",
    "dateModified": new Date().toISOString(),
    "inLanguage": "pt-BR",
    "isAccessibleForFree": true,
    "usageInfo": "Gratuito para profissionais da saúde",
    "specialty": "Pediatria"
  };

  // FAQ Schema para perguntas comuns
  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "Como calcular doses pediátricas?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "O PedBook oferece calculadora automática para medicamentos pediátricos. Basta inserir peso e idade da criança para obter a dose correta baseada em protocolos médicos validados."
        }
      },
      {
        "@type": "Question",
        "name": "Quais medicamentos estão disponíveis?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "O PedBook inclui medicamentos essenciais da pediatria organizados por categorias como antibióticos, anti-inflamatórios, broncodilatadores e outros, todos com cálculos automáticos de dose."
        }
      },
      {
        "@type": "Question",
        "name": "O PedBook é gratuito?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Sim, o PedBook é completamente gratuito para profissionais da saúde. Oferecemos acesso livre a todas as funcionalidades de cálculo de doses e prescrições."
        }
      },
      {
        "@type": "Question",
        "name": "O PedBook é confiável para uso clínico?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Sim, todas as fórmulas e dosagens são baseadas em protocolos médicos reconhecidos e literatura científica atualizada. Sempre consulte as diretrizes locais e use seu julgamento clínico."
        }
      }
    ]
  };

  return (
    <HelmetWrapper>
      {/* Título e Descrição Dinâmicos */}
      <title>{dynamicTitle}</title>
      <meta name="description" content={dynamicDescription} />
      <meta name="keywords" content={generateDynamicKeywords()} />

      {/* Meta tags adicionais para SEO */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1" />
      <meta name="googlebot" content="index, follow" />
      <meta name="bingbot" content="index, follow" />
      
      {/* Geo targeting */}
      <meta name="geo.region" content="BR" />
      <meta name="geo.country" content="Brazil" />
      <meta name="language" content="Portuguese" />

      {/* Open Graph Dinâmico */}
      <meta property="og:title" content={dynamicTitle} />
      <meta property="og:description" content={dynamicDescription} />
      <meta property="og:type" content="website" />
      <meta property="og:url" content="https://pedb.com.br" />
      <meta property="og:image" content="https://pedb.com.br/faviconx.webp" />
      <meta property="og:image:alt" content="PedBook - Calculadora Pediátrica" />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />
      <meta property="og:updated_time" content={new Date().toISOString()} />

      {/* Twitter Cards */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={dynamicTitle} />
      <meta name="twitter:description" content={dynamicDescription} />
      <meta name="twitter:image" content="https://pedb.com.br/faviconx.webp" />
      <meta name="twitter:site" content="@pedbook" />

      {/* Canonical */}
      <link rel="canonical" href="https://pedb.com.br" />

      {/* Schema.org Enriquecido */}
      <script type="application/ld+json">
        {JSON.stringify(enrichedSchema)}
      </script>

      {/* FAQ Schema */}
      <script type="application/ld+json">
        {JSON.stringify(faqSchema)}
      </script>

      {/* Preconnect para performance */}
      <link rel="preconnect" href="https://bxedpdmgvgatjdfxgxij.supabase.co" />
      <link rel="dns-prefetch" href="https://bxedpdmgvgatjdfxgxij.supabase.co" />
    </HelmetWrapper>
  );
};
