{"redirects": [{"source": "/:path*", "has": [{"type": "host", "value": "pedb.com.br"}], "destination": "https://www.pedb.com.br/:path*", "permanent": true}], "rewrites": [{"source": "/(robots.txt|sitemap.xml|faviconx.webp|manifest.json)", "destination": "/$1"}, {"source": "/api/:match*", "destination": "/api/:match*"}, {"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)\\.js", "headers": [{"key": "Content-Type", "value": "text/javascript; charset=utf-8"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/assets/(.*)\\.tsx", "headers": [{"key": "Content-Type", "value": "text/javascript; charset=utf-8"}]}, {"source": "/(.*)", "headers": [{"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}]}