import React from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

export const CrotalicSpecialConsiderations = () => {
  return (
    <div className="mt-8 p-6 rounded-xl bg-purple-50/50 border border-purple-200">
      <h2 className="text-xl font-semibold text-purple-800 mb-4">
        Considerações Especiais
      </h2>

      <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="monitoring">
          <AccordionTrigger>Monitoramento</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-4">
              <p>
                Após administração inicial de soro em qualquer quadro clínico:
              </p>
              <ul className="list-disc pl-5 space-y-2">
                <li>Monitorar continuamente a evolução clínica do paciente</li>
                <li>Detectar e tratar precocemente complicações como insuficiência renal ou distúrbios de coagulação</li>
                <li>Reclassificar o quadro clínico, se necessário, e ajustar o tratamento</li>
              </ul>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="discharge">
          <AccordionTrigger>Critérios de Alta</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-4">
              <p>Para liberar o paciente, confirmar:</p>
              <ul className="list-disc pl-5 space-y-2">
                <li>Estabilidade clínica</li>
                <li>Ausência de complicações</li>
                <li>Orientações claras sobre retorno em caso de novos sintomas</li>
              </ul>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="references">
          <AccordionTrigger>Referências</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                BRASIL. Ministério da Saúde. Secretaria de Vigilância em Saúde e Ambiente. Departamento de Doenças Transmissíveis. Guia de Animais Peçonhentos do Brasil. Brasília: Ministério da Saúde, 2024.
              </p>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};