
import { useState } from "react";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tag, AgeRange, WeightRange } from "@/types/dosage";
import { TagTypeSelector } from "./tag-selector/TagTypeSelector";
import { AgeRangeList } from "./tag-selector/AgeRangeList";
import { TagInputForm } from "./tag-selector/TagInputForm";
import { TagDisplay } from "./tag-selector/TagDisplay";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

interface DosageTagSelectorProps {
  tags: Tag[];
  onTagsChange: (tags: Tag[]) => void;
  medicationId: string;
}

export function DosageTagSelector({ tags, onTagsChange, medicationId }: DosageTagSelectorProps) {
  const [newTagName, setNewTagName] = useState("");
  const [newMultiplier, setNewMultiplier] = useState<number>(1);
  const [newMaxValue, setNewMaxValue] = useState<number | undefined>();
  const [tagType, setTagType] = useState<'fixed' | 'multiplier' | 'age' | 'fixed_by_weight' | 'multiplier_by_fixed_age'>('multiplier');
  const [ageRanges, setAgeRanges] = useState<AgeRange[]>([]);
  const [weightRanges, setWeightRanges] = useState<WeightRange[]>([]);
  const [newStartMonth, setNewStartMonth] = useState<number>(0);
  const [newEndMonth, setNewEndMonth] = useState<number>(0);
  const [newAgeValue, setNewAgeValue] = useState<number>(0);
  const [newStartWeight, setNewStartWeight] = useState<number>(0);
  const [newEndWeight, setNewEndWeight] = useState<number>(0);
  const [roundResult, setRoundResult] = useState<boolean>(false);

  const { data: existingTags } = useQuery({
    queryKey: ["medication-tags", medicationId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_medication_tags")
        .select("*")
        .eq("medication_id", medicationId);

      if (error) throw error;

      return data.map(tag => ({
        name: tag.name,
        type: tag.type as 'fixed' | 'multiplier' | 'age' | 'fixed_by_weight' | 'multiplier_by_fixed_age',
        multiplier: tag.multiplier,
        maxValue: tag.max_value,
        roundResult: tag.round_result,
        ...(tag.type === 'age' || tag.type === 'multiplier_by_fixed_age') && {
          ageRanges: [{
            startMonth: tag.start_month || 0,
            endMonth: tag.end_month || 0,
            value: tag.multiplier || 0
          }]
        },
        ...(tag.type === 'fixed_by_weight' && {
          weightRanges: [{
            startWeight: tag.start_weight || 0,
            endWeight: tag.end_weight || 0,
            value: tag.multiplier || 0
          }]
        })
      }));
    },
    enabled: !!medicationId
  });

  const handleAddAgeRange = () => {
    if (newStartMonth > newEndMonth || !newTagName) return;
    
    setAgeRanges([...ageRanges, {
      startMonth: newStartMonth,
      endMonth: newEndMonth,
      value: newAgeValue
    }]);
    
    setNewStartMonth(0);
    setNewEndMonth(0);
    setNewAgeValue(0);
  };

  const handleRemoveAgeRange = (index: number) => {
    setAgeRanges(ageRanges.filter((_, i) => i !== index));
  };

  const handleAddWeightRange = () => {
    if (newStartWeight > newEndWeight || !newTagName) return;
    
    setWeightRanges([...weightRanges, {
      startWeight: newStartWeight,
      endWeight: newEndWeight,
      value: newMultiplier
    }]);
    
    setNewStartWeight(0);
    setNewEndWeight(0);
    setNewMultiplier(0);
  };

  const handleAddTag = () => {
    if (!newTagName) return;
    
    const newTag: Tag = {
      name: newTagName,
      multiplier: newMultiplier,
      maxValue: newMaxValue,
      type: tagType,
      roundResult,
    };

    if (tagType === 'age' || tagType === 'multiplier_by_fixed_age') {
      newTag.ageRanges = [...ageRanges];
    } else if (tagType === 'fixed_by_weight') {
      newTag.weightRanges = [...weightRanges];
    }

    onTagsChange([...tags, newTag]);
    setNewTagName("");
    setNewMultiplier(1);
    setNewMaxValue(undefined);
    setAgeRanges([]);
    setWeightRanges([]);
    setRoundResult(false);
  };

  const handleRemoveTag = (index: number) => {
    const newTags = tags.filter((_, i) => i !== index);
    onTagsChange(newTags);
  };

  const handleToggleRounding = (index: number, value: boolean) => {
    const newTags = [...tags];
    newTags[index] = {
      ...newTags[index],
      roundResult: value
    };
    onTagsChange(newTags);
  };

  return (
    <div className="space-y-4">
      <Label>Tags para substituição</Label>
      
      <ScrollArea className="h-[200px] rounded-md border p-4">
        <TagDisplay 
          tags={tags} 
          onRemoveTag={handleRemoveTag}
          onToggleRounding={handleToggleRounding}
        />
      </ScrollArea>

      <div className="space-y-4">
        <TagTypeSelector
          value={tagType}
          onChange={(value) => {
            setTagType(value);
            setAgeRanges([]);
            setWeightRanges([]);
          }}
        />

        <TagInputForm
          tagName={newTagName}
          onTagNameChange={setNewTagName}
          tagType={tagType}
          multiplier={newMultiplier}
          onMultiplierChange={setNewMultiplier}
          maxValue={newMaxValue}
          onMaxValueChange={setNewMaxValue}
          startMonth={newStartMonth}
          onStartMonthChange={setNewStartMonth}
          endMonth={newEndMonth}
          onEndMonthChange={setNewEndMonth}
          ageValue={newAgeValue}
          onAgeValueChange={setNewAgeValue}
          startWeight={newStartWeight}
          onStartWeightChange={setNewStartWeight}
          endWeight={newEndWeight}
          onEndWeightChange={setNewEndWeight}
          onAddTag={handleAddTag}
          onAddAgeRange={handleAddAgeRange}
          onAddWeightRange={handleAddWeightRange}
          ageRangesCount={ageRanges.length}
          weightRangesCount={weightRanges.length}
          roundResult={roundResult}
          onRoundResultChange={setRoundResult}
        />

        <AgeRangeList
          ranges={ageRanges}
          onRemove={handleRemoveAgeRange}
        />
      </div>
    </div>
  );
}
