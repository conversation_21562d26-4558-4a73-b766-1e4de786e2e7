import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface PotassiumAssessmentProps {
  weight: number;
  onPotassiumNormal: (isNormal: boolean) => void;
}

export const PotassiumAssessment = ({ weight, onPotassiumNormal }: PotassiumAssessmentProps) => {
  const [potassiumLow, setPotassiumLow] = useState<boolean | null>(null);
  const [hypocalemiaResolved, setHypocalemiaResolved] = useState<boolean | null>(null);
  const [dehydrationAttempts, setDehydrationAttempts] = useState(0);
  const [showSuspendMessage, setShowSuspendMessage] = useState(false);

  const handlePotassiumResponse = (isLow: boolean) => {
    setPotassiumLow(isLow);
    if (!isLow) {
      onPotassiumNormal(!isLow);
    }
  };

  const handleHypocalemiaResponse = (isResolved: boolean) => {
    if (isResolved) {
      setHypocalemiaResolved(true);
      setShowSuspendMessage(true);
      // Timer to show the message before proceeding
      setTimeout(() => {
        onPotassiumNormal(true);
      }, 2000);
    } else {
      setDehydrationAttempts(prev => prev + 1);
      setPotassiumLow(true);
      setHypocalemiaResolved(null);
    }
  };

  if (potassiumLow === null) {
    return (
      <Card className="p-6 space-y-4">
        <h3 className="text-lg font-semibold">Avaliação do Potássio</h3>
        
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-4">
          <h4 className="font-medium text-blue-800">Recomenda-se manter a hidratação de manutenção com:</h4>
          <p className="text-blue-700">
            SF a 0,9%, 2000 mL/m²/dia, via EV
          </p>
        </div>

        <p>O paciente apresenta potássio sérico (&lt; 3,5 mEq/L)?</p>
        <div className="flex gap-4">
          <Button 
            variant="outline" 
            onClick={() => handlePotassiumResponse(true)}
            className="flex-1"
          >
            Sim
          </Button>
          <Button 
            variant="outline" 
            onClick={() => handlePotassiumResponse(false)}
            className="flex-1"
          >
            Não
          </Button>
        </div>
      </Card>
    );
  }

  if (potassiumLow && hypocalemiaResolved === null) {
    return (
      <Card className="p-6 space-y-4">
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-4">
          <h4 className="font-medium text-blue-800">Manejo para reposição rápida de potássio:</h4>
          <p className="text-blue-700">
            <li>Parâmetro a ser utilizado: 0,2-0,3 mEq/kg/hora</li>
            <li>Valor calculado para o paciente: {(0.2 * weight).toFixed(1)}-{(0.3 * weight).toFixed(1)} mEq/hora EV em bomba de infusão</li>
          </p>
          <p className="text-blue-700">Tempo: 3 horas</p>
          {dehydrationAttempts > 0 && (
            <p className="text-blue-700 mt-2">
              Manter reposição rápida de potássio e realizar nova coleta de potássio sérico
            </p>
          )}
        </div>
        <p>
          {dehydrationAttempts > 0 
            ? "Faça a coleta novamente do potássio sérico depois de 1 hora e avalie hipocalemia, hipocalemia persiste?"
            : "Faça a coleta de potássio sérico depois de 1 hora e avalie hipocalemia, hipocalemia persiste?"}
        </p>
        <div className="flex gap-4">
          <Button 
            variant="outline" 
            onClick={() => handleHypocalemiaResponse(true)}
            className="flex-1"
          >
            Não
          </Button>
          <Button 
            variant="outline" 
            onClick={() => handleHypocalemiaResponse(false)}
            className="flex-1"
          >
            Sim
          </Button>
        </div>
      </Card>
    );
  }

  if (showSuspendMessage) {
    return (
      <Card className="p-6 space-y-4">
        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
          <p className="text-green-800 font-medium">
            Suspenda a reposição de potássio
          </p>
        </div>
      </Card>
    );
  }

  return null;
};