import React, { useState } from "react";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Input } from "@/components/ui/input";
import { Search, ArrowLeft } from "lucide-react";
import { RecentPosts } from "@/components/blog/RecentPosts";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { useDebounce } from "@/hooks/useDebounce";

const POSTS_PER_PAGE = 12;

const Blog = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>();
  const navigate = useNavigate();

  // Debounce do termo de busca para melhor performance INP
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const { data: categories } = useQuery({
    queryKey: ['blog-categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_blog_categories')
        .select('*')
        .order('name');
      
      if (error) throw error;
      return data;
    },
  });

  const { data: postsData, isLoading } = useQuery({
    queryKey: ['blog-posts', selectedCategory, debouncedSearchTerm],
    queryFn: async () => {
      let query = supabase
        .from('pedbook_blog_posts')
        .select(`
          *,
          author:profiles(full_name),
          pedbook_blog_categories(name)
        `)
        .eq('published', true)
        .order('published_at', { ascending: false });

      if (selectedCategory) {
        query = query.eq('category_id', selectedCategory);
      }

      if (debouncedSearchTerm) {
        query = query.ilike('title', `%${debouncedSearchTerm}%`);
      }
      
      const { data, error } = await query.range(0, POSTS_PER_PAGE - 1);
      
      if (error) throw error;
      return data;
    },
  });

  return (
    <div className="min-h-screen flex flex-col">
      <HelmetWrapper>
        <title>PedBook | Blog</title>
        <meta name="description" content="Artigos, dicas e novidades sobre pediatria, escritos por especialistas para profissionais da saúde." />
      </HelmetWrapper>

      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <Button 
          variant="ghost" 
          onClick={() => navigate('/')}
          className="mb-6 hover:bg-white/50 text-gray-700 transition-all duration-300"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Voltar ao Início
        </Button>

        {/* Hero Section with modern gradient and animations */}
        <motion.section 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="relative rounded-2xl overflow-hidden mb-8"
        >
          <div className="relative z-10 p-8 md:p-12 bg-gradient-to-r from-primary/5 via-primary/10 to-purple-100/20 backdrop-blur-sm border border-white/20 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2, duration: 0.5 }}
              className="space-y-2"
            >
              <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
                Blog PedBook
              </h1>
              <div className="h-1 w-24 bg-gradient-to-r from-primary to-purple-600 rounded-full" />
            </motion.div>
            
            <motion.p 
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
              className="text-lg md:text-xl text-gray-600 max-w-2xl mt-4 font-light leading-relaxed"
            >
              Artigos, dicas e novidades sobre pediatria, escritos por especialistas para profissionais da saúde.
            </motion.p>
          </div>

          {/* Decorative elements */}
          <div className="absolute inset-0 -z-10 opacity-10">
            <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
              <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" strokeWidth="0.5"/>
              </pattern>
              <rect width="100%" height="100%" fill="url(#grid)" />
            </svg>
          </div>
        </motion.section>

        {/* Filters Section with improved UI */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="flex flex-col sm:flex-row gap-4 mb-8"
        >
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              type="search"
              placeholder="Pesquise aqui..."
              className="pl-10 bg-white/80 backdrop-blur-sm border-primary/20 focus:border-primary/40 transition-all"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <Select
            value={selectedCategory}
            onValueChange={setSelectedCategory}
          >
            <SelectTrigger className="w-full sm:w-[200px] bg-white/80 backdrop-blur-sm border-primary/20">
              <SelectValue placeholder="Filtre por categoria" />
            </SelectTrigger>
            <SelectContent className="bg-white/90 backdrop-blur-sm border-primary/20 shadow-lg">
              <SelectItem value={undefined} className="hover:bg-primary/5">
                Todas as categorias
              </SelectItem>
              {categories?.map((category) => (
                <SelectItem 
                  key={category.id} 
                  value={category.id}
                  className="hover:bg-primary/5"
                >
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </motion.div>

        {/* Posts Grid */}
        <div className="mb-8">
          <RecentPosts posts={postsData} isLoading={isLoading} />
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Blog;