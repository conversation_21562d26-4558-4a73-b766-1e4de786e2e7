interface Aliquot {
  volume: number;
  infusionRate: number;
  dropRate: number;
  infusionTime: number;
}

interface HydrationResult {
  dailyVolume: number;
  hourlyRate: number;
  minuteRate: number;
  aliquots: Aliquot[];
  components: {
    glucose: number;
    sodium: number;
    potassium: number;
  };
}

export const calculateHydration = (weightKg: number): HydrationResult | null => {
  if (weightKg < 3.5) return null;

  // Calculate daily volume (Holliday-Segar)
  let dailyVolume = 0;
  if (weightKg <= 10) {
    dailyVolume = 100 * weightKg;
  } else if (weightKg <= 20) {
    dailyVolume = 1000 + 50 * (weightKg - 10);
  } else {
    dailyVolume = 1500 + 20 * (weightKg - 20);
  }

  // Maximum volume limit
  if (dailyVolume > 2400) dailyVolume = 2400;

  // Calculate aliquots
  const aliquots: Aliquot[] = [];
  let remainingVolume = dailyVolume;
  let remainingTime = 24; // Total hours in a day

  while (remainingVolume > 0) {
    const volume = Math.min(500, remainingVolume);
    
    // Calculate proportional infusion time for this aliquot
    const infusionTime = (volume / dailyVolume) * 24;
    
    // Calculate infusion rate (mL/h) for this aliquot
    const infusionRate = volume / infusionTime;
    
    // Calculate drop rate (drops/min) using standard factor of 20 drops/mL
    const dropRate = Math.round((infusionRate * 20) / 60);
    
    aliquots.push({
      volume: Math.round(volume),
      infusionRate,
      dropRate,
      infusionTime: Number(infusionTime.toFixed(2))
    });
    
    remainingVolume -= volume;
    remainingTime -= infusionTime;
  }

  return {
    dailyVolume,
    hourlyRate: dailyVolume / 24,
    minuteRate: dailyVolume / 1440,
    aliquots,
    components: {
      glucose: 475,
      sodium: 20,
      potassium: 5
    }
  };
};