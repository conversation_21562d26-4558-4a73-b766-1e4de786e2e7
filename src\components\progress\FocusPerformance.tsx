import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

interface FocusPerformanceProps {
  byFocus: {
    [key: string]: { name: string; correct: number; total: number };
  };
}

export const FocusPerformance: React.FC<FocusPerformanceProps> = ({ byFocus }) => {
  return (
    <Card className="p-6">
      <CardHeader>
        <CardTitle>Desempenho por Foco</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {Object.entries(byFocus).map(([id, data]) => (
            <div key={id}>
              <div className="flex justify-between text-sm mb-2">
                <span className="font-medium">{data.name}</span>
                <span className="text-gray-600">
                  {data.correct} de {data.total} ({((data.correct / data.total) * 100).toFixed(1)}%)
                </span>
              </div>
              <Progress value={(data.correct / data.total) * 100} />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};