export const calculateHydration = (age: number, weight: number) => {
  // Convert age from months to years for easier comparison
  const ageInYears = age / 12;
  
  if (ageInYears >= 13) {
    // Adults and children 13+: 60 mL/kg/day
    return {
      totalVolume: Math.round(weight * 60),
      sro: Math.round((weight * 60) / 3),
      caseiros: Math.round((weight * 60 * 2) / 3),
      formula: "60 mL/kg/dia"
    };
  } else {
    // Children under 13
    let rate;
    if (weight <= 10) {
      rate = 130;
    } else if (weight <= 20) {
      rate = 100;
    } else {
      rate = 80;
    }
    
    const totalVolume = Math.round(weight * rate);
    return {
      totalVolume,
      sro: Math.round(totalVolume / 3),
      caseiros: Math.round((totalVolume * 2) / 3),
      formula: `${rate} mL/kg/dia`
    };
  }
};

export const formatMedication = (weight: number, age: number) => {
  const ageInYears = age / 12;
  
  // Paracetamol calculation
  const paracetamolDrops = Math.min(
    Math.round(weight * 1), // 1 drop/kg/dose
    ageInYears < 12 ? 35 : 55 // max drops per dose based on age
  );
  
  // Dipirona drops calculation
  const dipironaDrops = Math.min(
    Math.round(weight * 1), // 1 drop/kg/dose
    40 // max drops per dose
  );
  
  // Dipirona solution calculation
  const dipironaSolution = Math.min(
    Math.round(weight * 0.24 * 10) / 10, // 0.24 mL/kg/dose, rounded to 1 decimal
    15 // max mL per dose
  );
  
  return {
    paracetamol: {
      drops: paracetamolDrops,
      maxDaily: ageInYears < 12 ? "5 doses" : "4.000 mg/dia"
    },
    dipirona: {
      drops: dipironaDrops,
      solution: dipironaSolution
    }
  };
};