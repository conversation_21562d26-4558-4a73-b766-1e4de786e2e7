import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface TagTypeSelectorProps {
  value: 'fixed' | 'multiplier' | 'age' | 'fixed_by_weight' | 'multiplier_by_fixed_age';
  onChange: (value: 'fixed' | 'multiplier' | 'age' | 'fixed_by_weight' | 'multiplier_by_fixed_age') => void;
}

export function TagTypeSelector({ value, onChange }: TagTypeSelectorProps) {
  return (
    <RadioGroup
      value={value}
      onValueChange={(value) => onChange(value as 'fixed' | 'multiplier' | 'age' | 'fixed_by_weight' | 'multiplier_by_fixed_age')}
      className="flex items-center space-x-4"
    >
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="multiplier" id="multiplier" />
        <Label htmlFor="multiplier">Multiplicador</Label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="fixed" id="fixed" />
        <Label htmlFor="fixed">Valor Fixo</Label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="age" id="age" />
        <Label htmlFor="age">Baseado em Idade</Label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="multiplier_by_fixed_age" id="multiplier_by_fixed_age" />
        <Label htmlFor="age">Multiplicador por idade</Label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="fixed_by_weight" id="fixed_by_weight" />
        <Label htmlFor="fixed_by_weight">Valor Fixo por Peso</Label>
      </div>
    </RadioGroup>
  );
}