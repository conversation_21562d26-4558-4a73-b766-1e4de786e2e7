import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { FlashcardHierarchyNode } from "./FlashcardHierarchyNode";
import type { CategoryNode } from "@/types/flashcard";

interface FlashcardHierarchyProps {
  hierarchy: CategoryNode[];
  expandedNodes: Set<string>;
  onToggleNode: (nodeId: string) => void;
  showReviewDate?: boolean;
}

export const FlashcardHierarchy = ({
  hierarchy,
  expandedNodes,
  onToggleNode,
  showReviewDate = false
}: FlashcardHierarchyProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {showReviewDate ? "Próximas Revisões" : "Árvore de Habilidades"}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {hierarchy.length > 0 ? (
          hierarchy.map(node => (
            <FlashcardHierarchyNode
              key={node.id}
              node={node}
              isExpanded={expandedNodes.has(node.id)}
              onToggle={onToggleNode}
              showReviewDate={showReviewDate}
            />
          ))
        ) : (
          <div className="text-center text-muted-foreground">
            {showReviewDate 
              ? "Não há flashcards para revisão no próximo dia."
              : "Nenhum flashcard estudado ainda."}
          </div>
        )}
      </CardContent>
    </Card>
  );
};