import { useState } from 'react';
import { Stage } from './types';

export const useLoxoscelicFlow = () => {
  const [currentStage, setCurrentStage] = useState<Stage>('initial');

  const handleAnswer = (stage: Stage) => {
    setCurrentStage(stage);
  };

  const handleBack = () => {
    if (currentStage.startsWith('cutaneous-')) {
      if (currentStage === 'cutaneous-hemolytic') {
        setCurrentStage('initial');
      } else {
        setCurrentStage('cutaneous');
      }
    } else {
      setCurrentStage('initial');
    }
  };

  return {
    currentStage,
    handleAnswer,
    handleBack,
  };
};