
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";

interface DiscursiveAnswerProps {
  value: string;
  onChange: (text: string) => void;
  onSubmit: () => void;
  onAnalyze?: () => void;  // Nova prop para analisar a resposta
  readOnly?: boolean;
  hasAnswered: boolean;
  isAnalyzing?: boolean;   // Nova prop para indicar estado de análise
}

export const DiscursiveAnswer = ({
  value,
  onChange,
  onSubmit,
  onAnalyze,
  readOnly = false,
  hasAnswered,
  isAnalyzing = false
}: DiscursiveAnswerProps) => {
  const { toast } = useToast();

  const handleSubmit = () => {
    if (!value.trim()) {
      toast({
        title: "Resposta vazia",
        description: "Por favor, escreva sua resposta antes de confirmar",
        variant: "destructive"
      });
      return;
    }

    onSubmit();
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Textarea
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder="Digite sua resposta aqui..."
          className="min-h-[150px]"
          readOnly={readOnly || hasAnswered}
        />

        {!hasAnswered && (
          <div className="flex justify-end">
            <Button
              onClick={handleSubmit}
              disabled={!value.trim() || readOnly}
              className="w-full md:w-auto"
            >
              Confirmar Resposta
            </Button>
          </div>
        )}
      </div>

      {hasAnswered && (
        <div className="mt-4">
          <div className="font-medium text-gray-700 mb-2">Sua Resposta:</div>
          <div className="p-4 rounded-lg border bg-gray-50 whitespace-pre-wrap">
            {value}
          </div>

          {onAnalyze && (
            <div className="flex justify-end mt-4">
              <Button
                onClick={onAnalyze}
                disabled={isAnalyzing}
                className="w-full md:w-auto bg-blue-600 hover:bg-blue-700"
              >
                {isAnalyzing ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Analisando...
                  </>
                ) : (
                  "Gerar análise da minha resposta"
                )}
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
