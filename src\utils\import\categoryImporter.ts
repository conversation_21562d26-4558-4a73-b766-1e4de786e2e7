
import { supabase } from "@/integrations/supabase/client";
import type { CategoryResult } from "@/types/import";

export async function getOrCreateCategory(name: string, type: string, parentId?: string): Promise<CategoryResult> {
  // console.log(`🔍 Verificando categoria: ${name} (${type})`);
  
  try {
    const { data: existing, error: searchError } = await supabase
      .from('study_categories')
      .select('*')
      .eq('name', name)
      .eq('type', type)
      .maybeSingle();

    if (searchError) throw searchError;

    if (existing) {
      return {
        id: existing.id,
        name: existing.name,
        type: existing.type
      };
    }

    const { data: created, error: createError } = await supabase
      .from('study_categories')
      .insert({ name, type, parent_id: parentId })
      .select()
      .single();

    if (createError) throw createError;
    
    return {
      id: created.id,
      name: created.name,
      type: created.type
    };
  } catch (error) {
    // console.error(`❌ Erro ao processar categoria:`, error);
    throw error;
  }
}
