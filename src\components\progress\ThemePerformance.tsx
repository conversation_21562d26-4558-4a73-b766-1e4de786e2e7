import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

interface ThemePerformanceProps {
  byTheme: {
    [key: string]: { correct: number; total: number };
  };
}

export const ThemePerformance: React.FC<ThemePerformanceProps> = ({ byTheme }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {Object.entries(byTheme).map(([theme, data]) => (
        <Card key={theme} className="p-4">
          <CardHeader>
            <CardTitle className="text-lg">{theme}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Total de questões</span>
                <span>{data.total}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span><PERSON>rtos</span>
                <span className="text-green-600">{data.correct}</span>
              </div>
              <Progress value={(data.correct / data.total) * 100} />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};