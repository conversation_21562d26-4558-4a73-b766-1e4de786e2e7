import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Plus, ChevronRight, Info, Upload } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ICD10CategoryDialog } from "@/components/admin/ICD10CategoryDialog";
import { ImportICD10Dialog } from "@/components/admin/ImportICD10Dialog";
import { supabase } from "@/integrations/supabase/client";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@/components/ui/breadcrumb";

export default function ICD10() {
  const [selectedCategory, setSelectedCategory] = useState<any>(null);
  const [showDialog, setShowDialog] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [currentParentId, setCurrentParentId] = useState<string | null>(null);
  const [breadcrumbs, setBreadcrumbs] = useState<Array<{ id: string; name: string }>>([]);

  const { data: categories } = useQuery({
    queryKey: ["icd10-categories", currentParentId],
    queryFn: async () => {
      const query = supabase
        .from("pedbook_icd10_categories")
        .select("*")
        .order("code_range");

      if (currentParentId) {
        query.eq("parent_id", currentParentId);
      } else {
        query.is("parent_id", null);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    },
  });

  const handleCategoryClick = async (category: any) => {
    setCurrentParentId(category.id);
    setBreadcrumbs([...breadcrumbs, { id: category.id, name: category.name }]);
  };

  const handleBreadcrumbClick = (index: number) => {
    const newBreadcrumbs = breadcrumbs.slice(0, index);
    setBreadcrumbs(newBreadcrumbs);
    setCurrentParentId(index === 0 ? null : newBreadcrumbs[index - 1].id);
  };

  return (
    <div className="container mx-auto py-8 space-y-8 animate-fade-in">
      <div className="flex justify-between items-start">
        <div className="space-y-4">
          <h1 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600">
            Categorias CID-10
          </h1>
          <Breadcrumb>
            <BreadcrumbItem>
              <BreadcrumbLink 
                onClick={() => {
                  setCurrentParentId(null);
                  setBreadcrumbs([]);
                }}
                className="hover:text-primary transition-colors"
              >
                Início
              </BreadcrumbLink>
            </BreadcrumbItem>
            {breadcrumbs.map((crumb, index) => (
              <BreadcrumbItem key={crumb.id}>
                <BreadcrumbLink 
                  onClick={() => handleBreadcrumbClick(index + 1)}
                  className="hover:text-primary transition-colors"
                >
                  {crumb.name}
                </BreadcrumbLink>
              </BreadcrumbItem>
            ))}
          </Breadcrumb>
        </div>
        
        <div className="flex gap-2">
          <Button 
            onClick={() => setShowImportDialog(true)}
            className="bg-gradient-to-r from-green-600 to-green-500 hover:opacity-90 transition-all duration-300"
          >
            <Upload className="mr-2 h-4 w-4" />
            Importar Códigos
          </Button>
          <Button 
            onClick={() => {
              setSelectedCategory(null);
              setShowDialog(true);
            }}
            className="bg-gradient-to-r from-primary to-blue-600 hover:opacity-90 transition-all duration-300"
          >
            <Plus className="mr-2 h-4 w-4" />
            Nova Categoria
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {categories?.map((category) => (
          <div
            key={category.id}
            onClick={() => handleCategoryClick(category)}
            className="group relative bg-gradient-to-br from-white to-primary/5 rounded-xl p-6 space-y-3 border border-primary/10 shadow-sm hover:shadow-lg hover:-translate-y-1 transition-all duration-300 cursor-pointer backdrop-blur-sm animate-fade-in"
          >
            <div className="flex justify-between items-start">
              <div className="space-y-2">
                <div className="space-y-1">
                  <h3 className="text-lg font-semibold text-primary">
                    {category.code_range}
                  </h3>
                  <p className="text-gray-800 font-medium">{category.name}</p>
                </div>
                {category.description && (
                  <div className="flex items-start gap-2 text-sm text-muted-foreground">
                    <Info className="h-4 w-4 mt-0.5 shrink-0" />
                    <p className="line-clamp-2">{category.description}</p>
                  </div>
                )}
              </div>
              <ChevronRight className="h-5 w-5 text-primary/40 group-hover:text-primary transition-colors" />
            </div>

            <Button
              variant="ghost"
              size="sm"
              className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={(e) => {
                e.stopPropagation();
                setSelectedCategory(category);
                setShowDialog(true);
              }}
            >
              Editar
            </Button>
          </div>
        ))}
      </div>

      <ICD10CategoryDialog
        category={selectedCategory}
        parentId={currentParentId}
        isOpen={showDialog}
        onClose={() => {
          setShowDialog(false);
          setSelectedCategory(null);
        }}
      />

      <ImportICD10Dialog
        isOpen={showImportDialog}
        onClose={() => setShowImportDialog(false)}
        parentId={currentParentId}
      />
    </div>
  );
}