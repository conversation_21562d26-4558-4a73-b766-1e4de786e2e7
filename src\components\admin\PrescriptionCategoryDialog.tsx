import { useState, useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { Pencil, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface PrescriptionCategoryDialogProps {
  category?: {
    id: string;
    name: string;
  };
  isOpen: boolean;
  onClose: () => void;
  session: any;
}

export function PrescriptionCategoryDialog({ 
  category, 
  isOpen, 
  onClose,
  session 
}: PrescriptionCategoryDialogProps) {
  const [name, setName] = useState(category?.name || "");
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (isOpen) {
      setName(category?.name || "");
    }
  }, [isOpen, category]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (category) {
        const { error } = await supabase
          .from("pedbook_prescription_categories")
          .update({ name })
          .eq("id", category.id);

        if (error) throw error;

        toast({
          title: "Categoria atualizada com sucesso!",
          description: `A categoria ${name} foi atualizada.`,
        });
      } else {
        const { error } = await supabase
          .from("pedbook_prescription_categories")
          .insert([{ 
            name, 
            user_id: session.user.id 
          }]);

        if (error) throw error;

        toast({
          title: "Categoria criada com sucesso!",
          description: `A categoria ${name} foi adicionada.`,
        });
      }

      queryClient.invalidateQueries({ queryKey: ["prescription-categories"] });
      onClose();
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao salvar categoria",
        description: error.message || "Ocorreu um erro ao salvar a categoria.",
      });
    }
  };

  const handleDelete = async () => {
    try {
      const { error } = await supabase
        .from("pedbook_prescription_categories")
        .delete()
        .eq("id", category?.id);

      if (error) throw error;

      toast({
        title: "Categoria excluída com sucesso!",
        description: `A categoria ${category?.name} foi excluída.`,
      });

      queryClient.invalidateQueries({ queryKey: ["prescription-categories"] });
      setShowDeleteDialog(false);
      onClose();
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao excluir categoria",
        description: error.message || "Ocorreu um erro ao excluir a categoria.",
      });
    }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {category ? "Editar Categoria" : "Nova Categoria"}
            </DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="name">Nome</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
              />
            </div>
            <div className="flex justify-between">
              {category && (
                <Button
                  type="button"
                  variant="destructive"
                  onClick={() => setShowDeleteDialog(true)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Excluir
                </Button>
              )}
              <div className="flex gap-2">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancelar
                </Button>
                <Button type="submit">Salvar</Button>
              </div>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir a categoria "{category?.name}"? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}