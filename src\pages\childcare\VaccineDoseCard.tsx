
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Info, ChevronDown, ChevronUp, Syringe, Calendar, Shield } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { VaccineDose } from "./types";
import { MarkdownRenderer } from "@/components/ui/MarkdownRenderer";

interface VaccineDoseCardProps {
  dose: VaccineDose;
}

export function VaccineDoseCard({ dose }: VaccineDoseCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const formatDoseNumber = (number: number, type: string) => {
    return `${number}º ${type === 'reforço' ? 'reforço' : 'dose'}`;
  };

  const getVaccineTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'sus':
        return 'bg-gradient-to-r from-green-500 to-emerald-600 text-white';
      case 'privada':
        return 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white';
      default:
        return 'bg-gradient-to-r from-gray-500 to-gray-600 text-white';
    }
  };

  return (
    <Card className="overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/50 dark:from-slate-800 dark:via-slate-800/90 dark:to-slate-700/50">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative"
      >
        {/* Header */}
        <div className="p-4 sm:p-6 pb-3 sm:pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* Icon */}
              <div className="relative">
                <div className="w-14 h-14 bg-gradient-to-br from-emerald-400 via-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <Syringe className="w-7 h-7 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">{dose.dose_number}</span>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                  {dose.vaccine.name}
                </h3>
                <div className="flex flex-wrap gap-2">
                  <Badge className={`${getVaccineTypeColor(dose.type)} shadow-md`}>
                    <Shield className="w-3 h-3 mr-1" />
                    {dose.type}
                  </Badge>
                  <Badge variant="outline" className="bg-white/80 dark:bg-slate-700/80 border-emerald-200 dark:border-emerald-700">
                    {formatDoseNumber(dose.dose_number, dose.dose_type)}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Action Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="hover:bg-emerald-50 dark:hover:bg-slate-700 border-emerald-200 dark:border-emerald-700"
            >
              {isExpanded ? (
                <>
                  <ChevronUp className="h-4 w-4 mr-2" />
                  Ocultar
                </>
              ) : (
                <>
                  <Info className="h-4 w-4 mr-2" />
                  Ver Detalhes
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Expanded Content */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="overflow-hidden"
            >
              <div className="px-4 sm:px-6 pb-4 sm:pb-6 pt-2 border-t border-gray-100 dark:border-slate-600 bg-gradient-to-r from-gray-50/50 to-blue-50/50 dark:from-slate-700/50 dark:to-slate-600/50">
                {/* Description */}
                {dose.vaccine.description && (
                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                      <Calendar className="w-4 h-4 mr-2" />
                      Descrição
                    </h4>
                    <div className="bg-white/80 dark:bg-slate-800/80 rounded-lg p-3 border border-gray-200 dark:border-slate-600">
                      <MarkdownRenderer
                        content={dose.vaccine.description}
                        className="compact text-sm"
                      />
                    </div>
                  </div>
                )}

                {/* Related Vaccines */}
                {dose.related_vaccines && dose.related_vaccines.length > 0 && (
                  <div>
                    <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                      <Shield className="w-4 h-4 mr-2" />
                      Vacinas Incluídas ({dose.related_vaccines.length})
                    </h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      {dose.related_vaccines.map((relatedVaccine) => (
                        <motion.div
                          key={relatedVaccine.id}
                          whileHover={{ scale: 1.02 }}
                          className="bg-white/90 dark:bg-slate-800/90 rounded-lg p-3 border border-gray-200 dark:border-slate-600"
                        >
                          <div className="flex items-center space-x-2">
                            <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                            <span className="text-sm font-medium text-gray-800 dark:text-gray-200">
                              {relatedVaccine.name}
                            </span>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </Card>
  );
}
