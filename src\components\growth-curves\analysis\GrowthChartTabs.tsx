import { Card } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Scale, Ruler, LineChart, Brain } from "lucide-react";
import { GrowthChart } from "./GrowthChart";
import { getOptimalAgeRange, filterDataByAgeRange } from "@/utils/chartRangeCalculator";

interface PatientData {
  age: number;
  gender: 'male' | 'female';
  weight?: number;
  height?: number;
  headCircumference?: number;
}

interface GrowthChartTabsProps {
  patientData: PatientData;
  chartData: {
    weightData: any;
    heightData: any;
    bmiData: any;
    headCircumferenceData: any;
  };
}

export function GrowthChartTabs({ patientData, chartData }: GrowthChartTabsProps) {
  const getChartData = (type: 'weight' | 'height' | 'bmi' | 'head-circumference') => {
    const data = type === 'weight' ? chartData.weightData :
                type === 'height' ? chartData.heightData :
                type === 'bmi' ? chartData.bmiData :
                chartData.headCircumferenceData;

    if (!data?.data || !Array.isArray(data.data)) {
      return [];
    }

    // NOVO: Calcular range otimizado baseado na idade do paciente
    const ageRange = getOptimalAgeRange(patientData.age);

    // NOVO: Filtrar dados pelo range otimizado
    const filteredData = filterDataByAgeRange(data.data, ageRange);

    const patientBMI = patientData.weight && patientData.height ?
      (patientData.weight / Math.pow(patientData.height / 100, 2)) :
      undefined;

    // Interpolar entre dois pontos para criar um ponto exato na idade do paciente
    const interpolatePatientPoint = (targetAge: number) => {
      // Ordenar dados por idade
      const sortedData = [...data.data].sort((a, b) => a.age_months - b.age_months);

      // Encontrar pontos adjacentes
      let lowerPoint = sortedData[0];
      let upperPoint = sortedData[sortedData.length - 1];

      for (let i = 0; i < sortedData.length - 1; i++) {
        if (targetAge >= sortedData[i].age_months && targetAge <= sortedData[i + 1].age_months) {
          lowerPoint = sortedData[i];
          upperPoint = sortedData[i + 1];
          break;
        }
      }

      // Se a idade é exata, usar o ponto direto
      if (lowerPoint.age_months === targetAge) {
        return lowerPoint;
      }

      if (upperPoint.age_months === targetAge) {
        return upperPoint;
      }

      // Interpolar entre os dois pontos
      const ratio = (targetAge - lowerPoint.age_months) / (upperPoint.age_months - lowerPoint.age_months);

      const interpolatedPoint = {
        age_months: targetAge,
        percentiles: {
          "3rd": lowerPoint.percentiles["3rd"] + (upperPoint.percentiles["3rd"] - lowerPoint.percentiles["3rd"]) * ratio,
          "15th": lowerPoint.percentiles["15th"] + (upperPoint.percentiles["15th"] - lowerPoint.percentiles["15th"]) * ratio,
          "50th": lowerPoint.percentiles["50th"] + (upperPoint.percentiles["50th"] - lowerPoint.percentiles["50th"]) * ratio,
          "85th": lowerPoint.percentiles["85th"] + (upperPoint.percentiles["85th"] - lowerPoint.percentiles["85th"]) * ratio,
          "97th": lowerPoint.percentiles["97th"] + (upperPoint.percentiles["97th"] - lowerPoint.percentiles["97th"]) * ratio
        }
      };

      return interpolatedPoint;
    };

    const interpolatedPoint = interpolatePatientPoint(patientData.age);

    // MODIFICADO: Usar dados filtrados para o gráfico
    const processedData = [...filteredData.map((point: any) => ({
      age: point.age_months,
      p3: point.percentiles["3rd"],
      p15: point.percentiles["15th"],
      p50: point.percentiles["50th"],
      p85: point.percentiles["85th"],
      p97: point.percentiles["97th"],
      patient: undefined, // Inicialmente sem dados do paciente
    }))];

    // Adicionar o ponto exato do paciente
    const patientValue = type === 'weight' ? patientData.weight :
                        type === 'height' ? patientData.height :
                        type === 'bmi' ? patientBMI :
                        patientData.headCircumference;

    if (patientValue !== undefined) {
      processedData.push({
        age: interpolatedPoint.age_months,
        p3: interpolatedPoint.percentiles["3rd"],
        p15: interpolatedPoint.percentiles["15th"],
        p50: interpolatedPoint.percentiles["50th"],
        p85: interpolatedPoint.percentiles["85th"],
        p97: interpolatedPoint.percentiles["97th"],
        patient: patientValue,
      });
    }

    // Ordenar por idade para manter a sequência correta
    processedData.sort((a, b) => a.age - b.age);



    // Verificar se há ponto do paciente
    const patientPoints = processedData.filter(point => point.patient !== undefined);





    return processedData;
  };

  const content = (
    <Tabs defaultValue="weight" className="w-full">
      <div className="space-y-2 mb-6">
        <h3 className="text-sm text-center text-gray-500 font-light">
          Escolha uma curva de crescimento
        </h3>
        <TabsList className="grid w-full grid-cols-4 gap-1 bg-white/50 rounded-lg p-1 border border-primary/10">
          <TabsTrigger 
            value="weight" 
            className="data-[state=active]:bg-primary data-[state=active]:text-white border border-primary/20 text-xs sm:text-sm"
          >
            <Scale className="w-4 h-4 mr-1.5" />
            Peso
          </TabsTrigger>
          <TabsTrigger 
            value="height" 
            className="data-[state=active]:bg-primary data-[state=active]:text-white border border-primary/20 text-xs sm:text-sm"
          >
            <Ruler className="w-4 h-4 mr-1.5" />
            Altura
          </TabsTrigger>
          <TabsTrigger 
            value="bmi" 
            className="data-[state=active]:bg-primary data-[state=active]:text-white border border-primary/20 text-xs sm:text-sm"
          >
            <LineChart className="w-4 h-4 mr-1.5" />
            IMC
          </TabsTrigger>
          <TabsTrigger 
            value="head" 
            className="data-[state=active]:bg-primary data-[state=active]:text-white border border-primary/20 text-xs sm:text-sm"
          >
            <Brain className="w-4 h-4 mr-1.5" />
            PC
          </TabsTrigger>
        </TabsList>
      </div>

      <TabsContent value="weight" className="mt-0">
        {chartData.weightData && patientData.weight && (
          <div className="w-full">
            <div className="text-center mb-4">
              <h3 className="text-lg font-semibold text-gray-800">Curva de Peso</h3>
              <p className="text-xs text-gray-500 mt-1">
                📊 Visualizando: {getOptimalAgeRange(patientData.age).description}
              </p>
            </div>
            <GrowthChart
              data={getChartData('weight')}
              measurementType="weight"
            />
          </div>
        )}
      </TabsContent>

      <TabsContent value="height" className="mt-0">
        {chartData.heightData && patientData.height && (
          <div className="w-full">
            <div className="text-center mb-4">
              <h3 className="text-lg font-semibold text-gray-800">Curva de Altura</h3>
              <p className="text-xs text-gray-500 mt-1">
                📊 Visualizando: {getOptimalAgeRange(patientData.age).description}
              </p>
            </div>
            <GrowthChart
              data={getChartData('height')}
              measurementType="height"
            />
          </div>
        )}
      </TabsContent>

      <TabsContent value="bmi" className="mt-0">
        {chartData.bmiData && patientData.weight && patientData.height && (
          <div className="w-full">
            <div className="text-center mb-4">
              <h3 className="text-lg font-semibold text-gray-800">Curva de IMC</h3>
              <p className="text-xs text-gray-500 mt-1">
                📊 Visualizando: {getOptimalAgeRange(patientData.age).description}
              </p>
            </div>
            <GrowthChart
              data={getChartData('bmi')}
              measurementType="bmi"
            />
          </div>
        )}
      </TabsContent>

      <TabsContent value="head" className="mt-0">
        {chartData.headCircumferenceData && patientData.headCircumference && (
          <div className="w-full">
            <div className="text-center mb-4">
              <h3 className="text-lg font-semibold text-gray-800">Curva de Perímetro Cefálico</h3>
              <p className="text-xs text-gray-500 mt-1">
                📊 Visualizando: {getOptimalAgeRange(patientData.age).description}
              </p>
            </div>
            <GrowthChart
              data={getChartData('head-circumference')}
              measurementType="head-circumference"
            />
          </div>
        )}
      </TabsContent>
    </Tabs>
  );

  return (
    <>
      <div className="md:hidden">
        {content}
      </div>
      <div className="hidden md:block">
        <Card className="p-6 glass-card animate-fade-in-up">
          {content}
        </Card>
      </div>
    </>
  );
}