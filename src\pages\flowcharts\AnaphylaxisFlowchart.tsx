import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON>L<PERSON>t, AlertCircle, Heart } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { FlowchartSEO } from "@/components/seo/FlowchartSEO";
import { FLOWCHART_SEO_DATA } from "@/data/flowchartSEOData";
import { PatientInfo } from "@/components/flowcharts/anaphylaxis/PatientInfo";
import { PatientMonitoring } from "@/components/flowcharts/anaphylaxis/PatientMonitoring";
import { TreatmentPlan } from "@/components/flowcharts/anaphylaxis/TreatmentPlan";
import { DischargeInstructions } from "@/components/flowcharts/anaphylaxis/DischargeInstructions";
import { MonitoringDetails } from "@/components/flowcharts/anaphylaxis/MonitoringDetails";
import { DiagnosisCriteria } from "@/components/flowcharts/anaphylaxis/DiagnosisCriteria";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON>Header,
  <PERSON><PERSON>ooter,
  CardT<PERSON>le,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { getThemeClasses } from "@/components/ui/theme-utils";

type FlowStage = 'initial' | 'diagnosis' | 'treatment' | 'monitoring' | 'resolution' | 'discharge' | 'icu';

const AnaphylaxisFlowchart = () => {
  const [weight, setWeight] = useState(0);
  const [stage, setStage] = useState<FlowStage>('initial');
  const [answers, setAnswers] = useState<Record<string, boolean>>({});
  const [showDifferentialDiagnosis, setShowDifferentialDiagnosis] = useState(false);
  const [doseCount, setDoseCount] = useState(1);
  const [age, setAge] = useState(0);

  const seoData = FLOWCHART_SEO_DATA['anaphylaxis'];

  const handleAnswer = (questionId: string, answer: boolean) => {
    setAnswers(prev => ({ ...prev, [questionId]: answer }));
    
    if (questionId === 'initialSuspicion' && answer) {
      setStage('treatment');
    } else if (questionId === 'initialSuspicion' && !answer) {
      setShowDifferentialDiagnosis(true);
      setStage('diagnosis');
    }
  };

  const handleResolution = (hasResolution: boolean) => {
    if (hasResolution) {
      setStage('resolution');
    } else {
      if (doseCount >= 3) {
        setStage('icu');
      } else {
        setDoseCount(prev => prev + 1);
        setStage('treatment');
      }
    }
  };

  const handleBiphasicResponse = (hasBiphasicResponse: boolean) => {
    if (hasBiphasicResponse) {
      setDoseCount(1);
      setStage('treatment');
    } else {
      setStage('discharge');
    }
  };

  const calculateAdrenalineDose = () => {
    if (!weight) return "0";
    const dose = Math.min(0.01 * weight, 0.5);
    return dose.toFixed(2);
  };

  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen flex flex-col from-red-100 via-white to-red-50 dark:from-red-950 dark:via-slate-900 dark:to-slate-800")}>
      <FlowchartSEO {...seoData} />

      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <Link 
          to="/flowcharts" 
          className="inline-flex items-center gap-2 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 transition-colors mb-8"
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Voltar para Fluxogramas</span>
        </Link>

        <div className="max-w-4xl mx-auto space-y-8">
          <div className="text-center space-y-4">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 dark:bg-red-900/30 mb-4">
              <Heart className="w-8 h-8 text-red-600 dark:text-red-400" />
            </div>
            <h1 className={getThemeClasses.gradientHeading("text-4xl font-bold from-red-600 to-red-400 dark:from-red-400 dark:to-red-300")}>
              Fluxograma de Anafilaxia
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Guia para manejo de anafilaxia em pediatria
            </p>
          </div>

          <div className="space-y-6">
            {stage === 'initial' && (
              <div className="space-y-6">
                <PatientInfo
                  weight={weight}
                  onWeightChange={setWeight}
                  onWeightCommit={setWeight}
                  age={age}
                  onAgeChange={setAge}
                  onAgeCommit={setAge}
                />
                <DiagnosisCriteria />
                <Card className={getThemeClasses.gradientCard("red", "")}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-red-700 dark:text-red-400">
                      <AlertCircle className="h-6 w-6" />
                      Diagnóstico Inicial
                    </CardTitle>
                    <CardDescription className="text-gray-600 dark:text-gray-300">
                      Avaliação inicial de suspeita de anafilaxia
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="text-center pt-4">
                      <p className="font-medium text-red-700 dark:text-red-400 mb-4">O paciente apresenta pelo menos um dos critérios para anafilaxia?</p>
                      <div className="flex justify-center gap-4">
                        <Button 
                          variant={answers['initialSuspicion'] === true ? "default" : "outline"}
                          onClick={() => handleAnswer('initialSuspicion', true)}
                          className="bg-red-600 hover:bg-red-700 text-white dark:bg-red-700 dark:hover:bg-red-800 dark:text-white"
                        >
                          Sim
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
            
            {stage === 'treatment' && (
              <>
                <TreatmentPlan
                  weight={weight}
                  doseNumber={doseCount}
                  adrenalineDose={calculateAdrenalineDose()}
                />
                <MonitoringDetails 
                  showAdjuvantTherapy={true}
                  weight={weight}
                  age={age * 12} // Convert years to months for the monitoring component
                />
                <PatientMonitoring
                  onResolutionResponse={handleResolution}
                  onBiphasicResponse={handleBiphasicResponse}
                  showBiphasicQuestion={false}
                  doseCount={doseCount}
                />
              </>
            )}
            
            {(stage === 'resolution' || stage === 'monitoring') && (
              <PatientMonitoring
                onResolutionResponse={handleResolution}
                onBiphasicResponse={handleBiphasicResponse}
                showBiphasicQuestion={stage === 'resolution'}
              />
            )}
            
            {stage === 'discharge' && <DischargeInstructions />}
            
            {stage === 'icu' && (
              <TreatmentPlan
                weight={weight}
                doseNumber={doseCount}
                adrenalineDose={calculateAdrenalineDose()}
                requiresICU
              />
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default AnaphylaxisFlowchart;
