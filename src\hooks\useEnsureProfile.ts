import { useEffect } from 'react';
import { useSession } from '@supabase/auth-helpers-react';
import { supabase } from '@/integrations/supabase/client';

/**
 * Hook para garantir que o usuário tenha um perfil criado
 * Executa automaticamente quando o usuário está logado
 */
export const useEnsureProfile = () => {
  const session = useSession();

  useEffect(() => {
    const ensureProfile = async () => {
      if (!session?.user?.id) {
        return;
      }

      try {
        // Verificar se o perfil já existe
        const { data, error } = await supabase
          .from("profiles")
          .select("id")
          .eq("id", session.user.id)
          .single();

        // Se não existe, criar
        if (error && error.code === 'PGRST116') {
          console.log("Profile not found, creating...");
          
          const { error: createError } = await supabase
            .from("profiles")
            .upsert({
              id: session.user.id,
              username: session.user.email || "",
              full_name: session.user.user_metadata?.full_name || "Usuário",
              formation_area: session.user.user_metadata?.formation_area || "Não informado",
              graduation_year: session.user.user_metadata?.graduation_year || "Não informado",
              is_student: session.user.user_metadata?.is_student || false,
              is_professional: session.user.user_metadata?.is_professional || false,
              avatar_url: session.user.user_metadata?.avatar_url || null,
              professional_email: "",
              phone: "",
              registration_number: ""
            }, {
              onConflict: 'id'
            });

          if (createError) {
            console.error("Error creating profile:", createError);
          } else {
            console.log("Profile created successfully");
          }
        } else if (error) {
          console.error("Error checking profile:", error);
        }
      } catch (error) {
        console.error("Error in ensureProfile:", error);
      }
    };

    // Executar após um pequeno delay para garantir que a sessão está estável
    const timeoutId = setTimeout(ensureProfile, 1000);

    return () => clearTimeout(timeoutId);
  }, [session?.user?.id]);
};

export default useEnsureProfile;
