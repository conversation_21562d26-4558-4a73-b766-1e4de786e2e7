
import { motion } from "framer-motion";
import { Calendar } from "lucide-react";
import { Card } from "@/components/ui/card";
import type { DaySchedule } from "@/types/study-schedule";
import { WeekCard } from "./schedule/WeekCard";
import { Badge } from "@/components/ui/badge";
import { useState, useEffect } from "react";
import { sumDurations, parseDurationToMinutes } from "@/utils/formatTime";

interface StudyScheduleProps {
  weeklyPlan: DaySchedule[];
  onAddTopic?: (dayOfWeek: string, weekNumber: number, source: 'platform' | 'manual') => void;
}

export const StudySchedule = ({ weeklyPlan, onAddTopic }: StudyScheduleProps) => {
  // Agrupar dias por semana com cálculos atualizados de horas totais
  const weekGroups = weeklyPlan.reduce((acc, day) => {
    const weekNumber = day.weekNumber || Math.floor(weeklyPlan.indexOf(day) / 7) + 1;
    if (!acc[weekNumber]) {
      acc[weekNumber] = [];
    }
    
    // Atualiza o total de horas do dia com base nos tópicos
    const dayTotalMinutes = day.topics.reduce((sum, topic) => {
      return sum + parseDurationToMinutes(topic.duration || "0:00");
    }, 0);
    
    const dayWithCalculatedHours = {
      ...day,
      totalHours: dayTotalMinutes / 60, // Converte para horas
      calculatedTotalHours: sumDurations(day.topics.map(topic => topic.duration || "0:00"))
    };
    
    acc[weekNumber].push(dayWithCalculatedHours);
    return acc;
  }, {} as Record<number, DaySchedule[]>);

  console.log('📊 [StudySchedule] Grouped weekly plan with calculated hours:', weekGroups);

  // Determinar a semana atual
  const currentDate = new Date();
  const [currentWeekNumber, setCurrentWeekNumber] = useState<number | null>(null);
  
  useEffect(() => {
    // Converter a data atual para formato de string YYYY-MM-DD para comparação consistente
    const currentDateStr = currentDate.toISOString().split('T')[0];
    
    // Ordenar os números das semanas e iterar por eles em ordem crescente
    const weekNumbers = Object.keys(weekGroups).map(Number).sort((a, b) => a - b);
    
    // Encontrar a primeira semana que contém a data atual
    for (const weekNumber of weekNumbers) {
      const days = weekGroups[weekNumber];
      if (days.length > 0) {
        const weekStartDate = days[0].weekStartDate;
        const weekEndDate = days[0].weekEndDate;
        
        console.log(`📅 Checking week ${weekNumber} in StudySchedule:`, { 
          currentDate: currentDateStr, 
          weekStart: weekStartDate, 
          weekEnd: weekEndDate,
          isInRange: currentDateStr >= weekStartDate && currentDateStr <= weekEndDate
        });
        
        if (currentDateStr >= weekStartDate && currentDateStr <= weekEndDate) {
          console.log(`🎯 Found matching week: ${weekNumber}`);
          setCurrentWeekNumber(weekNumber);
          break; // Sai do loop assim que encontrar a primeira semana correspondente
        }
      }
    }
    
    console.log('📅 Current week number identified:', currentWeekNumber);
  }, [weekGroups, currentDate]); // Adiciona currentDate às dependências
  
  const handleAddTopic = (day: string, weekNumber: number, source: 'platform' | 'manual') => {
    console.log("📝 StudySchedule - Adding topic:", { day, weekNumber, source });
    if (onAddTopic) {
      onAddTopic(day, weekNumber, source);
    }
  };

  return (
    <Card className="p-4 sm:p-8 bg-white/50 backdrop-blur-sm">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between mb-4 sm:mb-8"
      >
        <div className="flex items-center gap-3">
          <Calendar className="h-6 w-6 sm:h-8 sm:w-8 text-primary" />
          <div>
            <h2 className="text-xl sm:text-2xl font-semibold">Cronograma de Estudos</h2>
            <p className="text-gray-500 text-sm sm:text-base">Organize sua semana de forma eficiente</p>
          </div>
        </div>
        
        {currentWeekNumber && (
          <Badge className="px-2 sm:px-3 py-0.5 sm:py-1 bg-primary/10 text-primary border border-primary/20 text-xs sm:text-sm">
            Semana atual: {currentWeekNumber}
          </Badge>
        )}
      </motion.div>

      <div className="space-y-4 sm:space-y-6">
        {Object.entries(weekGroups)
          .sort(([a], [b]) => parseInt(a) - parseInt(b))
          .map(([weekNumber, days]) => {
            // Calcular o total de horas real para cada semana baseado nos tópicos
            const allTopics = days.flatMap(day => day.topics || []);
            const weekTotalHours = allTopics.reduce((sum, topic) => {
              return sum + parseDurationToMinutes(topic.duration || "0:00");
            }, 0) / 60;
            
            return (
              <WeekCard
                key={weekNumber}
                weekNumber={parseInt(weekNumber)}
                days={days}
                totalHours={weekTotalHours}
                weekStartDate={days[0].weekStartDate}
                weekEndDate={days[0].weekEndDate}
                isCurrentWeek={parseInt(weekNumber) === currentWeekNumber}
                defaultExpanded={parseInt(weekNumber) === currentWeekNumber}
                onAddTopic={handleAddTopic}
              />
            );
          })}
      </div>
    </Card>
  );
};
