
import React from "react";
import { Link } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { Card } from "@/components/ui/card";
import { getThemeClasses } from "@/components/ui/theme-utils";

const VenomousFlowchart = () => {
  const venomousTypes = [
    {
      id: "bothropic",
      name: "<PERSON><PERSON><PERSON>",
      description: "Jararaca, jararacuçu, urutu, caiçaca",
      path: "/flowcharts/bothropic",
      color: "green"
    },
    {
      id: "crotalic",
      name: "<PERSON><PERSON><PERSON>",
      description: "Cascavel",
      path: "/flowcharts/crotalic",
      color: "purple"
    },
    {
      id: "elapidic",
      name: "Acid<PERSON>apídic<PERSON>",
      description: "Coral verdadeira",
      path: "/flowcharts/elapidic",
      color: "red"
    },
    {
      id: "loxoscelic",
      name: "Acid<PERSON>",
      description: "Aranha marrom",
      path: "/flowcharts/loxoscelic",
      color: "amber"
    },
    {
      id: "phoneutric",
      name: "Acidente Fonêutrico",
      description: "Aranha armadeira",
      path: "/flowcharts/phoneutria",
      color: "orange"
    },
    {
      id: "scorpionic",
      name: "Acidente Escorpiônico",
      description: "Escorpião",
      path: "/flowcharts/scorpion",
      color: "yellow"
    }
  ];

  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen flex flex-col from-emerald-100 via-white to-emerald-50 dark:from-emerald-950 dark:via-slate-900 dark:to-emerald-950")}>
      <HelmetWrapper>
        <title>PedBook | Animais Peçonhentos</title>
        <meta
          name="description"
          content="Fluxogramas para manejo de acidentes com animais peçonhentos em pediatria"
        />
      </HelmetWrapper>

      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto space-y-8">
          <div className="flex items-center gap-4">
            <Link
              to="/flowcharts"
              className="inline-flex items-center gap-2 text-emerald-600 hover:text-emerald-700 dark:text-emerald-400 dark:hover:text-emerald-300 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Voltar para Fluxogramas</span>
            </Link>
          </div>

          <div className="text-center space-y-4">
            <h1 className={getThemeClasses.gradientHeading("text-4xl font-bold from-emerald-600 to-green-600 dark:from-emerald-400 dark:to-green-400")}>
              Acidentes com Animais Peçonhentos
            </h1>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Fluxogramas para manejo de acidentes com animais peçonhentos em pediatria
            </p>
          </div>

          <div className="grid grid-cols-2 xs:grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-6">
            {venomousTypes.map((type) => (
              <Link 
                key={type.id} 
                to={type.path}
                className="block transform transition duration-300 hover:scale-105"
              >
                <Card className={getThemeClasses.gradientCard(type.color, "p-6 h-full")}>
                  <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-2">
                    {type.name}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300">
                    {type.description}
                  </p>
                </Card>
              </Link>
            ))}
          </div>

          <Card className={getThemeClasses.card("p-6 mt-8")}>
            <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
              Princípios Gerais do Atendimento
            </h3>
            <div className="space-y-4 text-gray-700 dark:text-gray-300">
              <p>
                O atendimento dos acidentes por animais peçonhentos baseia-se no diagnóstico do tipo de acidente, 
                na avaliação da sua gravidade e na indicação do antiveneno (soro) específico.
              </p>
              <p>
                A grande maioria dos acidentes ocorre em membros inferiores e superiores, onde a grande parte 
                dos acidentes ofídicos poderia ser evitada com o uso de calçados, botas, luvas de couro e outros.
              </p>
              <h4 className="font-semibold mt-4">Medidas que não devem ser realizadas:</h4>
              <ul className="list-disc list-inside">
                <li>Torniquete ou garrote</li>
                <li>Incisão, corte ou sucção no local da picada</li>
                <li>Aplicação de folhas, pó de café ou fezes no local da picada</li>
                <li>Uso de soluções caseiras para lavagem do local da picada</li>
              </ul>
            </div>
          </Card>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default VenomousFlowchart;
