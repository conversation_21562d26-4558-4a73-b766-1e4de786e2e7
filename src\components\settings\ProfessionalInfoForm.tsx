import React from "react";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Building2, GraduationCap } from "lucide-react";

interface ProfessionalInfoFormProps {
  profile: any;
  setProfile: (profile: any) => void;
}

const ProfessionalInfoForm = ({ profile, setProfile }: ProfessionalInfoFormProps) => {
  const currentYear = new Date().getFullYear();
  const graduationYears = Array.from({ length: 60 }, (_, i) => currentYear - i);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Informações Profissionais</CardTitle>
        <CardDescription>
          Mantenha seus dados profissionais atualizados.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="formation_area" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Área de formação
          </Label>
          <Select
            value={profile?.formation_area || ''}
            onValueChange={(value) =>
              setProfile({ ...profile, formation_area: value })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Selecione sua área" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="medicina">Medicina</SelectItem>
              <SelectItem value="enfermagem">Enfermagem</SelectItem>
              <SelectItem value="farmacia">Farmácia</SelectItem>
              <SelectItem value="fisioterapia">Fisioterapia</SelectItem>
              <SelectItem value="nutricao">Nutrição</SelectItem>
              <SelectItem value="outro">Outro</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="graduation_year" className="flex items-center gap-2">
            <GraduationCap className="h-4 w-4" />
            Ano de formação
          </Label>
          <Select
            value={profile?.graduation_year || ''}
            onValueChange={(value) =>
              setProfile({ ...profile, graduation_year: value })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Selecione o ano" />
            </SelectTrigger>
            <SelectContent>
              {graduationYears.map((year) => (
                <SelectItem key={year} value={year.toString()}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="registration_number" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Número de registro profissional
          </Label>
          <Input
            id="registration_number"
            value={profile?.registration_number || ''}
            onChange={(e) =>
              setProfile({ ...profile, registration_number: e.target.value })
            }
            placeholder="CRM/COREN/etc"
          />
        </div>

        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center justify-between space-x-2">
            <Label htmlFor="is_student">Sou estudante</Label>
            <Switch
              id="is_student"
              checked={profile?.is_student || false}
              onCheckedChange={(checked) =>
                setProfile({ ...profile, is_student: checked })
              }
            />
          </div>

          <div className="flex items-center justify-between space-x-2">
            <Label htmlFor="is_professional">Sou profissional</Label>
            <Switch
              id="is_professional"
              checked={profile?.is_professional || false}
              onCheckedChange={(checked) =>
                setProfile({ ...profile, is_professional: checked })
              }
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfessionalInfoForm;