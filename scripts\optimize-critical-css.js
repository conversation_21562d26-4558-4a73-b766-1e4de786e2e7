#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🎯 Optimizing critical CSS for better FCP/LCP...');

const distDir = path.join(process.cwd(), 'dist');
const htmlFile = path.join(distDir, 'index.html');

if (!fs.existsSync(htmlFile)) {
  console.error('❌ index.html not found in dist directory');
  process.exit(1);
}

let htmlContent = fs.readFileSync(htmlFile, 'utf8');

// CSS crítico ultra otimizado - apenas o essencial
const optimizedCriticalCSS = `
/* ULTRA CRITICAL CSS - Apenas essencial para FCP/LCP */
*{box-sizing:border-box;margin:0;padding:0}
body{font-family:'Inter',-apple-system,BlinkMacSystemFont,'Segoe UI',sans-serif;line-height:1.5;color:#1f2937;background:#fff}

/* Layout essencial */
.min-h-screen{min-height:100vh}
.flex{display:flex}
.flex-col{flex-direction:column}
.relative{position:relative}
.fixed{position:fixed}
.inset-0{inset:0}
.w-full{width:100%}
.container{width:100%;max-width:72rem;margin:0 auto}
.mx-auto{margin-left:auto;margin-right:auto}
.px-4{padding-left:1rem;padding-right:1rem}
.py-4{padding-top:1rem;padding-bottom:1rem}
.pb-24{padding-bottom:6rem}

/* Grid crítico */
.grid{display:grid}
.grid-cols-2{grid-template-columns:repeat(2,1fr)}
.gap-3{gap:.75rem}

/* Search crítico */
.search-input-field{width:100%;padding:.75rem 3rem;border-radius:9999px;border:none;outline:none;background:rgba(255,255,255,.9);box-shadow:0 4px 6px -1px rgba(0,0,0,.1);font-size:.875rem;color:#374151}

/* Cards essenciais */
.card-container{border-radius:.75rem;border:1px solid #f3f4f6;background:#fff;box-shadow:0 1px 2px rgba(0,0,0,.05);transition:transform .3s;cursor:pointer}
.card-container:hover{transform:translateY(-2px)}

/* Texto crítico */
.text-center{text-align:center}
.font-bold{font-weight:700}
.text-4xl{font-size:2.25rem;line-height:2.5rem}
.text-sm{font-size:.875rem;line-height:1.25rem}
.text-gray-600{color:#4b5563}
.text-transparent{color:transparent}
.bg-clip-text{-webkit-background-clip:text;background-clip:text}

/* Gradientes críticos */
.bg-gradient-to-b{background-image:linear-gradient(to bottom,var(--tw-gradient-stops))}
.bg-gradient-to-r{background-image:linear-gradient(to right,var(--tw-gradient-stops))}
.from-blue-50\\/80{--tw-gradient-from:#eff6ffcc;--tw-gradient-to:#eff6ff00;--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}
.to-white{--tw-gradient-to:#fff}
.from-primary{--tw-gradient-from:#0066ff;--tw-gradient-to:#0066ff00;--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}
.to-blue-600{--tw-gradient-to:#2563eb}

/* Spacing crítico */
.space-y-2>*+*{margin-top:.5rem}
.mb-4{margin-bottom:1rem}
.py-2{padding-top:.5rem;padding-bottom:.5rem}

/* Animação mínima */
.animate-fade-in-up{animation:fadeInUp .3s ease-out}
@keyframes fadeInUp{from{opacity:0;transform:translateY(1rem)}to{opacity:1;transform:translateY(0)}}

/* Responsive essencial */
.hidden{display:none}
@media (min-width:640px){.sm\\:grid-cols-3{grid-template-columns:repeat(3,1fr)}.sm\\:pb-0{padding-bottom:0}}
@media (min-width:768px){.md\\:grid-cols-4{grid-template-columns:repeat(4,1fr)}.md\\:py-6{padding-top:1.5rem;padding-bottom:1.5rem}.md\\:text-5xl{font-size:3rem;line-height:1}.md\\:text-base{font-size:1rem;line-height:1.5rem}.md\\:block{display:block}}

/* Dark mode essencial */
@media (prefers-color-scheme: dark){
body{background:#0f172a;color:#f1f5f9}
.card-container{background:#1e293b;border-color:#334155}
.text-gray-600{color:#d1d5db}
}
`;

// Substituir o CSS crítico existente
const cssRegex = /<style>[\s\S]*?<\/style>/;
const newCSSBlock = `<style>${optimizedCriticalCSS}</style>`;

htmlContent = htmlContent.replace(cssRegex, newCSSBlock);

// Escrever HTML otimizado
fs.writeFileSync(htmlFile, htmlContent);

const finalSize = fs.statSync(htmlFile).size;
console.log(`📊 Optimized HTML size: ${(finalSize / 1024).toFixed(2)} KB`);
console.log('🎉 Critical CSS optimization completed!');
