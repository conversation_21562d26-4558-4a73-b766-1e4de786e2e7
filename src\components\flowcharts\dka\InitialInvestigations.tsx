import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

interface InitialInvestigationsProps {
  onStart: () => void;
}

export const InitialInvestigations = ({ onStart }: InitialInvestigationsProps) => {
  return (
    <Card className="p-6 space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Suspeita de Cetoacidose Diabética</h3>
        
        <div className="space-y-2">
          <h4 className="font-medium">Exames necessários para investigação:</h4>
          <ul className="list-disc list-inside space-y-1 text-gray-600">
            <li>Glicemia</li>
            <li>Gasometria</li>
            <li>Eletrólitos</li>
            <li>Pesquisa de corpos cetônicos: elementos anormais do sedimento (EAS) ou cetonemia</li>
          </ul>
        </div>
      </div>

      <Button onClick={onStart} className="w-full">
        Iniciar avaliação
      </Button>
    </Card>
  );
};