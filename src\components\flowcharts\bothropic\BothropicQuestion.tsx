
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface BothropicQuestionProps {
  question: string;
  onAnswer: (answer: boolean) => void;
  selectedAnswer: boolean | null;
}

export const BothropicQuestion = ({
  question,
  onAnswer,
  selectedAnswer,
}: BothropicQuestionProps) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const isInitialQuestion = question === "O paciente apresenta clínica de envenenamento botrópico na admissão?";
  const isObservationQuestion = question === "A evolução apresenta clínica de envenenamento?";

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-8"
    >
      <div className="p-6 rounded-xl bg-green-50 border border-green-200 glass-card relative overflow-hidden dark:bg-green-900/30 dark:border-green-800/50">
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent pointer-events-none dark:from-green-800/10" />
        
        {isObservationQuestion && (
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg dark:bg-yellow-900/30 dark:border-yellow-800/50">
            <p className="text-yellow-800 dark:text-yellow-300 font-medium">
              Manter o paciente em observação mínima de 12h
            </p>
          </div>
        )}
        
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-6 text-center relative z-10">
          {question}
        </h2>
        
        <div className="flex flex-col sm:flex-row gap-4 relative z-10 mb-8">
          <Button
            onClick={() => onAnswer(true)}
            variant={selectedAnswer === true ? "default" : "outline"}
            className={`flex-1 transition-all duration-300 ${
              selectedAnswer === true
                ? "bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 dark:from-green-600 dark:to-emerald-600 dark:hover:from-green-700 dark:hover:to-emerald-700"
                : "hover:bg-green-50 dark:hover:bg-green-900/40 border-green-200 dark:border-green-700/50 dark:text-gray-200"
            }`}
          >
            Com clínica de envenenamento
          </Button>
          <Button
            onClick={() => onAnswer(false)}
            variant={selectedAnswer === false ? "default" : "outline"}
            className={`flex-1 transition-all duration-300 ${
              selectedAnswer === false
                ? "bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 dark:from-green-600 dark:to-emerald-600 dark:hover:from-green-700 dark:hover:to-emerald-700"
                : "hover:bg-green-50 dark:hover:bg-green-900/40 border-green-200 dark:border-green-700/50 dark:text-gray-200"
            }`}
          >
            Sem clínica de envenenamento
          </Button>
        </div>

        {isInitialQuestion && (
          <div className="space-y-4 bg-white/50 rounded-lg p-4 backdrop-blur-sm border border-green-100 dark:bg-slate-800/50 dark:border-green-900/30">
            <h3 className="text-lg font-semibold text-green-800 dark:text-green-300">
              Sinais de envenenamento botrópico incluem:
            </h3>
            
            <div className="space-y-3 text-gray-700 dark:text-gray-300">
              <div>
                <p className="font-medium text-green-700 dark:text-green-300">Manifestações locais:</p>
                <p>Dor, edema, equimose, sangramentos na região da picada.</p>
              </div>
              
              <div>
                <p className="font-medium text-green-700 dark:text-green-300">Manifestações sistêmicas leves:</p>
                <p>Náusea, vômito.</p>
              </div>
              
              <div>
                <p className="font-medium text-green-700 dark:text-green-300">Manifestações graves:</p>
                <p>Hemorragias, insuficiência renal, choque.</p>
              </div>
            </div>

            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button 
                  variant="outline" 
                  className="w-full mt-4 bg-green-100 hover:bg-green-200 border-green-300 dark:bg-green-900/30 dark:hover:bg-green-900/50 dark:border-green-800/50 dark:text-green-300"
                >
                  Saiba Mais
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl bg-white dark:bg-slate-900 dark:border-green-900/50">
                <DialogHeader>
                  <DialogTitle className="dark:text-white">Detalhamento dos Sinais de Envenenamento Botrópico</DialogTitle>
                  <DialogDescription className="space-y-4 pt-4 dark:text-gray-300">
                    <div>
                      <h4 className="font-semibold text-green-700 dark:text-green-300 mb-2">Manifestações Locais</h4>
                      <ul className="list-disc pl-5 space-y-1 dark:text-gray-300">
                        <li>Dor local intensa e imediata</li>
                        <li>Edema progressivo</li>
                        <li>Equimose</li>
                        <li>Sangramento local</li>
                      </ul>
                    </div>
                    
                    <div>
                      <h4 className="font-semibold text-green-700 dark:text-green-300 mb-2">Manifestações Sistêmicas</h4>
                      <ul className="list-disc pl-5 space-y-1 dark:text-gray-300">
                        <li>Náusea e vômitos</li>
                        <li>Sangramentos em mucosas</li>
                        <li>Hipotensão arterial</li>
                        <li>Choque</li>
                      </ul>
                    </div>
                    
                    <div>
                      <h4 className="font-semibold text-green-700 dark:text-green-300 mb-2">Manifestações Graves</h4>
                      <ul className="list-disc pl-5 space-y-1 dark:text-gray-300">
                        <li>Hemorragias</li>
                        <li>Insuficiência renal</li>
                        <li>Choque</li>
                      </ul>
                    </div>
                  </DialogDescription>
                </DialogHeader>
              </DialogContent>
            </Dialog>
          </div>
        )}
      </div>
    </motion.div>
  );
};
