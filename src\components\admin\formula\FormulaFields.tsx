import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ImageUpload } from "./ImageUpload";
import { ImagePreview } from "./ImagePreview";

interface FormulaFieldsProps {
  name: string;
  setName: (value: string) => void;
  brand: string;
  setBrand: (value: string) => void;
  categoryId: string;
  setCategoryId: (value: string) => void;
  description: string;
  setDescription: (value: string) => void;
  ageRange: string;
  setAgeRange: (value: string) => void;
  price: string;
  setPrice: (value: string) => void;
  imageUrl: string;
  setImageUrl: (value: string) => void;
  categories: any[];
}

export function FormulaFields({
  name,
  setName,
  brand,
  setBrand,
  categoryId,
  setCategoryId,
  description,
  setDescription,
  ageRange,
  setAgeRange,
  price,
  setPrice,
  imageUrl,
  setImageUrl,
  categories,
}: FormulaFieldsProps) {
  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="name">Nome</Label>
        <Input
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          required
        />
      </div>
      
      <div>
        <Label htmlFor="brand">Marca</Label>
        <Input
          id="brand"
          value={brand}
          onChange={(e) => setBrand(e.target.value)}
          required
        />
      </div>

      <div>
        <Label htmlFor="category">Categoria</Label>
        <Select
          value={categoryId}
          onValueChange={setCategoryId}
        >
          <SelectTrigger>
            <SelectValue placeholder="Selecione uma categoria" />
          </SelectTrigger>
          <SelectContent>
            {categories?.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="ageRange">Faixa Etária</Label>
        <Input
          id="ageRange"
          value={ageRange}
          onChange={(e) => setAgeRange(e.target.value)}
          required
        />
      </div>

      <div>
        <Label htmlFor="price">Preço</Label>
        <Input
          id="price"
          type="number"
          step="0.01"
          value={price}
          onChange={(e) => setPrice(e.target.value)}
        />
      </div>

      <div>
        <Label htmlFor="description">Descrição</Label>
        <Textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          className="min-h-[100px]"
        />
      </div>

      <div className="space-y-2">
        <Label>Imagem</Label>
        <div className="flex flex-col gap-4">
          {imageUrl && (
            <ImagePreview 
              imageUrl={imageUrl} 
              onRemove={() => setImageUrl('')} 
            />
          )}
          <ImageUpload onImageUploaded={setImageUrl} />
        </div>
      </div>
    </div>
  );
}