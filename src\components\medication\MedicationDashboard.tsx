
import { useState, useEffect } from "react";
import { getCategoryConfig } from "@/config/medicationCategories";
import { useLocation } from "react-router-dom";
import { CategorySelector } from "./components/CategorySelector";
import { MedicationList } from "./components/MedicationList";
import { ChevronLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import AppStyleMedicationCard from "./AppStyleMedicationCard";
import { useLoading } from "@/context/LoadingContext";
import { useMedicationCategoriesWithMedications } from "@/hooks/useMedicationData";

interface MedicationDashboardProps {
  showTitleOnMobile?: boolean;
}

export const MedicationDashboard = ({ showTitleOnMobile = false }: MedicationDashboardProps) => {
  const location = useLocation();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [isMobile, setIsMobile] = useState<boolean | null>(null);
  const { startLoading, stopLoading } = useLoading();

  const { data: categories, isLoading } = useMedicationCategoriesWithMedications();

  useEffect(() => {
    const state = location.state as { selectedCategory?: string } | null;
    if (state?.selectedCategory) {
      setSelectedCategory(state.selectedCategory);
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    // Parar o carregamento quando o componente for montado
    stopLoading();

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [stopLoading]);

  const selectedCategoryData = categories?.find(cat => cat.id === selectedCategory);

  // Mostrar um indicador de carregamento enquanto verificamos o tamanho da tela
  if (isMobile === null) {
    return null;
  }

  // Mostrar um esqueleto de carregamento enquanto os dados são buscados
  if (isLoading) {
    return (
      <div className="w-full space-y-8 p-6 md:p-10 bg-gradient-to-br from-white/95 via-white/90 to-blue-50/30 dark:from-slate-900/95 dark:via-slate-800/90 dark:to-slate-900/30 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 dark:border-white/10 overflow-hidden">
        <div className="animate-pulse">
          <div className="text-center space-y-6 mb-8">
            <div className="space-y-3">
              <div className="h-10 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded-lg w-2/3 mx-auto"></div>
              <div className="w-24 h-1 bg-gray-300 dark:bg-gray-600 mx-auto rounded-full"></div>
            </div>
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mx-auto"></div>
          </div>
          <div className="grid gap-4 sm:gap-6 grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {[...Array(12)].map((_, i) => (
              <div key={i} className="h-40 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded-2xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-8 p-6 md:p-10 bg-gradient-to-br from-white/98 via-white/95 to-blue-50/40 dark:from-slate-900/98 dark:via-slate-800/95 dark:to-slate-900/40 backdrop-blur-xl rounded-3xl shadow-2xl border-2 border-gray-300/50 dark:border-slate-600/50 overflow-hidden relative">
      {/* Padrão de fundo decorativo */}
      <div className="absolute inset-0 opacity-5 dark:opacity-10">
        <div className="absolute top-10 left-10 w-32 h-32 bg-blue-500 rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-purple-500 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 bg-cyan-500 rounded-full blur-2xl"></div>
      </div>
        <div className="relative z-10">
        {selectedCategory ? (
          <div className="space-y-6">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-2 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-xl px-4 py-2 transition-all duration-200"
                onClick={() => {
                  setSelectedCategory(null);
                }}
              >
                <ChevronLeft className="h-4 w-4" />
                Voltar para categorias
              </Button>
            </div>
            <MedicationList selectedCategory={selectedCategoryData} />
          </div>
        ) : (
          <>
            {/* Desktop */}
            {!isMobile && (
              <div className="text-center space-y-6 mb-8">
                <div className="space-y-3">
                  <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">
                    Categorias de Medicamentos
                  </h1>
                  <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full"></div>
                </div>

              </div>
            )}

            <div className="grid gap-4 sm:gap-6 grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4">
              {categories?.map((category) => {
                const config = getCategoryConfig(category);

                return (
                  <div
                    key={category.id}
                    className="cursor-pointer"
                  >
                    <AppStyleMedicationCard
                      title={category.name}
                      icon={config.icon}
                      color={config.color}
                      onClick={() => {
                        setSelectedCategory(category.id);
                      }}
                      badge={undefined}
                    />
                  </div>
                );
              })}
            </div>
          </>
        )}
        </div>
    </div>
  );
};
