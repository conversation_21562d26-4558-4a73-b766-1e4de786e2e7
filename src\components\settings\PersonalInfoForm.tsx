import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Building2, GraduationCap, User, Mail, Phone } from "lucide-react";
import { Button } from "@/components/ui/button";

interface PersonalInfoFormProps {
  profile: any;
  setProfile: (profile: any) => void;
  handleProfileUpdate: (e: React.FormEvent<HTMLFormElement>) => void;
}

const PersonalInfoForm = ({ profile, setProfile, handleProfileUpdate }: PersonalInfoFormProps) => {
  return (
    <form onSubmit={handleProfileUpdate} className="space-y-6">
      {/* Card com design limpo */}
      <Card className="border shadow-sm">
        <CardHeader className="px-4 sm:px-6 py-4 sm:py-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10 text-primary">
              <User className="h-5 w-5" />
            </div>
            <div>
              <CardTitle className="text-lg sm:text-xl">Informações Pessoais</CardTitle>
              <CardDescription className="text-sm sm:text-base">
                Mantenha seus dados atualizados
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6 px-4 sm:px-6 pb-6">
          {/* Grid responsivo para inputs */}
          <div className="grid grid-cols-1 gap-4 sm:gap-6">
            <div className="space-y-2">
              <Label htmlFor="full_name" className="flex items-center gap-2 text-sm font-medium">
                <User className="h-4 w-4" />
                Nome completo
              </Label>
              <Input
                id="full_name"
                value={profile?.full_name || ''}
                onChange={(e) =>
                  setProfile({ ...profile, full_name: e.target.value })
                }
                placeholder="Seu nome completo"
                className="h-11"
              />
            </div>
          </div>

          {/* Grid 2 colunas para email e telefone */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
            <div className="space-y-2">
              <Label htmlFor="email" className="flex items-center gap-2 text-sm font-medium">
                <Mail className="h-4 w-4" />
                Email profissional
              </Label>
              <Input
                id="email"
                type="email"
                value={profile?.professional_email || ''}
                onChange={(e) =>
                  setProfile({ ...profile, professional_email: e.target.value })
                }
                placeholder="<EMAIL>"
                className="h-11"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone" className="flex items-center gap-2 text-sm font-medium">
                <Phone className="h-4 w-4" />
                Telefone
              </Label>
              <Input
                id="phone"
                type="tel"
                value={profile?.phone || ''}
                onChange={(e) =>
                  setProfile({ ...profile, phone: e.target.value })
                }
                placeholder="(00) 00000-0000"
                className="h-11"
              />
            </div>
          </div>

          {/* Grid para área de formação e ano */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
            <div className="space-y-2">
              <Label htmlFor="formation_area" className="flex items-center gap-2 text-sm font-medium">
                <Building2 className="h-4 w-4" />
                Área de formação
              </Label>
              <Select
                value={profile?.formation_area || ''}
                onValueChange={(value) =>
                  setProfile({ ...profile, formation_area: value })
                }
              >
                <SelectTrigger className="h-11">
                  <SelectValue placeholder="Selecione sua área" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="medicina">Medicina</SelectItem>
                  <SelectItem value="enfermagem">Enfermagem</SelectItem>
                  <SelectItem value="farmacia">Farmácia</SelectItem>
                  <SelectItem value="fisioterapia">Fisioterapia</SelectItem>
                  <SelectItem value="nutricao">Nutrição</SelectItem>
                  <SelectItem value="outro">Outro</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="graduation_year" className="flex items-center gap-2 text-sm font-medium">
                <GraduationCap className="h-4 w-4" />
                Ano de formação
              </Label>
              <Input
                id="graduation_year"
                type="text"
                value={profile?.graduation_year || ''}
                onChange={(e) =>
                  setProfile({ ...profile, graduation_year: e.target.value })
                }
                placeholder="Ex: 2024"
                className="h-11"
              />
            </div>
          </div>

          {/* Registro profissional */}
          <div className="space-y-2">
            <Label htmlFor="registration_number" className="flex items-center gap-2 text-sm font-medium">
              <Building2 className="h-4 w-4" />
              Número de registro profissional
            </Label>
            <Input
              id="registration_number"
              value={profile?.registration_number || ''}
              onChange={(e) =>
                setProfile({ ...profile, registration_number: e.target.value })
              }
              placeholder="CRM/COREN/etc"
              className="h-11"
            />
          </div>

          {/* Switches com design limpo */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="flex items-center justify-between p-4 rounded-lg border bg-muted/30">
              <div className="flex items-center gap-2">
                <GraduationCap className="h-4 w-4 text-muted-foreground" />
                <Label htmlFor="is_student" className="text-sm font-medium cursor-pointer">Sou estudante</Label>
              </div>
              <Switch
                id="is_student"
                checked={profile?.is_student || false}
                onCheckedChange={(checked) =>
                  setProfile({ ...profile, is_student: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between p-4 rounded-lg border bg-muted/30">
              <div className="flex items-center gap-2">
                <Building2 className="h-4 w-4 text-muted-foreground" />
                <Label htmlFor="is_professional" className="text-sm font-medium cursor-pointer">Sou profissional</Label>
              </div>
              <Switch
                id="is_professional"
                checked={profile?.is_professional || false}
                onCheckedChange={(checked) =>
                  setProfile({ ...profile, is_professional: checked })
                }
              />
            </div>
          </div>

          {/* Botão simples e proporcional */}
          <div className="pt-6 border-t">
            <Button
              type="submit"
              className="w-full h-11"
            >
              <User className="h-4 w-4 mr-2" />
              Salvar alterações
            </Button>
          </div>
        </CardContent>
      </Card>
    </form>
  );
};

export default PersonalInfoForm;