import { useState, useEffect } from "react";

interface UseAgeInputProps {
  ageInMonths: number;
  onChange: (ageInMonths: number) => void;
  onCommit: (ageInMonths: number) => void;
}

export const useAgeInput = ({ ageInMonths, onChange, onCommit }: UseAgeInputProps) => {
  // Automatically set initial unit based on age value
  const getInitialUnit = (months: number) => {
    return months <= 12 ? "months" : "years";
  };

  const [unit, setUnit] = useState<"months" | "years">(getInitialUnit(ageInMonths));
  const [inputValue, setInputValue] = useState(() => {
    if (ageInMonths === 0) return "";
    return unit === "years" ? Math.floor(ageInMonths / 12).toString() : ageInMonths.toString();
  });

  const handleUnitChange = (newUnit: "months" | "years") => {
    const currentValue = parseFloat(inputValue);
    if (!isNaN(currentValue)) {
      if (newUnit === "months" && unit === "years") {
        // Convert years to months
        const monthValue = currentValue * 12;
        // Limit to 12 months
        const limitedMonthValue = Math.min(12, Math.max(0, monthValue));
        setInputValue(limitedMonthValue.toString());
        onChange(limitedMonthValue);
        onCommit(limitedMonthValue);
      } else if (newUnit === "years" && unit === "months") {
        // Convert months to years
        const yearValue = Math.floor(currentValue / 12);
        // Limit to 100 years
        const limitedYearValue = Math.min(100, Math.max(1, yearValue));
        setInputValue(limitedYearValue.toString());
        onChange(limitedYearValue * 12);
        onCommit(limitedYearValue * 12);
      }
    }
    setUnit(newUnit);
  };

  const handleChange = (value: string) => {
    setInputValue(value);
    const numValue = parseFloat(value);
    
    if (!isNaN(numValue)) {
      let adjustedValue = numValue;
      
      if (unit === "months") {
        // Limit months between 0 and 12
        adjustedValue = Math.min(12, Math.max(0, numValue));
        onChange(adjustedValue);
      } else {
        // Limit years between 1 and 100
        adjustedValue = Math.min(100, Math.max(1, numValue));
        onChange(adjustedValue * 12);
      }
    }
  };

  const handleBlur = () => {
    const numValue = parseFloat(inputValue);
    if (!isNaN(numValue)) {
      let adjustedValue = numValue;
      
      if (unit === "months") {
        // Limit months between 0 and 12
        adjustedValue = Math.min(12, Math.max(0, numValue));
        setInputValue(adjustedValue.toString());
        onCommit(adjustedValue);
      } else {
        // Limit years between 1 and 100
        adjustedValue = Math.min(100, Math.max(1, numValue));
        setInputValue(adjustedValue.toString());
        onCommit(adjustedValue * 12);
      }
    }
  };

  useEffect(() => {
    // When ageInMonths changes externally, update the display value
    // but maintain the current unit
    if (ageInMonths === 0) {
      setInputValue("");
    } else {
      const displayValue = unit === "years" ? 
        Math.min(100, Math.max(1, Math.floor(ageInMonths / 12))) : 
        Math.min(12, Math.max(0, ageInMonths));
      setInputValue(displayValue.toString());
    }
  }, [ageInMonths, unit]);

  return {
    unit,
    inputValue,
    handleUnitChange,
    handleChange,
    handleBlur
  };
};