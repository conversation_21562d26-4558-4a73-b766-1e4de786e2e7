import { ImportGrowthCurveMetadata } from "@/components/admin/growth-curve-metadata/ImportGrowthCurveMetadata";
import { GrowthCurveMetadataList } from "@/components/admin/growth-curve-metadata/GrowthCurveMetadataList";
import { GrowthCurveDataPoint, GrowthCurveGender, GrowthCurveType } from "@/components/admin/growth-curve-metadata/types";

export default function GrowthCurveMetadata() {
  const handleImport = ({ type, gender, data }: { 
    type: GrowthCurveType; 
    gender: GrowthCurveGender; 
    data: GrowthCurveDataPoint[] 
  }) => {
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Importação de Dados das Curvas de Crescimento
        </h1>
        <p className="text-gray-500 mt-1">
          Importe os dados das curvas de crescimento para uso no sistema
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <ImportGrowthCurveMetadata onImport={handleImport} />
        <GrowthCurveMetadataList />
      </div>
    </div>
  );
}