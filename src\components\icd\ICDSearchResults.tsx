import { Copy } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";

interface ICDResult {
  code: string;
  name: string;
  description: string | null;
}

interface ICDSearchResultsProps {
  results: ICDResult[];
  isLoading: boolean;
  searchTerm: string;
}

export const ICDSearchResults = ({ results, isLoading, searchTerm }: ICDSearchResultsProps) => {
  const { toast } = useToast();

  return (
    <div className="space-y-4 animate-fade-in">
      {isLoading ? (
        <div className="text-center py-8">
          <div className="inline-flex items-center gap-2 text-gray-500">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            Buscando códigos CID...
          </div>
        </div>
      ) : results && results.length > 0 ? (
        <>
          <div className="text-center text-sm text-gray-600 mb-4">
            {results.length} resultado{results.length !== 1 ? 's' : ''} encontrado{results.length !== 1 ? 's' : ''} para "{searchTerm}"
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-6xl mx-auto">
            {results.map((result, index) => (
            <Card key={`${result.code}-${index}`} className="p-4 hover:shadow-md transition-shadow bg-white/80 backdrop-blur-sm border-primary/10">
              <div className="flex items-start justify-between">
                <div>
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold text-primary">{result.code}</h3>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 text-gray-400 hover:text-primary"
                      onClick={() => {
                        navigator.clipboard.writeText(result.code);
                        toast({
                          description: "Código CID copiado com sucesso!",
                          duration: 2000,
                        });
                      }}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-gray-700 line-clamp-2">{result.name}</p>
                  {result.description && (
                    <p className="text-sm text-gray-500 mt-1 line-clamp-2">{result.description}</p>
                  )}
                </div>
              </div>
            </Card>
          ))}
          </div>
        </>
      ) : searchTerm && searchTerm.length >= 2 ? (
        <div className="text-center py-8">
          <p className="text-gray-500 mb-2">Nenhum resultado encontrado para "{searchTerm}"</p>
          <p className="text-sm text-gray-400">
            Tente buscar por:
            <br />• Nome da doença (ex: diabetes, pneumonia)
            <br />• Código CID (ex: E10, J18)
          </p>
        </div>
      ) : null}
    </div>
  );
};