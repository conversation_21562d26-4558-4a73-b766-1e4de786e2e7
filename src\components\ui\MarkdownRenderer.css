/* Markdown Renderer Styles */
.markdown-content {
  font-family: inherit;
  line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin: 0.5em 0 0.3em 0;
  font-weight: 600;
  line-height: 1.3;
}

.markdown-content h1 {
  font-size: 1.5em;
}

.markdown-content h2 {
  font-size: 1.3em;
}

.markdown-content h3 {
  font-size: 1.1em;
}

.markdown-content p {
  margin: 0.5em 0;
}

.markdown-content strong {
  font-weight: 600;
  color: hsl(var(--foreground));
}

.markdown-content em {
  font-style: italic;
}

.markdown-content ul,
.markdown-content ol {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.markdown-content li {
  margin: 0.2em 0;
}

.markdown-content ul li {
  list-style-type: disc;
}

.markdown-content ol li {
  list-style-type: decimal;
}

.markdown-content blockquote {
  border-left: 4px solid hsl(var(--border));
  padding-left: 1em;
  margin: 1em 0;
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

.markdown-content code {
  background-color: hsl(var(--muted));
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.9em;
}

.markdown-content pre {
  background-color: hsl(var(--muted));
  padding: 1em;
  border-radius: 6px;
  overflow-x: auto;
  margin: 1em 0;
}

.markdown-content pre code {
  background-color: transparent;
  padding: 0;
}

.markdown-content a {
  color: hsl(var(--primary));
  text-decoration: underline;
}

.markdown-content a:hover {
  text-decoration: none;
}

.markdown-content hr {
  border: none;
  border-top: 1px solid hsl(var(--border));
  margin: 1.5em 0;
}

/* Dark mode adjustments */
.dark .markdown-content {
  color: hsl(var(--foreground));
}

.dark .markdown-content strong {
  color: hsl(var(--foreground));
}

.dark .markdown-content blockquote {
  color: hsl(var(--muted-foreground));
  border-left-color: hsl(var(--border));
}

.dark .markdown-content code {
  background-color: hsl(var(--muted));
}

.dark .markdown-content pre {
  background-color: hsl(var(--muted));
}

/* Compact styles for small spaces */
.markdown-content.compact {
  font-size: 0.9em;
}

.markdown-content.compact h1,
.markdown-content.compact h2,
.markdown-content.compact h3,
.markdown-content.compact h4,
.markdown-content.compact h5,
.markdown-content.compact h6 {
  margin: 0.3em 0 0.2em 0;
}

.markdown-content.compact p {
  margin: 0.3em 0;
}

.markdown-content.compact ul,
.markdown-content.compact ol {
  margin: 0.3em 0;
  padding-left: 1.2em;
}

.markdown-content.compact li {
  margin: 0.1em 0;
}
