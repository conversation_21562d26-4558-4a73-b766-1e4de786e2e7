import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAgeInput } from "@/hooks/useAgeInput";
import { Calendar } from "lucide-react";

interface AgeInputProps {
  ageInMonths: number;
  onChange: (ageInMonths: number) => void;
  onCommit: (ageInMonths: number) => void;
  showMonths?: boolean;
  labelColor?: string;
}

export const AgeInput = ({ 
  ageInMonths, 
  onChange, 
  onCommit,
  showMonths = true,
  labelColor = "text-gray-700 dark:text-gray-200"
}: AgeInputProps) => {
  const {
    unit,
    inputValue,
    handleUnitChange,
    handleChange,
    handleBlur
  } = useAgeInput({
    ageInMonths,
    onChange,
    onCommit
  });

  return (
    <div className="space-y-2 w-full">
      <Label htmlFor="age" className={`text-sm font-medium ${labelColor} flex items-center gap-2`}>
        <Calendar className="h-4 w-4" />
        Idade do paciente
      </Label>
      <div className="flex items-center gap-2">
        <Input
          id="age"
          type="number"
          min={0}
          max={unit === "years" ? 100 : 12}
          value={inputValue}
          onChange={(e) => handleChange(e.target.value)}
          onBlur={handleBlur}
          className="flex-1 bg-white/50 border-primary/20 focus:border-primary/40 transition-colors text-center"
        />
        {showMonths ? (
          <Select value={unit} onValueChange={(value: "months" | "years") => handleUnitChange(value)}>
            <SelectTrigger
              className="w-[110px] bg-white/50 border-primary/20"
              aria-label="Selecionar unidade de idade (meses ou anos)"
            >
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="months">meses</SelectItem>
              <SelectItem value="years">anos</SelectItem>
            </SelectContent>
          </Select>
        ) : (
          <div className="w-[110px] px-4 py-2 bg-white/50 border border-primary/20 rounded-md text-sm text-center text-primary/80">
            anos
          </div>
        )}
      </div>
    </div>
  );
};