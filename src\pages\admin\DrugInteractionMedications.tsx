import React, { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Loader2, Check, Copy, AlertCircle } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

const DrugInteractionMedications = () => {
  const [inputText, setInputText] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [medications, setMedications] = useState<{id: string, active_ingredient: string, commercial_names: string}[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Fetch existing medications on component mount
  useEffect(() => {
    fetchMedications();
  }, []);

  const fetchMedications = async () => {
    setIsLoading(true);
    try {
      // Using the secure admin function to fetch medications
      const { data, error } = await supabase
        .rpc('admin_get_all_medications');

      if (error) throw error;

      setMedications(data || []);
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao carregar medicamentos",
        description: error.message || "Não foi possível carregar os medicamentos. Tente novamente.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const parseLine = (line: string) => {
    const parts = line.split("|").map(part => part.trim());
    if (parts.length !== 2) return null;

    return {
      active_ingredient: parts[0],
      commercial_names: parts[1]
    };
  };

  const handleImport = async () => {
    if (!inputText.trim()) {
      toast({
        variant: "destructive",
        title: "Texto vazio",
        description: "Por favor, insira algum conteúdo para importar.",
      });
      return;
    }

    setIsProcessing(true);

    try {
      const lines = inputText.split("\n").filter(line => line.trim());
      const validMedications: {active_ingredient: string, commercial_names: string}[] = [];
      const errors: string[] = [];
      const activePrincipleMap: {[key: string]: string} = {};

      // Parse each line
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const parsedLine = parseLine(line);

        if (!parsedLine) {
          errors.push(`Linha ${i + 1}: Formato inválido`);
          continue;
        }

        const { active_ingredient, commercial_names } = parsedLine;

        if (active_ingredient in activePrincipleMap) {
          // If active ingredient exists, merge commercial names
          if (commercial_names) {
            const existingNames = activePrincipleMap[active_ingredient];
            const newNames = commercial_names ? commercial_names : "";

            // Only append if not empty and not already contained
            if (newNames && existingNames) {
              activePrincipleMap[active_ingredient] = existingNames + ", " + newNames;
            } else if (newNames) {
              activePrincipleMap[active_ingredient] = newNames;
            }
          }
        } else {
          // New active ingredient
          activePrincipleMap[active_ingredient] = commercial_names || "";
        }
      }

      // Convert map to array of objects
      for (const [activeIngredient, commercialNames] of Object.entries(activePrincipleMap)) {
        validMedications.push({
          active_ingredient: activeIngredient,
          commercial_names: commercialNames
        });
      }



      if (errors.length > 0) {
        toast({
          variant: "destructive",
          title: `${errors.length} erros encontrados`,
          description: errors.join(", "),
        });
        return;
      }

      // Check for existing active ingredients to update instead of insert
      // This adds additional deduplication logic at insert time
      for (const medication of validMedications) {
        const { data: existingData, error: fetchError } = await supabase
          .from("pedbook_drug_interaction_medications")
          .select("*")
          .eq("active_ingredient", medication.active_ingredient)
          .maybeSingle();

        if (fetchError) throw fetchError;

        if (existingData) {
          // Update existing record by merging commercial names
          let newCommercialNames = medication.commercial_names;

          if (existingData.commercial_names && medication.commercial_names) {
            // Only add unique commercial names
            const existingNames = existingData.commercial_names.split(", ");
            const newNames = medication.commercial_names.split(", ");

            // Filter out duplicates and empty strings
            const combinedNames = [...new Set([...existingNames, ...newNames])].filter(name => name.trim());
            newCommercialNames = combinedNames.join(", ");
          } else if (existingData.commercial_names) {
            newCommercialNames = existingData.commercial_names;
          }

          const { error: updateError } = await supabase
            .from("pedbook_drug_interaction_medications")
            .update({ commercial_names: newCommercialNames })
            .eq("id", existingData.id);

          if (updateError) throw updateError;
        } else {
          // Insert new record
          const { error: insertError } = await supabase
            .from("pedbook_drug_interaction_medications")
            .insert([medication]);

          if (insertError) throw insertError;
        }
      }

      toast({
        title: "Importação concluída",
        description: `${validMedications.length} medicamentos processados com sucesso.`,
      });

      // Clear input and refresh medication list
      setInputText("");
      fetchMedications();

    } catch (error: any) {

      toast({
        variant: "destructive",
        title: "Erro na importação",
        description: error.message || "Não foi possível importar os medicamentos. Tente novamente.",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      const { error } = await supabase
        .from("pedbook_drug_interaction_medications")
        .delete()
        .eq("id", id);

      if (error) throw error;

      toast({
        title: "Medicamento excluído",
        description: "Medicamento removido com sucesso.",
      });

      setMedications(medications.filter(med => med.id !== id));
    } catch (error: any) {

      toast({
        variant: "destructive",
        title: "Erro ao excluir",
        description: error.message || "Não foi possível excluir o medicamento. Tente novamente.",
      });
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        toast({
          title: "Copiado!",
          description: "Informação copiada para a área de transferência."
        });
      },
      (err) => {

        toast({
          variant: "destructive",
          title: "Erro ao copiar",
          description: "Não foi possível copiar o texto."
        });
      }
    );
  };

  return (
    <div className="container mx-auto py-8">
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Importar Medicamentos para Interações</CardTitle>
            <CardDescription>
              Cole a lista de medicamentos no formato "Princípio ativo | Nome Comercial®, Nome Comercial®, ..."
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Textarea
                className="min-h-[200px] font-mono text-sm"
                placeholder="Azitromicina Di-hidratada | Astro®, Astro IV®, Azi®..."
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                disabled={isProcessing}
              />
              <Button
                onClick={handleImport}
                disabled={isProcessing || !inputText.trim()}
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processando...
                  </>
                ) : (
                  "Importar Medicamentos"
                )}
              </Button>

              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Formato esperado</AlertTitle>
                <AlertDescription>
                  Cada linha deve estar no formato: "Princípio ativo | Nome1®, Nome2®, Nome3®"
                </AlertDescription>
              </Alert>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Medicamentos para Interações</CardTitle>
            <CardDescription>
              Lista de medicamentos cadastrados para interações medicamentosas
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : medications.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                Nenhum medicamento cadastrado.
              </div>
            ) : (
              <ScrollArea className="h-[500px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Princípio Ativo</TableHead>
                      <TableHead>Nomes Comerciais</TableHead>
                      <TableHead className="w-24">Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {medications.map((med) => (
                      <TableRow key={med.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center space-x-2">
                            <span>{med.active_ingredient}</span>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                              onClick={() => copyToClipboard(med.active_ingredient)}
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <span className="truncate max-w-[400px]">{med.commercial_names || "—"}</span>
                            {med.commercial_names && (
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6"
                                onClick={() => copyToClipboard(med.commercial_names)}
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDelete(med.id)}
                          >
                            Excluir
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ScrollArea>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DrugInteractionMedications;
