import { useEffect, useRef, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

/**
 * Hook para detectar e recuperar automaticamente de problemas de conexão
 * Resolve o problema de timeout após inatividade
 */
export const useConnectionRecovery = () => {
  const queryClient = useQueryClient();
  const lastActivityRef = useRef<number>(Date.now());
  const recoveryTimeoutRef = useRef<NodeJS.Timeout>();
  const isRecoveringRef = useRef<boolean>(false);

  // Atualizar timestamp de última atividade
  const updateActivity = useCallback(() => {
    lastActivityRef.current = Date.now();
  }, []);

  // Função para recuperar conexões stale
  const recoverConnection = useCallback(async () => {
    if (isRecoveringRef.current) return;
    
    console.log('🔄 [ConnectionRecovery] Iniciando recuperação de conexão...');
    isRecoveringRef.current = true;

    try {
      // 1. Verificar sessão do Supabase
      const { data: session, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        console.warn('⚠️ [ConnectionRecovery] Erro na sessão:', sessionError);
      }

      // 2. Invalidar queries críticas que podem estar stale
      const criticalQueries = [
        ['medications'],
        ['medication-categories'],
        ['medications-with-instructions'],
        ['medication-instructions'],
      ];

      for (const queryKey of criticalQueries) {
        await queryClient.invalidateQueries({ queryKey });
      }

      // 3. Refetch queries ativas que podem ter falhado
      await queryClient.refetchQueries({
        type: 'active',
        stale: true
      });

      console.log('✅ [ConnectionRecovery] Recuperação concluída');
    } catch (error) {
      console.error('❌ [ConnectionRecovery] Erro na recuperação:', error);
    } finally {
      isRecoveringRef.current = false;
    }
  }, [queryClient]);

  // Detectar quando a página volta ao foco após inatividade
  const handleVisibilityChange = useCallback(() => {
    if (document.visibilityState === 'visible') {
      const timeSinceLastActivity = Date.now() - lastActivityRef.current;
      const INACTIVITY_THRESHOLD = 5 * 60 * 1000; // 5 minutos

      // Se ficou inativo por mais de 5 minutos, recuperar conexão
      if (timeSinceLastActivity > INACTIVITY_THRESHOLD) {
        console.log(`🕐 [ConnectionRecovery] Inatividade detectada: ${Math.round(timeSinceLastActivity / 1000)}s`);
        recoverConnection();
      }
      
      updateActivity();
    }
  }, [recoverConnection, updateActivity]);

  // Detectar quando a janela volta ao foco
  const handleWindowFocus = useCallback(() => {
    const timeSinceLastActivity = Date.now() - lastActivityRef.current;
    const FOCUS_THRESHOLD = 2 * 60 * 1000; // 2 minutos

    // Se ficou sem foco por mais de 2 minutos, verificar conexão
    if (timeSinceLastActivity > FOCUS_THRESHOLD) {
      console.log(`🎯 [ConnectionRecovery] Foco recuperado após ${Math.round(timeSinceLastActivity / 1000)}s`);
      
      // Delay pequeno para evitar múltiplas recuperações
      if (recoveryTimeoutRef.current) {
        clearTimeout(recoveryTimeoutRef.current);
      }
      
      recoveryTimeoutRef.current = setTimeout(() => {
        recoverConnection();
      }, 1000);
    }
    
    updateActivity();
  }, [recoverConnection, updateActivity]);

  // Detectar atividade do usuário
  const handleUserActivity = useCallback(() => {
    updateActivity();
  }, [updateActivity]);

  // Configurar listeners
  useEffect(() => {
    // Listeners para detectar atividade
    const activityEvents = ['click', 'keydown', 'scroll', 'touchstart'];
    
    // Listeners para detectar mudanças de foco/visibilidade
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleWindowFocus);
    
    // Listeners para atividade do usuário
    activityEvents.forEach(event => {
      document.addEventListener(event, handleUserActivity, { passive: true });
    });

    // Cleanup
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleWindowFocus);
      
      activityEvents.forEach(event => {
        document.removeEventListener(event, handleUserActivity);
      });

      if (recoveryTimeoutRef.current) {
        clearTimeout(recoveryTimeoutRef.current);
      }
    };
  }, [handleVisibilityChange, handleWindowFocus, handleUserActivity]);

  // Verificação periódica de conexão (a cada 30 minutos) - REDUZIDO PARA ECONOMIZAR BANDWIDTH
  useEffect(() => {
    const interval = setInterval(() => {
      const timeSinceLastActivity = Date.now() - lastActivityRef.current;
      const PERIODIC_CHECK_THRESHOLD = 30 * 60 * 1000; // 30 minutos - REDUZIDO

      if (timeSinceLastActivity > PERIODIC_CHECK_THRESHOLD) {
        console.log('⏰ [ConnectionRecovery] Verificação periódica - recuperando conexão');
        recoverConnection();
      }
    }, 30 * 60 * 1000); // A cada 30 minutos - REDUZIDO

    return () => clearInterval(interval);
  }, [recoverConnection]);

  return {
    recoverConnection,
    updateActivity,
    isRecovering: isRecoveringRef.current
  };
};
