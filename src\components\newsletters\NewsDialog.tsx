import React from 'react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  ExternalLink,
  Clock,
  Calendar,
  Tag,
  Globe,
  X
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { NewsItem } from '@/types/newsletter';
import { cn } from '@/lib/utils';

interface NewsDialogProps {
  news: NewsItem | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const NewsDialog: React.FC<NewsDialogProps> = ({
  news,
  open,
  onOpenChange
}) => {
  if (!news) return null;

  const formatDate = (dateString?: string | null) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return format(date, "EEEE, d 'de' MMMM 'de' yyyy 'às' HH:mm", { locale: ptBR });
    } catch (error) {
      return '';
    }
  };

  const handleOpenLink = () => {
    if (news.link) {
      window.open(news.link, '_blank', 'noopener,noreferrer');
    }
  };

  const renderCategories = () => {
    if (!news.category) return null;
    const categories = news.category.split(',').map(c => c.trim());

    return (
      <div className="flex flex-wrap gap-2">
        {categories.map((category, idx) => (
          <Badge
            key={idx}
            variant="secondary"
            className="bg-blue-50 text-blue-700 hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-300"
          >
            {category}
          </Badge>
        ))}
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95vw] max-w-4xl max-h-[80vh] sm:max-h-[90vh] overflow-hidden p-0">
        <div className="overflow-y-auto p-4 sm:p-6 max-h-[75vh] sm:max-h-[85vh]">
          <DialogHeader className="space-y-3 mb-4">
            <div className="flex items-start justify-between">
              <DialogTitle className="text-xl sm:text-2xl font-bold leading-tight pr-8">
                {news.title}
              </DialogTitle>
            </div>

            {/* Meta Information */}
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
              {news.source && (
                <div className="flex items-center gap-1">
                  <Globe className="w-4 h-4" />
                  <span className="font-medium">{news.source}</span>
                </div>
              )}

              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                <span>{formatDate(news.pub_date)}</span>
              </div>
            </div>

            {/* Categories */}
            {news.category && (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  <Tag className="w-4 h-4" />
                  <span>Categorias:</span>
                </div>
                {renderCategories()}
              </div>
            )}
          </DialogHeader>

          <Separator className="my-4" />

          {/* Image */}
          {news.image_url && (
            <div className="mb-4">
              <img
                src={news.image_url}
                alt={news.title}
                className="w-full h-48 sm:h-64 object-cover rounded-lg shadow-sm"
              />
            </div>
          )}

          {/* Summary */}
          {news.summary && (
            <div className="space-y-2">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-gray-100">
                Resumo
              </h3>
              <div className="prose prose-sm sm:prose prose-gray dark:prose-invert max-w-none">
                <p className="text-gray-700 dark:text-gray-300 leading-relaxed text-sm sm:text-base">
                  {news.summary}
                </p>
              </div>
            </div>
          )}

          {/* Content */}
          {news.content && (
            <div className="space-y-2 mt-4">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-gray-100">
                Conteúdo Completo
              </h3>
              <div className="prose prose-sm sm:prose prose-gray dark:prose-invert max-w-none">
                <div
                  className="text-gray-700 dark:text-gray-300 leading-relaxed text-sm sm:text-base"
                  dangerouslySetInnerHTML={{ __html: news.content }}
                />
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="flex-1 sm:flex-none order-2 sm:order-1 h-12 sm:h-10 text-base sm:text-sm"
              >
                Fechar
              </Button>

              {news.link && (
                <Button
                  onClick={handleOpenLink}
                  className="bg-blue-500 hover:bg-blue-600 text-white flex-1 sm:flex-none order-1 sm:order-2 h-12 sm:h-10 text-base sm:text-sm"
                >
                  <ExternalLink className="w-5 h-5 sm:w-4 sm:h-4 mr-2" />
                  <span className="hidden sm:inline">Ler Artigo Completo</span>
                  <span className="sm:hidden">Ler Artigo</span>
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
