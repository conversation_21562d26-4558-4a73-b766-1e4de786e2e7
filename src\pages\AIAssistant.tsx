import { useState, useEffect } from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { DiagnosisForm } from "@/components/ai-assistant/DiagnosisForm";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { TermsDialog } from "@/components/ai-assistant/TermsDialog";
import { supabase } from "@/integrations/supabase/client";

const AIAssistant = () => {
  const navigate = useNavigate();
  const [showTerms, setShowTerms] = useState(false);
  const [loading, setLoading] = useState(true);
  const [showDiagnosisResult, setShowDiagnosisResult] = useState(false);

  useEffect(() => {
    checkTermsAcceptance();
  }, []);

  const checkTermsAcceptance = async () => {
    try {
      const { data: session } = await supabase.auth.getSession();
      if (!session.session) {
        navigate("/");
        return;
      }

      const { data } = await supabase
        .from('ai_terms_acceptance')
        .select('accepted_at')
        .eq('user_id', session.session.user.id)
        .maybeSingle();

      setShowTerms(!data);
      setLoading(false);
    } catch (error) {
      console.error('Error checking terms acceptance:', error);
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-white via-primary/5 to-primary/10">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-primary/60 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
          <div className="w-4 h-4 bg-primary/60 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
          <div className="w-4 h-4 bg-primary/60 rounded-full animate-bounce"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-white via-primary/5 to-primary/10">
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto space-y-12">
          <Button
            onClick={() => navigate("/")}
            variant="outline"
            className="mb-6 bg-white/50 hover:bg-white/70 backdrop-blur-sm hidden sm:inline-flex"
          >
            Voltar ao Menu Inicial
          </Button>

          {!showDiagnosisResult && (
            <div className="text-center mb-8 animate-fade-in">
              <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 text-transparent bg-clip-text inline-block">
                Assistente IA PedBook
              </h1>
              <p className="text-gray-600 mt-2 max-w-2xl mx-auto">
                Auxílio inteligente para diagnósticos e condutas médicas em pediatria
              </p>
            </div>
          )}

          <div className="animate-fade-in-up">
            <DiagnosisForm onDiagnosisStart={() => setShowDiagnosisResult(true)} />
          </div>
        </div>
      </main>

      <Footer />

      {showTerms && <TermsDialog onAccept={() => setShowTerms(false)} />}
    </div>
  );
};

export default AIAssistant;