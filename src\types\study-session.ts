import { Json } from "@/integrations/supabase/types/json";

export interface StatCategory {
  name: string;
  correct: number;
  total: number;
}

export interface SessionStats {
  correct_answers: number;
  incorrect_answers: number;
  time_spent: number;
  by_theme: Record<string, StatCategory>;
  by_specialty: Record<string, StatCategory>;
  by_focus: Record<string, StatCategory>;
}

export interface StudySessionStats {
  correct_answers: number;
  incorrect_answers: number;
  time_spent: number;
  by_theme?: Record<string, StatCategory>;
  by_specialty?: Record<string, StatCategory>;
  by_focus?: Record<string, StatCategory>;
}

export interface CreateSessionParams {
  specialty?: string;
  theme?: string;
  focus?: string;
  total_questions: number;
  question_ids: string[];
}

export interface StudySession {
  id: string;
  user_id: string;
  specialty_id: string | null;
  theme_id: string | null;
  focus_id: string | null;
  started_at: string;
  completed_at: string | null;
  total_questions: number;
  current_question_index: number;
  stats: StudySessionStats;
  total_correct?: number | null;
  total_incorrect?: number | null;
  avg_response_time?: number | null;
  time_spent?: string | null;
  is_correct?: boolean | null;
  expires_at?: string | null;
  questions?: string[];
}

export interface StudySessionRow extends Omit<StudySession, 'stats'> {
  stats: Json;
  questions?: string[];
}