
import { useState } from "react";
import { Plus, AlertCircle, AlertTriangle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface DosageContentProps {
  name: string;
  calculatedDosage: string;
  summary?: string;
  requiredMeasures?: string[];
  restrictionMessage?: string | null;
  isCalculating?: boolean;
}

export const DosageContent = ({
  name,
  calculatedDosage,
  summary,
  requiredMeasures = ["weight", "age"],
  restrictionMessage,
  isCalculating = false
}: DosageContentProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const formatDosage = (dosage: string): string => {
    if (dosage === "Preencha os valores necessários") {
      return dosage;
    }

    // Verifica se há tags não calculadas (ex: ((gotas)), ((dosegeral)))
    const hasUnprocessedTags = /\(\([^)]+\)\)/.test(dosage);

    if (hasUnprocessedTags || isCalculating) {
      return "Calculando dosagem...";
    }

    return dosage.replace(/(\d+(?:\.\d{3})*(?:[,.]\d+)?)/g, (match) => {
      const cleanNumber = match.replace(/\./g, '').replace(',', '.');
      const num = parseFloat(cleanNumber);

      if (!isNaN(num)) {
        if (Math.abs(num) >= 1000) {
          const intValue = Math.round(num);
          return new Intl.NumberFormat('pt-BR', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
            useGrouping: true
          }).format(intValue);
        }

        const originalDecimals = match.includes(',') ? match.split(',')[1].length : 0;
        return new Intl.NumberFormat('pt-BR', {
          minimumFractionDigits: originalDecimals,
          maximumFractionDigits: originalDecimals,
          useGrouping: true
        }).format(num);
      }
      return match;
    });
  };

  const getMeasuresText = () => {
    const measures = [];
    if (requiredMeasures.includes("weight")) measures.push("peso");
    if (requiredMeasures.includes("age")) measures.push("idade");
    return measures.join(" e ");
  };

  return (
    <div className="card-container p-2 sm:p-6">
      <div className="space-y-3">
        <h4 className="text-lg sm:text-xl font-medium gradient-text">
          {name}
        </h4>

        {restrictionMessage ? (
          <Alert className="bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300 border border-amber-200 dark:border-amber-800/50">
            <AlertTriangle className="h-4 w-4 text-amber-500 dark:text-amber-400" />
            <AlertDescription className="text-amber-700 dark:text-amber-300">
              {restrictionMessage}
            </AlertDescription>
          </Alert>
        ) : calculatedDosage === "Preencha os valores necessários" ? (
          <Alert variant="destructive" className="bg-destructive/5 dark:bg-destructive/10 text-destructive dark:text-red-400 border-none">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Preencha o {getMeasuresText()} do paciente para calcular a dosagem
            </AlertDescription>
          </Alert>
        ) : (
          <div className="bg-gradient-to-br from-primary-light dark:from-blue-900/20 to-primary/5 dark:to-blue-800/10 rounded-lg p-2 sm:p-4 border border-blue-100 dark:border-blue-900/30 selectable-text">
            {(isCalculating || /\(\([^)]+\)\)/.test(calculatedDosage)) ? (
              <div className="flex items-center gap-3">
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-primary/30 border-t-primary"></div>
                <p className="text-base sm:text-lg text-gray-600 dark:text-gray-300 font-medium">
                  Calculando dosagem...
                </p>
              </div>
            ) : (
              <p className="text-base sm:text-lg text-gray-700 dark:text-gray-200 font-medium">
                {formatDosage(calculatedDosage)}
              </p>
            )}
          </div>
        )}

        {summary && (
          <div className="space-y-2">
            <Button
              variant="ghost"
              className="w-full flex items-center justify-center gap-2 text-primary dark:text-blue-400 hover:text-primary/80 dark:hover:text-blue-300 hover:bg-primary/5 dark:hover:bg-blue-900/20"
              onClick={() => setIsExpanded(!isExpanded)}
              aria-label={isExpanded ? 'Ocultar detalhes da dosagem' : 'Ver mais detalhes da dosagem'}
            >
              <Plus className={`h-4 w-4 transition-transform duration-200 ${isExpanded ? 'rotate-45' : ''}`} />
              {isExpanded ? 'Mostrar menos' : 'Ver mais detalhes'}
            </Button>

            {isExpanded && (
              <div className="text-sm sm:text-base text-gray-600 dark:text-gray-300 bg-gray-50/80 dark:bg-slate-700/50 p-2 sm:p-4 rounded-lg border border-primary/5 dark:border-primary/10 animate-slide-in-up whitespace-pre-line selectable-text">
                {summary}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
