
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    react(),
    mode === 'development' && componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    ssrManifest: true,
    modulePreload: {
      polyfill: true,
    },
    // FORÇAR CSS INLINE - Eliminar 8.580ms de blocking
    target: 'es2020',
    cssCodeSplit: false,
    sourcemap: false,
    // INLINE TODOS OS ASSETS PEQUENOS
    assetsInlineLimit: 50000, // 50KB - Forçar inline do CSS
    rollupOptions: {
      output: {
        manualChunks: {
          // ULTRA CRITICAL - Apenas React essencial
          'critical': ['react', 'react-dom'],

          // ROUTER completamente separado
          'router': ['react-router-dom'],

          // MICRO CHUNKS - Tree shaking máximo
          'radix-core': [
            '@radix-ui/react-dialog'
          ],
          'radix-toast': [
            '@radix-ui/react-toast'
          ],
          'radix-interactive': [
            '@radix-ui/react-dropdown-menu'
          ],
          'radix-popover': [
            '@radix-ui/react-popover'
          ],
          'radix-forms': [
            '@radix-ui/react-select',
            '@radix-ui/react-checkbox',
            '@radix-ui/react-radio-group',
            '@radix-ui/react-label',
            '@radix-ui/react-switch'
          ],
          'radix-layout': [
            '@radix-ui/react-accordion',
            '@radix-ui/react-tabs',
            '@radix-ui/react-collapsible',
            '@radix-ui/react-separator',
            '@radix-ui/react-scroll-area'
          ],
          'radix-navigation': [
            '@radix-ui/react-navigation-menu',
            '@radix-ui/react-menubar',
            '@radix-ui/react-context-menu'
          ],
          'radix-feedback': [
            '@radix-ui/react-alert-dialog',
            '@radix-ui/react-hover-card',
            '@radix-ui/react-tooltip',
            '@radix-ui/react-progress'
          ],

          // Backend & State
          'supabase-vendor': ['@supabase/supabase-js', '@supabase/auth-helpers-react', '@supabase/auth-ui-react'],
          'query-vendor': ['@tanstack/react-query'],
          'state-vendor': ['zustand'],

          // Editors & Rich Content
          'editor-vendor': [
            '@tiptap/react',
            '@tiptap/starter-kit',
            '@tiptap/extension-color',
            '@tiptap/extension-image',
            '@tiptap/extension-link',
            '@tiptap/extension-text-style',
            '@tiptap/extension-underline'
          ],

          // Charts & Visualization - Lazy loaded para melhor performance
          'chart-js': ['chart.js'],
          // recharts removido - agora lazy loaded
          // framer-motion removido - agora lazy loaded por componente

          // Utilities
          'date-vendor': ['date-fns'],
          'form-vendor': ['react-hook-form', '@hookform/resolvers', 'zod'],
          'dnd-vendor': ['@dnd-kit/core', '@dnd-kit/sortable'],
          'markdown-vendor': ['react-markdown', 'remark-math', 'rehype-katex'],

          // Heavy Libraries
          'pdf-vendor': ['jspdf'],
          'carousel-vendor': ['embla-carousel-react'],

          // 3D/Cinematic Components removido - agora lazy loaded
        },
      },
    },
    // Otimização CONSERVADORA mas efetiva
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
        passes: 2, // Duas passadas - mais estável
        dead_code: true,
        unused: true,
      },
      mangle: {
        safari10: true,
      },
    },
    // Configurações otimizadas para FCP
    chunkSizeWarningLimit: 1000,
    reportCompressedSize: false, // Melhora velocidade do build
  },
  ssr: {
    noExternal: ['react-helmet-async'],
    target: 'node',
    format: 'esm'
  }
}));
