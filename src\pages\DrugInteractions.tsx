import React, { useState, useRef, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { AlertTriangle, Info, Pill, X, Loader2, Search, Check, AlertCircle, ChevronRight, ArrowRight, ArrowLeft } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";
import { InteractionDisplay } from "@/components/drug-interactions/InteractionDisplay";
import { InteractionLegendDialog } from "@/components/drug-interactions/InteractionLegendDialog";
import { MedicationInput } from "@/components/drug-interactions/MedicationInput";
import { PatientDataForm } from "@/components/drug-interactions/PatientDataForm";
import { InteractionsFeedback } from "@/components/drug-interactions/InteractionsFeedback";
import { cn } from "@/lib/utils";
import { v4 as uuidv4 } from "uuid";

interface MedicationOption {
  id: string;
  value: string;
  label: string;
}

interface PatientData {
  age: string;
  gender: string;
  comorbidities: string;
  clinicalStatus: string;
}

interface InteractionResult {
  result: string;
}

interface ParsedSection {
  title: string;
  interactions: ParsedInteraction[];
  severityLevel: SeverityLevel;
  isEmpty: boolean;
}

interface ParsedInteraction {
  drugPair?: string;
  severity?: SeverityLevel;
  mechanism?: string;
  recommendation?: string;
  reference?: string;
  content?: string;
}

// Definir os tipos de severidade para interações medicamentosas
type SeverityLevel = "Contraindicado" | "Grave" | "Moderado" | "Leve" | "Sem interação relevante";

const DrugInteractions: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [medicationOptions, setMedicationOptions] = useState<MedicationOption[]>([]);
  const [selectedMedications, setSelectedMedications] = useState<string[]>([]);
  const [manuallyAddedMedications, setManuallyAddedMedications] = useState<string[]>([]); // Novo estado para rastrear medicamentos adicionados manualmente
  const [isAdvancedMode, setIsAdvancedMode] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [analysisResult, setAnalysisResult] = useState<string | null>(null);
  const [parsedSections, setParsedSections] = useState<ParsedSection[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const searchTimeout = useRef<NodeJS.Timeout | null>(null);
  const { toast } = useToast();

  // Patient data for advanced mode
  const [patientData, setPatientData] = useState<PatientData>({
    age: "",
    gender: "",
    comorbidities: "",
    clinicalStatus: "",
  });

  // Importante: garantir que o currentAnalysisId seja inicializado corretamente
  const [currentAnalysisId, setCurrentAnalysisId] = useState<string>("");

  // Parse result when it changes
  useEffect(() => {
    if (analysisResult) {
      const sections = parseAnalysisResult(analysisResult);
      setParsedSections(sections);
    } else {
      setParsedSections([]);
    }
  }, [analysisResult]);

  // Fetch medications from the database using our secure function
  useEffect(() => {
    if (searchTerm.length >= 3) {
      setIsSearching(true);

      // Clear existing timeout
      if (searchTimeout.current) {
        clearTimeout(searchTimeout.current);
      }

      // Set new timeout
      searchTimeout.current = setTimeout(async () => {
        try {
          // Normalize search term to remove accents and convert to lowercase
          const normalizedSearchTerm = searchTerm
            .normalize("NFD")
            .replace(/[\u0300-\u036f]/g, "")
            .toLowerCase();

          // Use the secure RPC function with minimum 3 chars
          const { data, error } = await supabase
            .rpc('search_drug_interactions', {
              search_term: normalizedSearchTerm
            });

          if (error) {
            throw error;
          }

          // Map to the format expected by the component
          const options = (data || []).map((med) => ({
            id: med.id,
            value: med.active_ingredient,
            label: med.active_ingredient,
          }));

          setMedicationOptions(options);
        } catch (error) {
          console.error("Erro ao buscar medicamentos:", error);
          toast({
            title: "Erro na busca",
            description: "Não foi possível buscar medicamentos. Tente novamente.",
            variant: "destructive",
          });
        } finally {
          setIsSearching(false);
        }
      }, 500);
    } else {
      setMedicationOptions([]);
    }

    return () => {
      if (searchTimeout.current) {
        clearTimeout(searchTimeout.current);
      }
    };
  }, [searchTerm, toast]);

  // Função para dividir medicamentos compostos em seus princípios ativos individuais
  const splitCompoundMedication = (medication: string): string[] => {
    // Verifique se o medicamento contém o separador "+"
    if (medication.includes("+")) {
      // Divida o medicamento por "+" e remova espaços em branco
      return medication.split("+").map(component => component.trim());
    }

    // Se não contiver "+", retorne o medicamento original
    return [medication];
  };

  // Função auxiliar para obter todos os componentes para o envio à API
  const getAllMedicationComponents = () => {
    // Mapeia cada medicamento selecionado para seus componentes e achata o array
    return selectedMedications.flatMap(med => splitCompoundMedication(med));
  };

  const handleAddMedication = (medication: string) => {
    // Verifique se já temos 6 medicamentos selecionados
    if (selectedMedications.length >= 6) {
      toast({
        title: "Limite excedido",
        description: "O limite máximo de 6 medicamentos foi atingido.",
        variant: "destructive",
      });
      return;
    }

    // Verifique se o medicamento já está na lista
    if (selectedMedications.includes(medication)) {
      toast({
        title: "Medicamento já adicionado",
        description: "Este medicamento já está na lista de selecionados.",
        variant: "warning",
      });
      return;
    }

    // Verificar se é um medicamento adicionado manualmente (não existe na lista de opções)
    const isManuallyAdded = !medicationOptions.some(option => option.value === medication);
    if (isManuallyAdded) {
      setManuallyAddedMedications(prev => [...prev, medication]);
      toast({
        title: "Medicamento adicionado manualmente",
        description: "Este medicamento foi adicionado para análise.",
      });
    }

    // Adicione o medicamento como uma única entrada, sem dividir para exibição
    setSelectedMedications([...selectedMedications, medication]);
    setSearchTerm("");
  };

  const handleRemoveMedication = (index: number) => {
    const medicationToRemove = selectedMedications[index];

    // Remover do array de medicamentos manuais se estiver lá
    if (manuallyAddedMedications.includes(medicationToRemove)) {
      setManuallyAddedMedications(prev => prev.filter(med => med !== medicationToRemove));
    }

    // Remover da lista principal
    const updated = [...selectedMedications];
    updated.splice(index, 1);
    setSelectedMedications(updated);
  };

  const handlePatientDataChange = (field: keyof PatientData, value: string) => {
    setPatientData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Função para determinar o ícone baseado na severidade da interação
  const getSeverityIcon = (severity: SeverityLevel) => {
    switch (severity) {
      case "Contraindicado":
        return <X className="h-6 w-6 text-white" />;
      case "Grave":
        return <AlertTriangle className="h-6 w-6 text-white" />;
      case "Moderado":
        return <AlertCircle className="h-6 w-6 text-white" />;
      case "Leve":
      case "Sem interação relevante":
        return <Check className="h-6 w-6 text-white" />;
      default:
        return <Info className="h-6 w-6 text-white" />;
    }
  };

  // Função para determinar a cor de fundo baseada na severidade
  const getSeverityBackgroundColor = (severity: SeverityLevel): string => {
    switch (severity) {
      case "Contraindicado":
        return "bg-red-600";
      case "Grave":
        return "bg-purple-600";
      case "Moderado":
        return "bg-orange-500";
      case "Leve":
      case "Sem interação relevante":
        return "bg-green-500";
      default:
        return "bg-blue-500";
    }
  };

  // Função para determinar a cor de borda baseada na severidade
  const getSeverityBorderColor = (severity: SeverityLevel): string => {
    switch (severity) {
      case "Contraindicado":
        return "border-red-200 dark:border-red-800";
      case "Grave":
        return "border-purple-200 dark:border-purple-800";
      case "Moderado":
        return "border-orange-200 dark:border-orange-800";
      case "Leve":
      case "Sem interação relevante":
        return "border-green-200 dark:border-green-800";
      default:
        return "border-blue-200 dark:border-blue-800";
    }
  };

  // Função para determinar a cor de texto baseada na severidade
  const getSeverityTextColor = (severity: SeverityLevel): string => {
    switch (severity) {
      case "Contraindicado":
        return "text-red-700 dark:text-red-300";
      case "Grave":
        return "text-purple-700 dark:text-purple-300";
      case "Moderado":
        return "text-orange-700 dark:text-orange-300";
      case "Leve":
      case "Sem interação relevante":
        return "text-green-700 dark:text-green-300";
      default:
        return "text-blue-700 dark:text-blue-300";
    }
  };

  // Função para determinar a cor de fundo leve baseada na severidade
  const getSeverityLightBackgroundColor = (severity: SeverityLevel): string => {
    switch (severity) {
      case "Contraindicado":
        return "bg-red-50 dark:bg-red-950/20";
      case "Grave":
        return "bg-purple-50 dark:bg-purple-950/20";
      case "Moderado":
        return "bg-orange-50 dark:bg-orange-950/20";
      case "Leve":
      case "Sem interação relevante":
        return "bg-green-50 dark:bg-green-950/20";
      default:
        return "bg-blue-50 dark:bg-blue-950/20";
    }
  };

  // Função para analisar o resultado e dividir em seções
  const parseAnalysisResult = (result: string): ParsedSection[] => {
    const sections: ParsedSection[] = [];

    try {


      // Dividir por seções principais (interações contraindicadas, graves, etc.)
      // Usamos um regex que captura melhor os cabeçalhos de seção no formato markdown
      const sectionRegex = /(#{2,4}\s+\d+\.\s+.*?)(?=#{2,4}\s+\d+\.|$)/gs;
      let match;
      let sectionMatches = [];

      while ((match = sectionRegex.exec(result)) !== null) {
        sectionMatches.push(match[1].trim());
      }

      // Se não encontrarmos seções estruturadas, tentamos um fallback
      if (sectionMatches.length === 0) {

        // Fallback: dividir por linhas que começam com "## 1.", "## 2.", etc.
        const mainSections = result.split(/#{2,4}\s+\d+\./);
        if (mainSections.length > 1) {
          // O primeiro item geralmente é a introdução
          mainSections.shift();
          sectionMatches = mainSections.map((section, index) => `### ${index + 1}. ${section.trim()}`);
        }
      }

      // Processar cada seção encontrada
      sectionMatches.forEach((sectionContent) => {
        // Extrair o título da seção a partir da primeira linha
        const titleMatch = sectionContent.match(/#{2,4}\s+\d+\.\s+(.*?)(?=\n|$)/);
        const sectionTitle = titleMatch ? titleMatch[1].trim() : "Seção sem título";

        // Remover o título da seção do conteúdo para processamento
        const contentWithoutTitle = sectionContent.replace(/#{2,4}\s+\d+\.\s+.*?\n/, "");

        // Determinar o nível de severidade com base no título
        let severityLevel: SeverityLevel = "Leve";

        if (sectionTitle.includes("Contraindicada")) {
          severityLevel = "Contraindicado";
        } else if (sectionTitle.includes("Grave")) {
          severityLevel = "Grave";
        } else if (sectionTitle.includes("Moderada")) {
          severityLevel = "Moderado";
        } else if (sectionTitle.includes("Leve") || sectionTitle.includes("Sem")) {
          severityLevel = "Leve";
        }



        // Processar conteúdo desta seção
        const interactions = parseInteractionsInSection(contentWithoutTitle, severityLevel);

        // Verificar se a seção está vazia - consideramos apenas interações com dados completos
        const isEmpty = interactions.filter(i => i && i.drugPair).length === 0;

        // Não adicionar seções de resumo
        if (!sectionTitle.toLowerCase().includes("resumo")) {
          sections.push({
            title: sectionTitle,
            interactions: interactions,
            severityLevel,
            isEmpty
          });
        }
      });


      return sections;
    } catch (error) {
      console.error("Erro ao analisar o resultado:", error);
      return [{
        title: "Erro na análise",
        interactions: [{ content: "Houve um erro ao analisar o resultado. Por favor, tente novamente." }],
        severityLevel: "Leve",
        isEmpty: false
      }];
    }
  };

  // Função para analisar as interações dentro de uma seção
  const parseInteractionsInSection = (sectionContent: string, defaultSeverity: SeverityLevel): ParsedInteraction[] => {
    const interactions: ParsedInteraction[] = [];

    try {
      // Verificar se há texto de "Nenhuma interação"
      if (sectionContent.includes("Nenhuma interação")) {

        interactions.push({
          content: sectionContent.trim(),
          severity: defaultSeverity
        });
        return interactions;
      }

      // Verificar se há blocos de interação - usamos regex melhorado para capturar
      const interactionBlockRegex = /- \*\*Interação:\*\* \[(.*?)\]([\s\S]*?)(?=- \*\*Interação:\*\*|\*\*Nenhuma interação|$)/g;
      let interactionMatch;

      while ((interactionMatch = interactionBlockRegex.exec(sectionContent)) !== null) {
        const drugPair = interactionMatch[1].trim();
        const detailsBlock = interactionMatch[0];

        // Extrair informações usando regex
        const severityMatch = detailsBlock.match(/\*\*Gravidade:\*\* (.*?)(?=\n|$)/);
        const mechanismMatch = detailsBlock.match(/\*\*Mecanismo:\*\* ([\s\S]*?)(?=\*\*|$)/);
        const recommendationMatch = detailsBlock.match(/\*\*Conduta Clínica Recomendada:\*\* ([\s\S]*?)(?=\*\*|$)/);
        const referenceMatch = detailsBlock.match(/\*\*Referência:\*\* ([\s\S]*?)(?=\*\*|$)/);

        // Determinar a severidade
        let severity = defaultSeverity;
        if (severityMatch) {
          const severityText = severityMatch[1].trim();
          if (severityText.includes("Contraindicada")) {
            severity = "Contraindicado";
          } else if (severityText.includes("Grave")) {
            severity = "Grave";
          } else if (severityText.includes("Moderada")) {
            severity = "Moderado";
          } else if (severityText.includes("Leve") || severityText.includes("Sem interação")) {
            severity = "Leve";
          }
        }

        // Extrair os demais campos
        const mechanism = mechanismMatch ? mechanismMatch[1].trim() : "";
        const recommendation = recommendationMatch ? recommendationMatch[1].trim() : "";
        const reference = referenceMatch ? referenceMatch[1].trim() : "";



        // Adicionar à lista de interações
        interactions.push({
          drugPair,
          severity,
          mechanism,
          recommendation,
          reference
        });
      }

      // Se não encontramos interações estruturadas, mas também não há mensagem de "nenhuma interação",
      // verificamos se há alguma informação relevante
      if (interactions.length === 0 && !sectionContent.includes("Nenhuma interação")) {

        // Tentar extrair pelo menos um par de medicamentos
        const genericMatch = sectionContent.match(/\[(.*?)\]/);
        if (genericMatch) {
          interactions.push({
            drugPair: genericMatch[1],
            content: sectionContent.trim(),
            severity: defaultSeverity
          });
        } else if (sectionContent.trim().length > 0) {
          // Se não encontramos nem colchetes, mas há conteúdo, adicionamos como genérico
          interactions.push({
            content: sectionContent.trim(),
            severity: defaultSeverity
          });
        }
      }

      return interactions;
    } catch (error) {
      console.error("Erro ao analisar interações:", error);
      return [];
    }
  };

  const analyzeInteractions = async () => {
    if (selectedMedications.length < 2) {
      toast({
        title: "Medicamentos insuficientes",
        description: "Selecione pelo menos 2 medicamentos para análise",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    setAnalysisResult(null);

    // Gerar um novo ID único para esta análise
    const analysisId = uuidv4();
    setCurrentAnalysisId(analysisId);


    try {
      // Obter os componentes individualizados para análise
      const allComponents = getAllMedicationComponents();

      const payload = {
        medications: allComponents, // Envia os componentes individualizados
        mode: isAdvancedMode ? "advanced" : "simple",
        originalMedicationsCount: selectedMedications.length, // Envia o número original de medicamentos
        manuallyAddedMedications: manuallyAddedMedications, // Indicar quais medicamentos foram adicionados manualmente
        ...(isAdvancedMode && { patientData }),
      };

      const response = await fetch("https://bxedpdmgvgatjdfxgxij.supabase.co/functions/v1/drug-interactions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`Erro na solicitação: ${response.status}`);
      }

      const data = await response.json() as InteractionResult;
      setAnalysisResult(data.result);


    } catch (error) {
      console.error("Erro na análise:", error);
      toast({
        title: "Erro na análise",
        description: "Não foi possível analisar as interações. Tente novamente.",
        variant: "destructive",
      });
      setCurrentAnalysisId(""); // Limpar ID em caso de erro
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <HelmetWrapper>
        <title>PedBook | Interações Medicamentosas</title>
        <meta
          name="description"
          content="Consulte interações entre medicamentos e evite combinações perigosas em pacientes pediátricos."
        />
      </HelmetWrapper>

      <Header />

      <main className="flex-1 container mx-auto px-4 py-8 md:py-12">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center gap-4 mb-8">
            <Link to="/">
              <Button
                variant="ghost"
                size="icon"
                className="hover:bg-primary/10 hidden sm:flex dark:hover:bg-primary/20"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <div className="text-center flex-1 space-y-3">
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100">
                Análise de Interações Medicamentosas
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                Identifique potenciais interações entre medicamentos
              </p>
            </div>
          </div>

          {!analysisResult ? (
            <Card className="overflow-hidden shadow-lg border border-gray-200 dark:border-gray-800 relative">
              {/* Banner BETA no topo do card */}
              <div className="absolute top-0 right-0 left-0 flex justify-center">
                <div className="bg-gradient-to-r from-purple-600 via-primary to-purple-500 text-white px-4 py-1 text-xs rounded-b-md shadow-md">
                  Ferramenta em fase BETA
                </div>
              </div>

              <div className="bg-gradient-to-r from-primary/10 to-primary/5 dark:from-primary/5 dark:to-transparent p-4 border-b border-gray-100 dark:border-gray-800 mt-6">
                <div className="flex items-center gap-3">
                  <div className="bg-primary/20 p-2 rounded-full">
                    <Pill className="h-5 w-5 text-primary" />
                  </div>
                  <h2 className="text-xl font-medium">Selecione os medicamentos</h2>
                </div>
              </div>

              <div className="p-6 space-y-6">
                <MedicationInput
                  searchTerm={searchTerm}
                  setSearchTerm={setSearchTerm}
                  isSearching={isSearching}
                  medicationOptions={medicationOptions}
                  handleAddMedication={handleAddMedication}
                  selectedMedications={selectedMedications}
                  handleRemoveMedication={handleRemoveMedication}
                />

                <div className="flex flex-col space-y-2">
                  <div className="flex items-center gap-2 bg-gray-50 dark:bg-gray-800/40 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                    <Switch
                      id="advanced-mode"
                      checked={isAdvancedMode}
                      onCheckedChange={setIsAdvancedMode}
                    />
                    <div>
                      <Label htmlFor="advanced-mode" className="font-medium">
                        Incluir dados clínicos do paciente
                      </Label>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                        Para uma análise mais personalizada e precisa
                      </p>
                    </div>
                  </div>
                </div>

                {isAdvancedMode && (
                  <div className="bg-gray-50 dark:bg-gray-800/30 rounded-lg p-4 border border-gray-200 dark:border-gray-700 animate-in fade-in-50 slide-in-from-top-5 duration-300">
                    <PatientDataForm
                      patientData={patientData}
                      handlePatientDataChange={handlePatientDataChange}
                    />
                  </div>
                )}

                <Button
                  className="w-full gap-2 py-6"
                  size="lg"
                  onClick={analyzeInteractions}
                  disabled={selectedMedications.length < 2 || isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      💡 Dr. Will está pensando...
                    </>
                  ) : (
                    <>
                      Analisar Interações
                      <ArrowRight className="h-5 w-5" />
                    </>
                  )}
                </Button>

                <Alert variant="default" className="bg-blue-50/50 dark:bg-blue-950/10 border border-blue-200 dark:border-blue-800">
                  <Info className="h-5 w-5 text-blue-500" />
                  <AlertDescription className="text-blue-700 dark:text-blue-300">
                    Selecione entre 2 e 6 medicamentos. Quanto mais específico for o nome do medicamento, mais precisa será a análise.
                    {manuallyAddedMedications.length > 0 && (
                      <div className="mt-2 text-amber-600 dark:text-amber-400 text-sm">
                        <strong>Atenção:</strong> Você adicionou medicamento(s) manualmente. A análise de interações com esses medicamentos pode ser menos precisa.
                      </div>
                    )}
                  </AlertDescription>
                </Alert>
              </div>
            </Card>
          ) : (
            <div className="space-y-6">
              <Card className="overflow-hidden shadow-lg border border-gray-200 dark:border-gray-800">
                <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/30 dark:to-blue-950/30 p-4 border-b border-gray-100 dark:border-gray-800">
                  <div className="flex items-center justify-between flex-wrap gap-2">
                    <div className="flex items-center gap-3">
                      <div className="bg-green-100 dark:bg-green-900/30 p-2 rounded-full">
                        <Check className="h-5 w-5 text-green-600 dark:text-green-400" />
                      </div>
                      <h2 className="text-xl font-medium">Análise completada</h2>
                    </div>
                    <Button
                      variant="outline"
                      className="text-sm"
                      onClick={() => {
                        setSelectedMedications([]);
                        setManuallyAddedMedications([]);
                        setAnalysisResult(null);
                        setParsedSections([]);
                        setPatientData({
                          age: "",
                          gender: "",
                          comorbidities: "",
                          clinicalStatus: "",
                        });
                        setIsAdvancedMode(false);
                        setCurrentAnalysisId("");
                      }}
                    >
                      Nova análise
                    </Button>
                  </div>
                </div>

                <div className="p-5">
                  <div className="flex flex-wrap gap-3 mb-5">
                    {selectedMedications.map((med, index) => {
                      // Verificar se o medicamento foi adicionado manualmente
                      const isManual = manuallyAddedMedications.includes(med);

                      return (
                        <div
                          key={`result-med-${index}`}
                          className={cn(
                            "bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 px-3 py-2 rounded-full flex items-center gap-2 border border-gray-200 dark:border-gray-700 w-full sm:w-auto",
                            isManual ? "border-dashed border-amber-300 dark:border-amber-700" : "",
                            index % 4 === 0 && "border-l-4 border-l-primary",
                            index % 4 === 1 && "border-l-4 border-l-secondary",
                            index % 4 === 2 && "border-l-4 border-l-green-500",
                            index % 4 === 3 && "border-l-4 border-l-amber-500"
                          )}
                        >
                          <Pill className="h-4 w-4 flex-shrink-0" />
                          <span className="text-sm">{med}</span>
                          {isManual && (
                            <span className="text-xs text-amber-500 dark:text-amber-400 ml-1">(manual)</span>
                          )}
                        </div>
                      );
                    })}
                  </div>

                  <div className="space-y-6">
                    {/* Verificar se há seções não vazias para exibir */}
                    {parsedSections && parsedSections.filter(section => section && !section.isEmpty).length > 0 ? (
                      parsedSections
                        .filter(section => section && !section.isEmpty)
                        .map((section, sectionIndex) => (
                          <Card
                            key={`section-${sectionIndex}`}
                            className={cn(
                              "overflow-hidden border transition-all hover:shadow-md",
                              getSeverityBorderColor(section.severityLevel)
                            )}
                          >
                            <div className={getSeverityLightBackgroundColor(section.severityLevel)}>
                              <div className="flex items-center gap-4 p-4 border-b border-gray-100 dark:border-gray-800">
                                <div className={`${getSeverityBackgroundColor(section.severityLevel)} p-2 rounded-full`}>
                                  {getSeverityIcon(section.severityLevel)}
                                </div>
                                <div>
                                  <h4 className="text-lg font-semibold">{section.title}</h4>
                                  <div className="text-sm font-medium mt-0.5">
                                    {section.interactions.filter(i => i && i.drugPair).length} interação(ões) encontrada(s)
                                  </div>
                                </div>
                              </div>

                              <div className="p-5 space-y-4">
                                {section.interactions && section.interactions
                                  .filter(interaction => interaction && interaction.drugPair)
                                  .map((interaction, interactionIndex) => (
                                    <InteractionDisplay
                                      key={`interaction-${sectionIndex}-${interactionIndex}`}
                                      interaction={interaction}
                                      isLast={interactionIndex === section.interactions.filter(i => i && i.drugPair).length - 1}
                                    />
                                  ))}

                                {/* Renderizar texto genérico apenas se for realmente vazio mas tem conteúdo */}
                                {section.isEmpty && section.interactions.length > 0 && (
                                  <div className="text-gray-600 dark:text-gray-400">
                                    Nenhuma interação {section.severityLevel.toLowerCase()} identificada entre os medicamentos selecionados.
                                  </div>
                                )}
                              </div>
                            </div>
                          </Card>
                        ))
                    ) : (
                      <Card className="overflow-hidden border border-green-200 dark:border-green-800">
                        <div className="bg-green-50 dark:bg-green-950/20">
                          <div className="flex items-center gap-4 p-4 border-b border-gray-100 dark:border-gray-800">
                            <div className="bg-green-500 p-2 rounded-full">
                              <Check className="h-6 w-6 text-white" />
                            </div>
                            <div>
                              <h4 className="text-lg font-semibold">Sem interações relevantes</h4>
                              <div className="text-sm font-medium text-green-700 dark:text-green-300">
                                Compatível
                              </div>
                            </div>
                          </div>
                          <div className="p-5">
                            <p className="text-gray-600 dark:text-gray-400">
                              Não foram identificadas interações medicamentosas significativas entre os medicamentos selecionados.
                            </p>
                          </div>
                        </div>
                      </Card>
                    )}
                  </div>

                  {/* Componente de feedback - verificar que o ID da análise existe */}
                  {currentAnalysisId && (
                    <InteractionsFeedback
                      interactionId={currentAnalysisId}
                      medicationList={selectedMedications}
                      analysisResult={analysisResult} // Passar o resultado completo da análise
                    />
                  )}
                </div>
              </Card>

              {/* Aviso com ênfase no BETA */}
              <div className="text-center mb-2">
                <div className="inline-flex items-center gap-3 p-4 rounded-lg bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-900">
                  <AlertTriangle className="h-5 w-5 text-amber-500" />
                  <div>
                    <p className="font-medium text-amber-700 dark:text-amber-300">
                      Resultado gerado por Dr. Will (IA)
                    </p>
                    <p className="text-amber-600 dark:text-amber-400 text-sm">
                      <span className="font-semibold">Ferramenta em fase BETA</span> • Como médico, é seu papel discernir estas informações de acordo com o contexto clínico.
                    </p>
                  </div>
                </div>
              </div>

              {/* Legenda com título */}
              <Card className="p-4">
                <h3 className="font-medium text-gray-700 dark:text-gray-300 text-center mb-3">
                  Legenda de severidade
                </h3>

                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3">
                  <div className="flex items-center gap-2.5 p-2.5 rounded-md bg-red-50 dark:bg-red-950/30 border border-red-100 dark:border-red-900">
                    <div className="bg-red-600 p-1.5 rounded-full">
                      <X className="h-4 w-4 text-white" />
                    </div>
                    <span className="font-medium text-red-700 dark:text-red-300">Contraindicado</span>
                  </div>
                  <div className="flex items-center gap-2.5 p-2.5 rounded-md bg-orange-50 dark:bg-orange-950/30 border border-orange-100 dark:border-orange-900">
                    <div className="bg-orange-600 p-1.5 rounded-full">
                      <AlertTriangle className="h-4 w-4 text-white" />
                    </div>
                    <span className="font-medium text-orange-700 dark:text-orange-300">Grave</span>
                  </div>
                  <div className="flex items-center gap-2.5 p-2.5 rounded-md bg-yellow-50 dark:bg-yellow-950/30 border border-yellow-100 dark:border-yellow-900">
                    <div className="bg-yellow-500 p-1.5 rounded-full">
                      <AlertCircle className="h-4 w-4 text-white" />
                    </div>
                    <span className="font-medium text-yellow-700 dark:text-yellow-300">Moderado</span>
                  </div>
                  <div className="flex items-center gap-2.5 p-2.5 rounded-md bg-blue-50 dark:bg-blue-950/30 border border-blue-100 dark:border-blue-900">
                    <div className="bg-green-500 p-1.5 rounded-full">
                      <Check className="h-4 w-4 text-white" />
                    </div>
                    <span className="font-medium text-blue-700 dark:text-blue-300">Leve/Sem interação</span>
                  </div>
                </div>

                <div className="flex justify-center mt-3">
                  <InteractionLegendDialog />
                </div>
              </Card>
            </div>
          )}
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default DrugInteractions;
