import { useQuery } from "@tanstack/react-query";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";
import { forwardRef } from "react";

interface CategorySelectorProps {
  value?: string;
  onValueChange?: (value: string) => void;
}

export const CategorySelector = forwardRef<HTMLButtonElement, CategorySelectorProps>(
  ({ value, onValueChange }, ref) => {
    const { data: categories, isLoading } = useQuery({
      queryKey: ["blog-categories"],
      queryFn: async () => {
        const { data, error } = await supabase
          .from("pedbook_blog_categories")
          .select("*")
          .order("name");

        if (error) throw error;
        return data;
      },
    });

    if (isLoading) return <div>Carregando categorias...</div>;

    return (
      <div className="space-y-2">
        <Label htmlFor="category">Categoria</Label>
        <Select value={value} onValueChange={onValueChange}>
          <SelectTrigger ref={ref}>
            <SelectValue placeholder="Selecione uma categoria" />
          </SelectTrigger>
          <SelectContent>
            {categories?.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );
  }
);

CategorySelector.displayName = "CategorySelector";