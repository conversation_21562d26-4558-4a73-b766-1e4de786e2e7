
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { X } from "lucide-react";

interface AlternativeStatistics {
  count: number;
  percentage: number;
}

interface AlternativeProps {
  alternatives: string[];
  selectedAnswer: string | null;
  setSelectedAnswer?: (answer: string) => void;
  hasAnswered: boolean;
  correct_answer: number;
  statistics?: AlternativeStatistics[];
  alternativeComments?: Record<number, string>;
  questionId?: string;
}

export const QuestionAlternatives: React.FC<AlternativeProps> = ({
  alternatives,
  selectedAnswer,
  setSelectedAnswer,
  hasAnswered,
  correct_answer,
  statistics,
  alternativeComments,
  questionId = 'default',
}) => {
  const [crossedAlternativesMap, setCrossedAlternativesMap] = useState<Record<string, number[]>>({});
  const [lastQuestionId, setLastQuestionId] = useState<string>(questionId);
  const crossedAlternatives = crossedAlternativesMap[questionId] || [];

  // Reset quando a questão muda para evitar flash das cores e limpar estado das alternativas riscadas
  useEffect(() => {
    if (lastQuestionId !== questionId) {
      // Sempre limpar alternativas riscadas da questão anterior quando mudamos de questão
      // Isso garante que não haja "vazamento" de estado entre questões
      if (lastQuestionId !== 'default') {
        setCrossedAlternativesMap(prev => {
          const newMap = { ...prev };
          delete newMap[lastQuestionId];
          return newMap;
        });
      }

      setLastQuestionId(questionId);
    }
  }, [questionId, lastQuestionId, hasAnswered]);

  const formatAlternativeContent = (text: string): string => {
    return text
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .replace(/\n/g, '<br />');
  };

  const toggleCrossed = (index: number) => {
    setCrossedAlternativesMap(prev => {
      const currentQuestionCrossed = prev[questionId] || [];
      let newQuestionCrossed;
      if (currentQuestionCrossed.includes(index)) {
        newQuestionCrossed = currentQuestionCrossed.filter(i => i !== index);
      } else {
        newQuestionCrossed = [...currentQuestionCrossed, index];
      }

      return {
        ...prev,
        [questionId]: newQuestionCrossed
      };
    });
  };

  // Determine the correct answer index (0-based)
  const correctAnswerIndex = typeof correct_answer === 'number'
    ? correct_answer
    : parseInt(String(correct_answer));

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Alternativas:</h3>
      <div className="space-y-3">
        {alternatives.map((alternative, index) => {
          const isCorrect = index === correctAnswerIndex;
          const isSelected = selectedAnswer === String(index + 1);
          const isIncorrect = hasAnswered && isSelected && !isCorrect;
          const isCrossed = crossedAlternatives.includes(index);

          return (
            <div
              key={index}
              className={cn(
                "p-4 rounded-lg border transition-all",
                hasAnswered
                  ? isCorrect
                    ? "bg-green-50 border-green-300"
                    : isIncorrect
                    ? "bg-red-50 border-red-300"
                    : "bg-white border-gray-200"
                  : isSelected
                  ? "bg-blue-50 border-blue-300"
                  : "bg-white border-gray-200 hover:border-blue-300 hover:bg-blue-50",
                setSelectedAnswer && !hasAnswered
                  ? "cursor-pointer"
                  : "cursor-default"
              )}
              onClick={() => {
                if (setSelectedAnswer && !hasAnswered) {
                  setSelectedAnswer(String(index + 1));
                }
              }}
            >
              <div className="flex items-start gap-3">
                <div
                  className={cn(
                    "flex items-center justify-center w-6 h-6 rounded-full text-sm font-medium mt-0.5",
                    hasAnswered
                      ? isCorrect
                        ? "bg-green-500 text-white"
                        : isIncorrect
                        ? "bg-red-500 text-white"
                        : isSelected
                        ? "bg-gray-200 text-gray-700"
                        : "bg-gray-200 text-gray-700"
                      : isSelected
                      ? "bg-blue-500 text-white"
                      : "bg-gray-200 text-gray-700"
                  )}
                >
                  {String.fromCharCode(65 + index)}
                </div>
                <div className="flex-1">
                  <div
                    className={cn(
                      "text-gray-700 whitespace-pre-line",
                      isCrossed && "line-through text-gray-400"
                    )}
                    dangerouslySetInnerHTML={{ __html: formatAlternativeContent(alternative) }}
                  />
                </div>
                {!hasAnswered && (
                  <button
                    type="button"
                    className={cn(
                      "p-1 ml-2 rounded-full transition-colors",
                      isCrossed ? "bg-red-100 text-red-500" : "bg-gray-100 text-gray-400 hover:text-gray-600 hover:bg-gray-200"
                    )}
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleCrossed(index);
                    }}
                  >
                    <X size={16} />
                  </button>
                )}
              </div>

              {hasAnswered && alternativeComments && alternativeComments[index + 1] && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div
                    className="text-sm text-gray-700 whitespace-pre-line"
                    dangerouslySetInnerHTML={{ __html: formatAlternativeContent(alternativeComments[index + 1]) }}
                  />
                </div>
              )}

              {hasAnswered && statistics && statistics[index] && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">
                      Escolhida por {statistics[index].percentage.toFixed(1)}% ({statistics[index].count} pessoas)
                    </span>
                    <div className="w-32 bg-gray-200 h-2 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-blue-500"
                        style={{ width: `${statistics[index].percentage}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default QuestionAlternatives;
