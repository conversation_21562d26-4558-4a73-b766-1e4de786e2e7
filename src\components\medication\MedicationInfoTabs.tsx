
import { useState, useEffect } from "react";
import { AlertTriangle, Info, BookOpen, Shield, Lightbulb, ExternalLink, ChevronDown, ChevronUp } from "lucide-react";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { DosageDisplay } from "@/components/DosageDisplay";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface MedicationInfoTabsProps {
  medication: {
    description?: string | null;
    brands?: string | null;
    contraindications?: string | null;
    scientific_references?: string | null;
    guidelines?: string | null;
    required_measures?: string[];
    pedbook_medication_use_cases?: Array<{
      id: string;
      name: string;
      description?: string | null;
      pedbook_medication_dosages: Array<any>;
      display_order?: number;
    }>;
  };
  weight: number;
  age: number;
  requiredMeasures?: string[];
}

export const MedicationInfoTabs = ({
  medication,
  weight,
  age,
  requiredMeasures,
}: MedicationInfoTabsProps) => {
  const sortedUseCases = [...medication.pedbook_medication_use_cases].map((useCase, index) => ({
    ...useCase,
    display_order: useCase.display_order ?? index,
  })).sort((a, b) => a.display_order - b.display_order);

  const [activeTab, setActiveTab] = useState<string>(sortedUseCases[0]?.id || '');
  const [showContraindications, setShowContraindications] = useState(false);
  const [showGuidelines, setShowGuidelines] = useState(false);
  const [showReferences, setShowReferences] = useState(false);

  useEffect(() => {
    if (sortedUseCases.length > 0 && !activeTab) {
      setActiveTab(sortedUseCases[0].id);
    }
  }, [sortedUseCases]);

  useEffect(() => {
    if (sortedUseCases.length > 0) {
      setActiveTab(sortedUseCases[0].id);
    }
  }, [medication]);

  if (!medication.pedbook_medication_use_cases?.length) {
    return (
      <div className="text-center py-8 bg-muted/20 rounded-lg backdrop-blur-sm">
        <p className="text-muted-foreground">
          Nenhuma indicação de uso cadastrada para este medicamento
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6 w-full">
      <Tabs value={activeTab} className="w-full">
        <div className="space-y-4">
          {/* Seletor de Indicações */}
          <div className="bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="p-3 sm:p-4">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span className="text-xs sm:text-sm font-medium text-primary">
                  Selecione uma indicação
                </span>
              </div>

              <Select value={activeTab} onValueChange={setActiveTab}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Escolha a condição clínica..." />
                </SelectTrigger>
                <SelectContent className="max-w-[90vw] sm:max-w-2xl">
                  {sortedUseCases.map((useCase) => (
                    <SelectItem
                      key={useCase.id}
                      value={useCase.id}
                      className="cursor-pointer"
                    >
                      {useCase.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {sortedUseCases.map((useCase) => (
          <TabsContent
            key={useCase.id}
            value={useCase.id}
            className="space-y-6 animate-fade-in"
          >
            {useCase.description && (
              <div className="bg-gradient-to-br from-primary/5 via-primary/10 to-transparent dark:from-blue-900/30 dark:via-blue-900/20 dark:to-transparent p-6 rounded-xl border border-primary/10 dark:border-blue-800/40 backdrop-blur-sm">
                <div className="max-w-full break-words">
                  <p className="text-sm text-muted-foreground leading-relaxed whitespace-pre-wrap">
                    {useCase.description}
                  </p>
                </div>
              </div>
            )}

            {useCase.pedbook_medication_dosages?.length > 0 ? (
              <div className="space-y-4">
                {useCase.pedbook_medication_dosages.map((dosage) => (
                  <DosageDisplay
                    key={dosage.id}
                    dosage={{
                      ...dosage,
                      medication_id: dosage.medication_id || medication?.id
                    }}
                    weight={weight}
                    age={age}
                    requiredMeasures={requiredMeasures}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-6 bg-muted/20 rounded-xl backdrop-blur-sm">
                <p className="text-muted-foreground">
                  Nenhuma dosagem cadastrada para esta indicação
                </p>
              </div>
            )}
          </TabsContent>
        ))}
      </Tabs>

      {medication.contraindications && (
        <div className="bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
          <button
            onClick={() => setShowContraindications(!showContraindications)}
            className="w-full p-3 sm:p-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors rounded-lg"
          >
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-100 dark:bg-red-900/30 rounded-lg">
                <Shield className="w-4 h-4 text-red-600 dark:text-red-400" />
              </div>
              <div className="text-left">
                <h3 className="text-sm sm:text-base font-medium text-gray-900 dark:text-gray-100">
                  Contraindicações
                </h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Situações em que não deve ser usado
                </p>
              </div>
            </div>
            {showContraindications ? (
              <ChevronUp className="w-4 h-4 text-gray-400" />
            ) : (
              <ChevronDown className="w-4 h-4 text-gray-400" />
            )}
          </button>

          {showContraindications && (
            <div className="px-3 sm:px-4 pb-3 sm:pb-4 border-t border-gray-100 dark:border-gray-700">
              <div className="pt-3 flex items-start gap-2">
                <AlertTriangle className="w-4 h-4 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap break-words min-w-0 flex-1">
                  {medication.contraindications}
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {medication.guidelines && (
        <div className="bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
          <button
            onClick={() => setShowGuidelines(!showGuidelines)}
            className="w-full p-3 sm:p-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors rounded-lg"
          >
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <Lightbulb className="w-4 h-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="text-left">
                <h3 className="text-sm sm:text-base font-medium text-gray-900 dark:text-gray-100">
                  Orientações Clínicas
                </h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Diretrizes para uso adequado
                </p>
              </div>
            </div>
            {showGuidelines ? (
              <ChevronUp className="w-4 h-4 text-gray-400" />
            ) : (
              <ChevronDown className="w-4 h-4 text-gray-400" />
            )}
          </button>

          {showGuidelines && (
            <div className="px-3 sm:px-4 pb-3 sm:pb-4 border-t border-gray-100 dark:border-gray-700">
              <div className="pt-3 flex items-start gap-2">
                <Info className="w-4 h-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap break-words min-w-0 flex-1">
                  {medication.guidelines}
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      <div className="bg-white dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <button
          onClick={() => setShowReferences(!showReferences)}
          className="w-full p-3 sm:p-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors rounded-lg"
        >
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
              <BookOpen className="w-4 h-4 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="text-left">
              <h3 className="text-sm sm:text-base font-medium text-gray-900 dark:text-gray-100">
                Referências Científicas
              </h3>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Fontes bibliográficas e evidências
              </p>
            </div>
          </div>
          {showReferences ? (
            <ChevronUp className="w-4 h-4 text-gray-400" />
          ) : (
            <ChevronDown className="w-4 h-4 text-gray-400" />
          )}
        </button>

        {showReferences && (
          <div className="px-3 sm:px-4 pb-3 sm:pb-4 border-t border-gray-100 dark:border-gray-700">
            <div className="pt-3 flex items-start gap-2">
              <ExternalLink className="w-4 h-4 text-purple-600 dark:text-purple-400 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap break-words min-w-0 flex-1">
                {medication.scientific_references || "UpToDate. Evidence-based clinical decision support resource. Waltham, MA: Wolters Kluwer."}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
