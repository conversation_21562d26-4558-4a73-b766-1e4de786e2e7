
import { ChevronDown, ChevronUp } from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

interface FilterAccordionProps {
  title: string;
  count?: number;
  isOpen: boolean;
  onToggle: (e: React.MouseEvent) => void;
  children: React.ReactNode;
}

export const FilterAccordion = ({
  title,
  count,
  isOpen,
  onToggle,
  children,
}: FilterAccordionProps) => {
  return (
    <>
      <button
        onClick={(e) => {
          // Make sure this event doesn't propagate to children
          e.stopPropagation();
          onToggle(e);
        }}
        className="w-full flex items-center justify-between p-4 transition-all duration-300 hover:bg-[#FEF7CD]/50"
      >
        <div className="flex items-center gap-3">
          <span className="text-lg font-bold text-gray-800">{title}</span>
          {count !== undefined && count > 0 && (
            <Badge
              variant="outline"
              className="bg-[#FF6B00] text-white border-black font-bold px-2 py-1 text-xs"
            >
              {count}
            </Badge>
          )}
        </div>
        {isOpen ? (
          <ChevronUp className="h-5 w-5 text-gray-500 transition-transform duration-300" />
        ) : (
          <ChevronDown className="h-5 w-5 text-gray-500 transition-transform duration-300" />
        )}
      </button>

      <div
        className={cn(
          "transition-all duration-300 ease-in-out overflow-hidden",
          isOpen ? "max-h-[500px] opacity-100" : "max-h-0 opacity-0"
        )}
      >
        <div className="p-4 border-t border-gray-200">
          {children}
        </div>
      </div>
    </>
  );
};
