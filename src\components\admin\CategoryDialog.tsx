import { useState, useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { Pencil, Trash2, Upload } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface CategoryDialogProps {
  category?: {
    id: string;
    name: string;
    description: string | null;
    color: string | null;
    icon_url: string | null;
  };
  isOpen: boolean;
  onClose: () => void;
}

const GRADIENT_COLORS = [
  "from-blue-500/20 to-blue-600/10 text-blue-700",
  "from-red-500/20 to-red-600/10 text-red-700",
  "from-green-500/20 to-green-600/10 text-green-700",
  "from-purple-500/20 to-purple-600/10 text-purple-700",
  "from-yellow-500/20 to-yellow-600/10 text-yellow-700",
  "from-pink-500/20 to-pink-600/10 text-pink-700",
  "from-cyan-500/20 to-cyan-600/10 text-cyan-700",
  "from-orange-500/20 to-orange-600/10 text-orange-700",
];

export function CategoryDialog({ category, isOpen, onClose }: CategoryDialogProps) {
  const [name, setName] = useState(category?.name || "");
  const [description, setDescription] = useState(category?.description || "");
  const [color, setColor] = useState(category?.color || GRADIENT_COLORS[0]);
  const [iconFile, setIconFile] = useState<File | null>(null);
  const [iconPreview, setIconPreview] = useState<string | null>(category?.icon_url || null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (isOpen) {
      setName(category?.name || "");
      setDescription(category?.description || "");
      setColor(category?.color || GRADIENT_COLORS[0]);
      setIconPreview(category?.icon_url || null);
      setIconFile(null);
    }
  }, [isOpen, category]);

  const handleIconChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setIconFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setIconPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadIcon = async (categoryId: string): Promise<string | null> => {
    if (!iconFile) return null;

    const fileExt = iconFile.name.split('.').pop();
    const filePath = `${categoryId}.${fileExt}`;

    const { error: uploadError } = await supabase.storage
      .from('category-icons')
      .upload(filePath, iconFile, {
        upsert: true,
      });

    if (uploadError) {
      throw uploadError;
    }

    const { data } = supabase.storage
      .from('category-icons')
      .getPublicUrl(filePath);

    return data.publicUrl;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      let icon_url = category?.icon_url;

      if (category) {
        if (iconFile) {
          icon_url = await uploadIcon(category.id);
        }

        const { error } = await supabase
          .from("pedbook_medication_categories")
          .update({ name, description, color, icon_url })
          .eq("id", category.id);

        if (error) throw error;

        toast({
          title: "Categoria atualizada com sucesso!",
          description: `A categoria ${name} foi atualizada.`,
        });
      } else {
        const { data: newCategory, error: insertError } = await supabase
          .from("pedbook_medication_categories")
          .insert([{ name, description, color }])
          .select()
          .single();

        if (insertError) throw insertError;

        if (iconFile && newCategory) {
          icon_url = await uploadIcon(newCategory.id);
          const { error: updateError } = await supabase
            .from("pedbook_medication_categories")
            .update({ icon_url })
            .eq("id", newCategory.id);

          if (updateError) throw updateError;
        }

        toast({
          title: "Categoria criada com sucesso!",
          description: `A categoria ${name} foi adicionada.`,
        });
      }

      queryClient.invalidateQueries({ queryKey: ["medication-categories"] });
      onClose();
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao salvar categoria",
        description: error.message || "Ocorreu um erro ao salvar a categoria.",
      });
    }
  };

  const handleDelete = async () => {
    try {
      if (category?.icon_url) {
        const iconPath = category.icon_url.split('/').pop();
        if (iconPath) {
          await supabase.storage
            .from('category-icons')
            .remove([iconPath]);
        }
      }

      const { error } = await supabase
        .from("pedbook_medication_categories")
        .delete()
        .eq("id", category?.id);

      if (error) throw error;

      toast({
        title: "Categoria excluída com sucesso!",
        description: `A categoria ${category?.name} foi excluída.`,
      });

      queryClient.invalidateQueries({ queryKey: ["medication-categories"] });
      setShowDeleteDialog(false);
      onClose();
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao excluir categoria",
        description: error.message || "Ocorreu um erro ao excluir a categoria.",
      });
    }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {category ? "Editar Categoria" : "Nova Categoria"}
            </DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="name">Nome</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="description">Descrição</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
              />
            </div>
            <div>
              <Label>Cor do Gradiente</Label>
              <div className="grid grid-cols-4 gap-2 mt-1">
                {GRADIENT_COLORS.map((gradientColor) => (
                  <button
                    key={gradientColor}
                    type="button"
                    className={`h-8 rounded-md bg-gradient-to-r ${gradientColor} ${
                      color === gradientColor ? 'ring-2 ring-primary' : ''
                    }`}
                    onClick={() => setColor(gradientColor)}
                  />
                ))}
              </div>
            </div>
            <div>
              <Label htmlFor="icon">Ícone</Label>
              <div className="mt-1 flex items-center gap-4">
                {iconPreview && (
                  <img
                    src={iconPreview}
                    alt="Category icon preview"
                    className="h-12 w-12 object-cover rounded-lg"
                  />
                )}
                <Label
                  htmlFor="icon-upload"
                  className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Escolher arquivo
                </Label>
                <Input
                  id="icon-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleIconChange}
                  className="hidden"
                />
              </div>
            </div>
            <div className="flex justify-between">
              {category && (
                <Button
                  type="button"
                  variant="destructive"
                  onClick={() => setShowDeleteDialog(true)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Excluir
                </Button>
              )}
              <div className="flex gap-2">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancelar
                </Button>
                <Button type="submit">Salvar</Button>
              </div>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir a categoria "{category?.name}"? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}