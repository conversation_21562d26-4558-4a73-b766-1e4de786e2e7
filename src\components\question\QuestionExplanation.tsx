
import { QuestionAICommentary } from "./QuestionAICommentary";
import type { Question } from "@/types/question";
import { FormattedContent } from "./FormattedContent";
import { useEffect } from "react";

interface QuestionExplanationProps {
  hasAnswered: boolean;
  aiCommentary?: any;
  question?: Question;
  onCommentaryGenerated: (commentary: any) => void;
}

export const QuestionExplanation = ({
  hasAnswered,
  aiCommentary,
  question,
  onCommentaryGenerated
}: QuestionExplanationProps) => {
  if (!hasAnswered || !question) return null;

  const explanationText = aiCommentary?.comentario_final || question.final_comment;



  return (
    <>
      {explanationText && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h3 className="font-medium text-gray-900 mb-2">Explicação:</h3>
          <FormattedContent content={explanationText} />
        </div>
      )}
    </>
  );
};
