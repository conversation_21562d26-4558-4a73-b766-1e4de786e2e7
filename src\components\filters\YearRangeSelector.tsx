import { Label } from "@/components/ui/label";
import { RangeSlider } from "../RangeSlider";

interface YearRangeSelectorProps {
  yearRange: [number, number];
  onYearRangeChange: (range: [number, number]) => void;
}

const YearRangeSelector = ({ yearRange, onYearRangeChange }: YearRangeSelectorProps) => {
  const currentYear = new Date().getFullYear();

  return (
    <div className="space-y-2">
      <Label>Período (2015 - {currentYear})</Label>
      <RangeSlider
        min={2015}
        max={currentYear}
        value={yearRange}
        onValueChange={onYearRangeChange}
      />
      <div className="flex justify-between text-sm text-gray-500">
        <span>{yearRange[0]}</span>
        <span>{yearRange[1]}</span>
      </div>
    </div>
  );
};

export default YearRangeSelector;