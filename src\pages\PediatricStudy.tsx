import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import QuestionFilter from "@/components/QuestionFilter";
import type { SelectedFilters } from "@/types/question";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const PediatricStudy = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [questions, setQuestions] = useState([]);
  const [selectedFilters, setSelectedFilters] = useState<SelectedFilters>({
    specialties: [],
    themes: [],
    focuses: [],
    locations: [],
    years: [],
    question_types: [],
    question_formats: [],
    excludeAnswered: false
  });

  const handleStartStudy = async (sessionId: string) => {
    try {
      // Navegar para a página de questões com o sessionId
      navigate(`/questions/${sessionId}`);
    } catch (error: any) {
      console.error('❌ [PediatricStudy] Error navigating to session:', error);
      toast({
        title: "Erro ao iniciar estudos",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <Header />

      {/* Main Content - Apenas o Filtro */}
      <main className="container mx-auto px-4 py-6 max-w-6xl">
        <QuestionFilter
          selectedFilters={selectedFilters}
          setSelectedFilters={setSelectedFilters}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          setQuestions={setQuestions}
          onSessionCreated={handleStartStudy}
          onShowRandomDialog={() => {}}
          domain="pediatria"
        />
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default PediatricStudy;
