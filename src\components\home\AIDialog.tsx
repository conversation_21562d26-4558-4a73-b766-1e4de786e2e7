
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON>ontent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogFooter,
  AlertDialogCancel,
} from "@/components/ui/alert-dialog";
import { <PERSON>R<PERSON>, Bot } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useSession } from '@supabase/auth-helpers-react';
import AuthDialog from '@/components/auth/AuthDialog';
import React from 'react';

interface AIDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const AIDialog = ({ open, onOpenChange }: AIDialogProps) => {
  const navigate = useNavigate();
  const session = useSession();
  const [showAuthDialog, setShowAuthDialog] = React.useState(false);

  const handleDrWillClick = () => {
    if (!session) {
      setShowAuthDialog(true);
      return;
    }

    onO<PERSON>Change(false);
    navigate('/dr-will');
  };

  return (
    <>
      <AlertDialog open={open} onOpenChange={onOpenChange}>
        <AlertDialogContent className="max-h-[70dvh] max-w-[80dvw] sm:max-w-[425px] rounded-2xl border-none shadow-xl">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-center">Assistente IA Dr. Will</AlertDialogTitle>
          </AlertDialogHeader>
          <div className="flex flex-col gap-4 py-4">
            <Button
              className="w-full flex items-center justify-between bg-gradient-to-r from-primary/90 to-primary hover:from-primary hover:to-primary/90 rounded-xl transition-all duration-300 hover:shadow-lg hover:scale-[1.02]"
              onClick={handleDrWillClick}
            >
              <div className="flex items-center gap-2">
                <Bot className="h-5 w-5" />
                <span>Conversar com Will</span>
              </div>
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel className="w-full sm:w-auto rounded-xl transition-all duration-300 hover:bg-gray-100 hover:scale-[1.02]">
              Voltar ao MENU
            </AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Auth dialog */}
      <AuthDialog
        open={showAuthDialog}
        onOpenChange={setShowAuthDialog}
        hidden={true}
      />
    </>
  );
};
