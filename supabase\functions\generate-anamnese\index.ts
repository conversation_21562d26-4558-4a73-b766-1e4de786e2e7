
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { patient, anamnese, pediatric, exame, templateType } = await req.json();

    // Log received data for debugging
    console.log("Received data for processing:");
    console.log("Patient data:", JSON.stringify(patient));
    
    if (templateType === "traditional") {
      console.log("Anamnese data:", JSON.stringify(anamnese || {}));
    } else if (templateType === "pediatric_1_2m") {
      console.log("Pediatric data:", JSON.stringify(pediatric || {}));
    }
    
    console.log("Exame data:", JSON.stringify(exame || {}));
    console.log("Template type:", templateType);

    // Create detailed prompt for OpenAI based on template type
    let prompt = "";
    
    if (templateType === "traditional") {
      prompt = `
      Você é um médico pediatra experiente, especializado em criar textos completos, organizados e bem detalhados de anamnese e exame físico pediátrico tradicional.
      
      Com base nas seguintes informações do paciente e achados clínicos, gere um texto completo, coeso e bem estruturado de anamnese e exame físico. Para campos não preenchidos, use valores normais apropriados para a idade e gênero do paciente.
      
      MUITO IMPORTANTE:
      - Se um campo estiver vazio, preencha com valores normais apropriados para a idade e gênero
      - Se uma seção do abdome tiver "dor à palpação" marcada, mantenha todo o texto normal do abdome, mudando apenas esta parte específica (Exemplo: "Abdome plano, ruídos hidroaéreos presentes, flácido, doloroso à palpação superficial e profunda, sem visceromegalias ou massas palpáveis")
      - Siga o formato tradicional de anamnese e exame físico pediátrico, usando subtítulos e organização clara
      - Gere texto profissional usando termos médicos formais
      - Para a anamnese tradicional, inclua: Identificação, Queixa Principal, HPMA, AP, AF, Histórico Gestacional, Neonatal, Alimentar, DNPM, Vacinal e Socioambiental
      - Para o exame físico, organize por sistemas: Geral, Cardiovascular, Respiratório, Abdome, Genitália, Membros, Neurológico, ORL
      - Formate cada seção com subtítulos em negrito
      
      DADOS DO PACIENTE:
      - Sexo: ${patient.gender === 'male' ? 'Masculino' : patient.gender === 'female' ? 'Feminino' : 'Outro'}
      - Idade: ${patient.age} ${patient.ageUnit === 'years' ? 'anos' : patient.ageUnit === 'months' ? 'meses' : 'dias'}
      - Tipo de consulta: ${patient.appointmentType || 'Consulta de rotina'}
      - Queixa principal: ${patient.mainComplaint || 'Não informada'}
      
      ANAMNESE (campos preenchidos apenas):
      ${anamnese && Object.entries(anamnese)
        .filter(([_, value]) => value)
        .map(([key, value]) => {
          let fieldName = key;
          switch(key) {
            case 'hda': fieldName = 'História da Doença Atual'; break;
            case 'perinatais': fieldName = 'Antecedentes Perinatais'; break;
            case 'patologicos': fieldName = 'Antecedentes Patológicos'; break;
            case 'cirurgicos': fieldName = 'Antecedentes Cirúrgicos'; break;
            case 'alergicos': fieldName = 'Antecedentes Alérgicos'; break;
            case 'medicamentosos': fieldName = 'Medicamentos em uso'; break;
            case 'vacinacao': fieldName = 'Vacinação'; break;
            case 'desenvolvimento': fieldName = 'Desenvolvimento Neuropsicomotor'; break;
            case 'alimentacao': fieldName = 'Alimentação'; break;
            case 'ambiente': fieldName = 'Ambiente Familiar/Social'; break;
          }
          return `- ${fieldName}: ${value}`;
        }).join('\n') || "Nenhum dado de anamnese fornecido."
      }
      
      EXAME FÍSICO (campos preenchidos apenas):
      - Estado Geral: ${exame?.estadoGeral || 'Não informado'}
      - Sinais Vitais: 
        FC: ${exame?.sinaisVitais?.fc || 'Não informado'}
        FR: ${exame?.sinaisVitais?.fr || 'Não informado'}
        PA: ${exame?.sinaisVitais?.pa || 'Não informado'}
        Temp: ${exame?.sinaisVitais?.temp || 'Não informado'}
        Sat: ${exame?.sinaisVitais?.sat || 'Não informado'}
      - Cabeça e Pescoço: ${exame?.cabecaPescoco || 'Não informado'}
      - Tórax: ${exame?.torax || 'Não informado'}
      - Abdome: 
        ${exame?.abdome?.distensao ? '- Distensão: Sim' : ''}
        ${exame?.abdome?.rhaAumentados ? '- RHA aumentados: Sim' : ''}
        ${exame?.abdome?.dorPalpacao ? '- Dor à palpação: Sim' : ''}
        ${exame?.abdome?.defesaInvoluntaria ? '- Defesa involuntária: Sim' : ''}
        ${exame?.abdome?.massaPalpavel ? '- Massa palpável: Sim' : ''}
        ${exame?.abdome?.detalhes ? `- Detalhes: ${exame.abdome.detalhes}` : ''}
      - Geniturinário: ${exame?.geniturinario || 'Não informado'}
      - Locomoção: ${exame?.locomocao || 'Não informado'}
      - Neurológico: ${exame?.neurologico || 'Não informado'}
      - Pele e Mucosas: ${exame?.peleMucosas || 'Não informado'}
      
      Siga este modelo de resposta para anamnese tradicional, incluindo as seguintes seções:
      
      **ANAMNESE**
      
      **Identificação:**
      [Dados de identificação baseados no perfil do paciente]
      
      **Queixa Principal:**
      [Queixa principal entre aspas conforme informado]
      
      **História da Doença Atual (HPMA):**
      [História detalhada baseada nas informações fornecidas ou valores normais]
      
      **Antecedentes Patológicos (AP):**
      [Informações sobre comorbidades ou "Nega comorbidades prévias"]
      
      **Antecedentes Familiares (AF):**
      [Informações sobre histórico familiar ou "Nega histórico familiar de doenças"]
      
      **Histórico Gestacional:**
      [Informações sobre gestação ou valores normais]
      
      **Histórico Neonatal:**
      [Informações sobre nascimento ou valores normais]
      
      **Histórico Alimentar:**
      [Informações sobre alimentação ou valores normais]
      
      **Desenvolvimento Neuropsicomotor (DNPM):**
      [Informações sobre desenvolvimento ou "Desenvolvimento adequado para idade"]
      
      **Histórico Vacinal:**
      [Informações sobre vacinação ou "Caderneta de vacinação atualizada"]
      
      **Características Socioambientais:**
      [Informações sobre ambiente ou valores normais]
      
      **EXAME FÍSICO**
      
      **Exame Físico Geral:**
      [Descrição do estado geral baseado nas informações ou valores normais]
      
      **Aparelho Cardiovascular (ACV):**
      [Descrição do exame cardiovascular baseado nas informações ou valores normais]
      
      **Aparelho Respiratório (AR):**
      [Descrição do exame respiratório baseado nas informações ou valores normais]
      
      **Abdome (ABD):**
      [Descrição do exame abdominal baseado nas informações ou valores normais, mantendo o texto padrão e alterando apenas as partes marcadas como alteradas]
      
      **Genitália:**
      [Descrição do exame genital baseado nas informações ou valores normais]
      
      **Membros:**
      [Descrição do exame dos membros baseado nas informações ou valores normais]
      
      **Exame Neurológico:**
      [Descrição do exame neurológico baseado nas informações ou valores normais]
      
      **Exame Otorrinolaringológico:**
      [Descrição do exame ORL baseado nas informações ou valores normais]
      
      Por favor, responda em formato JSON com duas chaves: "anamnese" e "exame", como no exemplo:
      {
        "anamnese": "texto da anamnese completo e formatado...",
        "exame": "texto do exame físico completo e formatado..."
      }
      `;
    } else if (templateType === "pediatric_1_2m") {
      // Prompt para puericultura 1-2 meses
      prompt = `
      Você é um médico pediatra experiente, especializado em criar textos detalhados de anamnese e exame físico para consultas de puericultura de 1-2 meses.
      
      Com base nas seguintes informações do paciente e achados clínicos, gere um texto completo, coeso e bem estruturado de anamnese e exame físico. Para campos não preenchidos, use valores normais apropriados para a idade e gênero do paciente.
      
      MUITO IMPORTANTE:
      - Se um campo estiver vazio, preencha com valores normais apropriados para bebês de 1-2 meses
      - Siga o formato de puericultura, usando subtítulos e organização clara
      - Gere texto profissional usando termos médicos formais
      - Para a anamnese de puericultura, inclua: Identificação, Queixas, Alimentação e Amamentação, Eliminações, Sono, Comportamento, Cuidados com o Bebê, e outros tópicos relevantes
      - Para o exame físico, organize conforme protocolo de puericultura: Medidas antropométricas, exame físico detalhado, incluindo reflexos e desenvolvimento apropriado para 1-2 meses
      - Formate cada seção com subtítulos em negrito
      
      DADOS DO PACIENTE:
      - Nome: ${patient.name || 'Bebê (nome não informado)'}
      - Sexo: ${patient.gender === 'male' ? 'Masculino' : patient.gender === 'female' ? 'Feminino' : 'Outro'}
      - Idade: ${patient.age} ${patient.ageUnit === 'years' ? 'anos' : patient.ageUnit === 'months' ? 'meses' : 'dias'}
      - Data de Nascimento: ${patient.birthDate || 'Não informada'}
      - Informante: ${patient.informant || 'Mãe/Pai (não especificado)'}
      - Diagnósticos Prévios: ${patient.previousDiagnosis || 'Nenhum'}
      - Prematuro: ${patient.premature ? 'Sim' : 'Não'}
      ${patient.premature ? `- Idade Corrigida: ${patient.correctedAge || 'Não informada'}` : ''}
      - Queixas: ${patient.mainComplaint || 'Nenhuma queixa específica. Consulta de rotina para puericultura.'}
      
      QUESTIONÁRIO DIRIGIDO (campos preenchidos apenas):
      ${pediatric && Object.entries(pediatric)
        .filter(([_, value]) => value)
        .map(([key, value]) => {
          let fieldName = key;
          switch(key) {
            case 'headSymptoms': fieldName = 'Crânio/Cabeça'; break;
            case 'mouthSymptoms': fieldName = 'Boca'; break;
            case 'earSymptoms': fieldName = 'Ouvidos'; break;
            case 'eyeSymptoms': fieldName = 'Olhos'; break;
            case 'breathingIssues': fieldName = 'Respiração'; break;
            case 'feedingIssues': fieldName = 'Alimentação'; break;
            case 'digestionIssues': fieldName = 'Digestão'; break;
            case 'urineStool': fieldName = 'Urina e Fezes'; break;
            case 'behaviorIssues': fieldName = 'Comportamento'; break;
            case 'breastfeeding': fieldName = 'Aleitamento Materno'; break;
            case 'breastfeedingTechnique': fieldName = 'Técnica da Amamentação'; break;
            case 'artificialFeeding': fieldName = 'Alimentação Artificial'; break;
            case 'sleep': fieldName = 'Sono'; break;
            case 'careRoutines': fieldName = 'Rotinas de Cuidado'; break;
            case 'motherMeds': fieldName = 'Medicamentos da Mãe'; break;
            case 'babyMeds': fieldName = 'Medicamentos do Bebê'; break;
            case 'vaccineReactions': fieldName = 'Vacinação'; break;
            case 'dnpmObservations': fieldName = 'Desenvolvimento Neuropsicomotor'; break;
          }
          return `- ${fieldName}: ${value}`;
        }).join('\n') || "Nenhum dado específico fornecido no questionário dirigido."
      }
      
      EXAME FÍSICO (campos preenchidos apenas):
      ${exame ? `
      - Peso: ${exame.weight || 'Não informado'}
      - Estatura: ${exame.height || 'Não informado'}
      - Perímetro Cefálico: ${exame.headCircumference || 'Não informado'}
      - Fontanela Anterior: ${exame.anteriorFontanelle || 'Não informado'}
      - Fontanela Posterior: ${exame.posteriorFontanelle || 'Não informado'}
      - Suturas: ${exame.sutures || 'Não informado'}
      - Frequência Cardíaca: ${exame.heartRate || 'Não informado'}
      - Frequência Respiratória: ${exame.respiratoryRate || 'Não informado'}
      - Temperatura: ${exame.temperature || 'Não informado'}
      - Cabeça e Pescoço: ${exame.headNeck || 'Não informado'}
      - Reflexo Vermelho: ${exame.redReflex || 'Não informado'}
      - Orofaringe: ${exame.oropharynx || 'Não informado'}
      - Otoscopia: ${exame.otoscopy || 'Não informado'}
      - Aparelho Cardiovascular: ${exame.cardiovascular || 'Não informado'}
      - Aparelho Respiratório: ${exame.respiratory || 'Não informado'}
      - Abdome: ${exame.abdomen || 'Não informado'}
      - Pulsos Periféricos: ${exame.peripheralPulses || 'Não informado'}
      - Genitália: ${exame.genitalia || 'Não informado'}
      - Cicatriz de BCG: ${exame.bcgScar || 'Não informado'}
      - Manobra de Ortolani: ${exame.ortolaniManeuver || 'Não informado'}
      - Marcha Reflexa: ${exame.reflexWalking || 'Não informado'}
      - Reflexo de Moro: ${exame.moro || 'Não informado'}
      - Reflexo Tônico Cervical Assimétrico: ${exame.asimetricTonicNeck || 'Não informado'}
      - Segue Objetos com o Olhar: ${exame.followsObjects || 'Não informado'}
      - Sorriso Social: ${exame.socialSmile || 'Não informado'}
      - Testes de Triagem: ${exame.screeningTests || 'Não informado'}
      ` : "Nenhum dado de exame físico fornecido."}
      
      Por favor, elabore um texto completo para anamnese e exame físico de uma consulta de puericultura de 1-2 meses, com as seguintes características:
      1. Texto coeso, bem estruturado e profissional
      2. Inclua todas as informações fornecidas e preencha os campos faltantes com valores normais apropriados para bebês de 1-2 meses
      3. Use terminologia médica adequada
      4. Responda em formato JSON com duas chaves: "anamnese" e "exame", como no exemplo:
      {
        "anamnese": "Texto da anamnese...",
        "exame": "Texto do exame físico..."
      }
      `;
    } else {
      // Default prompt for other template types or simple format
      prompt = `
      Você é um médico pediatra experiente, especializado em criar textos de anamnese e exame físico completos.
      
      Com base nas seguintes informações do paciente e achados clínicos, gere um texto completo, coeso e bem estruturado de anamnese e exame físico. Para campos não preenchidos, use valores normais apropriados para a idade e gênero do paciente.
      
      IMPORTANTE:
      - Para campos não preenchidos ou marcados como normais, use valores padrão adequados
      - Se houver campos preenchidos pelo médico, integre exatamente essas informações no texto
      - Se houver alguma alteração descrita, mantenha o texto original para as partes normais e apenas modifique a parte alterada
      - Use linguagem médica formal, técnica e precisa
      - Crie um texto fluido, como se fosse um prontuário médico real
      - Formate o texto usando markdown, com títulos em negrito
      
      DADOS DO PACIENTE:
      - Sexo: ${patient.gender === 'male' ? 'Masculino' : patient.gender === 'female' ? 'Feminino' : 'Outro'}
      - Idade: ${patient.age} ${patient.ageUnit === 'years' ? 'anos' : patient.ageUnit === 'months' ? 'meses' : 'dias'}
      - Tipo de consulta: ${patient.appointmentType || 'Consulta de rotina'}
      - Queixa principal: ${patient.mainComplaint || 'Não informada'}
      
      ANAMNESE (campos preenchidos apenas):
      ${anamnese && Object.entries(anamnese)
        .filter(([_, value]) => value)
        .map(([key, value]) => {
          let fieldName = key;
          switch(key) {
            case 'hda': fieldName = 'História da Doença Atual'; break;
            case 'perinatais': fieldName = 'Antecedentes Perinatais'; break;
            case 'patologicos': fieldName = 'Antecedentes Patológicos'; break;
            case 'cirurgicos': fieldName = 'Antecedentes Cirúrgicos'; break;
            case 'alergicos': fieldName = 'Antecedentes Alérgicos'; break;
            case 'medicamentosos': fieldName = 'Medicamentos em uso'; break;
            case 'vacinacao': fieldName = 'Vacinação'; break;
            case 'desenvolvimento': fieldName = 'Desenvolvimento Neuropsicomotor'; break;
            case 'alimentacao': fieldName = 'Alimentação'; break;
            case 'ambiente': fieldName = 'Ambiente Familiar/Social'; break;
          }
          return `- ${fieldName}: ${value}`;
        }).join('\n') || "Nenhum dado de anamnese fornecido."
      }
      
      EXAME FÍSICO (campos preenchidos apenas):
      - Estado Geral: ${exame?.estadoGeral || 'Não informado'}
      - Sinais Vitais: 
        FC: ${exame?.sinaisVitais?.fc || 'Não informado'}
        FR: ${exame?.sinaisVitais?.fr || 'Não informado'}
        PA: ${exame?.sinaisVitais?.pa || 'Não informado'}
        Temp: ${exame?.sinaisVitais?.temp || 'Não informado'}
        Sat: ${exame?.sinaisVitais?.sat || 'Não informado'}
      - Cabeça e Pescoço: ${exame?.cabecaPescoco || 'Não informado'}
      - Tórax: ${exame?.torax || 'Não informado'}
      - Abdome: 
        ${exame?.abdome?.distensao ? '- Distensão: Sim' : ''}
        ${exame?.abdome?.rhaAumentados ? '- RHA aumentados: Sim' : ''}
        ${exame?.abdome?.dorPalpacao ? '- Dor à palpação: Sim' : ''}
        ${exame?.abdome?.defesaInvoluntaria ? '- Defesa involuntária: Sim' : ''}
        ${exame?.abdome?.massaPalpavel ? '- Massa palpável: Sim' : ''}
        ${exame?.abdome?.detalhes ? `- Detalhes: ${exame.abdome.detalhes}` : ''}
      - Geniturinário: ${exame?.geniturinario || 'Não informado'}
      - Locomoção: ${exame?.locomocao || 'Não informado'}
      - Neurológico: ${exame?.neurologico || 'Não informado'}
      - Pele e Mucosas: ${exame?.peleMucosas || 'Não informado'}
      
      Gere um texto completo para anamnese e exame físico, com as seguintes características:
      1. Texto coeso, bem estruturado e profissional
      2. Inclua todas as informações fornecidas e preencha os campos faltantes com valores normais apropriados
      3. Use terminologia médica adequada
      4. Responda em formato JSON com duas chaves: "anamnese" e "exame", como no exemplo:
      {
        "anamnese": "Texto da anamnese...",
        "exame": "Texto do exame físico..."
      }
      `;
    }

    console.log("Sending request to OpenAI API");
    
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { 
            role: 'system', 
            content: 'Você é um assistente médico especializado em pediatria, com conhecimento avançado em elaborar prontuários médicos precisos, detalhados e bem formatados.' 
          },
          { role: 'user', content: prompt }
        ],
        temperature: 0.5,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('OpenAI API error:', errorData);
      throw new Error(`OpenAI API error: ${response.status} - ${JSON.stringify(errorData)}`);
    }

    const data = await response.json();
    console.log('Received response from OpenAI');
    
    // Extract assistant's response
    const assistantResponse = data.choices[0].message.content;
    
    // Process response to ensure we have valid JSON
    let parsedResponse;
    
    try {
      // Try to parse JSON directly from the response
      parsedResponse = JSON.parse(assistantResponse);
      console.log("Successfully parsed JSON response");
    } catch (e) {
      console.log("Failed to parse JSON directly, trying to extract JSON from text");
      
      // Look for JSON pattern in text response
      const jsonMatch = assistantResponse.match(/\{[\s\S]*\}/);
      
      if (jsonMatch) {
        try {
          parsedResponse = JSON.parse(jsonMatch[0]);
          console.log("Successfully extracted and parsed JSON from text");
        } catch (e2) {
          console.error("Failed to parse extracted JSON:", e2);
          
          // Fallback: extract anamnese and exame sections from text
          const anamneseMatch = assistantResponse.match(/anamnese[:\s]+([\s\S]+?)(?=exame|$)/i);
          const exameMatch = assistantResponse.match(/exame[:\s]+([\s\S]+?)$/i);
          
          parsedResponse = {
            anamnese: anamneseMatch ? anamneseMatch[1].trim() : 'Não foi possível gerar a anamnese.',
            exame: exameMatch ? exameMatch[1].trim() : 'Não foi possível gerar o exame físico.'
          };
          
          console.log("Created fallback response by extracting sections");
        }
      } else {
        console.error("No JSON pattern found in response");
        
        // Last resort: split text into two parts
        const halfPoint = Math.floor(assistantResponse.length / 2);
        parsedResponse = {
          anamnese: assistantResponse.substring(0, halfPoint).trim(),
          exame: assistantResponse.substring(halfPoint).trim()
        };
        
        console.log("Created last-resort response by splitting text");
      }
    }

    console.log("Final response structure:", Object.keys(parsedResponse));
    
    return new Response(
      JSON.stringify(parsedResponse),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error in generate-anamnese function:', error);
    return new Response(
      JSON.stringify({ 
        error: error.message,
        anamnese: "Ocorreu um erro ao gerar a anamnese. Por favor, tente novamente.",
        exame: "Ocorreu um erro ao gerar o exame físico. Por favor, tente novamente." 
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
