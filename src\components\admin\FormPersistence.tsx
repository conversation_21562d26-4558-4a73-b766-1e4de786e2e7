import React, { useEffect, useRef } from 'react';

interface FormPersistenceProps {
  formId: string;
  storageKey: string;
  children: React.ReactNode;
  debug?: boolean;
}

/**
 * Component that persists form data to localStorage when switching tabs
 * and restores it when returning to the tab.
 */
export const FormPersistence: React.FC<FormPersistenceProps> = ({
  formId,
  storageKey,
  children,
  debug = true,
}) => {
  const formRef = useRef<HTMLFormElement | null>(null);
  const isRestoringRef = useRef(false);

  // Save form data to localStorage
  const saveFormData = () => {
    const form = document.getElementById(formId) as HTMLFormElement;
    if (!form) {
      return;
    }

    formRef.current = form;
    const formData = new FormData(form);
    const data: Record<string, any> = {};

    formData.forEach((value, key) => {
      data[key] = value;
    });

    // Also save any rich text editors or other complex inputs
    const textareas = form.querySelectorAll('textarea');
    textareas.forEach(textarea => {
      data[textarea.name || textarea.id] = textarea.value;
    });

    // Save to localStorage
    localStorage.setItem(`form_${storageKey}`, JSON.stringify(data));
  };

  // Restore form data from localStorage
  const restoreFormData = () => {
    const savedData = localStorage.getItem(`form_${storageKey}`);
    if (!savedData) {
      return;
    }

    try {
      const data = JSON.parse(savedData);
      const form = document.getElementById(formId) as HTMLFormElement;
      
      if (!form) {
        if (debug) console.log(`[FormPersistence] Form with ID "${formId}" not found for restoration`);
        return;
      }

      isRestoringRef.current = true;

      // Restore values to form elements
      Object.entries(data).forEach(([key, value]) => {
        const element = form.elements.namedItem(key) as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
        if (element) {
          if (element.type === 'checkbox' || element.type === 'radio') {
            (element as HTMLInputElement).checked = value === 'on' || value === true;
          } else {
            element.value = value as string;
          }
        }
      });

      if (debug) {
        console.log(`[FormPersistence] Restored form data for "${storageKey}"`, data);
      }

      isRestoringRef.current = false;
    } catch (error) {
      console.error(`[FormPersistence] Error restoring form data for "${storageKey}"`, error);
    }
  };

  // Handle visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        // Tab is being hidden, save form data
        saveFormData();
      } else if (document.visibilityState === 'visible') {
        // Tab is becoming visible again, restore form data
        setTimeout(() => {
          restoreFormData();
        }, 100); // Small delay to ensure form is fully rendered
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Try to restore form data on mount
    setTimeout(() => {
      restoreFormData();
    }, 500); // Delay to ensure form is fully rendered

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      
      // Save form data on unmount
      saveFormData();
    };
  }, [formId, storageKey]);

  return <>{children}</>;
};
