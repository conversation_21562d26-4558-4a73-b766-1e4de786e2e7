
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { ExameData } from "./AnamneseForm";
import { Stethoscope, Info, ChevronDown, AlertCircle, Zap } from "lucide-react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Switch } from "@/components/ui/switch";

interface ExameBlocksProps {
  data: ExameData;
  onChange: (data: Partial<ExameData>) => void;
}

export const ExameBlocks: React.FC<ExameBlocksProps> = ({ data, onChange }) => {
  const [quickMode, setQuickMode] = useState(false);
  const [expandedBlocks, setExpandedBlocks] = useState<string[]>([]);
  
  const toggleBlockExpansion = (blockId: string) => {
    setExpandedBlocks(prev => 
      prev.includes(blockId) 
        ? prev.filter(id => id !== blockId) 
        : [...prev, blockId]
    );
  };

  const handleSinaisVitaisChange = (field: keyof ExameData["sinaisVitais"], value: string) => {
    onChange({
      sinaisVitais: {
        ...data.sinaisVitais,
        [field]: value,
      },
    });
  };

  const handleAbdomeChange = (field: keyof ExameData["abdome"], value: boolean | string) => {
    onChange({
      abdome: {
        ...data.abdome,
        [field]: value,
      },
    });
  };

  const handleSimpleChange = (field: keyof ExameData, value: string) => {
    onChange({ [field]: value } as Partial<ExameData>);
  };

  // Helper to determine if a section has any data filled
  const hasDataInSection = (sectionName: string): boolean => {
    switch (sectionName) {
      case "sinaisVitais":
        const sv = data.sinaisVitais;
        return !!(sv.fc || sv.fr || sv.pa || sv.temp || sv.sat);
      case "abdome":
        const abd = data.abdome;
        return !!(
          abd.distensao || 
          abd.rhaAumentados || 
          abd.dorPalpacao || 
          abd.defesaInvoluntaria || 
          abd.massaPalpavel || 
          abd.detalhes
        );
      default:
        return !!(data[sectionName as keyof ExameData]);
    }
  };

  const exameBlocks = [
    {
      id: "estadoGeral",
      title: "Estado Geral",
      placeholder: quickMode
        ? "Apenas descreva alterações. Ex: 'prostrado, desidratado'"
        : "BEG, LOTE, corado, hidratado, acianótico, anictérico...",
      hasData: !!data.estadoGeral
    },
    {
      id: "sinaisVitais",
      title: "Sinais Vitais",
      hasData: hasDataInSection("sinaisVitais")
    },
    {
      id: "cabecaPescoco",
      title: "Cabeça e Pescoço",
      placeholder: quickMode
        ? "Apenas descreva alterações. Ex: 'otoscopia com hiperemia à direita'"
        : "Pupilas isofotorreagentes, mucosas úmidas, orofaringe sem alterações...",
      hasData: !!data.cabecaPescoco
    },
    {
      id: "torax",
      title: "Tórax",
      placeholder: quickMode
        ? "Apenas descreva alterações. Ex: 'estertores crepitantes em base direita'"
        : "Simétrico, expansibilidade preservada, murmúrio vesicular presente bilateralmente sem ruídos adventícios...",
      hasData: !!data.torax
    },
    {
      id: "abdome",
      title: "Abdome",
      hasData: hasDataInSection("abdome")
    },
    {
      id: "geniturinario",
      title: "Geniturinário",
      placeholder: quickMode
        ? "Apenas descreva alterações. Ex: 'fimose'"
        : "Genitália externa típica para idade e sexo, sem alterações...",
      hasData: !!data.geniturinario
    },
    {
      id: "locomocao",
      title: "Locomoção",
      placeholder: quickMode
        ? "Apenas descreva alterações. Ex: 'claudicação à direita'"
        : "Marcha preservada, sem alterações, força e tônus muscular preservados...",
      hasData: !!data.locomocao
    },
    {
      id: "neurologico",
      title: "Neurológico",
      placeholder: quickMode
        ? "Apenas descreva alterações. Ex: 'rigidez de nuca'"
        : "Glasgow 15, sem alterações neurológicas, sem sinais meníngeos...",
      hasData: !!data.neurologico
    },
    {
      id: "peleMucosas",
      title: "Pele e Mucosas",
      placeholder: quickMode
        ? "Apenas descreva alterações. Ex: 'lesões eritematosas em região torácica'"
        : "Pele íntegra, sem lesões, mucosas úmidas e coradas...",
      hasData: !!data.peleMucosas
    }
  ];

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Stethoscope className="h-5 w-5 text-primary" />
            Exame Físico
          </CardTitle>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">Apenas alterações</span>
            <Switch 
              checked={quickMode} 
              onCheckedChange={setQuickMode} 
              className="data-[state=checked]:bg-primary"
            />
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-4 w-4 text-gray-400 cursor-help" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="w-[220px] text-xs">
                    No modo "Apenas alterações", você só precisa descrever o que está alterado. 
                    Os campos vazios serão preenchidos com valores normais.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {quickMode ? (
          <div className="p-4 bg-amber-50 border border-amber-200 rounded-md flex gap-3 items-start">
            <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5" />
            <div>
              <h3 className="font-medium text-amber-800">Modo rápido ativado</h3>
              <p className="text-sm text-amber-700">
                Preencha apenas os achados alterados. Os campos vazios serão automaticamente 
                preenchidos com valores normais para a idade e gênero do paciente.
              </p>
            </div>
          </div>
        ) : null}

        {exameBlocks.map((block) => (
          <Collapsible 
            key={block.id} 
            className="border rounded-md"
            open={expandedBlocks.includes(block.id)}
            onOpenChange={() => toggleBlockExpansion(block.id)}
          >
            <div className="flex items-center justify-between p-4">
              <Label className="font-medium">
                {block.title}
                {block.hasData && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary/10 text-primary">
                    <Zap className="h-3 w-3 mr-1" />
                    Preenchido
                  </span>
                )}
              </Label>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm">
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </CollapsibleTrigger>
            </div>
            <CollapsibleContent className="px-4 pb-4">
              {block.id === "sinaisVitais" ? (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="fc">FC</Label>
                    <div className="flex items-center mt-1">
                      <Input
                        id="fc"
                        value={data.sinaisVitais.fc}
                        onChange={(e) => handleSinaisVitaisChange("fc", e.target.value)}
                        placeholder="80"
                      />
                      <span className="ml-2">bpm</span>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="fr">FR</Label>
                    <div className="flex items-center mt-1">
                      <Input
                        id="fr"
                        value={data.sinaisVitais.fr}
                        onChange={(e) => handleSinaisVitaisChange("fr", e.target.value)}
                        placeholder="20"
                      />
                      <span className="ml-2">irpm</span>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="pa">PA</Label>
                    <div className="flex items-center mt-1">
                      <Input
                        id="pa"
                        value={data.sinaisVitais.pa}
                        onChange={(e) => handleSinaisVitaisChange("pa", e.target.value)}
                        placeholder="120x80"
                      />
                      <span className="ml-2">mmHg</span>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="temp">Temp</Label>
                    <div className="flex items-center mt-1">
                      <Input
                        id="temp"
                        value={data.sinaisVitais.temp}
                        onChange={(e) => handleSinaisVitaisChange("temp", e.target.value)}
                        placeholder="36.5"
                      />
                      <span className="ml-2">°C</span>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="sat">Sat</Label>
                    <div className="flex items-center mt-1">
                      <Input
                        id="sat"
                        value={data.sinaisVitais.sat}
                        onChange={(e) => handleSinaisVitaisChange("sat", e.target.value)}
                        placeholder="98"
                      />
                      <span className="ml-2">%</span>
                    </div>
                  </div>
                </div>
              ) : block.id === "abdome" ? (
                <div className="space-y-4">
                  <div className="flex flex-wrap gap-6">
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="distensao" 
                        checked={data.abdome.distensao}
                        onCheckedChange={(checked) => 
                          handleAbdomeChange("distensao", checked as boolean)
                        }
                      />
                      <Label htmlFor="distensao">Distensão</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="rhaAumentados" 
                        checked={data.abdome.rhaAumentados}
                        onCheckedChange={(checked) => 
                          handleAbdomeChange("rhaAumentados", checked as boolean)
                        }
                      />
                      <Label htmlFor="rhaAumentados">RHA aumentados</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="dorPalpacao" 
                        checked={data.abdome.dorPalpacao}
                        onCheckedChange={(checked) => 
                          handleAbdomeChange("dorPalpacao", checked as boolean)
                        }
                      />
                      <Label htmlFor="dorPalpacao">Dor à palpação</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="defesaInvoluntaria" 
                        checked={data.abdome.defesaInvoluntaria}
                        onCheckedChange={(checked) => 
                          handleAbdomeChange("defesaInvoluntaria", checked as boolean)
                        }
                      />
                      <Label htmlFor="defesaInvoluntaria">Defesa involuntária</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="massaPalpavel" 
                        checked={data.abdome.massaPalpavel}
                        onCheckedChange={(checked) => 
                          handleAbdomeChange("massaPalpavel", checked as boolean)
                        }
                      />
                      <Label htmlFor="massaPalpavel">Massa palpável</Label>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="abdomeDetalhes">Detalhes adicionais</Label>
                    <Textarea
                      id="abdomeDetalhes"
                      value={data.abdome.detalhes}
                      onChange={(e) => handleAbdomeChange("detalhes", e.target.value)}
                      placeholder={quickMode 
                        ? "Descreva apenas alterações adicionais. Ex: 'dor em FID'"
                        : "Outros detalhes sobre o abdome..."}
                      className="min-h-[60px] mt-2"
                    />
                  </div>
                </div>
              ) : (
                <Textarea
                  id={block.id}
                  value={data[block.id as keyof ExameData] as string}
                  onChange={(e) => handleSimpleChange(block.id as keyof ExameData, e.target.value)}
                  placeholder={block.placeholder}
                  className="min-h-[60px]"
                />
              )}
            </CollapsibleContent>
          </Collapsible>
        ))}
      </CardContent>
    </Card>
  );
};
