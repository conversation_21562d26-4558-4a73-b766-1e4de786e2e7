import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { DiagnosisResult } from "./DiagnosisResult";
import { FormFields } from "./FormFields";

interface FormData {
  age: number;
  weight: number;
  gender: "male" | "female";
  symptoms: string[];
  hasChronicDiseases: boolean;
  chronicDiseases?: string;
  symptomsIntensity: number;
  hasRecentExams: boolean;
  examDetails?: string;
  isPregnant?: boolean;
  isPediatric: boolean;
  manualSymptoms?: string;
}

interface DiagnosisResponse {
  diagnoses: Array<{
    condition: string;
    probability: number;
    clinicalPresentation: string;
    exams: string;
    treatment: string;
  }>;
  summary: string;
}

interface DiagnosisFormProps {
  onDiagnosisStart: () => void;
}

export function DiagnosisForm({ onDiagnosisStart }: DiagnosisFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [diagnosisResult, setDiagnosisResult] = useState<DiagnosisResponse | null>(null);
  const [formData, setFormData] = useState<FormData>({
    age: 0,
    weight: 0,
    gender: "male",
    symptoms: [],
    hasChronicDiseases: false,
    symptomsIntensity: 5,
    hasRecentExams: false,
    isPediatric: false,
  });
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Verifica se há pelo menos um sintoma selecionado ou sintomas manuais preenchidos
    if (formData.symptoms.length === 0 && (!formData.manualSymptoms || formData.manualSymptoms.trim() === "")) {
      toast({
        variant: "destructive",
        title: "Erro no formulário",
        description: "Por favor, selecione pelo menos um sintoma ou descreva os sintomas manualmente.",
      });
      return;
    }

    if (formData.weight <= 0) {
      toast({
        variant: "destructive",
        title: "Erro no formulário",
        description: "Por favor, insira um peso válido.",
      });
      return;
    }

    setIsLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke('diagnose', {
        body: formData,
      });

      if (error) throw error;
      setDiagnosisResult(data);
      onDiagnosisStart();
    } catch (error) {
      console.error('Error:', error);
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Ocorreu um erro ao processar o diagnóstico. Tente novamente.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (diagnosisResult) {
    return (
      <div className="space-y-6 animate-fade-in">
        <DiagnosisResult 
          diagnoses={diagnosisResult.diagnoses} 
          summary={diagnosisResult.summary}
          formData={formData}
        />
        <Button 
          onClick={() => setDiagnosisResult(null)} 
          className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-white shadow-lg hover:shadow-xl transition-all duration-300"
        >
          Nova Análise
        </Button>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-8 animate-fade-in">
      <Card className="p-8 bg-white/80 backdrop-blur-sm border border-primary/20 shadow-lg hover:shadow-xl transition-all duration-300">
        <FormFields 
          formData={formData}
          setFormData={setFormData}
        />
      </Card>

      <Button 
        type="submit" 
        className="w-full h-14 text-lg bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-white shadow-lg hover:shadow-xl transition-all duration-300"
        disabled={isLoading || formData.weight <= 0}
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-6 w-6 animate-spin" />
            Processando
          </>
        ) : (
          "Analisar Sintomas"
        )}
      </Button>
    </form>
  );
}