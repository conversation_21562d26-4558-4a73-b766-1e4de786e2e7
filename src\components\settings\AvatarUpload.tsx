import React, { useRef } from "react";
import { Input } from "@/components/ui/input";
import { Camera } from "lucide-react";

interface AvatarUploadProps {
  profile: any;
  session: any;
  uploading: boolean;
  handleAvatarUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const AvatarUpload = ({ profile, session, uploading, handleAvatarUpload }: AvatarUploadProps) => {
  const inputRef = useRef<HTMLInputElement>(null);
  
  const handleClick = () => {
    inputRef.current?.click();
  };

  return (
    <div className="relative w-20 h-20 sm:w-24 sm:h-24 mx-auto mb-4 sm:mb-6">
      <div className="w-20 h-20 sm:w-24 sm:h-24 rounded-full overflow-hidden cursor-pointer transition-transform hover:scale-105" onClick={handleClick}>
        <img
          src={profile?.avatar_url || `https://www.gravatar.com/avatar/${session?.user?.email}?d=mp`}
          alt={profile?.full_name}
          className="w-full h-full object-cover"
          onError={(e) => {
            (e.target as HTMLImageElement).src = "https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/avatars/user.png";
          }}
        />
      </div>
      
      <button
        onClick={handleClick}
        className="absolute bottom-0 right-0 p-1.5 sm:p-2 rounded-full bg-primary text-white shadow-lg hover:bg-primary/90 transition-colors"
        disabled={uploading}
        title="Alterar foto"
      >
        <Camera className="h-3 w-3 sm:h-4 sm:w-4" />
      </button>

      <Input
        ref={inputRef}
        type="file"
        accept=".jpg,.jpeg,.png,.gif,.webp"
        onChange={handleAvatarUpload}
        disabled={uploading}
        className="hidden"
      />
    </div>
  );
};

export default AvatarUpload;