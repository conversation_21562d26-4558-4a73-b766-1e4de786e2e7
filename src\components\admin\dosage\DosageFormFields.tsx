import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DosageTagSelector } from "./DosageTagSelector";
import { DosageFormData } from "@/types/dosage";

interface DosageFormFieldsProps {
  formData: DosageFormData;
  setFormData: (data: DosageFormData) => void;
  useCases: Array<{ id: string; name: string }>;
  medicationId: string;
}

export function DosageFormFields({ formData, setFormData, useCases, medicationId }: DosageFormFieldsProps) {
  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="name">Nome da Dosagem</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          placeholder="Ex: Dose padrão"
        />
      </div>

      <div>
        <Label htmlFor="age_group">Faixa Etária</Label>
        <Select
          value={formData.age_group}
          onValueChange={(value: "neonatal" | "pediatric" | "adult") => 
            setFormData({ ...formData, age_group: value })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Selecione a faixa etária" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="neonatal">Neonatal</SelectItem>
            <SelectItem value="pediatric">Pediátrica</SelectItem>
            <SelectItem value="adult">Adulto</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="use_case">Indicação de Uso</Label>
        <Select
          value={formData.use_case_id}
          onValueChange={(value) => setFormData({ ...formData, use_case_id: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Selecione uma indicação" />
          </SelectTrigger>
          <SelectContent>
            {useCases.map((useCase) => (
              <SelectItem key={useCase.id} value={useCase.id}>
                {useCase.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="summary">Resumo</Label>
        <Textarea
          id="summary"
          value={formData.summary}
          onChange={(e) => setFormData({ ...formData, summary: e.target.value })}
          placeholder="Breve descrição da dosagem"
          className="min-h-[100px] resize-y"
        />
      </div>

      <div>
        <Label htmlFor="dosage_template">Template da Dosagem</Label>
        <Input
          id="dosage_template"
          value={formData.dosage_template}
          onChange={(e) =>
            setFormData({ ...formData, dosage_template: e.target.value })
          }
          placeholder="Ex: ((peso)) mg/kg"
        />
      </div>

      <div>
        <Label>Tags da Dosagem</Label>
        <DosageTagSelector
          tags={formData.tags}
          onTagsChange={(tags) => setFormData({ ...formData, tags })}
          medicationId={medicationId}
        />
      </div>
    </div>
  );
}