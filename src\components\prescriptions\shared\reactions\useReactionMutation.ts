import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

export const useReactionMutation = (userId?: string) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ 
      prescriptionId, 
      type,
    }: { 
      prescriptionId: string;
      type: 'like' | 'dislike';
    }) => {
      if (!userId) throw new Error("User not authenticated");

      const { error } = await supabase.rpc('handle_prescription_reaction', {
        p_user_id: userId,
        p_prescription_id: prescriptionId,
        p_reaction_type: type
      });

      if (error) {
        console.error("Error processing reaction:", error);
        throw error;
      }

      return { success: true, type };
    },
    onSuccess: (result, variables) => {
      // Invalidar todas as queries relacionadas
      queryClient.invalidateQueries({
        queryKey: ["prescription-reactions"],
      });
      queryClient.invalidateQueries({
        queryKey: ["user-reaction"],
      });
      queryClient.invalidateQueries({
        queryKey: ["shared-prescriptions"],
      });

      toast({
        title: "Sucesso",
        description: `${variables.type === 'like' ? 'Like' : 'Dislike'} registrado!`,
      });
    },
    onError: (error) => {
      console.error("Error processing reaction:", error);
      toast({
        title: "Erro",
        description: "Não foi possível registrar sua reação. Por favor, tente novamente.",
        variant: "destructive",
      });
    },
  });
};