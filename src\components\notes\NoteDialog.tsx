import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Star, Trash2, <PERSON><PERSON>, Bookmark, Calendar } from "lucide-react";
import { Di<PERSON>, DialogContent } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Note } from "@/types/notes";
import { useToast } from "@/hooks/use-toast";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

interface NoteDialogProps {
  note: Note;
  isOpen: boolean;
  onClose: () => void;
  onEdit: () => void;
  onDelete: (noteId: string) => void;
  onToggleFavorite: (note: Note) => void;
}

export function NoteDialog({ 
  note, 
  isOpen,
  onClose,
  onEdit, 
  onDelete, 
  onToggleFavorite 
}: NoteDialogProps) {
  const { toast } = useToast();

  const handleCopyContent = () => {
    // Create a temporary div to handle HTML content
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = note.content;
    
    // Replace <p> and block elements with double newlines
    const text = tempDiv.innerHTML
      .replace(/<\/(p|div|h[1-6]|ul|ol|li|blockquote)>/g, '\n\n')
      .replace(/<br\s*\/?>/g, '\n')
      .replace(/<[^>]+>/g, '') // Remove remaining HTML tags
      .replace(/&nbsp;/g, ' ') // Replace &nbsp; with spaces
      .replace(/\n\s*\n/g, '\n\n') // Normalize multiple newlines
      .trim();
    
    navigator.clipboard.writeText(text).then(() => {
      toast({
        description: "Conteúdo copiado para a área de transferência!",
        duration: 2000,
      });
    }).catch(() => {
      toast({
        variant: "destructive",
        description: "Erro ao copiar o conteúdo.",
        duration: 2000,
      });
    });
  };

  const handleToggleFavorite = async () => {
    try {
      await onToggleFavorite(note);
      toast({
        description: note.is_favorite ? "Nota removida dos favoritos!" : "Nota adicionada aos favoritos!",
        duration: 2000,
      });
    } catch (error) {
      console.error('Error toggling favorite:', error);
      toast({
        variant: "destructive",
        description: "Erro ao atualizar favorito",
        duration: 2000,
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl h-[90vh] overflow-hidden flex flex-col p-0 rounded-xl">
        <div className="flex flex-col flex-1 min-h-0">
          {/* Header Section */}
          <div className="p-4 border-b bg-gradient-to-r from-primary-light to-white rounded-t-xl">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <span className="text-2xl bg-white rounded-full p-2 shadow-sm">📝</span>
                <div className="flex flex-col">
                  <h2 className="text-xl font-semibold text-gray-900 break-words">
                    {note.title || "Sem título"}
                  </h2>
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <Calendar className="h-4 w-4" />
                    {format(new Date(note.created_at), "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
                  </div>
                  <div className="flex items-center gap-2 mt-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-gray-500 hover:text-primary hover:bg-primary/10 px-2"
                      onClick={onEdit}
                      title="Editar nota"
                    >
                      <PencilLine className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-gray-500 hover:text-red-600 hover:bg-red-50 px-2"
                      onClick={() => onDelete(note.id)}
                      title="Excluir nota"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleToggleFavorite}
                      className={`text-gray-500 hover:text-yellow-500 hover:bg-yellow-50 px-2 ${
                        note.is_favorite ? "text-yellow-500" : ""
                      }`}
                      title={note.is_favorite ? "Remover dos favoritos" : "Adicionar aos favoritos"}
                    >
                      <Star 
                        className={`h-4 w-4 ${note.is_favorite ? "fill-current" : ""}`}
                      />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Tags Section */}
          {note.tags && note.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 p-4 border-b bg-gray-50">
              <Bookmark className="h-4 w-4 text-gray-400" />
              {note.tags.map((tag: string) => (
                <Badge 
                  key={tag} 
                  variant="secondary"
                  className="bg-white border shadow-sm text-primary hover:bg-primary-light transition-colors"
                >
                  {tag}
                </Badge>
              ))}
            </div>
          )}

          {/* Copy Button */}
          <div className="flex justify-center p-2 border-b">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyContent}
              className="text-gray-500 hover:text-gray-700"
            >
              <Copy className="h-4 w-4 mr-2" />
              Copiar texto
            </Button>
          </div>
          
          {/* Content Section */}
          <ScrollArea className="flex-1 min-h-0">
            <div 
              className="prose max-w-none p-6 break-words whitespace-pre-wrap"
              dangerouslySetInnerHTML={{ __html: note.content }}
              style={{ 
                overflowWrap: 'break-word', 
                wordWrap: 'break-word', 
                wordBreak: 'break-word',
                whiteSpace: 'pre-wrap'
              }}
            />
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
}