import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus } from "lucide-react";
import type { CategoryFormProps } from "./types";

export const CategoryForm = ({ 
  selectedType, 
  selectedParentId, 
  onAddCategory,
  onParentChange,
  getParentOptions 
}: CategoryFormProps) => {
  const [newCategoryName, setNewCategoryName] = useState("");

  return (
    <div>
      <Label>Nome da nova categoria</Label>
      <div className="flex gap-2 mt-1.5">
        <Input
          placeholder="Nome da nova categoria"
          value={newCategoryName}
          onChange={(e) => setNewCategoryName(e.target.value)}
        />
        {selectedType !== "specialty" && (
          <select
            className="border rounded p-2 min-w-[200px] hover:border-primary/50 transition-colors"
            onChange={(e) => onParentChange(e.target.value)}
            value={selectedParentId}
            required
          >
            <option value="">Selecione {selectedType === "theme" ? "a especialidade" : "o tema"}</option>
            {getParentOptions().map(cat => (
              <option key={cat.id} value={cat.id}>{cat.name}</option>
            ))}
          </select>
        )}
        <Button 
          onClick={() => {
            onAddCategory(newCategoryName);
            setNewCategoryName("");
          }}
          className="whitespace-nowrap hover:animate-scale transition-all"
        >
          <Plus className="h-4 w-4 mr-2" />
          Adicionar
        </Button>
      </div>
    </div>
  );
};