import { Button } from "../ui/button";

interface FilterButtonProps {
  id: string;
  name: string;
  isSelected: boolean;
  onToggle: (id: string) => void;
}

export const FilterButton = ({ id, name, isSelected, onToggle }: FilterButtonProps) => {
  return (
    <Button
      variant="ghost"
      className="w-full justify-start"
      onClick={() => onToggle(id)}
    >
      <input
        type="checkbox"
        checked={isSelected}
        onChange={() => {}}
        className="mr-2"
      />
      {name}
    </Button>
  );
};