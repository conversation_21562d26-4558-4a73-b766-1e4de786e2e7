import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

export const useThreadActions = () => {
  const { toast } = useToast();

  const renameThread = async (threadId: string, newTitle: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("User not authenticated");

      const { error } = await supabase
        .from('pedbook_chat_history')
        .update({
          metadata: {
            thread: {
              id: threadId,
              title: newTitle,
            },
          }
        })
        .eq('user_id', user.id)
        .contains('metadata', { thread: { id: threadId } });

      if (error) throw error;

      toast({
        title: "Thread renomeada",
        description: "O nome da conversa foi atualizado com sucesso.",
      });

      return true;
    } catch (error) {
      console.error('Error renaming thread:', error);
      toast({
        title: "Erro ao renomear",
        description: "Não foi possível renomear a conversa. Tente novamente.",
        variant: "destructive",
      });
      return false;
    }
  };

  const deleteThread = async (threadId: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("User not authenticated");

      const { error } = await supabase
        .from('pedbook_chat_history')
        .delete()
        .eq('user_id', user.id)
        .contains('metadata', { thread: { id: threadId } });

      if (error) throw error;

      toast({
        title: "Thread excluída",
        description: "A conversa foi excluída com sucesso.",
      });

      return true;
    } catch (error) {
      console.error('Error deleting thread:', error);
      toast({
        title: "Erro ao excluir",
        description: "Não foi possível excluir a conversa. Tente novamente.",
        variant: "destructive",
      });
      return false;
    }
  };

  return {
    renameThread,
    deleteThread,
  };
};