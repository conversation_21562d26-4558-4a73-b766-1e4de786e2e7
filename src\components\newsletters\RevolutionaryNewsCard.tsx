import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { format, formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  ExternalLink,
  Clock,
  Eye,
  Sparkles,
  MoreHorizontal
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { NewsItem } from '@/types/newsletter';
import { NewsDialog } from './NewsDialog';
import { cn } from '@/lib/utils';

interface RevolutionaryNewsCardProps {
  news: NewsItem;
  index: number;
  variant?: 'featured' | 'standard' | 'compact';
}

export const RevolutionaryNewsCard: React.FC<RevolutionaryNewsCardProps> = ({
  news,
  index,
  variant = 'standard'
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  const formatDate = (dateString?: string | null) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { locale: ptBR, addSuffix: true });
    } catch (error) {
      return '';
    }
  };

  const formatFullDate = (dateString?: string | null) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return format(date, "d 'de' MMMM, yyyy 'às' HH:mm", { locale: ptBR });
    } catch (error) {
      return '';
    }
  };

  const handleOpenLink = () => {
    if (news.link) {
      window.open(news.link, '_blank', 'noopener,noreferrer');
    }
  };

  const handleViewDetails = () => {
    setDialogOpen(true);
  };

  const getCardHeight = () => {
    switch (variant) {
      case 'featured': return 'h-[400px]';
      case 'compact': return 'h-[200px]';
      default: return 'h-[320px]';
    }
  };

  const getImageHeight = () => {
    switch (variant) {
      case 'featured': return 'h-48';
      case 'compact': return 'h-24';
      default: return 'h-40';
    }
  };

  const renderCategories = () => {
    if (!news.category) return null;
    const categories = news.category.split(',').map(c => c.trim()).slice(0, 2);

    return (
      <div className="flex flex-wrap gap-1">
        {categories.map((category, idx) => (
          <Badge
            key={idx}
            variant="secondary"
            className={cn(
              "text-[10px] px-2 py-0.5 font-medium transition-all duration-200",
              variant === 'featured'
                ? "bg-white/90 text-gray-700 hover:bg-white"
                : "bg-blue-50 text-blue-700 hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-300"
            )}
          >
            {category}
          </Badge>
        ))}
      </div>
    );
  };

  if (variant === 'featured') {
    return (
      <>
        <motion.div
          ref={cardRef}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
          className="col-span-full md:col-span-2 lg:col-span-3"
          onHoverStart={() => setIsHovered(true)}
          onHoverEnd={() => setIsHovered(false)}
        >
          <Card className="relative overflow-hidden h-[400px] group cursor-pointer" onClick={handleViewDetails}>
            {/* Background Image */}
            {news.image_url && (
              <div className="absolute inset-0">
                <img
                  src={news.image_url}
                  alt={news.title}
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-black/30" />
              </div>
            )}

            {/* Content Overlay */}
            <div className="relative h-full flex flex-col justify-end p-6 text-white">
              <motion.div
                animate={{ y: isHovered ? -10 : 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className="flex items-center gap-2 mb-3">
                  <div className="flex items-center gap-2 bg-yellow-500/20 backdrop-blur-sm px-3 py-1 rounded-full border border-yellow-400/30">
                    <Sparkles className="w-4 h-4 text-yellow-300" />
                    <span className="text-sm font-medium text-yellow-300">Destaque</span>
                  </div>
                  {renderCategories()}
                </div>

                <h2 className="text-2xl md:text-3xl font-bold mb-3 leading-tight text-white drop-shadow-lg">
                  {news.title}
                </h2>

                <p className="text-gray-100 mb-4 line-clamp-2 text-lg drop-shadow-md">
                  {news.summary}
                </p>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4 text-sm text-gray-200 drop-shadow-sm">
                    {news.source && <span className="font-medium bg-black/30 px-2 py-1 rounded">{news.source}</span>}
                    <div className="flex items-center gap-1 bg-black/30 px-2 py-1 rounded">
                      <Clock className="w-3 h-3" />
                      <span>{formatDate(news.pub_date)}</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white hover:bg-white/20 backdrop-blur-sm border border-white/20"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleViewDetails();
                      }}
                    >
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>

                    <Button
                      className="bg-white/90 text-black hover:bg-white backdrop-blur-sm shadow-lg"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleOpenLink();
                      }}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Ler Artigo
                    </Button>
                  </div>
                </div>
              </motion.div>
            </div>
          </Card>
        </motion.div>

        <NewsDialog
          news={news}
          open={dialogOpen}
          onOpenChange={setDialogOpen}
        />
      </>
    );
  }

  return (
    <>
      <motion.div
        ref={cardRef}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: index * 0.05 }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        className={variant === 'compact' ? 'col-span-1' : 'col-span-1'}
      >
        <Card className={cn(
          "overflow-hidden group cursor-pointer transition-all duration-300",
          getCardHeight(),
          isHovered && "shadow-xl shadow-blue-500/10 -translate-y-1"
        )} onClick={handleViewDetails}>
          {/* Image Section */}
          {news.image_url && (
            <div className={cn("relative overflow-hidden", getImageHeight())}>
              <img
                src={news.image_url}
                alt={news.title}
                className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
              />



              {/* Categories Overlay */}
              <div className="absolute top-2 right-2">
                {renderCategories()}
              </div>

              {/* Gradient Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </div>
          )}

          {/* Content Section */}
          <div className="p-4 flex flex-col h-full">
            <div className="flex-1">
              {/* Meta Info */}
              <div className="flex items-center justify-between mb-2 text-xs text-gray-500 dark:text-gray-400">
                <div className="flex items-center gap-2">
                  {news.source && <span className="font-medium">{news.source}</span>}
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    <span>{formatDate(news.pub_date)}</span>
                  </div>
                </div>
              </div>

              {/* Title */}
              <h3 className={cn(
                "font-bold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2 leading-tight",
                variant === 'compact' ? "text-sm" : "text-base"
              )}>
                {news.title}
              </h3>

              {/* Summary */}
              {variant !== 'compact' && (
                <p className="text-gray-600 dark:text-gray-300 text-sm line-clamp-2 mb-3">
                  {news.summary}
                </p>
              )}

              {/* Categories */}
              {variant !== 'compact' && !news.image_url && renderCategories()}
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-between pt-3 border-t border-gray-100 dark:border-gray-800">
              <Button
                variant="ghost"
                size="sm"
                className="text-xs"
                onClick={(e) => {
                  e.stopPropagation();
                  handleViewDetails();
                }}
              >
                <MoreHorizontal className="w-4 h-4 mr-1" />
                Ver Detalhes
              </Button>

              <Button
                size="sm"
                className="bg-blue-500 hover:bg-blue-600 text-white"
                onClick={(e) => {
                  e.stopPropagation();
                  handleOpenLink();
                }}
              >
                <ExternalLink className="w-3 h-3 mr-1" />
                Ler
              </Button>
            </div>
          </div>
        </Card>
      </motion.div>

      <NewsDialog
        news={news}
        open={dialogOpen}
        onOpenChange={setDialogOpen}
      />
    </>
  );
};
