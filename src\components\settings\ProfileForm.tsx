
import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { User, Lock, Settings } from "lucide-react";
import PersonalInfoForm from "./PersonalInfoForm";
import PasswordForm from "./PasswordForm";

interface ProfileFormProps {
  profile: any;
  setProfile: (profile: any) => void;
  handleProfileUpdate: (e: React.FormEvent<HTMLFormElement>) => void;
}

const ProfileForm = ({ profile, setProfile, handleProfileUpdate }: ProfileFormProps) => {
  return (
    <Tabs defaultValue="personal" className="space-y-6">
      {/* Tabs com design limpo e proporcional */}
      <TabsList className="grid w-full grid-cols-2 gap-1 bg-muted p-1 rounded-lg h-auto">
        <TabsTrigger
          value="personal"
          className="flex items-center justify-center gap-2 h-10 sm:h-11 text-sm sm:text-base font-medium data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm data-[state=inactive]:text-muted-foreground rounded-md transition-all"
        >
          <User className="h-4 w-4" />
          <span className="hidden sm:inline">Informações</span>
          <span className="sm:hidden">Info</span>
        </TabsTrigger>
        <TabsTrigger
          value="password"
          className="flex items-center justify-center gap-2 h-10 sm:h-11 text-sm sm:text-base font-medium data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm data-[state=inactive]:text-muted-foreground rounded-md transition-all"
        >
          <Lock className="h-4 w-4" />
          <span className="hidden sm:inline">Senha</span>
          <span className="sm:hidden">Senha</span>
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="personal">
        <PersonalInfoForm
          profile={profile}
          setProfile={setProfile}
          handleProfileUpdate={handleProfileUpdate}
        />
      </TabsContent>
      
      <TabsContent value="password">
        <PasswordForm />
      </TabsContent>
    </Tabs>
  );
};

export default ProfileForm;
