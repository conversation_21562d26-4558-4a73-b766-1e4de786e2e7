import React, { useState, useEffect } from "react";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { useNavigate } from "react-router-dom";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { FolderList } from "@/components/notes/FolderList";
import { NotesList } from "@/components/notes/NotesList";
import { CreateNoteButton } from "@/components/notes/CreateNoteButton";
import { NotesSearch } from "@/components/notes/NotesSearch";
import { Folder } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useFolders } from "@/hooks/useFolders";

const Notes: React.FC = () => {
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const navigate = useNavigate();
  const { folders, isLoading } = useFolders();
  const { toast } = useToast();

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        navigate("/");
        return;
      }
    };

    checkAuth();

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      if (!session) {
        navigate("/");
        return;
      }
    });

    return () => subscription.unsubscribe();
  }, [navigate]);

  useEffect(() => {
    if (!isLoading && folders && folders.length === 0) {
      toast({
        title: "Bem-vindo ao Sistema de Anotações!",
        description: (
          <div className="space-y-4 w-full max-w-[calc(100vw-2rem)] sm:max-w-lg mx-auto bg-white/95 p-4 sm:p-6 rounded-lg shadow-lg backdrop-blur-sm sm:max-h-[90vh] overflow-y-auto">
            <h2 className="text-lg sm:text-xl font-semibold text-primary text-center">
              Como funciona?
            </h2>
            <p className="text-sm sm:text-base text-gray-700 font-medium text-center">
              Organize suas anotações em pastas para melhor organização:
            </p>
            <ul className="list-none space-y-2 sm:space-y-3 mt-2 sm:mt-4 text-sm sm:text-base">
              <li className="flex items-center gap-2 bg-accent-blue p-2 rounded-md">
                <span className="text-primary">📁</span>
                <span>Crie pastas para organizar suas anotações por temas.</span>
              </li>
              <li className="flex items-center gap-2 bg-accent-purple p-2 rounded-md">
                <span className="text-primary">📝</span>
                <span>Adicione notas em cada pasta com conteúdo rico.</span>
              </li>
              <li className="flex items-center gap-2 bg-accent-green p-2 rounded-md">
                <span className="text-primary">🔍</span>
                <span>Pesquise facilmente em todas as suas anotações.</span>
              </li>
            </ul>
            <p className="text-xs sm:text-sm text-gray-600 text-center mt-2 sm:mt-4 italic">
              Vamos começar criando sua primeira pasta?
            </p>
            <div className="flex justify-center mt-2 sm:mt-4">
              <button 
                onClick={() => {
                  toast({
                    duration: 2000,
                    description: "Agora você pode criar sua primeira pasta!"
                  });
                }}
                className="bg-primary hover:bg-primary/90 text-white px-6 sm:px-8 py-2 rounded-full font-medium transition-all"
              >
                Começar
              </button>
            </div>
          </div>
        ),
        duration: 0,
        className: "fixed inset-0 flex items-center justify-center bg-black/20 backdrop-blur-sm max-w-none w-screen h-screen m-0 p-4",
      });
    }
  }, [folders, isLoading, toast]);

  return (
    <div className="min-h-screen flex flex-col bg-white">
      <HelmetWrapper>
        <title>PedBook | Minhas Anotações</title>
        <meta 
          name="description" 
          content="Sistema de anotações para profissionais de saúde - organize e acesse suas anotações de forma simples e eficiente." 
        />
      </HelmetWrapper>

      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        {!selectedFolderId ? (
          <>
            <div className="space-y-6">
              <button 
                onClick={() => navigate('/')}
                className="text-sm text-primary hover:underline"
              >
                ← Ir para o menu inicial
              </button>
              
              <div className="flex items-center gap-2">
                <Folder className="h-6 w-6 text-primary" />
                <h1 className="text-2xl font-bold text-gray-900">Minhas Pastas</h1>
              </div>
            </div>
            <div className="mt-6">
              <FolderList onFolderSelect={setSelectedFolderId} />
            </div>
          </>
        ) : (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <button 
                onClick={() => setSelectedFolderId(null)}
                className="text-sm text-primary hover:underline"
              >
                ← Voltar para pastas
              </button>
              <CreateNoteButton folderId={selectedFolderId} />
            </div>
            <NotesSearch onSearch={setSearchTerm} />
            <NotesList 
              folderId={selectedFolderId} 
              searchTerm={searchTerm}
            />
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
};

export default Notes;
