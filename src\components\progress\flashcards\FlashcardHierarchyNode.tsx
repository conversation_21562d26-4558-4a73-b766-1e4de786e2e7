import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { ChevronRight, ChevronDown } from "lucide-react";

interface CategoryNode {
  id: string;
  name: string;
  nextReview?: Date | null;
  cardsCount?: number;
  children?: CategoryNode[];
}

interface FlashcardHierarchyNodeProps {
  node: CategoryNode;
  level?: number;
  isExpanded: boolean;
  onToggle: (nodeId: string) => void;
  showReviewDate?: boolean;
}

export const FlashcardHierarchyNode = ({
  node,
  level = 0,
  isExpanded,
  onToggle,
  showReviewDate = false
}: FlashcardHierarchyNodeProps) => {
  const hasChildren = node.children && node.children.length > 0;

  return (
    <div className="space-y-2">
      <div className={`flex flex-col gap-2 p-2 hover:bg-secondary/50 rounded-lg ${level > 0 ? 'ml-6' : ''}`}>
        <div className="flex items-center gap-2">
          {hasChildren && (
            <Button
              variant="ghost"
              size="sm"
              className="p-1 h-6 w-6"
              onClick={() => onToggle(node.id)}
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          )}
          <span className="flex-1 font-medium">{node.name}</span>
          <span className="text-sm text-muted-foreground">
            {showReviewDate && node.nextReview ? (
              `Próxima revisão: ${format(node.nextReview, 'dd/MM/yyyy')}`
            ) : (
              node.cardsCount !== undefined && `${node.cardsCount} cards`
            )}
          </span>
        </div>
      </div>

      {isExpanded && hasChildren && (
        <div className="space-y-2">
          {node.children!.map(child => (
            <FlashcardHierarchyNode
              key={child.id}
              node={child}
              level={level + 1}
              isExpanded={isExpanded}
              onToggle={onToggle}
              showReviewDate={showReviewDate}
            />
          ))}
        </div>
      )}
    </div>
  );
};