
import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, Lock, ArrowLeft, CheckCircle } from "lucide-react";
import { useNotification } from "@/context/NotificationContext";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

const formSchema = z.object({
  password: z.string().min(6, "A senha deve ter pelo menos 6 caracteres"),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "As senhas não coincidem",
  path: ["confirmPassword"],
});

const ResetPassword = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [tokenError, setTokenError] = useState(false);
  const [validatedToken, setValidatedToken] = useState<string | null>(null);
  const [resetSuccess, setResetSuccess] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { showNotification } = useNotification();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  useEffect(() => {
    const validateRecovery = async () => {
      if (validatedToken) return;



      try {
        // Verificar se o usuário está logado com método de recuperação
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (session?.user) {
          // Verificar se foi logado via recuperação
          const userToken = session.access_token;

          // Decodificar o JWT para verificar o método de autenticação
          try {
            const tokenParts = userToken.split('.');
            const payload = JSON.parse(atob(tokenParts[1]));
            const amr = payload.amr || [];
            const hasRecoveryMethod = amr.some((method: any) => method.method === 'recovery');

            if (hasRecoveryMethod) {
              // Usuário logado via recuperação - manter sessão ativa
              sessionStorage.setItem('reset_user_id', session.user.id);
              sessionStorage.setItem('reset_token', userToken);
              sessionStorage.setItem('recovery_method', 'session_active');

              setValidatedToken("recovery_session_active");
              return;
            }
          } catch (jwtError) {
            // Erro ao decodificar JWT - continuar com outros métodos
          }
        }

        // Verificar parâmetros da URL
        const urlParams = new URLSearchParams(location.search);
        const urlToken = urlParams.get("token");
        const urlType = urlParams.get("type");

        if (urlToken && urlType === "recovery") {
          try {
            // Validar o token da URL
            const { data: userData, error: userError } = await supabase.auth.getUser(urlToken);

            if (userError) {
              setTokenError(true);
              return;
            }

            if (userData?.user) {
              // Garantir logout se estiver logado
              await supabase.auth.signOut();

              sessionStorage.setItem('reset_user_id', userData.user.id);
              sessionStorage.setItem('reset_token', urlToken);

              setValidatedToken("url_recovery");
              return;
            }
          } catch (error) {
            setTokenError(true);
            return;
          }
        }

        // Verificar dados armazenados de recuperação anterior
        const recoveryDetected = sessionStorage.getItem('recovery_login_detected');
        const recoveryUserId = sessionStorage.getItem('recovery_user_id');

        if (recoveryDetected && recoveryUserId) {
          sessionStorage.removeItem('recovery_login_detected');
          sessionStorage.setItem('reset_user_id', recoveryUserId);

          setValidatedToken("stored_recovery");
          return;
        }

        // Se chegou até aqui, não há recuperação válida
        setTokenError(true);

      } catch (error) {
        setTokenError(true);
      }
    };

    // Executar com delay para garantir que a página carregou
    setTimeout(validateRecovery, 100);
  }, [location.search, validatedToken]);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {

    if (!validatedToken) {
      showNotification({
        title: "Erro ao atualizar senha",
        description: "Token de redefinição inválido ou expirado.",
        type: "error",
        buttonText: "Voltar"
      });
      return;
    }

    try {
      setIsLoading(true);

      const userId = sessionStorage.getItem('reset_user_id');

      if (!userId) {
        throw new Error("ID do usuário não encontrado");
      }

      // Verificar se temos uma sessão ativa
      const { data: { session } } = await supabase.auth.getSession();

      if (session?.user?.id === userId) {
        // Usar updateUser diretamente com a sessão ativa
        const { error: updateError } = await supabase.auth.updateUser({
          password: values.password
        });

        if (updateError) {
          throw new Error(`Erro ao atualizar senha: ${updateError.message}`);
        }

        // Fazer logout após atualizar
        await supabase.auth.signOut();

      } else {
        throw new Error("Sessão de recuperação não encontrada. Solicite um novo link de recuperação.");
      }

      // Limpar todos os dados temporários de recuperação
      sessionStorage.removeItem('reset_user_id');
      sessionStorage.removeItem('reset_token');
      sessionStorage.removeItem('recovery_token');
      sessionStorage.removeItem('recovery_type');
      sessionStorage.removeItem('recovery_intercepted');

      showNotification({
        title: "Senha atualizada!",
        description: "Sua senha foi atualizada com sucesso. Agora você pode fazer login.",
        type: "success",
        buttonText: "Ir para o login",
        onButtonClick: () => navigate("/")
      });

      setResetSuccess(true);
    } catch (error: any) {
      showNotification({
        title: "Erro ao atualizar senha",
        description: error.message || "Ocorreu um erro ao atualizar sua senha. Tente novamente.",
        type: "error",
        buttonText: "Tentar novamente"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const Header = () => (
    <header className="w-full bg-white border-b">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            onClick={() => navigate("/")}
            className="flex items-center gap-2 text-primary hover:text-primary/80"
          >
            <ArrowLeft className="h-4 w-4" />
            Voltar
          </Button>
          <div className="flex items-center">
            <Lock className="h-6 w-6 text-primary mr-2" />
            <span className="font-bold text-lg">PedBook</span>
          </div>
        </div>
      </div>
    </header>
  );

  const Footer = () => (
    <footer className="w-full bg-white border-t mt-auto">
      <div className="container mx-auto px-4 py-6">
        <p className="text-center text-sm text-gray-500">
          © 2025 PedBook. Todos os direitos reservados.
        </p>
      </div>
    </footer>
  );

  if (resetSuccess) {
    return (
      <div className="min-h-screen flex flex-col bg-gray-50">
        <Header />
        <div className="container max-w-md mx-auto px-4 py-12 flex-grow flex items-center justify-center">
          <Card className="w-full shadow-lg border-primary/10">
            <CardContent className="pt-6 pb-8 px-6 flex flex-col items-center text-center">
              <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-6">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h2 className="text-2xl font-bold mb-2">Senha Atualizada!</h2>
              <p className="text-gray-600 mb-6">
                Sua senha foi alterada com sucesso. Você será redirecionado para a página de login em instantes.
              </p>
              <Button
                variant="duolingo"
                className="w-full"
                onClick={() => navigate("/")}
              >
                Ir para o login
              </Button>
            </CardContent>
          </Card>
        </div>
        <Footer />
      </div>
    );
  }

  if (tokenError) {
    return (
      <div className="min-h-screen flex flex-col bg-gray-50">
        <Header />
        <div className="container max-w-md mx-auto px-4 py-12 flex-grow flex items-center">
          <Card className="w-full shadow-lg border-red-200">
            <CardHeader className="pb-4">
              <div className="flex justify-center mb-4">
                <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
                  <AlertCircle className="h-6 w-6 text-red-600" />
                </div>
              </div>
              <CardTitle className="text-center text-xl">Link inválido ou expirado</CardTitle>
            </CardHeader>
            <CardContent className="pt-2">
              <p className="text-center text-gray-600 mb-6">
                O link de redefinição de senha é inválido ou expirou.
                Por favor, solicite uma nova redefinição de senha.
              </p>
              <Button
                variant="duolingo"
                onClick={() => navigate("/")}
                className="w-full"
              >
                Voltar para o início
              </Button>
            </CardContent>
          </Card>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Header />
      <div className="container max-w-md mx-auto px-4 py-12 flex-grow flex items-center">
        <Card className="w-full shadow-lg border-primary/10">
          <CardHeader className="space-y-1">
            <div className="flex justify-center mb-4">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                <Lock className="h-6 w-6 text-primary" />
              </div>
            </div>
            <CardTitle className="text-center text-xl">Redefinir Senha</CardTitle>
            <CardDescription className="text-center">Crie uma nova senha segura para sua conta</CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nova Senha</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="Digite sua nova senha"
                          className="bg-gray-50 border-gray-200 focus:border-primary"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirmar Nova Senha</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="Confirme sua nova senha"
                          className="bg-gray-50 border-gray-200 focus:border-primary"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button
                  type="submit"
                  variant="duolingo"
                  className="w-full"
                  disabled={isLoading}
                >
                  {isLoading ? "Atualizando..." : "Atualizar Senha"}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
      <Footer />
    </div>
  );
};

export default ResetPassword;
