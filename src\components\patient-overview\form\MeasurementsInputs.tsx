import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Scale, Ruler, Brain, Baby } from "lucide-react";

interface MeasurementsInputsProps {
  defaultValues?: {
    weight?: number;
    birthWeight?: number;
    height?: number;
    headCircumference?: number;
  };
}

export function MeasurementsInputs({ defaultValues }: MeasurementsInputsProps) {
  return (
    <div className="grid grid-cols-2 gap-3">
      {/* Peso ao Nascer */}
      <div className="space-y-2">
        <Label htmlFor="birthWeight" className="flex items-center gap-1.5 text-xs font-medium text-gray-700 dark:text-gray-300">
          <Baby className="h-3 w-3 text-blue-600" />
          Peso Nasc.
        </Label>
        <div className="relative">
          <Input
            id="birthWeight"
            name="birthWeight"
            type="number"
            step="1"
            min={500}
            max={6000}
            defaultValue={defaultValues?.birthWeight}
            required
            placeholder="3200"
            className="bg-white dark:bg-slate-800 h-9 text-sm pr-8"
          />
          <span className="absolute right-2 top-1/2 -translate-y-1/2 text-xs text-gray-500">g</span>
        </div>
      </div>

      {/* Peso Atual */}
      <div className="space-y-2">
        <Label htmlFor="weight" className="flex items-center gap-1.5 text-xs font-medium text-gray-700 dark:text-gray-300">
          <Scale className="h-3 w-3 text-green-600" />
          Peso Atual
        </Label>
        <div className="relative">
          <Input
            id="weight"
            name="weight"
            type="number"
            step="1"
            min={500}
            max={100000}
            defaultValue={defaultValues?.weight ? defaultValues.weight * 1000 : undefined}
            required
            placeholder="5200"
            className="bg-white dark:bg-slate-800 h-9 text-sm pr-8"
          />
          <span className="absolute right-2 top-1/2 -translate-y-1/2 text-xs text-gray-500">g</span>
        </div>
      </div>

      {/* Altura */}
      <div className="space-y-2">
        <Label htmlFor="height" className="flex items-center gap-1.5 text-xs font-medium text-gray-700 dark:text-gray-300">
          <Ruler className="h-3 w-3 text-purple-600" />
          Altura
        </Label>
        <div className="relative">
          <Input
            id="height"
            name="height"
            type="number"
            step="0.1"
            min={20}
            max={250}
            defaultValue={defaultValues?.height}
            required
            placeholder="65.5"
            className="bg-white dark:bg-slate-800 h-9 text-sm pr-10"
          />
          <span className="absolute right-2 top-1/2 -translate-y-1/2 text-xs text-gray-500">cm</span>
        </div>
      </div>

      {/* Perímetro Cefálico */}
      <div className="space-y-2">
        <Label htmlFor="headCircumference" className="flex items-center gap-1.5 text-xs font-medium text-gray-700 dark:text-gray-300">
          <Brain className="h-3 w-3 text-orange-600" />
          PC
        </Label>
        <div className="relative">
          <Input
            id="headCircumference"
            name="headCircumference"
            type="number"
            step="0.1"
            min={20}
            max={80}
            defaultValue={defaultValues?.headCircumference}
            required
            placeholder="42.0"
            className="bg-white dark:bg-slate-800 h-9 text-sm pr-10"
          />
          <span className="absolute right-2 top-1/2 -translate-y-1/2 text-xs text-gray-500">cm</span>
        </div>
      </div>
    </div>
  );
}