import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface Tag {
  id: string;
  name: string;
  user_id: string;
  created_at: string;
}

export const useTags = () => {
  const { data: tags, isLoading } = useQuery({
    queryKey: ['notes-tags'],
    queryFn: async () => {
      const { data: userData } = await supabase.auth.getUser();
      
      const { data, error } = await supabase
        .from('pedbook_notes_tags')
        .select('*')
        .eq('user_id', userData.user.id)
        .order('name');

      if (error) {
        toast.error('Erro ao carregar tags');
        throw error;
      }

      return data as Tag[];
    },
  });

  return {
    tags,
    isLoading,
  };
};