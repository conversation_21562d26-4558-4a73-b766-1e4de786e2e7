import React, { useEffect, useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

interface LoadingIndicatorProps {
  className?: string;
}

export const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({ className }) => {
  const { isLoading } = useAuth();
  const [visible, setVisible] = useState(false);
  
  useEffect(() => {
    // Só mostrar o indicador se o carregamento demorar mais de 300ms
    // Isso evita flashes rápidos do indicador para carregamentos rápidos
    let timer: NodeJS.Timeout;
    
    if (isLoading) {
      timer = setTimeout(() => {
        setVisible(true);
      }, 300);
    } else {
      setVisible(false);
    }
    
    return () => {
      clearTimeout(timer);
    };
  }, [isLoading]);
  
  if (!visible) return null;
  
  return (
    <div className={cn(
      "fixed top-0 left-0 right-0 z-50 flex items-center justify-center bg-primary/10 dark:bg-primary/20 h-1",
      className
    )}>
      <div className="h-1 bg-primary animate-pulse w-full max-w-md"></div>
      <Loader2 className="animate-spin text-primary absolute top-2 right-2 h-4 w-4" />
    </div>
  );
};
