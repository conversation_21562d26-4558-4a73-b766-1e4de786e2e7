
// Adicione/atualize o hook useQuestionMetadata para utilizar o isReady do useDomain
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import type { QuestionMetadata } from '@/types/question';
import { useDomain } from '@/hooks/useDomain';

export const useQuestionMetadata = () => {
  return useQuery({
    queryKey: ['pediatric-question-metadata'],
    queryFn: async () => {

      try {
        // Get the metadata specifically for pediatric questions
        const { data, error } = await supabase.rpc('get_pediatric_questions_metadata');

        if (error) {
          throw error;
        }

        if (!data) {
          return {
            specialties: [],
            themes: [],
            focuses: [],
            locations: [],
            years: []
          };
        }

        return data as QuestionMetadata;
      } catch (error) {
        console.error('❌ [useQuestionMetadata] Error in queryFn:', error);
        throw error;
      }
    },
    enabled: true, // Sempre habilitado para pediatria
    staleTime: 60 * 60 * 1000, // 1 hour cache
    cacheTime: 2 * 60 * 60 * 1000, // 2 hours cache time
    refetchOnWindowFocus: false, // Não refetch ao focar na janela
    refetchOnMount: false, // Não refetch ao montar se já tem dados em cache
    refetchOnReconnect: false, // Não refetch ao reconectar
    refetchInterval: false, // Não refetch automaticamente
    refetchIntervalInBackground: false, // Não refetch em background
    retry: 1, // Apenas 1 tentativa em caso de erro
    retryOnMount: false // Não retry ao montar
  });
};
