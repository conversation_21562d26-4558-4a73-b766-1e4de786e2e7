
import React from 'react';
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Grip, Plus, Trash2, ArrowUp, ArrowDown } from "lucide-react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

interface FAQBlockProps {
  id: string;
  type: 'title' | 'subtitle';
  content: string;
  textContent: string;
  onUpdate: (id: string, content: string, field: 'content' | 'textContent') => void;
  onDelete: (id: string) => void;
  onAddSubsection?: () => void;
  onMoveUp?: () => void;
  onMoveDown?: () => void;
  isFirst?: boolean;
  isLast?: boolean;
}

export function FAQBlock({
  id,
  type,
  content,
  textContent,
  onUpdate,
  onDelete,
  onAddSubsection,
  onMoveUp,
  onMoveDown,
  isFirst,
  isLast
}: FAQBlockProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div ref={setNodeRef} style={style} className="space-y-2">
      <Card className="p-4">
        <div className="flex items-start gap-4">
          <button
            className="cursor-grab active:cursor-grabbing p-1 hover:bg-gray-100 rounded"
            {...attributes}
            {...listeners}
          >
            <Grip className="h-4 w-4 text-gray-400" />
          </button>
          
          <div className="flex-1 space-y-4">
            <Textarea
              value={content}
              onChange={(e) => onUpdate(id, e.target.value, 'content')}
              placeholder={type === 'title' ? 'Digite o título...' : 'Digite o subtítulo...'}
              className="resize-none"
            />
            
            <Textarea
              value={textContent}
              onChange={(e) => onUpdate(id, e.target.value, 'textContent')}
              placeholder="Digite o conteúdo..."
              className="resize-none min-h-[100px]"
            />
          </div>

          <div className="flex flex-col gap-2">
            {!isFirst && (
              <Button
                onClick={onMoveUp}
                variant="ghost"
                size="icon"
                className="h-8 w-8"
              >
                <ArrowUp className="h-4 w-4" />
              </Button>
            )}
            
            {!isLast && (
              <Button
                onClick={onMoveDown}
                variant="ghost"
                size="icon"
                className="h-8 w-8"
              >
                <ArrowDown className="h-4 w-4" />
              </Button>
            )}

            {type === 'title' && (
              <Button
                onClick={onAddSubsection}
                variant="ghost"
                size="icon"
                className="h-8 w-8"
              >
                <Plus className="h-4 w-4" />
              </Button>
            )}

            <Button
              onClick={() => onDelete(id)}
              variant="destructive"
              size="icon"
              className="h-8 w-8"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}
