import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON><PERSON>, Mail, Heart, Stethoscope } from "lucide-react";

interface EnvelopeCardProps {
  stage: 'envelope-arrival' | 'envelope-opening';
  onEnvelopeClick: () => void;
  isAudioEnabled: boolean;
}

export const EnvelopeCard: React.FC<EnvelopeCardProps> = ({
  stage,
  onEnvelopeClick,
  isAudioEnabled
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [showClickHint, setShowClickHint] = useState(false);
  const [isOpening, setIsOpening] = useState(false);

  useEffect(() => {
    if (stage === 'envelope-opening') {
      const timer = setTimeout(() => {
        setShowClickHint(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [stage]);

  // Efeito sonoro (simulado)
  const playSound = (soundType: 'arrival' | 'hover' | 'click') => {
    if (!isAudioEnabled) return;
    // Sons são gerenciados pelo SoundManager
  };

  useEffect(() => {
    if (stage === 'envelope-arrival') {
      playSound('arrival');
    }
  }, [stage]);

  const handleClick = () => {
    if (isOpening) return;
    setIsOpening(true);
    playSound('click');

    // Não chamar callback automaticamente - usuário deve ler a carta e clicar no botão
  };

  return (
    <div className="flex items-center justify-center h-full relative">
      {/* Partículas sutis ao redor do envelope */}
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: 8 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-blue-200/60 rounded-full"
            style={{
              left: `${30 + Math.random() * 40}%`,
              top: `${30 + Math.random() * 40}%`,
            }}
            animate={{
              scale: [0, 1, 0],
              opacity: [0, 0.6, 0],
              y: [0, -20, -40],
              x: [0, (Math.random() - 0.5) * 20],
            }}
            transition={{
              duration: 4 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 4,
              ease: "easeOut"
            }}
          />
        ))}
      </div>

      {/* Envelope Principal */}
      <motion.div
        className="relative cursor-pointer"
        initial={{ 
          y: -200, 
          x: -100, 
          rotate: -15, 
          scale: 0.5, 
          opacity: 0 
        }}
        animate={stage === 'envelope-arrival' ? {
          y: 0,
          x: 0,
          rotate: 0,
          scale: 1,
          opacity: 1,
        } : {
          y: 0,
          x: 0,
          rotate: 0,
          scale: 1.1,
          opacity: 1,
        }}
        transition={{
          type: "spring",
          stiffness: 100,
          damping: 15,
          duration: 2,
        }}
        whileHover={{
          scale: stage === 'envelope-opening' ? 1.15 : 1.05,
          rotate: stage === 'envelope-opening' ? [0, -2, 2, 0] : 0,
        }}
        onHoverStart={() => {
          setIsHovered(true);
          playSound('hover');
        }}
        onHoverEnd={() => setIsHovered(false)}
        onClick={() => {
          if (stage === 'envelope-opening') {
            handleClick();
          }
        }}
      >
        {/* Brilho ao redor do envelope */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-blue-400/30 via-purple-400/30 to-pink-400/30 rounded-3xl blur-xl"
          animate={{
            scale: isHovered ? 1.3 : 1.1,
            opacity: isHovered ? 0.8 : 0.5,
          }}
          transition={{ duration: 0.3 }}
        />

        {/* Envelope Retangular Realista */}
        <div className="relative w-[480px] h-[320px] max-w-[90vw] max-h-[60vh] perspective-1000">
          {/* Envelope Base - Retangular como carta real */}
          <motion.div
            className="relative w-full h-full bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 shadow-2xl rounded-lg overflow-hidden"
            animate={{
              rotateY: isOpening ? 8 : 0,
              scale: isHovered ? 1.02 : 1,
            }}
            transition={{ duration: 0.5 }}
          >
            {/* Bordas do envelope */}
            <div className="absolute inset-0 border-2 border-blue-300/30 rounded-lg" />

            {/* Brilhos e reflexos no envelope */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-blue-700/20 rounded-lg" />

            {/* Linhas decorativas do envelope */}
            <div className="absolute top-4 left-4 right-4 h-px bg-blue-300/40" />
            <div className="absolute bottom-4 left-4 right-4 h-px bg-blue-300/40" />
            <div className="absolute top-4 bottom-4 left-4 w-px bg-blue-300/40" />
            <div className="absolute top-4 bottom-4 right-4 w-px bg-blue-300/40" />

            {/* Padrão de envelope no centro */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-32 h-32 border-2 border-blue-300/30 rounded-full flex items-center justify-center">
                <Mail className="h-16 w-16 text-blue-200/60" />
              </div>
            </div>

            {/* Brilho superior */}
            <div className="absolute top-0 left-0 right-0 h-20 bg-gradient-to-b from-white/30 to-transparent rounded-t-lg" />
          </motion.div>

          {/* Aba superior do envelope que vai abrir */}
          <motion.div
            className="absolute top-0 left-0 w-full h-40 bg-gradient-to-br from-blue-300 to-blue-400 shadow-lg origin-top rounded-t-lg"
            style={{
              clipPath: 'polygon(0 0, 100% 0, 85% 70%, 50% 100%, 15% 70%)'
            }}
            animate={{
              rotateX: isOpening ? -140 : 0,
              transformOrigin: 'top center',
            }}
            transition={{
              duration: 1.8,
              ease: "easeInOut",
              type: "spring",
              stiffness: 80,
              damping: 15
            }}
          >
            {/* Brilho na aba */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/40 via-transparent to-transparent"
                 style={{ clipPath: 'polygon(0 0, 100% 0, 85% 70%, 50% 100%, 15% 70%)' }} />


          </motion.div>

          {/* Carta que sai do envelope */}
          <motion.div
            className="absolute -top-20 -left-20 -right-20 bg-white rounded-xl shadow-2xl border border-gray-200 z-10 min-h-fit"
            initial={{ y: 0, opacity: 0, scale: 0.9 }}
            animate={{
              y: isOpening ? -100 : 0,
              opacity: isOpening ? 1 : 0,
              scale: isOpening ? 1.02 : 0.9,
            }}
            transition={{
              duration: 1.5,
              delay: isOpening ? 0.5 : 0,
              ease: "easeOut",
              type: "spring",
              stiffness: 100
            }}
          >
            {/* Cabeçalho da carta compacto */}
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4 text-white text-center">
              <motion.h3
                className="text-xl font-bold"
                initial={{ opacity: 0, y: 10 }}
                animate={{
                  opacity: isOpening ? 1 : 0,
                  y: isOpening ? 0 : 10,
                }}
                transition={{ delay: isOpening ? 1 : 0, duration: 0.6 }}
              >
                Bem-vindo ao PedBook!
              </motion.h3>
            </div>

            {/* Conteúdo da carta */}
            <div className="p-6 space-y-4">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{
                  opacity: isOpening ? 1 : 0,
                  y: isOpening ? 0 : 20,
                }}
                transition={{ delay: isOpening ? 1.4 : 0, duration: 0.6 }}
                className="text-center"
              >
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4 mb-4">
                  <h4 className="text-base font-bold text-green-800 mb-2">
                    🎯 ACESSO ANTECIPADO LIBERADO!
                  </h4>
                  <p className="text-green-700 leading-relaxed text-sm">
                    Você foi selecionado para ter acesso antecipado à nossa plataforma de estudos
                    especializada em <span className="font-bold">pediatria para residência médica</span>.
                  </p>
                </div>

                <div className="space-y-3 text-left">
                  <div className="flex items-start gap-3">
                    <div className="w-7 h-7 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-blue-600 font-bold text-xs">1</span>
                    </div>
                    <div>
                      <h5 className="font-semibold text-gray-800 text-sm">Banco de Questões Exclusivo</h5>
                      <p className="text-gray-600 text-xs">Acesso a milhares de questões atualizadas do MedEvo</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-7 h-7 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-purple-600 font-bold text-xs">2</span>
                    </div>
                    <div>
                      <h5 className="font-semibold text-gray-800 text-sm">Filtros Inteligentes</h5>
                      <p className="text-gray-600 text-xs">Sistema avançado por especialidade, tema e instituição</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-7 h-7 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-green-600 font-bold text-xs">3</span>
                    </div>
                    <div>
                      <h5 className="font-semibold text-gray-800 text-sm">Análise de Performance</h5>
                      <p className="text-gray-600 text-xs">Relatórios detalhados e insights personalizados</p>
                    </div>
                  </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-3">
                  <p className="text-yellow-800 text-xs font-medium">
                    ⚡ <strong>Período de Teste:</strong> Aproveite todos os recursos premium!
                  </p>
                </div>
              </motion.div>

              {/* Botão para continuar */}
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{
                  opacity: isOpening ? 1 : 0,
                  scale: isOpening ? 1 : 0.9,
                }}
                transition={{ delay: isOpening ? 1.8 : 0, duration: 0.6 }}
                className="text-center pt-2"
              >
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => {
                    // Aguardar um pouco antes de continuar
                    setTimeout(() => {
                      onEnvelopeClick();
                    }, 300);
                  }}
                  className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-6 py-2.5 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  ✨ Continuar Jornada
                </motion.button>

                <p className="text-xs text-gray-500 mt-2">
                  Clique para prosseguir
                </p>
              </motion.div>
            </div>
          </motion.div>

        </div>


      </motion.div>

      {/* Instrução simples abaixo do envelope */}
      <AnimatePresence>
        {stage === 'envelope-opening' && showClickHint && !isOpening && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute bottom-16 left-1/2 transform -translate-x-1/2 text-center z-20"
          >
            <motion.p
              className="text-white text-lg font-medium bg-black/40 backdrop-blur-sm rounded-full px-6 py-3 shadow-lg"
              animate={{
                scale: [1, 1.05, 1],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              👆 Clique no envelope para abrir
            </motion.p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
