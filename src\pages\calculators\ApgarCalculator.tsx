
import React, { useState } from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Header from "@/components/Header";
import { Baby, ChevronLeft } from "lucide-react";
import { Link } from "react-router-dom";
import { CalculatorSEO } from "@/components/seo/CalculatorSEO";
import { CALCULATOR_SEO_DATA } from "@/data/calculatorSEOData";
import { getThemeClasses } from "@/components/ui/theme-utils";

const ApgarCalculator = () => {
  const seoData = CALCULATOR_SEO_DATA['apgar'];

  const [scores, setScores] = useState({
    heartRate: "",
    breathing: "",
    muscleTone: "",
    skinColor: "",
    reflexes: ""
  });

  const parameters = [
    {
      name: "heartRate",
      title: "Frequência Cardíaca",
      options: [
        { value: "0", label: "Ausente" },
        { value: "1", label: "Menor que 100/minuto" },
        { value: "2", label: "Maior que 100/minuto" }
      ]
    },
    {
      name: "breathing",
      title: "Respiração",
      options: [
        { value: "0", label: "Ausente" },
        { value: "1", label: "Fraca/Irregular" },
        { value: "2", label: "Forte/Choro" }
      ]
    },
    {
      name: "muscleTone",
      title: "Tônus Muscular",
      options: [
        { value: "0", label: "Flácido" },
        { value: "1", label: "Flexão de pernas e braços" },
        { value: "2", label: "Movimento ativo/Boa flexão" }
      ]
    },
    {
      name: "skinColor",
      title: "Cor da Pele",
      options: [
        { value: "0", label: "Cianótico/Pálido" },
        { value: "1", label: "Cianose de extremidades" },
        { value: "2", label: "Rosado" }
      ]
    },
    {
      name: "reflexes",
      title: "Irritabilidade Reflexa",
      options: [
        { value: "0", label: "Ausente" },
        { value: "1", label: "Algum movimento" },
        { value: "2", label: "Espirros/Choro" }
      ]
    }
  ];

  const calculateTotal = () => {
    return Object.values(scores).reduce((acc, curr) => acc + (parseInt(curr) || 0), 0);
  };

  const getResultInterpretation = (total: number) => {
    if (total >= 7) return { text: "Normal", color: "text-green-600" };
    if (total >= 4) return { text: "Alterado (Baixo)", color: "text-yellow-600" };
    return { text: "Crítico", color: "text-red-600" };
  };

  const allFieldsFilled = Object.values(scores).every(value => value !== "");
  const total = calculateTotal();
  const interpretation = getResultInterpretation(total);

  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen flex flex-col")}>
      <CalculatorSEO {...seoData} />
      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto space-y-8">
          <div className="flex items-center gap-4">
            <Link to="/calculadoras">
              <Button variant="ghost" size="icon" className="hover:bg-primary/10 dark:hover:bg-primary/20">
                <ChevronLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h1 className={getThemeClasses.gradientHeading("text-3xl")}>
              Calculadora de APGAR
            </h1>
          </div>
          <p className="text-gray-600 dark:text-gray-300">
            Avaliação da vitalidade de recém-nascidos baseada em cinco parâmetros clínicos
          </p>

          <Card className={getThemeClasses.card("p-6 space-y-8")}>
            {parameters.map((param) => (
              <div key={param.name} className="space-y-4">
                <h3 className="font-semibold text-lg text-gray-800 dark:text-gray-100">{param.title}</h3>
                <RadioGroup
                  value={scores[param.name as keyof typeof scores]}
                  onValueChange={(value) => 
                    setScores(prev => ({ ...prev, [param.name]: value }))
                  }
                  className="space-y-2"
                >
                  {param.options.map((option) => (
                    <div key={option.value} className="flex items-center space-x-2">
                      <RadioGroupItem value={option.value} id={`${param.name}-${option.value}`} />
                      <Label htmlFor={`${param.name}-${option.value}`} className="text-gray-700 dark:text-gray-300">
                        {option.label} ({option.value} {option.value === "1" ? "ponto" : "pontos"})
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>
            ))}

            {allFieldsFilled && (
              <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
                <div className="text-center space-y-4">
                  <div className="text-4xl font-bold text-primary dark:text-blue-400">
                    {total} pontos
                  </div>
                  <div className={`text-xl font-semibold ${interpretation.color}`}>
                    {interpretation.text}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {total >= 7 ? (
                      "Considerado adequado e sem necessidade de intervenção."
                    ) : total >= 4 ? (
                      "Necessita acompanhamento próximo e possível intervenção médica."
                    ) : (
                      "Necessita intervenção de urgência."
                    )}
                  </div>
                </div>
              </div>
            )}
          </Card>
        </div>
      </main>
    </div>
  );
};

export default ApgarCalculator;
