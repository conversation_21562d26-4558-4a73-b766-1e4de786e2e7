import { Link } from "react-router-dom";

const AdminNav = () => {
  const menuItems = [
    { label: "Blog", path: "/admin/blog" },
    { label: "Categorias", path: "/admin/categories" },
    { label: "Medicamentos", path: "/admin/medications" },
    { label: "Dosagens", path: "/admin/dosages" },
    { label: "Categorias de Prescrição", path: "/admin/prescription-categories" },
    { label: "CID-10", path: "/admin/icd10" },
    { label: "Curvas de Crescimento", path: "/admin/growth-curves" },
    { label: "Metadados das Curvas", path: "/admin/growth-curve-metadata" },
    { label: "Vacinas", path: "/admin/vaccines" },
    { label: "Medicamentos na Amamentação", path: "/admin/breastfeeding-medications-enhancement" },
    { label: "Fórmulas", path: "/admin/formulas" },
    { label: "DNPM", path: "/admin/dnpm" },
    { label: "Configurações do Site", path: "/admin/site-settings" },
  ];

  return (
    <nav className="bg-white shadow">
      <ul className="flex space-x-4">
        {menuItems.map((item) => (
          <li key={item.label}>
            <Link to={item.path} className="text-gray-700 hover:text-gray-900">
              {item.label}
            </Link>
          </li>
        ))}
      </ul>
    </nav>
  );
};

export default AdminNav;
