
import React from "react";
import { FilterItem } from "@/components/filters/components/FilterItem";
import type { SelectedFilters } from "@/components/filters/types";

interface QuestionTypeFilterSectionProps {
  selectedTypes: string[];
  onToggleType: (type: string) => void;
  searchTerm?: string;
  selectedFilters: SelectedFilters;
  questionCounts?: {
    totalCounts: {[key: string]: number};
    filteredCounts: {[key: string]: number};
  };
}

export const QuestionTypeFilterSection = ({ 
  selectedTypes, 
  onToggleType, 
  searchTerm = "",
  selectedFilters,
  questionCounts
}: QuestionTypeFilterSectionProps) => {
  // Define question types with their display names
  const questionTypes = [
    { id: 'teorica-1', name: 'Teórica I', count: 0 },
    { id: 'teorica-2', name: 'Teórica II', count: 0 },
    { id: 'teorico-pratica', name: 'Teórico-Prática', count: 0 }
  ];

  // Filter question types based on search term
  const filteredTypes = questionTypes.filter(type => 
    type.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Check if any filters are active
  const hasActiveFilters = Object.values(selectedFilters).some(filters => filters.length > 0);

  return (
    <div className="space-y-2">
      {filteredTypes.length === 0 ? (
        <div className="text-center py-4 text-gray-500">
          Nenhum tipo de prova encontrado
        </div>
      ) : (
        <div className="space-y-1">
          {filteredTypes.map(type => (
            <FilterItem
              key={type.id}
              id={type.id}
              name={type.name}
              type="question_type"
              isSelected={selectedTypes.includes(type.id)}
              onToggle={() => onToggleType(type.id)}
              count={type.count}
              hasActiveFilters={hasActiveFilters}
            />
          ))}
        </div>
      )}
    </div>
  );
};
