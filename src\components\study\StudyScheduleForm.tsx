import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useForm, Controller } from 'react-hook-form';
import { toast } from '@/components/ui/use-toast';

interface StudyScheduleFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: StudyScheduleFormData) => void;
  isLoading: boolean;
}

export interface StudyScheduleFormData {
  hoursPerDay: number;
  availableDays: string[];
}

const WEEK_DAYS = [
  { id: 'monday', label: 'Segunda-feira' },
  { id: 'tuesday', label: 'Terça-feira' },
  { id: 'wednesday', label: 'Quarta-feira' },
  { id: 'thursday', label: '<PERSON><PERSON><PERSON>-f<PERSON>' },
  { id: 'friday', label: '<PERSON><PERSON>-feira' },
  { id: 'saturday', label: 'Sábado' },
  { id: 'sunday', label: 'Domingo' },
];

export const StudyScheduleForm = ({ open, onOpenChange, onSubmit, isLoading }: StudyScheduleFormProps) => {
  const { register, handleSubmit, control, formState: { errors } } = useForm<StudyScheduleFormData>({
    defaultValues: {
      hoursPerDay: 2,
      availableDays: []
    }
  });

  const onSubmitForm = (data: StudyScheduleFormData) => {
    if (!data.availableDays || data.availableDays.length === 0) {
      toast({
        title: "Erro",
        description: "Selecione pelo menos um dia da semana",
        variant: "destructive"
      });
      return;
    }

    onSubmit(data);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Configurar Cronograma de Estudos</DialogTitle>
          <DialogDescription>
            Defina sua disponibilidade para personalizar seu cronograma de estudos
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmitForm)} className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="hoursPerDay">Horas disponíveis por dia</Label>
              <Input
                id="hoursPerDay"
                type="number"
                min={1}
                max={12}
                {...register('hoursPerDay', { 
                  required: 'Este campo é obrigatório',
                  min: { value: 1, message: 'Mínimo de 1 hora' },
                  max: { value: 12, message: 'Máximo de 12 horas' }
                })}
              />
              {errors.hoursPerDay && (
                <p className="text-sm text-red-500 mt-1">{errors.hoursPerDay.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label>Dias disponíveis para estudo</Label>
              <div className="grid grid-cols-1 gap-2">
                <Controller
                  name="availableDays"
                  control={control}
                  defaultValue={[]}
                  render={({ field }) => (
                    <>
                      {WEEK_DAYS.map((day) => (
                        <div key={day.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={day.id}
                            checked={field.value?.includes(day.id)}
                            onCheckedChange={(checked) => {
                              const updatedValue = checked
                                ? [...(field.value || []), day.id]
                                : (field.value || []).filter((value) => value !== day.id);
                              field.onChange(updatedValue);
                            }}
                          />
                          <Label htmlFor={day.id}>{day.label}</Label>
                        </div>
                      ))}
                    </>
                  )}
                />
              </div>
            </div>
          </div>

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? 'Gerando cronograma...' : 'Gerar cronograma'}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
};