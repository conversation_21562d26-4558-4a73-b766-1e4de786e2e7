import { useState } from "react";
import { SkillNodeCard } from "./SkillNodeCard";

interface SkillNode {
  id: string;
  name: string;
  progress: number;
  children?: SkillNode[];
}

interface SkillTreeNodeProps {
  node: SkillNode;
  level?: number;
  expandedNodes: Set<string>;
  onToggleNode: (nodeId: string) => void;
}

export const SkillTreeNode = ({
  node,
  level = 0,
  expandedNodes,
  onToggleNode,
}: SkillTreeNodeProps) => {
  const isExpanded = expandedNodes.has(node.id);
  const hasChildren = node.children && node.children.length > 0;

  return (
    <div key={node.id} className={`mb-4`}>
      <SkillNodeCard
        name={node.name}
        progress={node.progress}
        isExpanded={isExpanded}
        hasChildren={hasChildren}
        onToggle={() => onToggleNode(node.id)}
        level={level}
      />
      
      {isExpanded && hasChildren && (
        <div className="ml-4 mt-2 space-y-2">
          {node.children.map(child => (
            <SkillTreeNode
              key={child.id}
              node={child}
              level={level + 1}
              expandedNodes={expandedNodes}
              onToggleNode={onToggleNode}
            />
          ))}
        </div>
      )}
    </div>
  );
};