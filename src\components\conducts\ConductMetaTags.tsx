
import HelmetWrapper from "@/components/utils/HelmetWrapper";

interface ConductMetaTagsProps {
  name: string;
  description?: string | null;
  slug: string;
  categoryName?: string | null;
  categorySlug?: string | null;
}

export const ConductMetaTags = ({ 
  name, 
  description, 
  slug,
  categoryName,
  categorySlug 
}: ConductMetaTagsProps) => {

  const generateKeywords = () => {
    const baseKeywords = new Set([
      `${name} pediatria`,
      `conduta ${name.toLowerCase()}`,
      `manejo ${name.toLowerCase()}`,
      `protocolo ${name.toLowerCase()}`,
      `pediatria ${name.toLowerCase()}`,
      `tratamento ${name.toLowerCase()}`,
      `guideline ${name.toLowerCase()}`
    ]);

    if (categoryName) {
      baseKeywords.add(`${categoryName} pediatria`);
      baseKeywords.add(`condutas ${categoryName.toLowerCase()}`);
      baseKeywords.add(`protocolos ${categoryName.toLowerCase()}`);
    }

    return Array.from(baseKeywords).join(', ');
  };

  const metaDescription = description || 
    `Condutas e manejos pediátricos sobre ${name.toLowerCase()}, incluindo protocolos, diagnóstico e tratamento baseados em evidências.`;
  const metaKeywords = generateKeywords();
  const pageUrl = `https://pedb.com.br/condutas-e-manejos/${categorySlug}/${slug}`;
  const pageTitle = `PedBook | ${name} - Condutas e Manejos Pediátricos`;
  const ogImage = "https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/1496949.webp";


  return (
    <HelmetWrapper>
      {/* Título e Descrição */}
      <title>{pageTitle}</title>
      <meta name="description" content={metaDescription} />
      <meta name="keywords" content={metaKeywords} />

      {/* Configurações Gerais */}
      <meta name="robots" content="index, follow, max-image-preview:large" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta charSet="UTF-8" />
      <link rel="canonical" href={pageUrl} />

      {/* Open Graph para Redes Sociais */}
      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={metaDescription} />
      <meta property="og:type" content="article" />
      <meta property="og:url" content={pageUrl} />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />
      <meta property="og:image" content={ogImage} />
      <meta property="og:image:alt" content={`Protocolo pediátrico sobre ${name}`} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="article:publisher" content="https://pedb.com.br" />
      <meta property="article:section" content={categoryName || 'Condutas e Manejos'} />

      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={metaDescription} />
      <meta name="twitter:image" content={ogImage} />
      <meta name="twitter:site" content="@PedBook" />

      {/* JSON-LD Schema.org para diretrizes médicas */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "MedicalGuideline",
          "name": pageTitle,
          "description": metaDescription,
          "url": pageUrl,
          "keywords": metaKeywords.split(', '),
          "inLanguage": "pt-BR",
          "specialty": "Pediatria",
          "guideline": {
            "@type": "MedicalGuidelineRecommendation",
            "recommendationStrength": "Strong",
            "evidenceLevel": "Evidence-based"
          },
          "guidelineSubject": {
            "@type": "MedicalCondition",
            "name": name
          },
          "guidelineCategory": categoryName,
          "audience": {
            "@type": "MedicalAudience",
            "audienceType": "Profissionais de Saúde"
          },
          "publisher": {
            "@type": "Organization",
            "name": "PedBook",
            "url": "https://pedb.com.br",
            "logo": {
              "@type": "ImageObject",
              "url": "https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/faviconx.png"
            }
          },
          "image": {
            "@type": "ImageObject",
            "url": ogImage,
            "width": 1200,
            "height": 630
          },
          "dateModified": new Date().toISOString()
        })}
      </script>
    </HelmetWrapper>
  );
};
