import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";

interface FilterHeaderProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}

const FilterHeader = ({ searchTerm, setSearchTerm }: FilterHeaderProps) => {
  return (
    <div className="relative">
      <Search className="absolute left-2 top-3 h-4 w-4 text-gray-400" />
      <Input
        placeholder="Pesquisar questões..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        className="pl-8"
      />
    </div>
  );
};

export default FilterHeader;