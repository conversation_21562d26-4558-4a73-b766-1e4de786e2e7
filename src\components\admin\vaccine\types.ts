export interface RelatedVaccine {
  id: string;
  doseNumber?: number;
  doseType?: string;
}

export interface VaccineDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  vaccine?: any;
}

export interface VaccineRelationship {
  child_vaccine_id: string;
  dose_number: number | null;
  dose_type: string | null;
}

export interface VaccineDose {
  id: string;
  dose_number: number;
  age_recommendation: string;
  type: string;
  dose_type: string;
  vaccine: {
    id: string;
    name: string;
    description: string;
  };
}