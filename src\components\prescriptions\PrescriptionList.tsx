import { format } from "date-fns";
import { useNavigate } from "react-router-dom";

interface Prescription {
  id: string;
  name: string;
  created_at: string;
  patient_age: number;
  patient_weight: number;
}

interface PrescriptionListProps {
  prescriptions: Prescription[] | undefined;
}

export const PrescriptionList = ({ prescriptions }: PrescriptionListProps) => {
  const navigate = useNavigate();

  if (!prescriptions?.length) {
    return (
      <div className="text-center py-8 bg-muted/20 rounded-lg backdrop-blur-sm">
        <p className="text-muted-foreground">
          Você ainda não tem nenhuma prescrição
        </p>
      </div>
    );
  }

  return (
    <div className="grid gap-4">
      {prescriptions.map((prescription) => (
        <div
          key={prescription.id}
          className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow cursor-pointer"
          onClick={() => navigate(`/prescriptions/${prescription.id}`)}
        >
          <div className="flex justify-between items-start">
            <div className="space-y-1">
              <h2 className="text-lg font-semibold">{prescription.name}</h2>
              <p className="text-sm text-gray-500">
                Criada em: {format(new Date(prescription.created_at), "dd/MM/yyyy")}
              </p>
              <p className="text-sm text-gray-600">
                Paciente: {prescription.patient_age} anos, {prescription.patient_weight}kg
              </p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};