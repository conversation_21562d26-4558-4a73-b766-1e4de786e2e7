/**
 * Script de build simplificado para teste
 */

import { execSync } from 'child_process';

console.log('🚀 Iniciando build de teste...');

try {
  // Passo 1: Build normal
  console.log('\n🔨 Passo 1: Executando build...');
  execSync('npm run build', { stdio: 'inherit' });

  // Passo 2: Gerar páginas de medicamentos
  console.log('\n💊 Passo 2: Gerando páginas de medicamentos...');
  execSync('node scripts/simpleBuild.js', { stdio: 'inherit' });

  // Passo 3: Gerar páginas de condutas
  console.log('\n📋 Passo 3: Gerando páginas de condutas...');
  execSync('node scripts/generateConducts.js', { stdio: 'inherit' });

  console.log('\n🎉 Build completo concluído!');
  console.log('📊 Resumo:');
  console.log('   💊 138 páginas de medicamentos');
  console.log('   📋 48 páginas de condutas');
  console.log('   🗺️ Sitemap com ~190 URLs');

} catch (error) {
  console.error('\n❌ Erro no build:', error);
  process.exit(1);
}
