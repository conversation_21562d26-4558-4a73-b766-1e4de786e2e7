import React from 'react';
import MDEditor from '@uiw/react-md-editor';
import { Label } from './label';

interface MarkdownEditorProps {
  label?: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  height?: number;
  preview?: 'edit' | 'preview' | 'live';
}

export const MarkdownEditor: React.FC<MarkdownEditorProps> = ({
  label,
  value,
  onChange,
  placeholder = "Digite seu texto aqui...",
  height = 200,
  preview = 'live'
}) => {
  return (
    <div className="space-y-2">
      {label && (
        <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
        </Label>
      )}
      
      <div className="markdown-editor-container">
        <MDEditor
          value={value}
          onChange={(val) => onChange(val || '')}
          height={height}
          preview={preview}
          data-color-mode="auto"
          visibleDragBar={false}
          textareaProps={{
            placeholder,
            style: {
              fontSize: 14,
              lineHeight: 1.5,
              fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace'
            }
          }}
        />
      </div>
      
      {/* Dicas de formatação */}
      <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
        <p className="mb-1"><strong>Dicas:</strong></p>
        <div className="grid grid-cols-2 gap-1 text-xs">
          <span>**negrito** → <strong>negrito</strong></span>
          <span>*itálico* → <em>itálico</em></span>
          <span>`código` → <code className="bg-gray-100 dark:bg-gray-800 px-1 rounded">código</code></span>
          <span>- lista → • lista</span>
        </div>
      </div>
    </div>
  );
};

// Componente para exibir markdown renderizado
interface MarkdownViewerProps {
  content: string;
  className?: string;
}

export const MarkdownViewer: React.FC<MarkdownViewerProps> = ({
  content,
  className = ""
}) => {
  return (
    <div className={`markdown-viewer prose prose-sm max-w-none ${className}`}>
      <MDEditor.Markdown
        source={content}
        style={{
          backgroundColor: 'transparent',
          color: 'inherit'
        }}
      />
    </div>
  );
};
