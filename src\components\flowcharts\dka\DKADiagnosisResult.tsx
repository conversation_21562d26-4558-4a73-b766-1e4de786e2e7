import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface DKADiagnosisResultProps {
  onContinue: () => void;
  weight: number;
}

export const DKADiagnosisResult = ({ onContinue, weight }: DKADiagnosisResultProps) => {
  const hydrationVolume = Math.round(weight * 10);
  const maxHydrationVolume = Math.round(weight * 20);

  return (
    <div className="space-y-4">
      <Card className="p-6 space-y-4 bg-green-50 border-green-200">
        <h3 className="text-lg font-semibold text-green-800">Diagnóstico:</h3>
        <p className="text-green-700">Cetoacidose diabética</p>
        
        <div className="space-y-2">
          <h4 className="font-medium text-green-800">Estabelecer:</h4>
          <p className="text-green-700">2 acessos periféricos</p>
        </div>
        
        <div className="space-y-2">
          <h4 className="font-medium text-green-800">Hidratação inicial:</h4>
          <p className="text-green-700">
            SF 0,9% {hydrationVolume}-{maxHydrationVolume} mL EV na 1ª hora
          </p>
        </div>
      </Card>

      <Alert className="bg-blue-50 border-blue-200">
        <AlertDescription className="space-y-2 text-sm text-blue-800">
          <h4 className="font-semibold">Critérios de avaliação de desidratação:</h4>
          <ul className="list-disc pl-4 space-y-1">
            <li>Nível de consciência irritado, letárgico ou inconsciente</li>
            <li>Olhos fundos e secos</li>
            <li>Lágrimas ausentes</li>
            <li>Mucosa oral seca</li>
            <li>Sedento ou sem conseguir beber líquidos</li>
            <li>Sinal da prega lento ou muito lento (&gt; 2 segundos)</li>
            <li>Pulsos rápidos e fracos ou ausentes</li>
          </ul>
        </AlertDescription>
      </Alert>

      <div className="flex justify-center">
        <Button onClick={onContinue} className="w-full max-w-md">
          Continuar avaliação
        </Button>
      </div>
    </div>
  );
};