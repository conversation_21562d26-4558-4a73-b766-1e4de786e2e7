import { useState, useEffect } from 'react';
import { useUser } from "@supabase/auth-helpers-react";

/**
 * Hook para gerenciar o estado do feedback do PediDrop
 * Controla quando mostrar o dialog de feedback baseado na interação do usuário
 */
export const usePediDropFeedback = () => {
  const [showFeedback, setShowFeedback] = useState(false);
  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false);
  const [timeOnPage, setTimeOnPage] = useState(0);
  const user = useUser();

  useEffect(() => {
    // Verificar se já mostrou feedback nesta sessão
    const hasShownFeedback = sessionStorage.getItem('pedidrop_feedback_shown');
    if (hasShownFeedback) return;

    // Timer para contar tempo na página
    const startTime = Date.now();
    const timer = setInterval(() => {
      setTimeOnPage(Date.now() - startTime);
    }, 1000);

    // Listener para scroll
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      
      // Considera que chegou ao final se está a 200px do final
      const isNearBottom = scrollTop + windowHeight >= documentHeight - 200;
      
      if (isNearBottom && !hasScrolledToBottom) {
        setHasScrolledToBottom(true);
      }
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      clearInterval(timer);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [hasScrolledToBottom]);

  // Mostrar feedback quando:
  // 1. Usuário rolou até o final da página
  // 2. Passou pelo menos 30 segundos na página
  // 3. Está logado
  // 4. Ainda não mostrou nesta sessão
  useEffect(() => {
    if (
      hasScrolledToBottom && 
      timeOnPage > 30000 && // 30 segundos
      user && 
      !showFeedback &&
      !sessionStorage.getItem('pedidrop_feedback_shown')
    ) {
      // Delay de 2 segundos após detectar que chegou ao final
      const delayTimer = setTimeout(() => {
        setShowFeedback(true);
        sessionStorage.setItem('pedidrop_feedback_shown', 'true');
      }, 2000);

      return () => clearTimeout(delayTimer);
    }
  }, [hasScrolledToBottom, timeOnPage, user, showFeedback]);

  const triggerFeedback = () => {
    if (user) {
      setShowFeedback(true);
      sessionStorage.setItem('pedidrop_feedback_shown', 'true');
    }
  };

  const closeFeedback = () => {
    setShowFeedback(false);
  };

  return {
    showFeedback,
    triggerFeedback,
    closeFeedback,
    hasScrolledToBottom,
    timeOnPage: Math.floor(timeOnPage / 1000), // em segundos
    isLoggedIn: !!user
  };
};
