
import { Mail, Instagram, FileText, MessageSquare, Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { FeedbackPage } from "@/pages/feedback/FeedbackPage";
import { useState } from "react";

const Footer = () => {
  const [showFeedbackDialog, setShowFeedbackDialog] = useState(false);

  return (
    <footer className="hidden sm:block bg-blue-50/90 dark:bg-blue-900/30 backdrop-blur-md shadow-md mt-16 border-t border-blue-100 dark:border-blue-800/50">
      <div className="container mx-auto px-4 py-6">
        <div className="text-center space-y-5">
          <Button
            variant="default"
            className="bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg transition-all duration-300 rounded-md"
            onClick={() => setShowFeedbackDialog(true)}
          >
            <MessageSquare className="h-4 w-4 mr-2" />
            <span>Feedback / Sugestões</span>
          </Button>

          <p className="max-w-2xl mx-auto text-sm text-gray-600 dark:text-gray-300">
            Plataforma para profissionais de saúde com foco em pediatria, oferecendo informações atualizadas e práticas.
          </p>

          <div className="flex justify-center gap-5">
            <a
              href="mailto:<EMAIL>"
              className="text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-blue-400 transition-colors"
            >
              <Mail size={20} />
            </a>
            <a
              href="https://www.instagram.com/pedbookmed/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-blue-400 transition-colors"
            >
              <Instagram size={20} />
            </a>
            <Link
              to="/terms"
              className="text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-blue-400 transition-colors"
            >
              <FileText size={20} />
            </Link>
            <Link
              to="/politica-privacidade"
              className="text-gray-600 hover:text-primary dark:text-gray-300 dark:hover:text-blue-400 transition-colors"
            >
              <FileText size={20} />
            </Link>
          </div>

          <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
            <p>© 2025 PedBook. Todos os direitos reservados.</p>
            <p className="text-gray-500 dark:text-gray-400">
            Desenvolvimento por{" "}
              <a
                href="https://medunity.com.br/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary hover:text-primary/80 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
              >
                Med Unity
              </a>
            </p>
          </div>
        </div>
      </div>

      {/* Feedback Dialog with improved closing behavior */}
      <Dialog
        open={showFeedbackDialog}
        onOpenChange={(value) => {
          setShowFeedbackDialog(value);
          // Ensure pointer events are properly reset when dialog closes
          if (!value) {
            document.body.style.pointerEvents = 'auto';
          }
        }}
      >
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto dark:bg-slate-800">
          <DialogHeader>
            <DialogTitle>Feedback</DialogTitle>
          </DialogHeader>
          <FeedbackPage />
        </DialogContent>
      </Dialog>
    </footer>
  );
};

export default Footer;
