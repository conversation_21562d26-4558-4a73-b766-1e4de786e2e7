import CategoryManager from "@/components/CategoryManager";
import ExamLocationManager from "@/components/ExamLocationManager";
import { QuestionImport } from "@/components/settings/QuestionImport";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

const Settings = () => {
  return (

      <div className="container py-8">
        <Card>
          <CardHeader>
            <CardTitle>Configurações do Sistema</CardTitle>
            <CardDescription>
              Gerencie categorias, locais de prova e outras configurações do sistema
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="categories">
              <TabsList>
                <TabsTrigger value="categories">Categorias</TabsTrigger>
                <TabsTrigger value="locations">Locais de Prova</TabsTrigger>
                <TabsTrigger value="import">Importar Questões</TabsTrigger>
              </TabsList>
              <TabsContent value="categories">
                <CategoryManager />
              </TabsContent>
              <TabsContent value="locations">
                <ExamLocationManager />
              </TabsContent>
              <TabsContent value="import">
                <QuestionImport />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
  );
};

export default Settings;