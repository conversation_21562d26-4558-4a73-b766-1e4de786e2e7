import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Wrench, AlertTriangle, Info } from 'lucide-react';
import MaintenanceToggle from '@/components/admin/MaintenanceToggle';
import { useMaintenanceMode } from '@/hooks/useMaintenanceMode';
import { Alert, AlertDescription } from '@/components/ui/alert';

const MaintenancePage: React.FC = () => {
  const { isSuperAdmin, isMaintenanceActive } = useMaintenanceMode();



  if (!isSuperAdmin) {
    return (
      <div className="container mx-auto px-4 py-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Acesso negado. Apenas super administradores podem acessar esta página.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-4xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-orange-100 rounded-lg">
            <Wrench className="h-6 w-6 text-orange-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">Modo de Manutenção</h1>
            <p className="text-gray-600">
              Controle o acesso ao site durante manutenções e atualizações
            </p>
          </div>
        </div>

        {/* Status Alert */}
        {isMaintenanceActive && (
          <Alert variant="destructive" className="mb-6">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Site em Manutenção:</strong> Todos os usuários (exceto você) estão sendo redirecionados para a página de manutenção.
            </AlertDescription>
          </Alert>
        )}
      </div>

      {/* Informações importantes */}
      <div className="grid gap-6 mb-8">
        <Card className="border-blue-200 bg-blue-50/50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-800">
              <Info className="h-5 w-5" />
              Como Funciona
            </CardTitle>
          </CardHeader>
          <CardContent className="text-blue-700 space-y-2">
            <p>• <strong>Ativado:</strong> Todos os usuários são redirecionados para a página de manutenção</p>
            <p>• <strong>Super Admin:</strong> Você continua com acesso total ao site</p>
            <p>• <strong>Verificação:</strong> O sistema verifica o status a cada 30 segundos</p>
            <p>• <strong>Desativado:</strong> Usuários voltam automaticamente ao site normal</p>
          </CardContent>
        </Card>

        <Card className="border-amber-200 bg-amber-50/50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-amber-800">
              <AlertTriangle className="h-5 w-5" />
              Cuidados Importantes
            </CardTitle>
          </CardHeader>
          <CardContent className="text-amber-700 space-y-2">
            <p>• Use apenas durante manutenções reais ou atualizações críticas</p>
            <p>• Informe uma mensagem clara e tempo estimado para os usuários</p>
            <p>• Teste sempre em ambiente de desenvolvimento primeiro</p>
            <p>• Mantenha comunicação com a equipe durante a manutenção</p>
          </CardContent>
        </Card>
      </div>

      {/* Componente principal */}
      <div className="flex justify-center">
        <MaintenanceToggle />
      </div>

      {/* Informações técnicas */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Informações Técnicas</CardTitle>
            <CardDescription>
              Detalhes sobre o funcionamento do sistema de manutenção
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-medium mb-2">Banco de Dados</h4>
                <p className="text-gray-600">
                  Status armazenado na tabela <code className="bg-gray-100 px-1 rounded">site_maintenance</code>
                </p>
              </div>
              <div>
                <h4 className="font-medium mb-2">Verificação</h4>
                <p className="text-gray-600">
                  Polling automático a cada 30 segundos via React Query
                </p>
              </div>
              <div>
                <h4 className="font-medium mb-2">Redirecionamento</h4>
                <p className="text-gray-600">
                  Automático via hook <code className="bg-gray-100 px-1 rounded">useMaintenanceMode</code>
                </p>
              </div>
              <div>
                <h4 className="font-medium mb-2">Permissões</h4>
                <p className="text-gray-600">
                  Apenas super admin (<EMAIL>) pode alterar
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default MaintenancePage;
