import HelmetWrapper from "@/components/utils/HelmetWrapper";

export const SupplementationMetaTags = () => {
  const pageTitle = "PedBook | Suplementação Infantil - Guia de Recomendações";
  const pageDescription = "Guia completo sobre suplementação infantil, com recomendações de vitaminas e minerais essenciais para cada fase do desenvolvimento.";
  const pageUrl = "https://pedb.com.br/puericultura/suplementacao-infantil";
  const imageUrl = "https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/suplementacao.webp";
  const keywords = [
    "suplementação infantil",
    "vitaminas crianças",
    "minerais crianças",
    "vitamina D",
    "ferro infantil",
    "zinco infantil",
    "ômega 3",
    "suplementação pediátrica",
    "nutrição infantil",
    "desenvolvimento infantil",
    "prevenção deficiências",
    "saúde infantil",
    "suplementos vitamínicos"
  ].join(", ");

  return (
    <HelmetWrapper>
      <title>{pageTitle}</title>
      <meta name="description" content={pageDescription} />
      <meta name="keywords" content={keywords} />

      <meta name="robots" content="index, follow, max-image-preview:large" />
      <link rel="canonical" href={pageUrl} />

      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={pageDescription} />
      <meta property="og:type" content="article" />
      <meta property="og:url" content={pageUrl} />
      <meta property="og:image" content={imageUrl} />
      <meta property="og:image:alt" content="Ilustração sobre suplementação infantil" />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />

      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={pageDescription} />
      <meta name="twitter:image" content={imageUrl} />

      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "MedicalWebPage",
          "name": pageTitle,
          "description": pageDescription,
          "url": pageUrl,
          "image": imageUrl,
          "keywords": keywords.split(", "),
          "publisher": {
            "@type": "Organization",
            "name": "PedBook",
            "logo": {
              "@type": "ImageObject",
              "url": "https://pedb.com.br/logo.png"
            }
          },
          "specialty": "Pediatria",
          "audience": {
            "@type": "MedicalAudience",
            "audienceType": "Profissionais de Saúde"
          },
          "about": {
            "@type": "MedicalCondition",
            "name": "Suplementação Infantil",
            "description": "Recomendações e guia completo sobre suplementação vitamínica e mineral na infância"
          }
        })}
      </script>
    </HelmetWrapper>
  );
};
