import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { useReactionMutation } from "./reactions/useReactionMutation";
import { useReactionQueries } from "./reactions/useReactionQueries";
import { ThumbsUp, ThumbsDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

interface PrescriptionReactionsProps {
  prescriptionId: string;
  userId?: string;
  initialLikes?: number;
  initialDislikes?: number;
}

export const PrescriptionReactions = ({ 
  prescriptionId, 
  userId,
  initialLikes = 0,
  initialDislikes = 0 
}: PrescriptionReactionsProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { prescription, userReaction } = useReactionQueries(prescriptionId, userId);
  const reactionMutation = useReactionMutation(userId);

  const handleReaction = async (type: 'like' | 'dislike') => {
    if (!userId) {
      toast({
        title: "Erro",
        description: "Você precisa estar logado para reagir a uma prescrição",
        variant: "destructive",
      });
      return;
    }

    if (isSubmitting) return;

    try {
      setIsSubmitting(true);
      await reactionMutation.mutateAsync({
        prescriptionId,
        type,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const likesCount = prescription?.likes_count ?? initialLikes;
  const dislikesCount = prescription?.dislikes_count ?? initialDislikes;

  return (
    <motion.div 
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex items-center gap-1.5 bg-white/50 backdrop-blur-sm rounded-full px-2 py-1 shadow-sm"
    >
      <button
        onClick={() => handleReaction('like')}
        disabled={isSubmitting}
        className={cn(
          "flex items-center gap-1 text-xs font-medium transition-all duration-200",
          userReaction?.reaction_type === 'like'
            ? "text-green-500"
            : "text-gray-400 hover:text-green-500"
        )}
      >
        <ThumbsUp className="h-3.5 w-3.5" />
        <span>{likesCount}</span>
      </button>
      <div className="w-px h-3 bg-gray-200" />
      <button
        onClick={() => handleReaction('dislike')}
        disabled={isSubmitting}
        className={cn(
          "flex items-center gap-1 text-xs font-medium transition-all duration-200",
          userReaction?.reaction_type === 'dislike'
            ? "text-red-500"
            : "text-gray-400 hover:text-red-500"
        )}
      >
        <ThumbsDown className="h-3.5 w-3.5" />
        <span>{dislikesCount}</span>
      </button>
    </motion.div>
  );
};