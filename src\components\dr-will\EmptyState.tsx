import React from 'react';
import { motion } from "framer-motion";
import { Stethoscope, MessageSquare, ArrowDown, Image, Sparkles, BadgeCheck } from 'lucide-react';

interface EmptyStateProps {
  onSendMessage: (message: string) => void;
}

export const EmptyState: React.FC<EmptyStateProps> = ({ onSendMessage }) => {
  return (
    <div className="flex flex-col items-center justify-center h-full text-center px-4 py-6 max-w-3xl mx-auto">
      <motion.div
        initial="hidden"
        animate="visible"
        variants={{
          hidden: { opacity: 0, y: 20 },
          visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
        }}
        className="space-y-6 w-full max-w-md"
      >
        {/* Header with icon and version badge */}
        <div className="space-y-4">
          <motion.div 
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className="flex justify-center mb-2"
          >
            <div className="rounded-full bg-gradient-to-br from-blue-500 to-blue-700 p-4 shadow-lg shadow-blue-500/20 relative">
              <Stethoscope className="w-7 h-7 text-white" />
              <motion.div
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.5, duration: 0.3 }}
                className="absolute -top-2 -right-2 bg-green-500 text-white text-xs font-bold px-2 py-0.5 rounded-full shadow-md"
              >
                2.0
              </motion.div>
            </div>
          </motion.div>

          <h2 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-blue-800 dark:from-blue-400 dark:to-blue-600">
            Dr. Will 2.0
          </h2>
          
          <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
            Assistente médico completo, agora com conhecimento em 
            <span className="font-semibold text-blue-600 dark:text-blue-400"> todas as áreas da medicina</span>.
          </p>
        </div>

        {/* Features */}
        <div className="flex justify-center gap-3">
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="flex flex-col items-center bg-blue-50 dark:bg-blue-900/20 p-2 rounded-lg"
          >
            <div className="bg-white dark:bg-slate-800 p-1.5 rounded-full mb-1.5 shadow-sm">
              <Sparkles className="w-4 h-4 text-blue-500" />
            </div>
            <span className="text-xs text-blue-700 dark:text-blue-300 font-medium">Atualizado</span>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
            className="flex flex-col items-center bg-blue-50 dark:bg-blue-900/20 p-2 rounded-lg"
          >
            <div className="bg-white dark:bg-slate-800 p-1.5 rounded-full mb-1.5 shadow-sm">
              <Image className="w-4 h-4 text-blue-500" />
            </div>
            <span className="text-xs text-blue-700 dark:text-blue-300 font-medium">Análise de imagens</span>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="flex flex-col items-center bg-blue-50 dark:bg-blue-900/20 p-2 rounded-lg"
          >
            <div className="bg-white dark:bg-slate-800 p-1.5 rounded-full mb-1.5 shadow-sm">
              <BadgeCheck className="w-4 h-4 text-blue-500" />
            </div>
            <span className="text-xs text-blue-700 dark:text-blue-300 font-medium">Mais preciso</span>
          </motion.div>
        </div>

        {/* Animated arrow pointing to input */}
        <motion.div 
          className="flex flex-col items-center gap-3 mt-6"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9, duration: 0.5 }}
        >
          <div className="bg-blue-600 dark:bg-blue-500 text-white px-3 py-1.5 rounded-lg shadow-md flex items-center gap-2 text-sm">
            <MessageSquare className="w-4 h-4" />
            <span className="font-medium">Digite sua pergunta</span>
          </div>
          <motion.div
            animate={{ y: [0, 8, 0] }}
            transition={{ repeat: Infinity, duration: 1.5, ease: "easeInOut" }}
            className="text-blue-600 dark:text-blue-400"
          >
            <ArrowDown className="w-5 h-5" />
          </motion.div>
        </motion.div>
      </motion.div>
    </div>
  );
};
