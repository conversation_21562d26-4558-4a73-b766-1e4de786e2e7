import { useState } from "react";
import { DengueResult } from "./types";
import { getDengueQuestions } from "./constants/dengueQuestions";
import { getDengueResults } from "./constants/dengueResults";

export const useDengueFlow = (weight: number) => {
  const [currentStep, setCurrentStep] = useState<string>("start");
  const [answers, setAnswers] = useState<Record<string, boolean>>({});

  const handleAnswer = (answer: boolean) => {
    const newAnswers = { ...answers, [currentStep]: answer };
    setAnswers(newAnswers);

    switch (currentStep) {
      case "start":
        setCurrentStep(answer ? "gravidade" : "sangramento");
        break;
      case "gravidade":
        setCurrentStep(answer ? "grupo_d" : "grupo_c");
        break;
      case "sangramento":
        setCurrentStep(answer ? "grupo_b" : "grupo_a");
        break;
      case "grupo_d_melhora":
        setCurrentStep(answer ? "grupo_c" : "grupo_d_hemoconcentracao");
        break;
      case "grupo_d_hemoconcentracao":
        setCurrentStep(answer ? "grupo_d_albumina" : "grupo_d_choque");
        break;
      case "grupo_d_choque":
        setCurrentStep(answer ? "grupo_d_transfusao" : "grupo_d_icc");
        break;
      case "grupo_d_icc":
        setCurrentStep("grupo_c");
        break;
      case "grupo_c_melhora":
        setCurrentStep(answer ? "grupo_c_manutencao" : "grupo_c_repetir");
        break;
      case "grupo_b_hemoconcentracao":
        setCurrentStep(answer ? "grupo_c" : "grupo_b_final");
        break;
    }
  };

  const getQuestion = (): string => {
    const questions = getDengueQuestions();
    return questions[currentStep] || "";
  };

  const getResult = (): DengueResult | null => {
    const results = getDengueResults(weight);
    return currentStep in results ? results[currentStep] : null;
  };

  const resetFlow = () => {
    setCurrentStep("start");
    setAnswers({});
  };

  return {
    currentStep,
    getQuestion,
    getResult,
    handleAnswer,
    resetFlow,
    answers,
    setCurrentStep
  };
};