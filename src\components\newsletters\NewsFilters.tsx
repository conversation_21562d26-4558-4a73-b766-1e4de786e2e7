import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Filter,
  Calendar,
  Tag,
  TrendingUp,
  Clock,
  Star,
  Grid3X3,
  List,
  Search,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useNewsCategories } from '@/hooks/useNewsletters';
import { cn } from '@/lib/utils';
import { useDebounce } from '@/hooks/useDebounce';

interface NewsFiltersProps {
  onFilterChange: (filters: FilterState) => void;
  onLayoutChange: (layout: 'grid' | 'list') => void;
  currentLayout: 'grid' | 'list';
}

export interface FilterState {
  search: string;
  category: string;
  sortBy: 'recent' | 'trending' | 'popular';
  timeRange: 'today' | 'week' | 'month' | 'all';
}

export const NewsFilters: React.FC<NewsFiltersProps> = ({
  onFilterChange,
  onLayoutChange,
  currentLayout
}) => {
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    category: '',
    sortBy: 'recent',
    timeRange: 'all'
  });
  const [isExpanded, setIsExpanded] = useState(false);

  // Debounce do termo de busca para melhor performance INP
  const debouncedSearchTerm = useDebounce(filters.search, 300);

  // Aplicar busca debounced
  useEffect(() => {
    if (debouncedSearchTerm !== filters.search) {
      // Atualizar filtros com termo debounced
      const updatedFilters = { ...filters, search: debouncedSearchTerm };
      onFilterChange(updatedFilters);
    }
  }, [debouncedSearchTerm]);

  // Buscar categorias reais do sistema
  const { data: newsCategories } = useNewsCategories();
  const categories = newsCategories?.map(cat => cat.name) || [];

  const sortOptions = [
    { value: 'recent', label: 'Mais Recentes', icon: Clock }
  ];

  const timeRanges = [
    { value: 'today', label: 'Hoje' },
    { value: 'week', label: 'Esta Semana' },
    { value: 'month', label: 'Este Mês' },
    { value: 'all', label: 'Todos' }
  ];

  const updateFilters = (newFilters: Partial<FilterState>) => {
    const updated = { ...filters, ...newFilters };
    setFilters(updated);
    onFilterChange(updated);
  };

  const clearFilters = () => {
    const cleared: FilterState = {
      search: '',
      category: '',
      sortBy: 'recent',
      timeRange: 'all'
    };
    setFilters(cleared);
    onFilterChange(cleared);
  };

  const hasActiveFilters = filters.search || filters.category || filters.sortBy !== 'recent' || filters.timeRange !== 'all';

  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-3 sm:p-4 mb-4">
      {/* Layout Responsivo */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-center gap-3 sm:gap-4 max-w-4xl mx-auto">
        {/* Busca Compacta */}
        <div className="relative flex-1 sm:w-80 sm:flex-none">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Buscar notícias..."
            value={filters.search}
            onChange={(e) => updateFilters({ search: e.target.value })}
            className="pl-10 h-10 bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
          />
        </div>

        {/* Linha de Controles */}
        <div className="flex items-center justify-between sm:justify-center gap-3">
          {/* Filtros Rápidos */}
          <div className="flex items-center gap-2">
            {hasActiveFilters && (
              <Badge variant="secondary" className="bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 text-xs">
                {[filters.search, filters.category, filters.timeRange !== 'all' ? 'Período' : '']
                  .filter(Boolean).length} filtro(s)
              </Badge>
            )}

            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="h-9 px-2 sm:px-3 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <X className="w-4 h-4 sm:mr-1" />
                <span className="hidden sm:inline">Limpar</span>
              </Button>
            )}
          </div>

          {/* Layout Toggle */}
          <div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <Button
              variant={currentLayout === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onLayoutChange('grid')}
              className="h-8 w-8 p-0"
            >
              <Grid3X3 className="w-4 h-4" />
            </Button>
            <Button
              variant={currentLayout === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onLayoutChange('list')}
              className="h-8 w-8 p-0"
            >
              <List className="w-4 h-4" />
            </Button>
          </div>

          {/* Botão Expandir */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-9 w-9 p-0"
          >
            <Filter className={cn("w-4 h-4 transition-transform", isExpanded && "rotate-180")} />
          </Button>
        </div>
      </div>

      {/* Expanded Filters */}
      <motion.div
        initial={false}
        animate={{ height: isExpanded ? 'auto' : 0, opacity: isExpanded ? 1 : 0 }}
        transition={{ duration: 0.3 }}
        className="overflow-hidden"
      >
        <div className="space-y-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          {/* Time Range */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Calendar className="w-4 h-4 inline mr-1" />
              Período
            </label>
            <div className="flex flex-wrap gap-2">
              {timeRanges.map((range) => (
                <Button
                  key={range.value}
                  variant={filters.timeRange === range.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updateFilters({ timeRange: range.value as any })}
                  className="text-xs"
                >
                  {range.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Categories */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Tag className="w-4 h-4 inline mr-1" />
              Categorias
            </label>
            <div className="flex flex-wrap gap-2">
              <Button
                variant={!filters.category ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateFilters({ category: '' })}
                className="text-xs"
              >
                Todas
              </Button>
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={filters.category === category ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updateFilters({ category: filters.category === category ? '' : category })}
                  className="text-xs"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};
