export interface ICDSEOData {
  title: string;
  description: string;
  slug: string;
  keywords: string[];
  clinicalUse: string;
  features: string[];
  benefits: string[];
  clinicalSignificance: string;
  relatedTopics: string[];
  searchType?: 'main' | 'category' | 'specific';
  categoryName?: string;
  icdCode?: string;
}

export const ICD_SEO_DATA: Record<string, ICDSEOData> = {
  'main': {
    title: 'CID-10',
    description: 'Busca rápida e eficiente de códigos CID-10 para pediatria com classificação internacional de doenças',
    slug: 'main',
    keywords: ['cid-10', 'códigos cid', 'classificação doenças', 'busca cid', 'diagnóstico médico'],
    clinicalUse: 'Ferramenta completa para busca e consulta de códigos CID-10 específicos para pediatria, facilitando a codificação médica e documentação clínica.',
    features: ['Busca por nome da doença', 'Busca por código CID', 'Resultados instantâneos', 'Interface intuitiva', 'Base de dados completa'],
    benefits: ['Codificação rápida', 'Precisão diagnóstica', 'Documentação adequada', 'Conformidade regulatória'],
    clinicalSignificance: 'Essencial para codificação médica precisa e documentação clínica adequada em pediatria.',
    relatedTopics: ['classificação internacional doenças', 'codificação médica', 'documentação clínica', 'diagnóstico pediátrico', 'prontuário médico'],
    searchType: 'main'
  }
};
