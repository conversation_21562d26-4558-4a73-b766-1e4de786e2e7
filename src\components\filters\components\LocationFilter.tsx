import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";

interface LocationFilterProps {
  locations: { id: string; name: string }[];
  selectedLocations: string[];
  onToggleLocation: (id: string) => void;
  questionCounts: {
    totalCounts: { [key: string]: number };
    filteredCounts: { [key: string]: number };
  };
  hasActiveFilters: boolean;
}

export const LocationFilter = ({
  locations,
  selectedLocations,
  onToggleLocation,
  questionCounts,
  hasActiveFilters
}: LocationFilterProps) => {
  const getLocationCount = (locationId: string) => {
    // Always show filtered counts if there are active filters
    if (hasActiveFilters) {
      return questionCounts.filteredCounts[locationId] || 0;
    }
    return questionCounts.totalCounts[locationId] || 0;
  };

  const availableLocations = locations
    .map(location => ({
      ...location,
      count: getLocationCount(location.id)
    }))
    .filter(location => location.count > 0)
    .sort((a, b) => b.count - a.count);

  return (
    <div className="space-y-2">
      {availableLocations.map((location) => (
        <div
          key={location.id}
          className="flex items-center justify-between p-2 hover:bg-secondary/50 rounded-lg cursor-pointer"
          onClick={() => onToggleLocation(location.id)}
        >
          <div className="flex items-center gap-2">
            <Checkbox 
              checked={selectedLocations.includes(location.id)}
              onCheckedChange={() => onToggleLocation(location.id)}
            />
            <span>{location.name}</span>
          </div>
          <Badge 
            variant={selectedLocations.includes(location.id) ? "default" : "secondary"}
          >
            {location.count}
          </Badge>
        </div>
      ))}
      {availableLocations.length === 0 && (
        <p className="text-center text-gray-500 py-4">
          Nenhuma instituição encontrada com questões disponíveis para os filtros selecionados
        </p>
      )}
    </div>
  );
};