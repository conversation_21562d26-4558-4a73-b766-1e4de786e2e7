
import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useNavigate } from 'react-router-dom';
import { SessionSummary } from '@/components/study-session/SessionSummary';
import { ArrowRight, Home, RotateCcw, ExternalLink, Sparkles } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import type { QuestionStats } from '@/types/question';

interface ResultsContainerProps {
  stats: QuestionStats;
}

export const ResultsContainer = ({ stats }: ResultsContainerProps) => {
  const navigate = useNavigate();
  const answeredQuestions = stats.correct_answers + stats.incorrect_answers;
  const accuracy = answeredQuestions > 0
    ? Math.round((stats.correct_answers / answeredQuestions) * 100)
    : 0;



  return (
    <div className="container mx-auto px-3 md:px-4 py-4 md:py-8 space-y-4 md:space-y-8 max-w-4xl">
      {/* Header Section - Melhorado para mobile */}
      <div className="rounded-xl bg-gradient-to-r from-primary/5 via-primary/20 to-primary/5 backdrop-blur-sm border border-primary/20 p-4 md:p-8 text-center shadow-md">
        <h1 className="text-2xl md:text-3xl font-bold gradient-text bg-gradient-to-r from-primary to-violet-500 bg-clip-text text-transparent">
          Parabéns!
        </h1>
        <p className="text-gray-600 mt-2 text-base md:text-lg">
          Você completou esta sessão de estudos
        </p>
      </div>

      {/* Performance Overview - Melhorado para mobile */}
      <Card className="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden animate-fade-in-up">
        <CardContent className="p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-bold text-gray-800 mb-4 md:mb-6 text-center border-b pb-3">Desempenho Geral</h2>

          <div className="space-y-4 md:space-y-8">
            {/* Taxa de Acerto com visual aprimorado */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 md:p-5 rounded-lg border border-blue-100">
              <div className="flex flex-col items-center mb-3">
                <span className="uppercase text-xs font-semibold tracking-wider text-blue-500 mb-1">Taxa de Acerto</span>
                <div className="relative">
                  <span className="text-3xl md:text-4xl font-bold text-blue-600">{accuracy}%</span>
                  {accuracy >= 70 && (
                    <span className="absolute -top-1 -right-4 md:-right-6 text-yellow-500">★</span>
                  )}
                </div>
              </div>

              <Progress
                value={accuracy}
                className="h-3 bg-blue-100"
                style={{
                  background: 'linear-gradient(to right, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1))'
                }}
              />

              <div className="flex justify-between mt-3 text-xs md:text-sm">
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 rounded-full bg-green-400"></div>
                  <span className="text-gray-600">Corretas: {stats.correct_answers}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 rounded-full bg-red-400"></div>
                  <span className="text-gray-600">Incorretas: {stats.incorrect_answers}</span>
                </div>
              </div>
            </div>

            {/* Tempo médio estilizado */}
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 md:p-5 rounded-lg border border-purple-100">
              <div className="flex flex-col items-center">
                <span className="uppercase text-xs font-semibold tracking-wider text-purple-500 mb-1">Tempo Médio por Questão</span>
                <div className="flex items-center space-x-2">
                  <span className="text-2xl md:text-3xl font-bold text-purple-600">{Math.round(stats.time_spent / answeredQuestions)}</span>
                  <span className="text-sm text-purple-500 mt-1">segundos</span>
                </div>
              </div>

              <div className="h-2 w-full bg-purple-100 rounded-full mt-4 overflow-hidden">
                <div
                  className="h-full bg-purple-400 rounded-full"
                  style={{
                    width: `${Math.min(100, (Math.round(stats.time_spent / answeredQuestions) / 60) * 100)}%`,
                    transition: 'width 1s ease-in-out'
                  }}
                ></div>
              </div>

              <p className="text-xs text-center text-gray-500 mt-3">
                {Math.round(stats.time_spent / answeredQuestions) < 30
                  ? "Excelente tempo de resposta!"
                  : Math.round(stats.time_spent / answeredQuestions) < 45
                    ? "Bom tempo de resposta!"
                    : "Continue praticando para melhorar seu tempo!"}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Stats - Melhorado para mobile */}
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4 md:p-6 space-y-4 md:space-y-8 animate-fade-in-up">
        <h2 className="text-lg md:text-xl font-bold text-gray-800 mb-4">Análise Detalhada</h2>
        <SessionSummary
          stats={{
            correct_answers: stats.correct_answers,
            incorrect_answers: stats.incorrect_answers,
            time_spent: stats.time_spent,
            by_specialty: stats.by_specialty,
            by_theme: stats.by_theme,
            by_focus: stats.by_focus
          }}
          totalQuestions={answeredQuestions}
        />
      </div>

      {/* Seção de Navegação Melhorada para mobile */}
      <div className="pt-4 md:pt-6">
        <Card className="bg-gradient-to-br from-blue-50 via-white to-purple-50 border-2 border-blue-100">
          <CardContent className="p-4 md:p-8">
            <div className="text-center mb-4 md:mb-6">
              <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
                <Sparkles className="h-5 w-5 md:h-6 md:w-6 text-blue-500" />
                <h3 className="text-lg md:text-2xl font-bold">Continue Sua Jornada de Estudos</h3>
                <Sparkles className="h-5 w-5 md:h-6 md:w-6 text-purple-500" />
              </div>
              <p className="text-gray-600 text-sm md:text-lg">
                Escolha como deseja prosseguir com seus estudos em pediatria
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-4">
              {/* Continuar Estudos */}
              <Button
                onClick={() => navigate('/estudos')}
                className="group bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 transition-all duration-300 text-white shadow-lg hover:shadow-xl p-4 md:p-6 h-auto flex-col gap-2 md:gap-3 rounded-xl"
              >
                <RotateCcw className="h-6 w-6 md:h-8 md:w-8 transition-transform group-hover:rotate-12" />
                <div className="text-center">
                  <div className="font-bold text-base md:text-lg">Fazer Mais Questões</div>
                  <div className="text-blue-100 text-xs md:text-sm">Continue praticando pediatria</div>
                </div>
                <ArrowRight className="h-3 w-3 md:h-4 md:w-4 transition-transform group-hover:translate-x-1" />
              </Button>

              {/* Ir para Início */}
              <Button
                onClick={() => navigate('/')}
                variant="outline"
                className="group border-2 border-gray-200 hover:border-blue-300 bg-white hover:bg-blue-50 transition-all duration-300 p-4 md:p-6 h-auto flex-col gap-2 md:gap-3 rounded-xl"
              >
                <Home className="h-6 w-6 md:h-8 md:w-8 text-gray-600 group-hover:text-blue-600 transition-colors" />
                <div className="text-center">
                  <div className="font-bold text-base md:text-lg text-gray-800">Página Inicial</div>
                  <div className="text-gray-500 text-xs md:text-sm">Voltar ao PedBook</div>
                </div>
                <ArrowRight className="h-3 w-3 md:h-4 md:w-4 text-gray-400 group-hover:text-blue-600 transition-all group-hover:translate-x-1" />
              </Button>

              {/* MedEvo BETA */}
              <Button
                onClick={() => window.open('https://medevo.com.br', '_blank')}
                className="group bg-gradient-to-r from-purple-500 to-violet-600 hover:from-purple-600 hover:to-violet-700 transition-all duration-300 text-white shadow-lg hover:shadow-xl p-4 md:p-6 h-auto flex-col gap-2 md:gap-3 rounded-xl"
              >
                <ExternalLink className="h-6 w-6 md:h-8 md:w-8 transition-transform group-hover:scale-110" />
                <div className="text-center">
                  <div className="font-bold text-base md:text-lg">MedEvo BETA</div>
                  <div className="text-purple-100 text-xs md:text-sm">Plataforma completa</div>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-xs">Abrir</span>
                  <ExternalLink className="h-3 w-3" />
                </div>
              </Button>
            </div>

            {/* Mensagem motivacional - Melhorada para mobile */}
            <div className="mt-4 md:mt-6 p-3 md:p-4 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg">
              <p className="text-center text-amber-800 font-medium text-sm md:text-base">
                🎯 <strong>Dica:</strong> A prática constante é a chave para o sucesso na residência médica!
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
