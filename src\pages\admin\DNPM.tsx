import { useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { DNPMForm } from "@/components/admin/dnpm/DNPMForm";
import { DNPMList } from "@/components/admin/dnpm/DNPMList";

const AdminDNPM = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: milestones, isLoading: isLoadingMilestones } = useQuery({
    queryKey: ["dnpm-milestones"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_dnpm_milestones")
        .select("*")
        .order("age_months", { ascending: true });

      if (error) throw error;
      return data;
    },
  });

  const handleSubmit = async (formData: any) => {
    setIsLoading(true);

    try {
      const { error } = await supabase
        .from("pedbook_dnpm_milestones")
        .insert({
          age_type: formData.ageType,
          age_years: formData.ageType === "years" ? formData.ageYears : null,
          age_months: formData.ageType === "months" ? formData.ageMonths : (formData.ageYears || 0) * 12 + formData.ageMonths,
          social_emotional: formData.socialEmotional,
          language_communication: formData.languageCommunication,
          cognition: formData.cognition,
          motor_physical: formData.motorPhysical,
          image_url: formData.imageUrl,
        });

      if (error) throw error;

      toast({
        title: "Marco DNPM criado com sucesso!",
        description: "O novo marco foi adicionado ao sistema.",
      });

      queryClient.invalidateQueries({ queryKey: ["dnpm-milestones"] });
      setIsDialogOpen(false);
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao criar marco DNPM",
        description: error.message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      const { error } = await supabase
        .from("pedbook_dnpm_milestones")
        .delete()
        .eq("id", id);

      if (error) throw error;

      toast({
        title: "Marco DNPM removido com sucesso!",
        description: "O marco foi removido do sistema.",
      });

      queryClient.invalidateQueries({ queryKey: ["dnpm-milestones"] });
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao remover marco DNPM",
        description: error.message,
      });
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Marcos DNPM</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>Novo Marco DNPM</Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Cadastro de Marco DNPM</DialogTitle>
            </DialogHeader>
            <DNPMForm onSubmit={handleSubmit} isLoading={isLoading} />
          </DialogContent>
        </Dialog>
      </div>

      <DNPMList 
        milestones={milestones || []} 
        isLoading={isLoadingMilestones}
        onDelete={handleDelete}
      />
    </div>
  );
};

export default AdminDNPM;