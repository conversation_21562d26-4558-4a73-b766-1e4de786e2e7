
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Plus, Search, Pencil } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { MedicationDialog } from "@/components/admin/MedicationDialog";
import { supabase } from "@/integrations/supabase/client";
import { getThemeClasses } from "@/components/ui/theme-utils";

export default function Medications() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedMedication, setSelectedMedication] = useState<any>(null);
  const [showDialog, setShowDialog] = useState(false);

  const { data: categories } = useQuery({
    queryKey: ["medication-categories"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_medication_categories")
        .select("*")
        .order("name");
      
      if (error) throw error;
      return data;
    },
  });

  const { data: medications } = useQuery({
    queryKey: ["medications"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_medications")
        .select(`
          *,
          pedbook_medication_categories (
            name
          )
        `)
        .order("name");
      
      if (error) throw error;
      return data;
    },
  });

  const filteredMedications = medications?.filter(medication =>
    medication.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    medication.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    medication.brands?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    medication.pedbook_medication_categories?.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className={getThemeClasses.pageBackground("container mx-auto py-8")}>
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-2xl font-bold dark:text-gray-100">Medicamentos</h1>
        
        <Button onClick={() => {
          setSelectedMedication(null);
          setShowDialog(true);
        }}>
          <Plus className="mr-2 h-4 w-4" />
          Novo Medicamento
        </Button>
      </div>

      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={18} />
        <Input
          type="search"
          placeholder="Pesquisar medicamentos..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 dark:bg-slate-800 dark:border-slate-700 dark:text-gray-100"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredMedications?.map((medication) => (
          <div
            key={medication.id}
            className={getThemeClasses.card("p-6 space-y-2")}
          >
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-semibold dark:text-gray-100">{medication.name}</h3>
                <span className="inline-block px-2 py-1 rounded-full text-xs bg-primary/10 text-primary dark:bg-blue-900/30 dark:text-blue-400 mt-2">
                  {medication.pedbook_medication_categories?.name || 'Sem categoria'}
                </span>
                {medication.description && (
                  <p className="text-gray-600 dark:text-gray-300 mt-2">{medication.description}</p>
                )}
                {medication.brands && (
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{medication.brands}</p>
                )}
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setSelectedMedication(medication);
                  setShowDialog(true);
                }}
                className="dark:text-gray-300 dark:hover:text-white"
              >
                <Pencil className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}
      </div>

      <MedicationDialog
        medication={selectedMedication}
        categories={categories || []}
        isOpen={showDialog}
        onClose={() => {
          setShowDialog(false);
          setSelectedMedication(null);
        }}
      />
    </div>
  );
}
