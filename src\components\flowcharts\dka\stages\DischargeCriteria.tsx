import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";

interface DischargeCriteriaProps {
  onComplete: () => void;
}

export const DischargeCriteria = ({ onComplete }: DischargeCriteriaProps) => {
  const [checkedItems, setCheckedItems] = useState<string[]>([]);

  const criteria = [
    "Resolução da cetoacidose",
    "Controle glicêmico adequado",
    "Orientações de seguimento com endocrinologista fornecidas",
    "Paciente e família orientados sobre sinais de alerta"
  ];

  const handleCheck = (item: string) => {
    setCheckedItems(prev => 
      prev.includes(item) 
        ? prev.filter(i => i !== item)
        : [...prev, item]
    );
  };

  return (
    <Card className="p-6 space-y-6">
      <h3 className="text-lg font-semibold">Critérios para Alta Hospitalar</h3>

      <div className="space-y-4">
        {criteria.map((item) => (
          <div key={item} className="flex items-center space-x-2">
            <Checkbox
              id={item}
              checked={checkedItems.includes(item)}
              onCheckedChange={() => handleCheck(item)}
            />
            <label htmlFor={item} className="text-sm">{item}</label>
          </div>
        ))}
      </div>

      <Alert className="bg-yellow-50 border-yellow-200">
        <AlertDescription>
          <p className="text-yellow-800">
            O paciente só deve receber alta após resolução completa da cetoacidose
            e estabelecimento de controle glicêmico adequado.
          </p>
        </AlertDescription>
      </Alert>

      <Button 
        onClick={onComplete}
        className="w-full"
        disabled={checkedItems.length < criteria.length}
      >
        Finalizar
      </Button>
    </Card>
  );
};