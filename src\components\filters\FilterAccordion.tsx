import { Badge } from "@/components/ui/badge";
import { ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";

interface FilterAccordionProps {
  title: string;
  count?: number;
  isOpen: boolean;
  onToggle: () => void;
  children: React.ReactNode;
}

export const FilterAccordion = ({
  title,
  count,
  isOpen,
  onToggle,
  children
}: FilterAccordionProps) => {
  return (
    <div className="border rounded-lg mb-2 overflow-hidden bg-white shadow-sm hover:shadow-md transition-all duration-200">
      <button
        onClick={onToggle}
        className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-primary-light/50 to-accent-blue/30 hover:from-primary-light hover:to-accent-blue/40 transition-all duration-300"
      >
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-full bg-white/80 shadow-sm">
            {title === "Especialidades" && (
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                <path d="M4.8 2.3A.3.3 0 1 0 5 2H4a2 2 0 0 0-2 2v5a6 6 0 0 0 6 6v0a6 6 0 0 0 6-6V4a2 2 0 0 0-2-2h-1a.2.2 0 1 0 .3.3"/>
                <path d="M8 15v1a6 6 0 0 0 6 6v0a6 6 0 0 0 6-6v-4"/>
                <circle cx="20" cy="10" r="2"/>
              </svg>
            )}
            {title === "Instituições" && (
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                <path d="M3 21h18"/>
                <path d="M5 21V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16"/>
                <path d="M9 21v-4a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v4"/>
              </svg>
            )}
            {title === "Anos" && (
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                <rect width="18" height="18" x="3" y="4" rx="2" ry="2"/>
                <line x1="16" x2="16" y1="2" y2="6"/>
                <line x1="8" x2="8" y1="2" y2="6"/>
                <line x1="3" x2="21" y1="10" y2="10"/>
              </svg>
            )}
          </div>
          <span className="font-medium text-lg text-gray-800">{title}</span>
          {count !== undefined && count > 0 && (
            <Badge 
              variant="default" 
              className="bg-primary/10 text-primary hover:bg-primary/20 transition-colors ml-2"
            >
              {count}
            </Badge>
          )}
        </div>
        <ChevronDown 
          className={cn(
            "h-5 w-5 text-gray-500 transition-transform duration-300",
            isOpen && "transform rotate-180"
          )}
        />
      </button>
      
      <div 
        className={cn(
          "transition-all duration-300 ease-in-out",
          isOpen ? "max-h-[500px] opacity-100" : "max-h-0 opacity-0"
        )}
      >
        <div className="p-4 border-t bg-white">
          {children}
        </div>
      </div>
    </div>
  );
};