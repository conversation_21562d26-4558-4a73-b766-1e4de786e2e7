
import { motion } from "framer-motion";
import { Calendar, Clock, Plus } from "lucide-react";
import type { StudyTopic } from "@/types/study-schedule";
import { TopicCard } from "./TopicCard";
import { Button } from "@/components/ui/button";
import { parseDurationToMinutes, sumDurations } from "@/utils/formatTime";
import { useEffect, useState } from "react";

interface DayCardProps {
  day: string;
  topics: StudyTopic[];
  totalHours: number;
  onAddTopic?: (day: string) => void;
}

export const DayCard = ({ day, topics, totalHours, onAddTopic }: DayCardProps) => {
  const [calculatedTotalHours, setCalculatedTotalHours] = useState<string>("0:00");
  
  // Recalculate total hours whenever topics change
  useEffect(() => {
    if (topics && topics.length > 0) {
      const totalDuration = sumDurations(topics.map(topic => topic.duration || "0:00"));
      console.log(`📊 [DayCard] Calculated total for ${day}:`, {
        topics: topics.length,
        durations: topics.map(topic => topic.duration),
        total: totalDuration
      });
      setCalculatedTotalHours(totalDuration);
    } else {
      setCalculatedTotalHours("0:00");
    }
  }, [topics, day]);
  
  const getDayInitial = (day: string) => {
    return day.substring(0, 1).toUpperCase();
  };

  const getDayColor = (day: string) => {
    switch(day) {
      case 'Segunda-feira': return 'bg-blue-100 text-blue-600';
      case 'Terça-feira': return 'bg-purple-100 text-purple-600';
      case 'Quarta-feira': return 'bg-green-100 text-green-600';
      case 'Quinta-feira': return 'bg-yellow-100 text-yellow-600';
      case 'Sexta-feira': return 'bg-red-100 text-red-600';
      case 'Sábado': return 'bg-orange-100 text-orange-600';
      case 'Domingo': return 'bg-pink-100 text-pink-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const handleAddTopic = () => {
    if (onAddTopic) {
      console.log("🔘 Add topic button clicked for day:", day);
      onAddTopic(day);
    }
  };

  // Ordenar os tópicos por horário de início para exibição cronológica
  const sortedTopics = [...topics].sort((a, b) => {
    if (!a.startTime || !b.startTime) return 0;
    return a.startTime.localeCompare(b.startTime);
  });

  return (
    <motion.div
      whileHover={{ scale: 1.005 }}
      className="rounded-xl border border-gray-100 shadow-sm bg-white p-4 hover:shadow-md transition-all w-full"
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getDayColor(day)}`}>
            <span className="text-sm font-bold">
              {getDayInitial(day)}
            </span>
          </div>
          <h4 className="font-medium text-gray-700">{day}</h4>
        </div>
        <div className="flex items-center gap-2 text-sm bg-gray-50 px-2.5 py-1 rounded-full">
          <Clock className="h-3.5 w-3.5 text-gray-500" />
          <span className="font-medium text-gray-600">{calculatedTotalHours}</span>
        </div>
      </div>
      
      {sortedTopics.length > 0 ? (
        <div className="space-y-3">
          {sortedTopics.map((topic, index) => (
            <TopicCard key={index} {...topic} />
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center py-4 text-center bg-gray-50 rounded-lg mb-3">
          <p className="text-gray-500 text-sm">Nenhum estudo agendado</p>
        </div>
      )}
      
      {/* Add topic button placed at bottom */}
      {onAddTopic && (
        <div className="mt-3">
          <Button 
            variant="ghost" 
            className="w-full border border-dashed border-gray-200 hover:border-[#1CB0F6] hover:bg-[#1CB0F6]/5 text-gray-500 hover:text-[#1CB0F6] transition-colors"
            onClick={handleAddTopic}
          >
            <Plus className="w-4 h-4 mr-2" />
            Adicionar tópico
          </Button>
        </div>
      )}
    </motion.div>
  );
};
