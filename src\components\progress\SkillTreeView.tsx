import React from 'react';
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { ChevronR<PERSON>, Brain, Target, <PERSON>hair } from "lucide-react";

interface SkillNode {
  name: string;
  correct: number;
  total: number;
  children?: Record<string, SkillNode>;
}

interface SkillTreeViewProps {
  bySpecialty: Record<string, { name: string; correct: number; total: number }>;
  byTheme: Record<string, { name: string; correct: number; total: number }>;
  byFocus: Record<string, { name: string; correct: number; total: number }>;
}

export const SkillTreeView = ({ bySpecialty, byTheme, byFocus }: SkillTreeViewProps) => {
  const calculateAccuracy = (correct: number, total: number) => {
    return total > 0 ? (correct / total) * 100 : 0;
  };

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold flex items-center gap-2">
        <Brain className="h-5 w-5" />
        <PERSON><PERSON><PERSON><PERSON> de Habilidades
      </h3>

      <div className="space-y-4">
        {Object.entries(bySpecialty).map(([specialtyId, specialty]) => (
          <div key={specialtyId} className="space-y-2">
            <div className="bg-secondary/20 p-4 rounded-lg">
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center gap-2">
                  <Brain className="h-4 w-4" />
                  <span className="font-medium">{specialty.name}</span>
                </div>
                <span className="text-sm text-gray-600">
                  {specialty.correct} de {specialty.total} ({calculateAccuracy(specialty.correct, specialty.total).toFixed(1)}%)
                </span>
              </div>
              <Progress value={calculateAccuracy(specialty.correct, specialty.total)} className="h-2" />
            </div>

            <div className="ml-6 space-y-3">
              {Object.entries(byTheme).map(([themeId, theme]) => (
                <div key={themeId} className="space-y-2">
                  <div className="bg-secondary/10 p-3 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <div className="flex items-center gap-2">
                        <Target className="h-4 w-4" />
                        <span className="font-medium">{theme.name}</span>
                      </div>
                      <span className="text-sm text-gray-600">
                        {theme.correct} de {theme.total} ({calculateAccuracy(theme.correct, theme.total).toFixed(1)}%)
                      </span>
                    </div>
                    <Progress value={calculateAccuracy(theme.correct, theme.total)} className="h-2" />
                  </div>

                  <div className="ml-6 space-y-2">
                    {Object.entries(byFocus).map(([focusId, focus]) => (
                      <div key={focusId} className="bg-secondary/5 p-3 rounded-lg">
                        <div className="flex justify-between items-center mb-2">
                          <div className="flex items-center gap-2">
                            <Crosshair className="h-4 w-4" />
                            <span className="font-medium">{focus.name}</span>
                          </div>
                          <span className="text-sm text-gray-600">
                            {focus.correct} de {focus.total} ({calculateAccuracy(focus.correct, focus.total).toFixed(1)}%)
                          </span>
                        </div>
                        <Progress value={calculateAccuracy(focus.correct, focus.total)} className="h-2" />
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};