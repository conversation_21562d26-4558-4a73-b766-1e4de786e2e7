import React from 'react'
import ReactDOMServer from 'react-dom/server'
import { StaticRouter } from 'react-router-dom/server'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { supabase } from '@/integrations/supabase/client'
import App from './App'

export async function fetchPageData(url: string) {
  // Extract slug from URL for medicine pages
  const match = url.match(/\/medicamentos\/(.+)/)
  if (match) {
    const slug = match[1]
    try {
      const { data: medicine, error } = await supabase
        .from('pedbook_medications')
        .select(`
          *,
          category:pedbook_medication_categories(*),
          use_cases:pedbook_medication_use_cases(*),
          dosages:pedbook_medication_dosages(*)
        `)
        .eq('slug', slug)
        .maybeSingle()
      
      if (error) {
        console.error('Error fetching medicine:', error)
        return {}
      }
      
      return { medicine }
    } catch (error) {
      console.error('Error in fetchPageData:', error)
      return {}
    }
  }
  
  return {}
}

export function render(url: string, pageData: any) {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        refetchOnWindowFocus: false
      }
    }
  })
  
  // Pre-populate the query cache with the fetched data
  if (pageData.medicine) {
    queryClient.setQueryData(['medicine', pageData.medicine.slug], pageData.medicine)
  }

  // Generate meta tags based on the page data
  let head = `
    <title>PedBook - Calculadora Pediátrica e Doses Pediátricas</title>
    <meta name="description" content="Calculadora pediátrica e doses pediátricas completas para profissionais da saúde. Faça cálculos de doses, prescrições e acesse fluxogramas clínicos com precisão." />
  `

  if (pageData.medicine) {
    const description = pageData.medicine.description || 'Informações detalhadas sobre dosagem, indicações e contraindicações.'
    head = `
      <title>${pageData.medicine.name} | PedBook</title>
      <meta name="description" content="${description}" />
      <meta property="og:title" content="${pageData.medicine.name} | PedBook" />
      <meta property="og:description" content="${description}" />
      <meta property="og:image" content="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/1496949.webp" />
      <meta property="og:image:alt" content="Ícone de medicamento do PedBook" />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
    `
  }

  const html = ReactDOMServer.renderToString(
    <React.StrictMode>
      <QueryClientProvider client={queryClient}>
        <StaticRouter location={url}>
          <App />
        </StaticRouter>
      </QueryClientProvider>
    </React.StrictMode>
  )

  return { html, head }
}