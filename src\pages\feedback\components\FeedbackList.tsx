import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Card } from "@/components/ui/card";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MessageSquare } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { StatusToggle } from "./StatusToggle";

interface Feedback {
  id: string;
  type_id: string;
  message: string;
  status: string;
  created_at: string;
  whatsapp: string | null;
  title: string;
  responses: FeedbackResponse[];
}

interface FeedbackResponse {
  id: string;
  message: string;
  created_at: string;
  user: {
    full_name: string;
    is_admin: boolean;
  };
}

export function FeedbackList() {
  const [feedbacks, setFeedbacks] = useState<Feedback[]>([]);
  const [loading, setLoading] = useState(true);
  const [newResponses, setNewResponses] = useState<{ [key: string]: string }>({});
  const { toast } = useToast();
  const { user } = useAuth();

  const fetchFeedbacks = async () => {
    try {
      const { data, error } = await supabase
        .from("pedbook_feedbacks")
        .select(`
          *,
          responses:pedbook_feedback_responses(
            id,
            message,
            created_at,
            user:profiles!pedbook_feedback_responses_user_profiles_fkey(
              full_name,
              is_admin
            )
          )
        `)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching feedbacks:", error);
        toast({
          title: "Erro ao carregar feedbacks",
          description: error.message,
          variant: "destructive",
        });
        return;
      }

      if (data) {
        // Transform the data to match our interface
        const transformedData = data.map(feedback => ({
          ...feedback,
          responses: feedback.responses.map(response => ({
            ...response,
            user: response.user || { full_name: "Usuário", is_admin: false }
          }))
        }));
        setFeedbacks(transformedData);
      }
    } catch (error: any) {
      console.error("Error in fetchFeedbacks:", error);
      toast({
        title: "Erro ao carregar feedbacks",
        description: "Ocorreu um erro ao carregar os feedbacks",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFeedbacks();
  }, []);

  const handleSendResponse = async (feedbackId: string) => {
    if (!user) return;
    
    const message = newResponses[feedbackId];
    if (!message?.trim()) return;

    try {
      const { error } = await supabase
        .from("pedbook_feedback_responses")
        .insert({
          feedback_id: feedbackId,
          message: message.trim(),
          user_id: user.id
        });

      if (error) throw error;

      toast({
        title: "Resposta enviada",
        description: "Sua resposta foi enviada com sucesso!"
      });

      setNewResponses(prev => ({ ...prev, [feedbackId]: "" }));
      fetchFeedbacks();
    } catch (error: any) {
      console.error("Error sending response:", error);
      toast({
        title: "Erro ao enviar resposta",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const handleStatusChange = (feedbackId: string, newStatus: string) => {
    setFeedbacks(prevFeedbacks =>
      prevFeedbacks.map(feedback =>
        feedback.id === feedbackId
          ? { ...feedback, status: newStatus }
          : feedback
      )
    );
  };

  if (loading) {
    return <div>Carregando...</div>;
  }

  if (feedbacks.length === 0) {
    return <p className="text-center text-gray-500">Nenhum feedback encontrado.</p>;
  }

  return (
    <div className="space-y-4">
      {feedbacks.map((feedback) => (
        <Card key={feedback.id} className="p-4">
          <div className="space-y-4">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-medium">{feedback.title}</h3>
                <p className="text-sm text-gray-600 mt-1">{feedback.message}</p>
                {feedback.whatsapp && (
                  <p className="text-sm text-gray-500 mt-1">WhatsApp: {feedback.whatsapp}</p>
                )}
              </div>
              <StatusToggle
                feedbackId={feedback.id}
                currentStatus={feedback.status}
                onStatusChange={(newStatus) => handleStatusChange(feedback.id, newStatus)}
              />
            </div>

            <div className="border-t pt-4 mt-4">
              <h4 className="font-medium mb-2">Respostas</h4>
              <ScrollArea className="h-[200px] rounded-md border p-4">
                <div className="space-y-4">
                  {feedback.responses?.map((response) => (
                    <div key={response.id} className="bg-gray-50 p-3 rounded-lg">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{response.user.full_name}</span>
                        {response.user.is_admin && (
                          <Badge variant="default" className="bg-primary">Admin</Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-600">{response.message}</p>
                    </div>
                  ))}
                </div>
              </ScrollArea>

              <div className="mt-4 space-y-2">
                <Textarea
                  placeholder="Digite sua resposta..."
                  value={newResponses[feedback.id] || ""}
                  onChange={(e) => setNewResponses(prev => ({
                    ...prev,
                    [feedback.id]: e.target.value
                  }))}
                />
                <Button
                  onClick={() => handleSendResponse(feedback.id)}
                  disabled={!newResponses[feedback.id]?.trim()}
                >
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Enviar Resposta
                </Button>
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}