import { useAuth } from "@/hooks/useAuth";
import { FeedbackForm } from "./components/FeedbackForm";
import { FeedbackList } from "./components/FeedbackList";
import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { UserFeedbackList } from "./components/UserFeedbackList";

export function FeedbackPage() {
  const { user } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const isMobile = window.innerWidth < 768;

  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user) return;

      const { data } = await supabase
        .from('secure_profiles')
        .select('is_admin')
        .eq('id', user.id)
        .single();

      setIsAdmin(data?.is_admin || false);
    };

    checkAdminStatus();
  }, [user]);

  if (!user) {
    return (
      <div className="p-6 text-center">
        <p>Faça login para enviar um feedback.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-4 space-y-4 mobile-feedback-container">
      <Tabs defaultValue="new" className="space-y-4">
        <TabsList className="w-full justify-start overflow-x-auto mobile-tabs-list">
          <TabsTrigger value="new">Novo Feedback</TabsTrigger>
          <TabsTrigger value="history">Meus Feedbacks</TabsTrigger>
          {isAdmin && <TabsTrigger value="all">Todos os Feedbacks</TabsTrigger>}
        </TabsList>

        <TabsContent value="new">
          <FeedbackForm />
        </TabsContent>

        <TabsContent value="history">
          <UserFeedbackList />
        </TabsContent>

        {isAdmin && (
          <TabsContent value="all">
            <FeedbackList />
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
