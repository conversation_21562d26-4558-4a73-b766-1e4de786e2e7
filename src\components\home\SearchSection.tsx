
import React, { memo } from "react";
import { SearchBar } from "@/components/search/SearchBar";

interface SearchSectionProps {
  searchPlaceholder: string;
}

export const SearchSection = memo<SearchSectionProps>(({ searchPlaceholder }) => {
  return (
    <div className="w-full max-w-6xl mx-auto relative z-10 flex items-center gap-2 px-2">
      <div className="flex-1 relative group">
        <SearchBar customPlaceholder={searchPlaceholder || "Buscar medicamentos, condutas, calculadoras..."} />
      </div>
    </div>
  );
});
