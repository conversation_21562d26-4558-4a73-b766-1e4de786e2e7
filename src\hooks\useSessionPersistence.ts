import { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import type { StudySessionRow } from '@/types/study-session';

export const useSessionPersistence = (userId: string) => {
  const [activeSession, setActiveSession] = useState<StudySessionRow | null>(null);
  const [answeredQuestions, setAnsweredQuestions] = useState<Set<string>>(new Set());
  const { toast } = useToast();

  useEffect(() => {
    const recoverActiveSession = async () => {
      try {
        // Recuperando sessão ativa para userId: ${userId}
        const { data: sessions, error: sessionError } = await supabase
          .from('study_sessions')
          .select('*')
          .eq('user_id', userId)
          .eq('status', 'in_progress')
          .order('started_at', { ascending: false })
          .limit(1);

        if (sessionError) throw sessionError;

        if (!sessions || sessions.length === 0) {
          return;
        }

        const session = sessions[0];

        const { data: events, error: eventsError } = await supabase
          .from('session_events')
          .select('question_id')
          .eq('session_id', session.id);

        if (eventsError) throw eventsError;

        const answered = new Set(events?.map(event => event.question_id) || []);
        setAnsweredQuestions(answered);
        setActiveSession(session);
      } catch (error: any) {
        toast({
          title: "Erro ao recuperar sessão",
          description: error.message,
          variant: "destructive"
        });
      }
    };

    if (userId) {
      recoverActiveSession();
    }
  }, [userId, toast]);

  const markQuestionAsAnswered = (questionId: string) => {
    setAnsweredQuestions(prev => new Set([...prev, questionId]));
  };

  return {
    activeSession,
    setActiveSession,
    answeredQuestions,
    markQuestionAsAnswered
  };
};
