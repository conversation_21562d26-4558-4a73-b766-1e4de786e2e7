import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";

const WEIGHT_KEY = "patient-weight";
const DEFAULT_WEIGHT = 20;

export const useWeight = () => {
  const queryClient = useQueryClient();
  const [tempWeight, setTempWeight] = useState<number | null>(null);

  const { data: weight = DEFAULT_WEIGHT } = useQuery({
    queryKey: [WEIGHT_KEY],
    queryFn: () => {
      const savedWeight = localStorage.getItem(WEIGHT_KEY);
      return savedWeight ? parseFloat(savedWeight) : DEFAULT_WEIGHT;
    },
    staleTime: Infinity,
  });

  const setWeight = (newWeight: number) => {
    setTempWeight(null);
    localStorage.setItem(WEIGHT_KEY, newWeight.toString());
    queryClient.setQueryData([WEIGHT_KEY], newWeight);
  };

  const setTempWeightValue = (value: number) => {
    setTempWeight(value);
  };

  return { 
    weight, 
    setWeight, 
    displayWeight: tempWeight ?? weight,
    setTempWeight: setTempWeightValue,
    commitWeight: setWeight // Add this line
  };
};