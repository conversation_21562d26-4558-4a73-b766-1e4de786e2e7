
import { cn } from "@/lib/utils";

/**
 * Utilitário para aplicar estilos adaptados ao tema (claro/escuro)
 */
export const getThemeClasses = {
  // Headings com gradiente que se adaptam ao tema
  gradientHeading: (className?: string) => 
    cn("font-bold bg-gradient-to-r from-primary to-primary/70 text-transparent bg-clip-text dark:from-blue-400 dark:to-blue-600", className),
  
  // Containers com fundo semitransparente
  glassContainer: (className?: string) => 
    cn("bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-xl border border-primary/10 dark:border-primary/20 shadow-lg", className),
  
  // Cards adaptados ao tema
  card: (className?: string) => 
    cn("bg-white dark:bg-slate-800 shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg", className),
  
  // Campos de texto adaptados ao tema  
  input: (className?: string) =>
    cn("bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-gray-100", className),
  
  // Textos secundários adaptados ao tema
  secondaryText: (className?: string) =>
    cn("text-gray-500 dark:text-gray-400", className),
    
  // Botões com fundo adaptado ao tema
  button: (className?: string) =>
    cn("bg-white dark:bg-slate-800 hover:bg-gray-50 dark:hover:bg-slate-700 border border-gray-200 dark:border-gray-700", className),
    
  // Select e dropdowns adaptados ao tema
  select: (className?: string) =>
    cn("bg-white dark:bg-slate-800 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-gray-100", className),
  
  // Gradientes de fundo adaptados ao tema com melhor contraste em dark mode
  gradientBackground: (className?: string) =>
    cn("bg-gradient-to-b from-white via-primary/5 to-primary/10 dark:from-slate-900 dark:via-slate-800/30 dark:to-slate-800/50", className),
  
  // Cards com gradiente adaptados ao tema
  gradientCard: (colorName: string, className?: string) => {
    const baseClasses = "border border-gray-200 dark:border-gray-700";
    
    const colorMap: Record<string, string> = {
      blue: "from-blue-50 to-white dark:from-blue-900/20 dark:to-slate-900 dark:border-blue-900/30",
      purple: "from-purple-50 to-white dark:from-purple-900/20 dark:to-slate-900 dark:border-purple-900/30",
      green: "from-green-50 to-white dark:from-green-900/20 dark:to-slate-900 dark:border-green-900/30",
      amber: "from-amber-50 to-white dark:from-amber-900/20 dark:to-slate-900 dark:border-amber-900/30",
      red: "from-red-50 to-white dark:from-red-900/20 dark:to-slate-900 dark:border-red-900/30",
      orange: "from-orange-50 to-white dark:from-orange-900/20 dark:to-slate-900 dark:border-orange-900/30",
      yellow: "from-yellow-50 to-white dark:from-yellow-900/20 dark:to-slate-900 dark:border-yellow-900/30",
      indigo: "from-indigo-50 to-white dark:from-indigo-900/20 dark:to-slate-900 dark:border-indigo-900/30",
      pink: "from-pink-50 to-white dark:from-pink-900/20 dark:to-slate-900 dark:border-pink-900/30",
      cyan: "from-cyan-50 to-white dark:from-cyan-900/20 dark:to-slate-900 dark:border-cyan-900/30"
    };
    
    const colorClasses = colorMap[colorName] || colorMap.blue;
    
    return cn(`bg-gradient-to-br ${colorClasses} ${baseClasses}`, className);
  },
  
  // Adicionando novo método para fundos de página
  pageBackground: (className?: string) =>
    cn("min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800", className)
};
