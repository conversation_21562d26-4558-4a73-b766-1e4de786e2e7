
import { FileText } from "lucide-react";
import { motion } from "framer-motion";

interface RecentPrescription {
  id: string;
  name: string;
}

interface RecentPrescriptionsProps {
  prescriptions: RecentPrescription[];
  onSelectPrescription: (id: string) => void;
}

export const RecentPrescriptions = ({ prescriptions, onSelectPrescription }: RecentPrescriptionsProps) => {
  if (!prescriptions || prescriptions.length === 0) return null;

  return (
    <div className="mt-8 pt-8 border-t border-primary/10 dark:border-primary/30">
      <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-200 mb-4">
        Suas Prescrições Recentes
      </h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 max-w-xl mx-auto">
        {prescriptions.map((prescription) => (
          <motion.div
            key={prescription.id}
            whileHover={{ scale: 1.02 }}
            className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-lg p-3 shadow-sm hover:shadow-md transition-all cursor-pointer border border-primary/10 dark:border-primary/30"
            onClick={() => onSelectPrescription(prescription.id)}
          >
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-primary/60 dark:text-blue-400/80 flex-shrink-0" />
              <h4 className="font-medium text-gray-800 dark:text-gray-200 text-sm truncate">
                {prescription.name}
              </h4>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};
