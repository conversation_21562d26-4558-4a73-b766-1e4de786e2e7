
import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface AICommentaryResponse {
  alternativas: {
    texto: string;
    comentario: string;
    correta: boolean;
  }[];
  comentario_final: string;
}

export const useAICommentary = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [commentary, setCommentary] = useState<AICommentaryResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const generateCommentary = async (
    questionId: string,
    statement: string,
    alternatives: string[],
    correctAnswer: number,
    sessionId?: string,
    specialty?: string
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      // Verificar se alternatives é um array válido
      if (!Array.isArray(alternatives)) {
        console.error("📝 [useAICommentary] Alternatives não é um array:", alternatives);
        throw new Error('Formato de alternativas inválido');
      }

      console.log("📝 [useAICommentary] Gerando comentário AI para questão", {
        questionId,
        alternativesCount: alternatives.length,
        alternativesType: typeof alternatives,
        alternatives: alternatives.slice(0, 2), // Primeiras 2 para debug
        specialty: specialty || 'Medicina Geral'
      });

      const cleanStatement = statement.replace(/<[^>]+>/g, '');
      const cleanAlternatives = alternatives.map(alt => {
        if (typeof alt !== 'string') {
          return String(alt).replace(/<[^>]+>/g, '');
        }
        return alt.replace(/<[^>]+>/g, '');
      });

      const { data, error: supabaseError } = await supabase.functions.invoke(
        'question-commentary',
        {
          body: {
            statement: cleanStatement,
            alternatives: cleanAlternatives,
            correctAnswer,
            specialty: specialty || 'Medicina Geral'
          }
        }
      );

      if (supabaseError) {
        throw new Error(`Erro ao gerar comentário: ${supabaseError.message}`);
      }

      if (!data) {
        throw new Error('Nenhum dado recebido da IA');
      }


      setCommentary(data);

      toast({
        title: "Análise gerada",
        description: "A análise da questão foi gerada com sucesso."
      });

      return data;
    } catch (err: any) {
      console.error("📝 [useAICommentary] Erro:", err);
      const errorMessage = err.message || 'Erro ao gerar comentários';
      setError(errorMessage);
      setCommentary(null);

      toast({
        title: "Erro ao gerar análise",
        description: errorMessage,
        variant: "destructive"
      });

      return null;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    generateCommentary,
    commentary,
    isLoading,
    error
  };
};
