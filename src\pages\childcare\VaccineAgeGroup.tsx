
import { motion } from "framer-motion";
import { Calendar, Clock } from "lucide-react";
import { VaccineDose } from "./types";
import { VaccineDoseCard } from "./VaccineDoseCard";

interface VaccineAgeGroupProps {
  age: string;
  doses: VaccineDose[];
  index: number;
}

export function VaccineAgeGroup({ age, doses, index }: VaccineAgeGroupProps) {
  return (
    <motion.div
      className="mb-8 sm:mb-12 relative"
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.15, duration: 0.6, ease: "easeOut" }}
    >
      {/* Timeline Connector - Hidden on mobile */}
      <div className="hidden sm:block absolute left-8 -translate-x-1/2 w-1 h-full bg-gradient-to-b from-emerald-200 via-emerald-300 to-emerald-400 dark:from-emerald-700 dark:via-emerald-600 dark:to-emerald-500 rounded-full opacity-30" />

      {/* Age Badge */}
      <div className="relative mb-6">
        {/* Timeline Badge - Hidden on mobile */}
        <div className="hidden sm:block absolute left-8 -translate-x-1/2 w-6 h-6 bg-gradient-to-br from-emerald-400 via-emerald-500 to-emerald-600 rounded-full shadow-lg border-4 border-white dark:border-slate-900 z-10" />

        <div className="ml-0 sm:ml-20">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: index * 0.15 + 0.2 }}
            className="inline-flex items-center bg-gradient-to-r from-emerald-500 via-emerald-600 to-teal-600 text-white px-6 py-3 rounded-2xl font-bold shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105"
          >
            <Calendar className="w-5 h-5 mr-3" />
            <span className="text-lg">{age}</span>
            <div className="ml-3 bg-white/20 rounded-full px-2 py-1">
              <span className="text-sm font-medium">{doses.length} vacina{doses.length !== 1 ? 's' : ''}</span>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Vaccines Grid */}
      <div className="ml-0 sm:ml-20 space-y-4 sm:space-y-6">
        {doses.map((dose, doseIndex) => (
          <motion.div
            key={dose.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{
              delay: index * 0.15 + doseIndex * 0.1 + 0.3,
              duration: 0.5,
              ease: "easeOut"
            }}
          >
            <VaccineDoseCard dose={dose} />
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
}
