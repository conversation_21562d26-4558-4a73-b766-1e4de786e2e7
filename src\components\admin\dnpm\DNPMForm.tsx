import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ImageUpload } from "./ImageUpload";
import { AgeSelector } from "./AgeSelector";

export const DNPMForm = ({ onSubmit, isLoading }: { 
  onSubmit: (data: any) => void, 
  isLoading: boolean 
}) => {
  const [ageType, setAgeType] = useState<"months" | "years">("months");
  const [ageYears, setAgeYears] = useState<number | null>(null);
  const [ageMonths, setAgeMonths] = useState<number>(0);
  const [socialEmotional, setSocialEmotional] = useState("");
  const [languageCommunication, setLanguageCommunication] = useState("");
  const [cognition, setCognition] = useState("");
  const [motorPhysical, setMotorPhysical] = useState("");
  const [imageUrl, setImageUrl] = useState<string>("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      ageType,
      ageYears,
      ageMonths,
      socialEmotional,
      languageCommunication,
      cognition,
      motorPhysical,
      imageUrl,
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <AgeSelector
        ageType={ageType}
        setAgeType={setAgeType}
        ageYears={ageYears}
        setAgeYears={setAgeYears}
        ageMonths={ageMonths}
        setAgeMonths={setAgeMonths}
      />

      <div>
        <Label htmlFor="socialEmotional">Social e Emocional</Label>
        <Textarea
          id="socialEmotional"
          value={socialEmotional}
          onChange={(e) => setSocialEmotional(e.target.value)}
          className="min-h-[100px]"
        />
      </div>

      <div>
        <Label htmlFor="languageCommunication">Linguagem e Comunicação</Label>
        <Textarea
          id="languageCommunication"
          value={languageCommunication}
          onChange={(e) => setLanguageCommunication(e.target.value)}
          className="min-h-[100px]"
        />
      </div>

      <div>
        <Label htmlFor="cognition">Cognição</Label>
        <Textarea
          id="cognition"
          value={cognition}
          onChange={(e) => setCognition(e.target.value)}
          className="min-h-[100px]"
        />
      </div>

      <div>
        <Label htmlFor="motorPhysical">Motora/Física</Label>
        <Textarea
          id="motorPhysical"
          value={motorPhysical}
          onChange={(e) => setMotorPhysical(e.target.value)}
          className="min-h-[100px]"
        />
      </div>

      <ImageUpload imageUrl={imageUrl} setImageUrl={setImageUrl} />

      <Button type="submit" disabled={isLoading}>
        {isLoading ? "Salvando..." : "Salvar Marco DNPM"}
      </Button>
    </form>
  );
};