import { useEffect, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';

/**
 * Hook para monitorar e renovar automaticamente a sessão do Supabase
 * Previne expiração de JWT e erros de sessão
 */
export const useSessionRefresh = () => {
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastRefreshRef = useRef<number>(0);

  useEffect(() => {
    const setupSessionRefresh = async () => {
      // Verificar se já temos uma sessão ativa
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) return;

      // Função para renovar a sessão
      const refreshSession = async () => {
        try {
          const now = Date.now();
          
          // Evitar refresh muito frequente (mínimo 5 minutos entre refreshes)
          if (now - lastRefreshRef.current < 5 * 60 * 1000) {
            return;
          }

          const { data, error } = await supabase.auth.refreshSession();
          
          if (error) {
            console.warn('Erro ao renovar sessão:', error.message);
            
            // Se o refresh falhar, tentar reautenticar silenciosamente
            const { data: { session: currentSession } } = await supabase.auth.getSession();
            if (!currentSession) {
              // Sessão perdida, redirecionar para login se necessário
              console.warn('Sessão perdida, usuário precisa fazer login novamente');
            }
          } else if (data.session) {
            lastRefreshRef.current = now;
            console.log('Sessão renovada com sucesso');
          }
        } catch (error) {
          console.warn('Erro inesperado ao renovar sessão:', error);
        }
      };

      // Configurar refresh automático a cada 4 horas (14400 segundos)
      // Isso garante que o token seja renovado bem antes de expirar (7 dias)
      refreshIntervalRef.current = setInterval(refreshSession, 4 * 60 * 60 * 1000);

      // Fazer um refresh inicial se a sessão estiver próxima do vencimento
      const tokenExpiresAt = session.expires_at;
      if (tokenExpiresAt) {
        const expiresIn = tokenExpiresAt * 1000 - Date.now();
        const oneHour = 60 * 60 * 1000;
        
        // Se expira em menos de 1 hora, renovar imediatamente
        if (expiresIn < oneHour) {
          refreshSession();
        }
      }
    };

    setupSessionRefresh();

    // Cleanup
    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, []);

  // Monitorar mudanças de visibilidade da página
  useEffect(() => {
    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible') {
        // Quando a página volta a ficar visível, verificar se a sessão ainda é válida
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session) {
          const tokenExpiresAt = session.expires_at;
          if (tokenExpiresAt) {
            const expiresIn = tokenExpiresAt * 1000 - Date.now();
            const thirtyMinutes = 30 * 60 * 1000;
            
            // Se expira em menos de 30 minutos, renovar
            if (expiresIn < thirtyMinutes) {
              try {
                await supabase.auth.refreshSession();
                console.log('Sessão renovada após retorno à página');
              } catch (error) {
                console.warn('Erro ao renovar sessão após retorno:', error);
              }
            }
          }
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Monitorar eventos de foco da janela
  useEffect(() => {
    const handleFocus = async () => {
      // Verificar sessão quando a janela recebe foco
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session) {
        const tokenExpiresAt = session.expires_at;
        if (tokenExpiresAt) {
          const expiresIn = tokenExpiresAt * 1000 - Date.now();
          const fifteenMinutes = 15 * 60 * 1000;
          
          // Se expira em menos de 15 minutos, renovar
          if (expiresIn < fifteenMinutes) {
            try {
              await supabase.auth.refreshSession();
              console.log('Sessão renovada após foco da janela');
            } catch (error) {
              console.warn('Erro ao renovar sessão após foco:', error);
            }
          }
        }
      }
    };

    window.addEventListener('focus', handleFocus);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, []);
};
