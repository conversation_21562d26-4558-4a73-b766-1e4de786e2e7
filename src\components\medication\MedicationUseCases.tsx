import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { DosageDisplay } from "../DosageDisplay";

interface MedicationUseCasesProps {
  useCases: Array<{
    id: string;
    name: string;
    description?: string | null;
    pedbook_medication_dosages: Array<any>;
    display_order: number;
  }>;
  weight: number;
  age: number;
  medicationId?: string; // ID do medicamento principal
}

export const MedicationUseCases = ({ useCases, weight, age, medicationId }: MedicationUseCasesProps) => {
  if (!useCases.length) {
    return (
      <div className="text-center py-4 text-muted-foreground">
        Nenhuma indicação de uso cadastrada
      </div>
    );
  }

  return (
    <Tabs defaultValue={useCases[0]?.id} className="w-full">
      <TabsList className="w-full">
        {useCases.map((useCase) => (
          <TabsTrigger key={useCase.id} value={useCase.id}>
            {useCase.name}
          </TabsTrigger>
        ))}
      </TabsList>

      {useCases.map((useCase) => (
        <TabsContent key={useCase.id} value={useCase.id} className="space-y-3 sm:space-y-6">
          {useCase.description && (
            <p className="text-sm text-muted-foreground">{useCase.description}</p>
          )}
          {useCase.pedbook_medication_dosages?.map((dosage) => (
            <DosageDisplay
              key={dosage.id}
              dosage={{
                ...dosage,
                medication_id: dosage.medication_id || medicationId // Usar o ID do medicamento se não estiver na dosagem
              }}
              weight={weight}
              age={age}
            />
          ))}
        </TabsContent>
      ))}
    </Tabs>
  );
};