import { Dialog, Di<PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import type { PrescriptionWithMedications } from "@/components/prescriptions/types";

interface PrescriptionDetailsProps {
  prescription: PrescriptionWithMedications | null;
  onClose: () => void;
}

export const PrescriptionDetails = ({ prescription, onClose }: PrescriptionDetailsProps) => {
  const cleanTemplateText = (template: string) => {
    return template.replace(/\(\([^)]+\)\)/g, "___");
  };

  if (!prescription) return null;

  return (
    <Dialog open={!!prescription} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{prescription.name}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          {prescription.profiles && (
            <div className="text-sm text-gray-500">
              Criado por: {prescription.profiles.full_name} ({prescription.profiles.formation_area})
            </div>
          )}
          
          {prescription.description && (
            <div className="bg-primary/5 rounded-lg p-4 border border-primary/10">
              <h3 className="text-sm font-medium mb-2">Descrição</h3>
              <p className="text-sm text-gray-600">{prescription.description}</p>
            </div>
          )}
          
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-r-lg mb-6">
            <p className="text-sm text-yellow-800">
              Os cálculos automáticos de dosagem estão disponíveis apenas na aba de prescrições.
              Aqui você pode visualizar o modelo da prescrição.
            </p>
          </div>
          
          <div className="space-y-6">
            {prescription.pedbook_prescription_medications?.map((med) => (
              <div key={med.id} className="border-l-4 border-primary p-4 bg-primary/5 rounded-r-lg">
                <h3 className="font-medium text-lg">{med.pedbook_medications.name}</h3>
                {med.pedbook_medications.brands && (
                  <p className="text-sm text-gray-600 mt-1">
                    Marcas: {med.pedbook_medications.brands}
                  </p>
                )}
                {med.pedbook_medication_dosages && (
                  <div className="mt-2">
                    <p className="font-medium text-sm">{med.pedbook_medication_dosages.name}</p>
                    <p className="text-sm text-gray-600">
                      {cleanTemplateText(med.pedbook_medication_dosages.dosage_template)}
                    </p>
                    {med.pedbook_medication_dosages.summary && (
                      <p className="text-sm text-gray-500 mt-1">
                        {med.pedbook_medication_dosages.summary}
                      </p>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};