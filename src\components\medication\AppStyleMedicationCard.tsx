import React, { useState } from "react";
import { LucideIcon, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { useQueryClient } from "@tanstack/react-query";

interface AppStyleMedicationCardProps {
  title: string;
  icon: LucideIcon;
  color: string;
  onClick?: () => void;
  badge?: string;
}

const AppStyleMedicationCard: React.FC<AppStyleMedicationCardProps> = ({
  title,
  icon: Icon,
  color,
  onClick,
  badge,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const queryClient = useQueryClient();

  const handleClick = () => {
    if (isLoading) return; // Prevenir cliques duplos
    setIsLoading(true);
    onClick?.();
    // Reset após um tempo para permitir novos cliques
    setTimeout(() => setIsLoading(false), 2000);
  };

  // Prefetch ao fazer hover para melhorar performance
  const handleMouseEnter = () => {
    // Prefetch das categorias se ainda não estiver em cache
    queryClient.prefetchQuery({
      queryKey: ["medication-categories", "with-medications"],
      staleTime: 15 * 60 * 1000
    });
  };
  // Gradientes modernos baseados na cor
  const getGradientClass = () => {
    if (color.includes("yellow")) return "from-yellow-400/20 via-yellow-300/10 to-yellow-500/20";
    if (color.includes("purple")) return "from-purple-400/20 via-purple-300/10 to-purple-500/20";
    if (color.includes("blue")) return "from-blue-400/20 via-blue-300/10 to-blue-500/20";
    if (color.includes("pink")) return "from-pink-400/20 via-pink-300/10 to-pink-500/20";
    if (color.includes("green")) return "from-green-400/20 via-green-300/10 to-green-500/20";
    if (color.includes("amber")) return "from-amber-400/20 via-amber-300/10 to-amber-500/20";
    if (color.includes("red")) return "from-red-400/20 via-red-300/10 to-red-500/20";
    if (color.includes("cyan")) return "from-cyan-400/20 via-cyan-300/10 to-cyan-500/20";
    if (color.includes("indigo")) return "from-indigo-400/20 via-indigo-300/10 to-indigo-500/20";
    if (color.includes("rose")) return "from-rose-400/20 via-rose-300/10 to-rose-500/20";
    return "from-blue-400/20 via-blue-300/10 to-blue-500/20";
  };

  // Cor do ícone mais vibrante
  const getIconColorClass = () => {
    if (color.includes("yellow")) return "text-yellow-600 dark:text-yellow-400";
    if (color.includes("purple")) return "text-purple-600 dark:text-purple-400";
    if (color.includes("blue")) return "text-blue-600 dark:text-blue-400";
    if (color.includes("pink")) return "text-pink-600 dark:text-pink-400";
    if (color.includes("green")) return "text-green-600 dark:text-green-400";
    if (color.includes("amber")) return "text-amber-600 dark:text-amber-400";
    if (color.includes("red")) return "text-red-600 dark:text-red-400";
    if (color.includes("cyan")) return "text-cyan-600 dark:text-cyan-400";
    if (color.includes("indigo")) return "text-indigo-600 dark:text-indigo-400";
    if (color.includes("rose")) return "text-rose-600 dark:text-rose-400";
    return "text-blue-600 dark:text-blue-400";
  };

  // Fundo do ícone com glassmorphism
  const getIconBackgroundClass = () => {
    if (color.includes("yellow")) return "bg-yellow-100/80 dark:bg-yellow-900/40 border-yellow-200/50 dark:border-yellow-700/50";
    if (color.includes("purple")) return "bg-purple-100/80 dark:bg-purple-900/40 border-purple-200/50 dark:border-purple-700/50";
    if (color.includes("blue")) return "bg-blue-100/80 dark:bg-blue-900/40 border-blue-200/50 dark:border-blue-700/50";
    if (color.includes("pink")) return "bg-pink-100/80 dark:bg-pink-900/40 border-pink-200/50 dark:border-pink-700/50";
    if (color.includes("green")) return "bg-green-100/80 dark:bg-green-900/40 border-green-200/50 dark:border-green-700/50";
    if (color.includes("amber")) return "bg-amber-100/80 dark:bg-amber-900/40 border-amber-200/50 dark:border-amber-700/50";
    if (color.includes("red")) return "bg-red-100/80 dark:bg-red-900/40 border-red-200/50 dark:border-red-700/50";
    if (color.includes("cyan")) return "bg-cyan-100/80 dark:bg-cyan-900/40 border-cyan-200/50 dark:border-cyan-700/50";
    if (color.includes("indigo")) return "bg-indigo-100/80 dark:bg-indigo-900/40 border-indigo-200/50 dark:border-indigo-700/50";
    if (color.includes("rose")) return "bg-rose-100/80 dark:bg-rose-900/40 border-rose-200/50 dark:border-rose-700/50";
    return "bg-blue-100/80 dark:bg-blue-900/40 border-blue-200/50 dark:border-blue-700/50";
  };

  // Sombra colorida no hover
  const getHoverShadowClass = () => {
    if (color.includes("yellow")) return "hover:shadow-yellow-500/25";
    if (color.includes("purple")) return "hover:shadow-purple-500/25";
    if (color.includes("blue")) return "hover:shadow-blue-500/25";
    if (color.includes("pink")) return "hover:shadow-pink-500/25";
    if (color.includes("green")) return "hover:shadow-green-500/25";
    if (color.includes("amber")) return "hover:shadow-amber-500/25";
    if (color.includes("red")) return "hover:shadow-red-500/25";
    if (color.includes("cyan")) return "hover:shadow-cyan-500/25";
    if (color.includes("indigo")) return "hover:shadow-indigo-500/25";
    if (color.includes("rose")) return "hover:shadow-rose-500/25";
    return "hover:shadow-blue-500/25";
  };

  return (
    <div
      className={cn(
        "group relative h-full p-4 sm:p-6 rounded-2xl transition-all duration-500 cursor-pointer",
        "bg-gradient-to-br", getGradientClass(),
        "backdrop-blur-xl border-2 border-gray-200/60 dark:border-white/10",
        "shadow-lg hover:shadow-2xl", getHoverShadowClass(),
        "hover:-translate-y-2 hover:scale-[1.02]",
        "overflow-hidden",
        isLoading && "opacity-75 scale-95 pointer-events-none" // Feedback visual quando carregando
      )}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
    >


      {/* Padrão de pontos decorativo */}
      <div className="absolute top-2 right-2 w-16 h-16 opacity-10">
        <div className="grid grid-cols-4 gap-1 w-full h-full">
          {[...Array(16)].map((_, i) => (
            <div key={i} className={cn("w-1 h-1 rounded-full", getIconColorClass().split(' ')[0])} />
          ))}
        </div>
      </div>

      <div className="relative flex flex-col items-center text-center h-full justify-between">
        {/* Ícone com glassmorphism */}
        <div className={cn(
          "w-12 h-12 sm:w-16 sm:h-16 rounded-2xl flex items-center justify-center mb-3 sm:mb-4",
          "backdrop-blur-sm border shadow-lg",
          "group-hover:scale-110 transition-transform duration-300",
          getIconBackgroundClass()
        )}>
          {isLoading ? (
            <Loader2 className={cn(
              "w-6 h-6 sm:w-8 sm:h-8 transition-all duration-300 animate-spin",
              getIconColorClass()
            )} />
          ) : (
            <Icon className={cn(
              "w-6 h-6 sm:w-8 sm:h-8 transition-all duration-300",
              "group-hover:scale-110",
              getIconColorClass()
            )} />
          )}
        </div>

        {/* Título com melhor tipografia */}
        <h3 className="font-bold text-sm sm:text-base text-gray-800 dark:text-gray-100 line-clamp-2 leading-tight">
          {title}
        </h3>

        {/* Badge modernizado */}
        {badge && (
          <div className="mt-3">
            <Badge
              variant="outline"
              className={cn(
                "backdrop-blur-sm border-white/30 dark:border-white/20",
                "text-gray-700 dark:text-gray-200 text-[10px] font-medium px-3 py-1",
                "bg-white/50 dark:bg-black/30"
              )}
            >
              {badge}
            </Badge>
          </div>
        )}
      </div>
    </div>
  );
};

export default AppStyleMedicationCard;
