
import { useEffect, useState } from "react";
import HelmetWrapper from "@/components/utils/HelmetWrapper";
import { useNavigate } from "react-router-dom";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import type { Session } from "@supabase/supabase-js";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { PrescriptionSidebar } from "@/components/prescriptions/PrescriptionSidebar";
import { PrescriptionContent } from "@/components/prescriptions/PrescriptionContent";
import AuthDialog from "@/components/auth/AuthDialog";
import { getThemeClasses } from "@/components/ui/theme-utils";

export default function Prescriptions() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [session, setSession] = useState<Session | null>(null);
  const [selectedPrescription, setSelectedPrescription] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const queryClient = useQueryClient();

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (!session) {
        navigate("/");
        return;
      }
      setSession(session);
    });

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      if (!session) {
        navigate("/");
        return;
      }
      setSession(session);
    });

    return () => subscription.unsubscribe();
  }, [navigate, toast]);

  const updatePrescriptionMutation = useMutation({
    mutationFn: async (values: { id: string; weight?: number; age?: number }) => {
      const { data: currentPrescription } = await supabase
        .from("pedbook_prescriptions")
        .select("patient_weight, patient_age")
        .eq("id", values.id)
        .single();

      const updateData = {
        patient_weight: values.weight ?? currentPrescription?.patient_weight ?? 0,
        patient_age: values.age ?? currentPrescription?.patient_age ?? 0,
      };

      const { error } = await supabase
        .from("pedbook_prescriptions")
        .update(updateData)
        .eq("id", values.id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["prescription", selectedPrescription] });
    },
  });

  const handleWeightChange = (weight: string) => {
    if (!selectedPrescription) return;
    
    const numWeight = parseFloat(weight);
    if (!isNaN(numWeight)) {
      updatePrescriptionMutation.mutate({
        id: selectedPrescription,
        weight: numWeight,
      });
    }
  };

  const handleAgeChange = (age: string) => {
    if (!selectedPrescription) return;
    
    const numAge = parseInt(age);
    if (!isNaN(numAge)) {
      updatePrescriptionMutation.mutate({
        id: selectedPrescription,
        age: numAge,
      });
    }
  };

  const handleSelectPrescription = (id: string) => {
    setSelectedPrescription(id);
  };

  return (
    <div className={getThemeClasses.pageBackground()}>
      <HelmetWrapper>
        <title>PedBook | Prescrições</title>
        <meta name="description" content="Gerencie suas prescrições pediátricas de forma eficiente." />
      </HelmetWrapper>

      <Header />
      <div className="flex-1 container mx-auto py-2 px-2 md:py-4 md:px-6">
        <div className="flex flex-col space-y-2 mb-4">
          <Button
            variant="ghost"
            className="text-primary hover:text-primary/80 transition-colors w-fit dark:text-blue-400 dark:hover:text-blue-300"
            onClick={() => navigate("/")}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar para o menu
          </Button>
        </div>

        <div className="flex flex-col lg:flex-row gap-4 md:gap-6">
          {session && (
            <div className="w-full lg:w-80 flex-shrink-0">
              <PrescriptionSidebar
                selectedPrescription={selectedPrescription}
                onPrescriptionSelect={handleSelectPrescription}
                searchTerm={searchTerm}
                onSearchChange={setSearchTerm}
                session={session}
              />
            </div>
          )}

          <div className="flex-1 overflow-hidden">
            <div className="max-w-full px-0 md:px-6">
              <PrescriptionContent
                prescriptionId={selectedPrescription || ""}
                onWeightChange={handleWeightChange}
                onAgeChange={handleAgeChange}
                onSelectPrescription={handleSelectPrescription}
              />
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
