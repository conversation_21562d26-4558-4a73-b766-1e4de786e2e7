import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import type { PrescriptionWithMedications } from "@/components/prescriptions/types";
import { useToast } from "@/components/ui/use-toast";

export const usePrescriptionMutations = (userId?: string) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const addPrescriptionMutation = useMutation({
    mutationFn: async (prescription: PrescriptionWithMedications) => {
      if (!userId) throw new Error("User not authenticated");

      const { data: newPrescription, error: prescriptionError } = await supabase
        .from("pedbook_prescriptions")
        .insert({
          name: prescription.name,
          description: prescription.description,
          patient_weight: prescription.patient_weight,
          patient_age: prescription.patient_age,
          user_id: userId,
        })
        .select()
        .single();

      if (prescriptionError) {
        throw prescriptionError;
      }

      if (prescription.pedbook_prescription_medications?.length) {
        const prescriptionMedications = prescription.pedbook_prescription_medications.map((med, index) => ({
          prescription_id: newPrescription.id,
          medication_id: med.medication_id,
          dosage_id: med.dosage_id,
          notes: med.notes,
          display_order: index + 1,
        }));

        const { error: medicationsError } = await supabase
          .from("pedbook_prescription_medications")
          .insert(prescriptionMedications);

        if (medicationsError) {
          throw medicationsError;
        }
      }

      return newPrescription;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["prescription-categories"],
        exact: false,
        refetchType: "all"
      });
      queryClient.invalidateQueries({
        queryKey: ["uncategorized-prescriptions"],
        exact: false,
        refetchType: "all"
      });
      
      toast({
        title: "Prescrição adicionada com sucesso!",
        description: "A prescrição foi copiada para sua biblioteca.",
      });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Erro ao adicionar prescrição",
        description: "Ocorreu um erro ao tentar adicionar a prescrição.",
      });
    },
  });

  const removePrescriptionMutation = useMutation({
    mutationFn: async (prescriptionId: string) => {
      if (!userId) throw new Error("User not authenticated");

      const { error } = await supabase
        .from("pedbook_prescriptions")
        .delete()
        .eq("id", prescriptionId)
        .eq("user_id", userId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["prescription-categories"],
        exact: false,
        refetchType: "all"
      });
      queryClient.invalidateQueries({
        queryKey: ["uncategorized-prescriptions"],
        exact: false,
        refetchType: "all"
      });
      
      toast({
        title: "Prescrição removida",
        description: "A prescrição foi removida com sucesso.",
      });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Erro ao remover prescrição",
        description: "Ocorreu um erro ao tentar remover a prescrição.",
      });
    },
  });

  return {
    addPrescriptionMutation,
    removePrescriptionMutation,
  };
};