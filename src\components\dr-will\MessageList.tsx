import React, { useEffect, useRef } from 'react';
import { Message } from '@/types/chat';
import { MessageBubble } from './MessageBubble';
import { TypingIndicator } from './TypingIndicator';
import { ScrollArea } from '@/components/ui/scroll-area';

interface MessageListProps {
  messages: Message[];
  isLoading: boolean;
  onSendMessage: (content: string) => void;
}

export const MessageList: React.FC<MessageListProps> = ({ messages, isLoading, onSendMessage }) => {
  const bottomRef = useRef<HTMLDivElement>(null);
  const scrollTimeout = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (scrollTimeout.current) {
      clearTimeout(scrollTimeout.current);
    }

    // Scroll immediately for better UX
    const scrollImmediately = () => {
      if (bottomRef.current) {
        bottomRef.current.scrollIntoView({ behavior: 'auto', block: 'end' });
      }
    };

    // For user messages or when loading, scroll immediately
    if (messages.length > 0 && messages[messages.length - 1]?.role === 'user') {
      scrollImmediately();
    }

    // For all messages, also set a timeout to ensure scrolling works
    scrollTimeout.current = setTimeout(() => {
      if (bottomRef.current) {
        const scrollContainer = bottomRef.current.parentElement;
        if (scrollContainer) {
          const viewportHeight = scrollContainer.clientHeight;
          const scrollHeight = scrollContainer.scrollHeight;
          const currentScroll = scrollContainer.scrollTop;
          const distanceFromBottom = scrollHeight - (currentScroll + viewportHeight);

          // Always scroll for new messages, loading state, or if we're already near the bottom
          if (distanceFromBottom < 200 || messages.length <= 2 || isLoading) {
            bottomRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' });
          }
        }
      }
    }, 50);

    return () => {
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
    };
  }, [messages, isLoading]);

  // Efeito para rolar automaticamente quando o indicador de digitação aparecer
  useEffect(() => {
    if (isLoading && bottomRef.current) {
      // Pequeno atraso para garantir que o indicador de digitação seja renderizado
      setTimeout(() => {
        bottomRef.current?.scrollIntoView({ behavior: 'smooth', block: 'end' });
      }, 100);
    }
  }, [isLoading]);

  // Memoize messages para evitar re-renderizações desnecessárias
  // Usamos uma chave estável para cada mensagem para evitar o efeito de piscar
  const memoizedMessages = React.useMemo(() => {
    return messages.map((message, index) => {
      // Criar uma chave estável baseada no conteúdo e não no timestamp
      const stableKey = `${message.role}-${index}-${message.content.substring(0, 20)}`;
      return <MessageBubble key={stableKey} message={message} />;
    });
  }, [messages]);

  return (
    <ScrollArea className="flex-1 p-0">
      <div className="space-y-6 w-full px-2">
        {memoizedMessages}
        {isLoading && <TypingIndicator />}
        <div ref={bottomRef} className="h-4" />
      </div>
    </ScrollArea>
  );
};
