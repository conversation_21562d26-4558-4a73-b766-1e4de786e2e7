-- <PERSON><PERSON><PERSON> tabela de analytics geral do site
CREATE TABLE IF NOT EXISTS public.site_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  session_id TEXT NOT NULL,
  action_type TEXT NOT NULL CHECK (action_type IN ('page_view', 'medication_view', 'search', 'calculator_use', 'download', 'form_submit')),
  page_url TEXT NOT NULL,
  medication_id UUID REFERENCES public.pedbook_medications(id) ON DELETE SET NULL,
  category_id UUID REFERENCES public.pedbook_medication_categories(id) ON DELETE SET NULL,
  search_query TEXT,
  metadata JSONB DEFAULT '{}',
  user_agent TEXT,
  referrer TEXT,
  ip_address INET,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_site_analytics_created_at ON public.site_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_site_analytics_session_id ON public.site_analytics(session_id);
CREATE INDEX IF NOT EXISTS idx_site_analytics_user_id ON public.site_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_site_analytics_page_url ON public.site_analytics(page_url);
CREATE INDEX IF NOT EXISTS idx_site_analytics_action_type ON public.site_analytics(action_type);
CREATE INDEX IF NOT EXISTS idx_site_analytics_referrer ON public.site_analytics(referrer);

-- RLS Policies
ALTER TABLE public.site_analytics ENABLE ROW LEVEL SECURITY;

-- Política para inserção (qualquer um pode inserir)
CREATE POLICY "Anyone can insert analytics" ON public.site_analytics
  FOR INSERT WITH CHECK (true);

-- Política para leitura (apenas admins)
CREATE POLICY "Only admins can read analytics" ON public.site_analytics
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.is_admin = true
    )
  );

-- Função para registrar eventos do site
CREATE OR REPLACE FUNCTION public.track_site_event(
  p_session_id TEXT,
  p_action_type TEXT,
  p_page_url TEXT,
  p_medication_id UUID DEFAULT NULL,
  p_category_id UUID DEFAULT NULL,
  p_search_query TEXT DEFAULT NULL,
  p_metadata JSONB DEFAULT '{}',
  p_user_agent TEXT DEFAULT NULL,
  p_referrer TEXT DEFAULT NULL,
  p_ip_address TEXT DEFAULT NULL,
  p_user_id UUID DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_event_id UUID;
  v_final_user_id UUID;
  v_ip_address INET;
BEGIN
  -- Se p_user_id foi fornecido, usar ele; senão tentar auth.uid() (pode ser NULL para anônimos)
  IF p_user_id IS NOT NULL THEN
    v_final_user_id := p_user_id;
  ELSE
    v_final_user_id := auth.uid(); -- Pode ser NULL para usuários anônimos
  END IF;

  -- Converter IP address se fornecido
  IF p_ip_address IS NOT NULL AND p_ip_address != '' THEN
    BEGIN
      v_ip_address := p_ip_address::INET;
    EXCEPTION WHEN OTHERS THEN
      v_ip_address := NULL;
    END;
  END IF;

  -- Inserir evento (user_id pode ser NULL para anônimos)
  INSERT INTO public.site_analytics (
    user_id,
    session_id,
    action_type,
    page_url,
    medication_id,
    category_id,
    search_query,
    metadata,
    user_agent,
    referrer,
    ip_address
  ) VALUES (
    v_final_user_id, -- Pode ser NULL para usuários anônimos
    p_session_id,
    p_action_type,
    p_page_url,
    p_medication_id,
    p_category_id,
    p_search_query,
    p_metadata,
    p_user_agent,
    p_referrer,
    v_ip_address
  ) RETURNING id INTO v_event_id;
  
  RETURN v_event_id;
END;
$$;

-- Função para buscar páginas mais visitadas
CREATE OR REPLACE FUNCTION public.get_top_pages(
  start_date DATE,
  end_date DATE,
  page_size INTEGER DEFAULT 20,
  page_number INTEGER DEFAULT 1
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  offset_value INTEGER;
  total_count INTEGER;
  result JSON;
BEGIN
  -- Calcular offset
  offset_value := (page_number - 1) * page_size;
  
  -- Contar total de páginas únicas no período
  SELECT COUNT(DISTINCT page_url) INTO total_count
  FROM site_analytics
  WHERE created_at AT TIME ZONE 'America/Sao_Paulo' >= start_date::timestamp
    AND created_at AT TIME ZONE 'America/Sao_Paulo' <= (end_date::timestamp + interval '1 day' - interval '1 second')
    AND action_type = 'page_view';
  
  -- Buscar dados paginados
  WITH page_stats AS (
    SELECT 
      page_url,
      COUNT(*) as page_views,
      COUNT(DISTINCT session_id) as unique_visitors,
      COUNT(DISTINCT CASE WHEN user_id IS NOT NULL THEN user_id END) as authenticated_users,
      ROUND(AVG(CASE 
        WHEN metadata->>'time_spent_seconds' IS NOT NULL 
        THEN (metadata->>'time_spent_seconds')::numeric 
        END), 2) as avg_time_spent
    FROM site_analytics
    WHERE created_at AT TIME ZONE 'America/Sao_Paulo' >= start_date::timestamp
      AND created_at AT TIME ZONE 'America/Sao_Paulo' <= (end_date::timestamp + interval '1 day' - interval '1 second')
      AND action_type = 'page_view'
    GROUP BY page_url
    ORDER BY page_views DESC
    LIMIT page_size OFFSET offset_value
  )
  SELECT json_build_object(
    'items', json_agg(page_stats),
    'total_count', total_count,
    'page_size', page_size,
    'page_number', page_number
  ) INTO result
  FROM page_stats;
  
  RETURN result;
END;
$$;

-- Função para buscar referrers mais comuns
CREATE OR REPLACE FUNCTION public.get_top_referrers(
  start_date DATE,
  end_date DATE,
  page_size INTEGER DEFAULT 20,
  page_number INTEGER DEFAULT 1
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  offset_value INTEGER;
  total_count INTEGER;
  result JSON;
BEGIN
  -- Calcular offset
  offset_value := (page_number - 1) * page_size;

  -- Contar total de referrers únicos no período
  SELECT COUNT(DISTINCT COALESCE(referrer, 'Direct')) INTO total_count
  FROM site_analytics
  WHERE created_at AT TIME ZONE 'America/Sao_Paulo' >= start_date::timestamp
    AND created_at AT TIME ZONE 'America/Sao_Paulo' <= (end_date::timestamp + interval '1 day' - interval '1 second')
    AND action_type = 'page_view';

  -- Buscar dados paginados
  WITH referrer_stats AS (
    SELECT
      CASE
        WHEN referrer IS NULL OR referrer = '' THEN 'Direct'
        WHEN referrer LIKE '%google.%' THEN 'google.com'
        WHEN referrer LIKE '%bing.%' THEN 'bing.com'
        WHEN referrer LIKE '%yahoo.%' THEN 'yahoo.com'
        WHEN referrer LIKE '%instagram.%' THEN 'instagram.com'
        WHEN referrer LIKE '%facebook.%' THEN 'facebook.com'
        WHEN referrer LIKE '%twitter.%' OR referrer LIKE '%t.co%' THEN 'twitter.com'
        ELSE
          CASE
            WHEN referrer ~ '^https?://([^/]+)' THEN
              regexp_replace(referrer, '^https?://([^/]+).*', '\1')
            ELSE referrer
          END
      END as referrer_domain,
      COUNT(*) as visits,
      COUNT(DISTINCT session_id) as unique_visitors,
      COUNT(DISTINCT page_url) as pages_visited
    FROM site_analytics
    WHERE created_at AT TIME ZONE 'America/Sao_Paulo' >= start_date::timestamp
      AND created_at AT TIME ZONE 'America/Sao_Paulo' <= (end_date::timestamp + interval '1 day' - interval '1 second')
      AND action_type = 'page_view'
    GROUP BY referrer_domain
    ORDER BY visits DESC
    LIMIT page_size OFFSET offset_value
  )
  SELECT json_build_object(
    'items', json_agg(referrer_stats),
    'total_count', total_count,
    'page_size', page_size,
    'page_number', page_number
  ) INTO result
  FROM referrer_stats;

  RETURN result;
END;
$$;

-- Função para buscar estatísticas gerais do site
CREATE OR REPLACE FUNCTION public.get_site_analytics_overview(
  start_date DATE,
  end_date DATE
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result JSON;
BEGIN
  WITH stats AS (
    SELECT
      COUNT(*) as total_page_views,
      COUNT(DISTINCT session_id) as unique_sessions,
      COUNT(DISTINCT user_id) FILTER (WHERE user_id IS NOT NULL) as authenticated_users,
      COUNT(DISTINCT page_url) as unique_pages,
      COUNT(*) FILTER (WHERE action_type = 'search') as total_searches,
      COUNT(*) FILTER (WHERE action_type = 'calculator_use') as calculator_uses,
      COUNT(*) FILTER (WHERE action_type = 'download') as downloads,

      -- Bounce rate (sessões com apenas 1 page view)
      ROUND(
        (COUNT(DISTINCT session_id) FILTER (
          WHERE session_id IN (
            SELECT session_id
            FROM site_analytics s2
            WHERE s2.action_type = 'page_view'
              AND s2.created_at AT TIME ZONE 'America/Sao_Paulo' >= start_date::timestamp
              AND s2.created_at AT TIME ZONE 'America/Sao_Paulo' <= (end_date::timestamp + interval '1 day' - interval '1 second')
            GROUP BY session_id
            HAVING COUNT(*) = 1
          )
        )::numeric / NULLIF(COUNT(DISTINCT session_id), 0)) * 100, 2
      ) as bounce_rate,

      -- Tempo médio na página
      ROUND(AVG(CASE
        WHEN metadata->>'time_spent_seconds' IS NOT NULL
        THEN (metadata->>'time_spent_seconds')::numeric
        END), 2) as avg_time_spent_seconds

    FROM site_analytics
    WHERE created_at AT TIME ZONE 'America/Sao_Paulo' >= start_date::timestamp
      AND created_at AT TIME ZONE 'America/Sao_Paulo' <= (end_date::timestamp + interval '1 day' - interval '1 second')
  )
  SELECT json_build_object(
    'total_page_views', total_page_views,
    'unique_sessions', unique_sessions,
    'authenticated_users', authenticated_users,
    'unique_pages', unique_pages,
    'total_searches', total_searches,
    'calculator_uses', calculator_uses,
    'downloads', downloads,
    'bounce_rate', bounce_rate,
    'avg_time_spent_seconds', avg_time_spent_seconds,
    'period_start', start_date,
    'period_end', end_date
  ) INTO result
  FROM stats;

  RETURN result;
END;
$$;

-- Função para buscar analytics por timeline (gráfico de evolução)
CREATE OR REPLACE FUNCTION public.get_site_analytics_timeline(
  start_date DATE,
  end_date DATE,
  interval_type TEXT DEFAULT 'day' -- 'hour', 'day', 'week', 'month'
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result JSON;
  date_format TEXT;
  date_trunc_format TEXT;
BEGIN
  -- Definir formato baseado no intervalo
  CASE interval_type
    WHEN 'hour' THEN
      date_format := 'YYYY-MM-DD HH24:00';
      date_trunc_format := 'hour';
    WHEN 'day' THEN
      date_format := 'YYYY-MM-DD';
      date_trunc_format := 'day';
    WHEN 'week' THEN
      date_format := 'YYYY-"W"WW';
      date_trunc_format := 'week';
    WHEN 'month' THEN
      date_format := 'YYYY-MM';
      date_trunc_format := 'month';
    ELSE
      date_format := 'YYYY-MM-DD';
      date_trunc_format := 'day';
  END CASE;

  WITH timeline_stats AS (
    SELECT
      TO_CHAR(DATE_TRUNC(date_trunc_format, created_at AT TIME ZONE 'America/Sao_Paulo'), date_format) as period,
      DATE_TRUNC(date_trunc_format, created_at AT TIME ZONE 'America/Sao_Paulo') as period_date,
      COUNT(*) FILTER (WHERE action_type = 'page_view') as page_views,
      COUNT(DISTINCT session_id) as unique_sessions,
      COUNT(DISTINCT user_id) FILTER (WHERE user_id IS NOT NULL) as authenticated_users,
      COUNT(*) FILTER (WHERE action_type = 'search') as searches,
      COUNT(*) FILTER (WHERE action_type = 'calculator_use') as calculator_uses,
      COUNT(*) FILTER (WHERE action_type = 'medication_view') as medication_views
    FROM site_analytics
    WHERE created_at AT TIME ZONE 'America/Sao_Paulo' >= start_date::timestamp
      AND created_at AT TIME ZONE 'America/Sao_Paulo' <= (end_date::timestamp + interval '1 day' - interval '1 second')
    GROUP BY DATE_TRUNC(date_trunc_format, created_at AT TIME ZONE 'America/Sao_Paulo')
    ORDER BY period_date
  )
  SELECT json_build_object(
    'timeline', json_agg(timeline_stats ORDER BY period_date),
    'interval_type', interval_type,
    'period_start', start_date,
    'period_end', end_date
  ) INTO result
  FROM timeline_stats;

  RETURN result;
END;
$$;

-- Função para buscar buscas mais realizadas
CREATE OR REPLACE FUNCTION public.get_top_searches(
  start_date DATE,
  end_date DATE,
  page_size INTEGER DEFAULT 20,
  page_number INTEGER DEFAULT 1
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  offset_value INTEGER;
  total_count INTEGER;
  result JSON;
BEGIN
  -- Calcular offset
  offset_value := (page_number - 1) * page_size;

  -- Contar total de buscas únicas no período
  SELECT COUNT(DISTINCT search_query) INTO total_count
  FROM site_analytics
  WHERE created_at AT TIME ZONE 'America/Sao_Paulo' >= start_date::timestamp
    AND created_at AT TIME ZONE 'America/Sao_Paulo' <= (end_date::timestamp + interval '1 day' - interval '1 second')
    AND action_type = 'search'
    AND search_query IS NOT NULL
    AND search_query != '';

  -- Buscar dados paginados
  WITH search_stats AS (
    SELECT
      search_query,
      COUNT(*) as search_count,
      COUNT(DISTINCT session_id) as unique_searchers,
      AVG((metadata->>'results_count')::numeric) as avg_results
    FROM site_analytics
    WHERE created_at AT TIME ZONE 'America/Sao_Paulo' >= start_date::timestamp
      AND created_at AT TIME ZONE 'America/Sao_Paulo' <= (end_date::timestamp + interval '1 day' - interval '1 second')
      AND action_type = 'search'
      AND search_query IS NOT NULL
      AND search_query != ''
    GROUP BY search_query
    ORDER BY search_count DESC
    LIMIT page_size OFFSET offset_value
  )
  SELECT json_build_object(
    'items', json_agg(search_stats),
    'total_count', total_count,
    'page_size', page_size,
    'page_number', page_number
  ) INTO result
  FROM search_stats;

  RETURN result;
END;
$$;
