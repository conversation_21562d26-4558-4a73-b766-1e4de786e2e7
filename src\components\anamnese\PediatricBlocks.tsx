
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { PediatricData } from "./AnamneseForm";
import { FileText, Info, ChevronDown, AlertCircle, Zap } from "lucide-react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Switch } from "@/components/ui/switch";

interface PediatricBlocksProps {
  data: PediatricData;
  onChange: (data: Partial<PediatricData>) => void;
}

export const PediatricBlocks: React.FC<PediatricBlocksProps> = ({ data, onChange }) => {
  const [quickMode, setQuickMode] = useState(false);
  const [expandedBlocks, setExpandedBlocks] = useState<string[]>([]);
  
  const handleChange = (field: keyof PediatricData, value: string) => {
    onChange({ [field]: value } as Partial<PediatricData>);
  };

  const toggleBlockExpansion = (blockId: string) => {
    setExpandedBlocks(prev => 
      prev.includes(blockId) 
        ? prev.filter(id => id !== blockId) 
        : [...prev, blockId]
    );
  };

  const getPlaceholderText = (blockId: string) => {
    if (quickMode) {
      return "Apenas descreva alterações relevantes.";
    }
    
    switch (blockId) {
      case "headSymptoms":
        return "Descreva alterações em crânio/cabeça.";
      case "mouthSymptoms":
        return "Descreva alterações na boca.";
      case "earSymptoms":
        return "Descreva alterações nos ouvidos.";
      case "eyeSymptoms":
        return "Descreva estrabismo (fixo/variável), secreção ocular, etc.";
      case "breathingIssues":
        return "Descreva cianose, cansaço às mamadas, tosse, engasgos, etc.";
      case "feedingIssues":
        return "Descreva regurgitação, vômitos, sucção, etc.";
      case "digestionIssues":
        return "Descreva número de evacuações por dia, consistência, etc.";
      case "urineStool":
        return "Descreva frequência da troca de fraldas, características da urina e fezes.";
      case "behaviorIssues":
        return "Descreva alterações de comportamento, sonolência, irritabilidade, tremores, espasmos, convulsões, etc.";
      case "breastfeeding":
        return "Aleitamento materno exclusivo? Duração e intervalo das mamadas?";
      case "breastfeedingTechnique":
        return "Posição, alternância dos seios, dificuldades, ingurgitamento, dor, fissuras, etc.";
      case "artificialFeeding":
        return "Tipo de leite, preparo, número de mamadeiras por dia, etc.";
      case "sleep":
        return "Onde dorme, posição, quantas horas por dia, etc.";
      case "careRoutines":
        return "Banhos, sabonete, talco, cremes de barreira, quem cuida, uso de chupeta, etc.";
      case "motherMeds":
        return "Contraceptivos, medicações contínuas usadas pela mãe.";
      case "babyMeds":
        return "Medicamentos em uso pelo bebê - qual, dose, tempo, indicação.";
      case "vaccineReactions":
        return "Reações adversas a vacinas, indicação de vacinas especiais (CRIE).";
      case "dnpmObservations":
        return "Observações sobre o desenvolvimento neuropsicomotor.";
      default:
        return "Descreva os achados...";
    }
  };

  const blocks = [
    {
      id: "headSymptoms",
      title: "Crânio/Cabeça",
      placeholder: getPlaceholderText("headSymptoms"),
      value: data.headSymptoms,
    },
    {
      id: "mouthSymptoms",
      title: "Boca",
      placeholder: getPlaceholderText("mouthSymptoms"),
      value: data.mouthSymptoms,
    },
    {
      id: "earSymptoms",
      title: "Ouvidos",
      placeholder: getPlaceholderText("earSymptoms"),
      value: data.earSymptoms,
    },
    {
      id: "eyeSymptoms",
      title: "Olhos",
      placeholder: getPlaceholderText("eyeSymptoms"),
      value: data.eyeSymptoms,
    },
    {
      id: "breathingIssues",
      title: "Respiração",
      placeholder: getPlaceholderText("breathingIssues"),
      value: data.breathingIssues,
    },
    {
      id: "feedingIssues",
      title: "Alimentação",
      placeholder: getPlaceholderText("feedingIssues"),
      value: data.feedingIssues,
    },
    {
      id: "digestionIssues",
      title: "Digestão",
      placeholder: getPlaceholderText("digestionIssues"),
      value: data.digestionIssues,
    },
    {
      id: "urineStool",
      title: "Urina e Fezes",
      placeholder: getPlaceholderText("urineStool"),
      value: data.urineStool,
    },
    {
      id: "behaviorIssues",
      title: "Comportamento",
      placeholder: getPlaceholderText("behaviorIssues"),
      value: data.behaviorIssues,
    },
    {
      id: "breastfeeding",
      title: "Aleitamento Materno",
      placeholder: getPlaceholderText("breastfeeding"),
      value: data.breastfeeding,
    },
    {
      id: "breastfeedingTechnique",
      title: "Técnica da Amamentação",
      placeholder: getPlaceholderText("breastfeedingTechnique"),
      value: data.breastfeedingTechnique,
    },
    {
      id: "artificialFeeding",
      title: "Alimentação Artificial",
      placeholder: getPlaceholderText("artificialFeeding"),
      value: data.artificialFeeding,
    },
    {
      id: "sleep",
      title: "Sono",
      placeholder: getPlaceholderText("sleep"),
      value: data.sleep,
    },
    {
      id: "careRoutines",
      title: "Rotinas de Cuidado",
      placeholder: getPlaceholderText("careRoutines"),
      value: data.careRoutines,
    },
    {
      id: "motherMeds",
      title: "Medicamentos da Mãe",
      placeholder: getPlaceholderText("motherMeds"),
      value: data.motherMeds,
    },
    {
      id: "babyMeds",
      title: "Medicamentos do Bebê",
      placeholder: getPlaceholderText("babyMeds"),
      value: data.babyMeds,
    },
    {
      id: "vaccineReactions",
      title: "Vacinação",
      placeholder: getPlaceholderText("vaccineReactions"),
      value: data.vaccineReactions,
    },
    {
      id: "dnpmObservations",
      title: "Desenvolvimento Neuropsicomotor",
      placeholder: getPlaceholderText("dnpmObservations"),
      value: data.dnpmObservations,
    },
  ];

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-primary" />
            Questionário Dirigido - Puericultura 1-2 meses
          </CardTitle>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">Apenas alterações</span>
            <Switch 
              checked={quickMode} 
              onCheckedChange={setQuickMode} 
              className="data-[state=checked]:bg-primary"
            />
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-4 w-4 text-gray-400 cursor-help" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="w-[220px] text-xs">
                    No modo "Apenas alterações", você só precisa descrever o que está alterado. 
                    Os campos vazios serão preenchidos com valores normais para a idade e gênero.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {quickMode ? (
          <div className="p-4 bg-amber-50 border border-amber-200 rounded-md flex gap-3 items-start">
            <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5" />
            <div>
              <h3 className="font-medium text-amber-800">Modo rápido ativado</h3>
              <p className="text-sm text-amber-700">
                Preencha apenas os achados alterados. Os campos vazios serão automaticamente 
                preenchidos com valores normais para a idade e gênero do paciente.
              </p>
            </div>
          </div>
        ) : null}

        {blocks.map((block) => (
          <Collapsible 
            key={block.id} 
            className="border rounded-md"
            open={expandedBlocks.includes(block.id)}
            onOpenChange={() => toggleBlockExpansion(block.id)}
          >
            <div className="flex items-center justify-between p-4">
              <Label htmlFor={block.id} className="font-medium">
                {block.title}
                {block.value && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary/10 text-primary">
                    <Zap className="h-3 w-3 mr-1" />
                    Preenchido
                  </span>
                )}
              </Label>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm">
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </CollapsibleTrigger>
            </div>
            <CollapsibleContent className="px-4 pb-4">
              <Textarea
                id={block.id}
                value={block.value}
                onChange={(e) => handleChange(block.id as keyof PediatricData, e.target.value)}
                placeholder={block.placeholder}
                className="min-h-[60px]"
              />
            </CollapsibleContent>
          </Collapsible>
        ))}
      </CardContent>
    </Card>
  );
};
