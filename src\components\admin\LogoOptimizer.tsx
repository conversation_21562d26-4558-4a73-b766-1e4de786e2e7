import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { Download, Upload, Zap, FileImage, RefreshCw } from 'lucide-react';
import { createOptimizedLogoVersions, downloadBlob } from '@/utils/logoOptimizer';
import { supabase } from '@/integrations/supabase/client';

export const LogoOptimizer: React.FC = () => {
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [results, setResults] = useState<any>(null);
  const { toast } = useToast();

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      toast({
        variant: "destructive",
        title: "Arquivo inválido",
        description: "Por favor, selecione uma imagem.",
      });
      return;
    }

    setIsOptimizing(true);
    
    try {
      const optimizationResults = await createOptimizedLogoVersions(file);
      setResults(optimizationResults);
      
      toast({
        title: "Otimização concluída!",
        description: `Economia total: ${optimizationResults.summary.compressionRatio}% (${(optimizationResults.summary.totalSavings / 1024).toFixed(2)} KB)`,
      });
    } catch (error) {
      console.error('Erro na otimização:', error);
      toast({
        variant: "destructive",
        title: "Erro na otimização",
        description: "Não foi possível otimizar a imagem.",
      });
    } finally {
      setIsOptimizing(false);
    }
  };

  const handleDownload = (blob: Blob, filename: string) => {
    downloadBlob(blob, filename);
  };

  const formatFileSize = (bytes: number) => {
    return (bytes / 1024).toFixed(2) + ' KB';
  };

  const handleUseLocalLogo = async () => {
    try {
      // Limpar a configuração do logo_url para forçar o uso do fallback local
      const { error } = await supabase
        .from("pedbook_site_settings")
        .update({ value: "" })
        .eq("key", "logo_url");

      if (error) throw error;

      toast({
        title: "Logo local ativado!",
        description: "O site agora está usando o logo otimizado local (/faviconx.webp).",
      });

      // Recarregar a página para aplicar a mudança
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error('Erro ao ativar logo local:', error);
      toast({
        variant: "destructive",
        title: "Erro",
        description: "Não foi possível ativar o logo local.",
      });
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Otimizador de Logo
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg mb-4">
            <h4 className="font-semibold text-green-800 dark:text-green-200 mb-2">
              🚀 Ação Rápida: Ativar Logo Otimizado
            </h4>
            <p className="text-sm text-green-700 dark:text-green-300 mb-3">
              O logo local já foi otimizado (7,6KB - 200x200px). Clique para ativá-lo imediatamente:
            </p>
            <Button
              onClick={handleUseLocalLogo}
              className="w-full"
              variant="default"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Usar Logo Local Otimizado (7,6KB)
            </Button>
          </div>

          <div className="flex items-center gap-4">
            <Input
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              disabled={isOptimizing}
              className="flex-1"
            />
            <Button
              variant="outline"
              disabled={isOptimizing}
              className="flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              {isOptimizing ? 'Otimizando...' : 'Upload'}
            </Button>
          </div>

          {isOptimizing && (
            <div className="text-center py-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="text-sm text-gray-600 mt-2">Otimizando imagem...</p>
            </div>
          )}

          {results && (
            <div className="space-y-4">
              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <h3 className="font-semibold text-green-800 dark:text-green-200 mb-2">
                  Resumo da Otimização
                </h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Tamanho Original:</span>
                    <span className="ml-2 font-medium">{formatFileSize(results.summary.originalSize)}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Economia Total:</span>
                    <span className="ml-2 font-medium text-green-600">
                      {formatFileSize(results.summary.totalSavings)} ({results.summary.compressionRatio}%)
                    </span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Favicon */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Favicon (32x32)</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      <div>Tamanho: {formatFileSize(results.favicon.optimizedSize)}</div>
                      <div>Economia: {results.favicon.compressionRatio}%</div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDownload(results.favicon.optimizedBlob, 'favicon-32x32.webp')}
                      className="w-full"
                    >
                      <Download className="h-3 w-3 mr-1" />
                      Download
                    </Button>
                  </CardContent>
                </Card>

                {/* Header Logo */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Header (48x48)</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      <div>Tamanho: {formatFileSize(results.header.optimizedSize)}</div>
                      <div>Economia: {results.header.compressionRatio}%</div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDownload(results.header.optimizedBlob, 'logo-48x48.webp')}
                      className="w-full"
                    >
                      <Download className="h-3 w-3 mr-1" />
                      Download
                    </Button>
                  </CardContent>
                </Card>

                {/* General Logo */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Geral (200x200)</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      <div>Tamanho: {formatFileSize(results.general.optimizedSize)}</div>
                      <div>Economia: {results.general.compressionRatio}%</div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDownload(results.general.optimizedBlob, 'logo-optimized.webp')}
                      className="w-full"
                    >
                      <Download className="h-3 w-3 mr-1" />
                      Download
                    </Button>
                  </CardContent>
                </Card>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2 flex items-center gap-2">
                  <FileImage className="h-4 w-4" />
                  Próximos Passos
                </h4>
                <ol className="text-sm text-blue-700 dark:text-blue-300 space-y-1 list-decimal list-inside mb-4">
                  <li>Baixe a versão "Header (48x48)" e substitua o arquivo <code>public/faviconx.webp</code></li>
                  <li>Baixe a versão "Favicon (32x32)" para usar como favicon</li>
                  <li>Clique no botão abaixo para ativar o logo local</li>
                  <li>Execute um novo teste no PageSpeed Insights</li>
                </ol>
                <Button
                  onClick={handleUseLocalLogo}
                  className="w-full"
                  variant="default"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Usar Logo Local Otimizado
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
