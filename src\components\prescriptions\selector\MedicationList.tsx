import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

interface MedicationListProps {
  onSelect: (medicationId: string, dosageId: string) => void;
  weight: number;
  age: number;
}

const ageGroupLabels = {
  neonatal: "Dosagem neonatal",
  pediatric: "Dosagem pediátrica",
  adult: "Dosagem adulta"
};

export const MedicationList = ({
  onSelect,
  weight,
  age,
}: MedicationListProps) => {
  const [selectedMedicationId, setSelectedMedicationId] = useState<string>("");
  const [selectedDosageId, setSelectedDosageId] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  const { data: medications } = useQuery({
    queryKey: ["medications"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_medications")
        .select(`
          *,
          pedbook_medication_categories (
            id,
            name
          ),
          pedbook_medication_dosages (
            id,
            name,
            dosage_template,
            summary,
            age_group
          )
        `);

      if (error) throw error;
      return data;
    },
  });

  const { data: categories } = useQuery({
    queryKey: ["medication-categories"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_medication_categories")
        .select("*");

      if (error) throw error;
      return data;
    },
  });

  const filteredMedications = medications?.filter(med => {
    const matchesSearch = med.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "all" || med.category_id === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const selectedMedication = medications?.find(
    (med) => med.id === selectedMedicationId
  );

  return (
    <div className="space-y-4">
      <div className="flex gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Buscar medicamento..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select
          value={selectedCategory}
          onValueChange={setSelectedCategory}
        >
          <SelectTrigger className="w-[200px] bg-white">
            <SelectValue placeholder="Todas categorias" />
          </SelectTrigger>
          <SelectContent className="bg-white">
            <SelectItem value="all">Todas categorias</SelectItem>
            {categories?.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <ScrollArea className="h-[200px] rounded-md border">
        <div className="p-4 space-y-2">
          {filteredMedications?.map((medication) => (
            <div
              key={medication.id}
              className={`p-4 rounded-lg cursor-pointer transition-colors border ${
                selectedMedicationId === medication.id
                  ? "bg-primary text-primary-foreground border-primary"
                  : "hover:bg-gray-50 border-border"
              }`}
              onClick={() => {
                setSelectedMedicationId(medication.id);
                setSelectedDosageId("");
              }}
            >
              <div className="font-medium">{medication.name}</div>
              {medication.pedbook_medication_categories && (
                <div className="text-sm mt-1 opacity-80">
                  {medication.pedbook_medication_categories.name}
                </div>
              )}
            </div>
          ))}
        </div>
      </ScrollArea>

      {selectedMedication && (
        <Card className="p-4 space-y-4">
          <div>
            <h3 className="font-medium text-lg">{selectedMedication.name}</h3>
            {selectedMedication.pedbook_medication_categories?.name && (
              <span className="text-sm text-muted-foreground">
                {selectedMedication.pedbook_medication_categories.name}
              </span>
            )}
            {selectedMedication.description && (
              <p className="text-sm text-muted-foreground mt-2">
                {selectedMedication.description}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Selecione a dosagem:</label>
            <ScrollArea className="h-[200px] rounded-md border">
              <div className="p-4 space-y-2">
                {selectedMedication.pedbook_medication_dosages?.map((dosage) => (
                  <div
                    key={dosage.id}
                    className={`p-4 rounded-lg cursor-pointer transition-colors border ${
                      selectedDosageId === dosage.id
                        ? "bg-primary text-primary-foreground border-primary"
                        : "hover:bg-gray-50 border-border"
                    }`}
                    onClick={() => setSelectedDosageId(dosage.id)}
                  >
                    <div className="font-medium">{dosage.name}</div>
                    <div className="text-sm mt-1 opacity-90">
                      {ageGroupLabels[dosage.age_group]}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>

          <Button
            type="button"
            onClick={() => onSelect(selectedMedicationId, selectedDosageId)}
            disabled={!selectedDosageId}
            className="w-full"
          >
            Adicionar Medicamento
          </Button>
        </Card>
      )}
    </div>
  );
};