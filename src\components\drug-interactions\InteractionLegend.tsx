
import React from "react";
import { Check, Alert<PERSON>riangle, <PERSON>ert<PERSON>ircle, X } from "lucide-react";

export const InteractionLegend: React.FC = () => {
  return (
    <div className="mb-6">
      <h2 className="text-2xl font-bold mb-2">Legenda de severidade</h2>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
        <div className="flex items-center gap-2 p-2 rounded-md bg-red-50 dark:bg-red-950/30">
          <div className="bg-red-600 p-1 rounded-full">
            <X className="h-4 w-4 text-white" />
          </div>
          <span className="font-medium text-red-700 dark:text-red-300">Contraindicado</span>
        </div>
        <div className="flex items-center gap-2 p-2 rounded-md bg-purple-50 dark:bg-purple-950/30">
          <div className="bg-purple-600 p-1 rounded-full">
            <AlertTriangle className="h-4 w-4 text-white" />
          </div>
          <span className="font-medium text-purple-700 dark:text-purple-300">Grave</span>
        </div>
        <div className="flex items-center gap-2 p-2 rounded-md bg-orange-50 dark:bg-orange-950/30">
          <div className="bg-orange-500 p-1 rounded-full">
            <AlertCircle className="h-4 w-4 text-white" />
          </div>
          <span className="font-medium text-orange-700 dark:text-orange-300">Moderado</span>
        </div>
        <div className="flex items-center gap-2 p-2 rounded-md bg-green-50 dark:bg-green-950/30">
          <div className="bg-green-500 p-1 rounded-full">
            <Check className="h-4 w-4 text-white" />
          </div>
          <span className="font-medium text-green-700 dark:text-green-300">Leve/Sem interação</span>
        </div>
      </div>
    </div>
  );
};
