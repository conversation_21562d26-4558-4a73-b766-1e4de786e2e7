import { Card } from "@/components/ui/card";
import { VaccineDose } from "@/components/admin/vaccine/types";

interface VaccineListProps {
  title: string;
  icon: React.ReactNode;
  doses: { [key: string]: VaccineDose[] };
  titleColor: string;
  bgColor: string;
  textColor: string;
  borderColor: string;
}

export function VaccineList({
  title,
  icon,
  doses,
  titleColor,
  bgColor,
  textColor,
  borderColor,
}: VaccineListProps) {
  return (
    <Card className="p-4">
      <h3 className={`text-lg font-semibold mb-3 flex items-center gap-2 ${titleColor}`}>
        {icon}
        {title}
      </h3>
      <div className="space-y-4 max-h-[500px] overflow-y-auto pr-2">
        {Object.entries(doses).map(([ageKey, doseGroup]) => (
          <div key={ageKey} className="space-y-2">
            <h4 className={`font-medium ${textColor} border-b ${borderColor} pb-1`}>
              {ageKey}
            </h4>
            {doseGroup.map((dose) => (
              <div key={dose.id} className={`p-2 ${bgColor} rounded`}>
                <div className={`font-medium ${textColor}`}>
                  💉 {dose.vaccine.name}
                  {dose.dose_type === 'reforço' ? ' (Reforço)' : ` (${dose.dose_number}ª dose)`}
                </div>
                <div className={`text-sm ${textColor.replace('800', '600')}`}>
                  {dose.type}
                </div>
              </div>
            ))}
          </div>
        ))}
        {Object.keys(doses).length === 0 && (
          <div className="text-gray-500 text-center py-4">
            Não há próximas vacinas previstas
          </div>
        )}
      </div>
    </Card>
  );
}