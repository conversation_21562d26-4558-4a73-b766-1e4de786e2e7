
import React, { useState, useEffect } from "react";
import { useSupabaseClient } from "@supabase/auth-helpers-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/components/ui/use-toast";
import { Modules } from "@/types";

interface AdminUser {
  id: string;
  full_name: string | null;
  is_admin: boolean;
  is_super_admin: boolean;
  permissions: string[];
}

interface AdminModulePermissionsProps {
  admin: AdminUser;
  onSaved: () => void;
}

export function AdminModulePermissions({ admin, onSaved }: AdminModulePermissionsProps) {
  const supabase = useSupabaseClient();
  const [selectedModules, setSelectedModules] = useState<string[]>([]);
  const [availableModules, setAvailableModules] = useState<Modules[]>([]);
  const [saving, setSaving] = useState(false);

  // Definir a lista de módulos disponíveis
  useEffect(() => {
    const modules: Modules[] = [
      { id: "admin-users", name: "Administradores" },
      { id: "blog", name: "Blog" },
      { id: "categories", name: "Categorias de Medicamentos" },
      { id: "medications", name: "Medicamentos" },
      { id: "formulas", name: "Fórmulas" },
      { id: "dosages", name: "Dosagens" },
      { id: "instructions", name: "Bulas" },
      { id: "prescription-categories", name: "Categorias de Prescrição" },
      { id: "icd10", name: "CID-10" },
      { id: "growth-curves", name: "Curvas de Crescimento" },
      { id: "growth-curve-metadata", name: "Dados das Curvas" },
      { id: "question-import", name: "Importar Questões" },
      { id: "question-formatting", name: "Formatação de Questões" },
      { id: "format-themes", name: "Formatar Temas" },
      { id: "breastfeeding-medications-enhancement", name: "Medicamentos na Amamentação" },
      { id: "condutas-e-manejos", name: "Condutas e Manejos" },
      { id: "vaccines", name: "Vacinas" },
      { id: "dnpm", name: "Marcos DNPM" },
      { id: "settings", name: "Configurações" },
      { id: "site-settings", name: "Configurações do Site" }
    ];

    setAvailableModules(modules);
    setSelectedModules(admin.permissions);

    console.log("🔍 [AdminModulePermissions] Permissões carregadas:", admin.permissions);
  }, [admin]);

  const handleModuleToggle = (moduleId: string) => {
    setSelectedModules(prev => {
      if (prev.includes(moduleId)) {
        return prev.filter(id => id !== moduleId);
      } else {
        return [...prev, moduleId];
      }
    });
  };

  const handleSavePermissions = async () => {
    console.log("💾 [AdminModulePermissions] Salvando permissões:", selectedModules);
    try {
      setSaving(true);

      // Primeiro remover permissões existentes
      const { error: deleteError } = await supabase
        .from("admin_user_permissions")
        .delete()
        .eq("user_id", admin.id);

      if (deleteError) throw deleteError;

      // Se não houver módulos selecionados, não precisamos inserir nada
      if (selectedModules.length === 0) {
        toast({
          title: "Permissões atualizadas",
          description: "O administrador agora não tem acesso a nenhum módulo.",
        });
        onSaved();
        return;
      }

      // Inserir novas permissões
      const permissionsToInsert = selectedModules.map(resource => ({
        user_id: admin.id,
        resource: resource
      }));

      const { error: insertError } = await supabase
        .from("admin_user_permissions")
        .insert(permissionsToInsert);

      if (insertError) throw insertError;

      toast({
        title: "Permissões atualizadas",
        description: "As permissões do administrador foram atualizadas com sucesso.",
      });

      onSaved();
    } catch (error: any) {
      console.error("❌ [AdminModulePermissions] Erro ao salvar permissões:", error);
      toast({
        variant: "destructive",
        title: "Erro ao salvar permissões",
        description: error.message,
      });
    } finally {
      setSaving(false);
    }
  };

  if (admin.is_super_admin) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Permissões não editáveis</CardTitle>
          <CardDescription>
            Usuários com papel de Super Admin têm acesso completo a todos os módulos.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Gerenciar Permissões: {admin.full_name}</CardTitle>
        <CardDescription>
          Selecione os módulos que este administrador poderá acessar
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {availableModules.map((module) => (
            <div className="flex items-center space-x-2" key={module.id}>
              <Checkbox
                id={`module-${module.id}`}
                checked={selectedModules.includes(module.id)}
                onCheckedChange={() => handleModuleToggle(module.id)}
              />
              <label
                htmlFor={`module-${module.id}`}
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
              >
                {module.name}
              </label>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter className="flex justify-end gap-2">
        <Button variant="outline" onClick={() => onSaved()}>
          Cancelar
        </Button>
        <Button disabled={saving} onClick={handleSavePermissions}>
          {saving ? "Salvando..." : "Salvar Permissões"}
        </Button>
      </CardFooter>
    </Card>
  );
}
