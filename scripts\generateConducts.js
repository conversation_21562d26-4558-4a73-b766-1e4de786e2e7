/**
 * Script para gerar páginas SEO de condutas médicas
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

console.log('🚀 Gerando páginas de condutas médicas...');

const supabaseUrl = 'https://bxedpdmgvgatjdfxgxij.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ4ZWRwZG1ndmdhdGpkZnhneGlqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzIyNzk3MTgsImV4cCI6MjA0Nzg1NTcxOH0.cjoaggOXt1kY9WmVNbAipCOQ2dP4PWLP43KMf8cO8Wo';
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * <PERSON>car todas as condutas publicadas
 */
async function fetchConductsData() {
  try {
    console.log('🔍 Buscando TODAS as condutas publicadas...');

    const { data, error } = await supabase
      .from('pedbook_conducts_summaries')
      .select(`
        id,
        title,
        slug,
        content,
        sections,
        topic_id,
        pedbook_conducts_topics (
          name,
          slug,
          description,
          category_id,
          pedbook_conducts_categories (
            name,
            slug,
            description
          )
        )
      `)
      .eq('published', true)
      .order('title', { ascending: true });

    if (error) {
      console.error('❌ Erro:', error);
      return [];
    }

    if (data && data.length > 0) {
      console.log(`✅ Encontradas ${data.length} condutas:`);
      data.forEach(conduct => {
        console.log(`📋 ${conduct.title} (${conduct.slug})`);
      });
      return data;
    } else {
      console.log('⚠️ Nenhuma conduta encontrada');
      return [];
    }

  } catch (err) {
    console.error('❌ Erro na conexão:', err.message);
    return [];
  }
}

/**
 * Buscar categorias com contagem
 */
async function fetchCategoriesData() {
  try {
    console.log('🔍 Buscando categorias de condutas...');

    const { data, error } = await supabase
      .from('pedbook_conducts_categories')
      .select(`
        id,
        name,
        slug,
        description,
        pedbook_conducts_topics (
          id,
          pedbook_conducts_summaries (
            id,
            published
          )
        )
      `)
      .eq('coming_soon', false)
      .order('display_order', { ascending: true });

    if (error) {
      console.error('❌ Erro ao buscar categorias:', error);
      return [];
    }

    // Filtrar categorias que têm conteúdo publicado
    const categoriesWithContent = data.filter(cat => {
      const publishedCount = cat.pedbook_conducts_topics?.reduce((total, topic) => {
        const summariesCount = topic.pedbook_conducts_summaries?.filter(s => s.published).length || 0;
        return total + summariesCount;
      }, 0) || 0;
      return publishedCount > 0;
    });

    console.log(`✅ Encontradas ${categoriesWithContent.length} categorias com conteúdo`);
    return categoriesWithContent;

  } catch (err) {
    console.error('❌ Erro ao buscar categorias:', err.message);
    return [];
  }
}

/**
 * Função para normalizar texto (remover acentos e caracteres especiais)
 */
function normalizeText(text) {
  return text
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove acentos
    .replace(/[^\w\s]/g, ' ') // Remove caracteres especiais
    .replace(/\s+/g, ' ') // Remove espaços duplos
    .trim();
}



/**
 * Extrair texto do conteúdo JSON
 */
function extractTextFromContent(content) {
  if (!content) return '';

  try {
    if (typeof content === 'string') {
      return content.substring(0, 500);
    }

    if (typeof content === 'object') {
      // Tentar extrair texto de diferentes estruturas possíveis
      if (content.text) return content.text.substring(0, 500);
      if (content.content) return extractTextFromContent(content.content);
      if (Array.isArray(content)) {
        return content.map(item => extractTextFromContent(item)).join(' ').substring(0, 500);
      }

      // Converter objeto para string e extrair
      return JSON.stringify(content).replace(/[{}"\[\]]/g, ' ').substring(0, 500);
    }

    return '';
  } catch (err) {
    return '';
  }
}

/**
 * Gerar páginas HTML das condutas
 */
async function generateConductPages(conductsData) {
  if (!conductsData || conductsData.length === 0) {
    console.log('❌ Sem dados para gerar páginas');
    return { successCount: 0, conductsData: [] };
  }

  console.log(`\n📄 Gerando ${conductsData.length} páginas de condutas...`);

  const distPath = path.join(process.cwd(), 'dist');

  // Verificar se dist existe
  if (!fs.existsSync(distPath)) {
    console.log('❌ Diretório dist/ não encontrado');
    return { successCount: 0, conductsData: [] };
  }

  // Verificar se index.html existe
  const indexPath = path.join(distPath, 'index.html');
  if (!fs.existsSync(indexPath)) {
    console.log('❌ index.html não encontrado');
    return { successCount: 0, conductsData: [] };
  }

  // Ler HTML base uma vez
  const baseHtml = fs.readFileSync(indexPath, 'utf8');
  let successCount = 0;

  // Processar cada conduta
  for (const conductData of conductsData) {
    try {
      // Usar slug diretamente do banco (já normalizado)
      const slug = conductData.slug;
      const category = conductData.pedbook_conducts_topics?.pedbook_conducts_categories;

      if (!category || !category.slug) {
        console.log(`⚠️ Pulando conduta sem categoria: ${conductData.title}`);
        continue;
      }

      // Criar diretório da conduta com estrutura correta
      const conductDir = path.join(distPath, 'condutas-e-manejos', category.slug, slug);
      if (!fs.existsSync(conductDir)) {
        fs.mkdirSync(conductDir, { recursive: true });
      }

      // Gerar conteúdo personalizado
      const title = `${conductData.title} - Conduta Pediátrica | PedBook`;
      const categoryName = conductData.pedbook_conducts_topics?.pedbook_conducts_categories?.name || 'Condutas Médicas';
      const topicName = conductData.pedbook_conducts_topics?.name || conductData.title;

      // Extrair resumo do conteúdo
      const contentSummary = extractTextFromContent(conductData.content || conductData.sections);
      const cleanSummary = contentSummary
        .replace(/<[^>]*>/g, ' ') // Remove tags HTML
        .replace(/\n/g, ' ')
        .replace(/\r/g, ' ')
        .replace(/\s+/g, ' ')
        .replace(/##\./g, '') // Remove marcadores de seção
        .replace(/\*\*/g, '') // Remove markdown bold
        .trim();

      const description = `${conductData.title}: ${cleanSummary.substring(0, 120)}... Conduta médica completa, diagnóstico e tratamento em pediatria.`;

      // Keywords específicas
      const normalizedTitle = normalizeText(conductData.title);
      const normalizedCategory = normalizeText(categoryName);
      const keywords = `${normalizedTitle}, conduta ${normalizedTitle}, ${normalizedTitle} pediatria, tratamento ${normalizedTitle}, ${normalizedCategory}, conduta medica, pediatria`;

      let customHtml = baseHtml;

      // Substituir título
      customHtml = customHtml.replace(
        /<title>.*?<\/title>/,
        `<title>${title}</title>`
      );

      // Substituir meta description
      customHtml = customHtml.replace(
        /<meta name="description" content=".*?" ?\/?>/,
        `<meta name="description" content="${description}" />`
      );

      // Substituir meta keywords
      customHtml = customHtml.replace(
        /<meta name="keywords" content=".*?" ?\/?>/,
        `<meta name="keywords" content="${keywords}" />`
      );

      // URL correta com categoria
      const conductUrl = `https://pedb.com.br/condutas-e-manejos/${category.slug}/${slug}`;

      // Atualizar canonical URL
      customHtml = customHtml.replace(
        /<link rel="canonical" href=".*?" \/>/,
        `<link rel="canonical" href="${conductUrl}" />`
      );

      // Atualizar Open Graph tags
      customHtml = customHtml.replace(
        /<meta property="og:title" content=".*?" ?\/?>/,
        `<meta property="og:title" content="${title}" />`
      );

      customHtml = customHtml.replace(
        /<meta property="og:description" content=".*?" ?\/?>/,
        `<meta property="og:description" content="${description}" />`
      );

      customHtml = customHtml.replace(
        /<meta property="og:url" content=".*?" ?\/?>/,
        `<meta property="og:url" content="${conductUrl}" />`
      );

      // Atualizar Twitter Cards
      customHtml = customHtml.replace(
        /<meta name="twitter:title" content=".*?" ?\/?>/,
        `<meta name="twitter:title" content="${title}" />`
      );

      customHtml = customHtml.replace(
        /<meta name="twitter:description" content=".*?" ?\/?>/,
        `<meta name="twitter:description" content="${description}" />`
      );

      customHtml = customHtml.replace(
        /<meta name="twitter:url" content=".*?" ?\/?>/,
        `<meta name="twitter:url" content="${conductUrl}" />`
      );

      // Adicionar conteúdo SEO
      const seoContent = `
    <div id="root"></div>
    <!-- SEO Content for Crawlers -->
    <div style="display: none;" data-seo-content>
      <h1>${conductData.title} - Conduta Pediátrica</h1>
      <p>Conduta médica completa para ${conductData.title} em pediatria, incluindo diagnóstico, tratamento e manejo clínico.</p>

      <h2>Categoria</h2>
      <p>${categoryName}</p>

      <h2>Tópico</h2>
      <p>${topicName}</p>

      <h2>Resumo da Conduta</h2>
      <p>${cleanSummary}</p>

      <h2>Aplicação Clínica</h2>
      <p>Esta conduta médica fornece orientações baseadas em evidências para o manejo de ${conductData.title} em pacientes pediátricos.</p>

      <h2>Público-Alvo</h2>
      <p>Pediatras, médicos residentes, estudantes de medicina e profissionais de saúde que atendem crianças e adolescentes.</p>
    </div>`;

      customHtml = customHtml.replace('<div id="root"></div>', seoContent);

      // Rich Snippets - MedicalGuideline Schema avançado
      const medicalGuidelineSchema = {
        "@context": "https://schema.org",
        "@type": "MedicalGuideline",
        "name": conductData.title,
        "description": description,
        "url": conductUrl,
        "medicalSpecialty": {
          "@type": "MedicalSpecialty",
          "name": "Pediatria"
        },
        "audience": {
          "@type": "MedicalAudience",
          "audienceType": "Profissionais de Saúde Pediátrica"
        },
        "about": {
          "@type": "MedicalCondition",
          "name": conductData.title,
          "medicalSpecialty": "Pediatria"
        },
        "publisher": {
          "@type": "Organization",
          "name": "PedBook",
          "url": "https://pedb.com.br"
        },
        "datePublished": new Date().toISOString().split('T')[0],
        "dateModified": new Date().toISOString().split('T')[0]
      };

      // FAQ Schema para Rich Snippets das condutas
      const faqSchema = {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": [
          {
            "@type": "Question",
            "name": `O que é ${conductData.title}?`,
            "acceptedAnswer": {
              "@type": "Answer",
              "text": `${conductData.title} é uma condição/procedimento em pediatria. Esta página fornece informações baseadas em literatura médica para profissionais de saúde.`
            }
          },
          {
            "@type": "Question",
            "name": `Como abordar ${conductData.title} em pediatria?`,
            "acceptedAnswer": {
              "@type": "Answer",
              "text": `A abordagem de ${conductData.title} segue protocolos estabelecidos na literatura médica. Esta conduta serve como material de consulta para profissionais de saúde.`
            }
          },
          {
            "@type": "Question",
            "name": `Onde encontrar mais informações sobre ${conductData.title}?`,
            "acceptedAnswer": {
              "@type": "Answer",
              "text": `Esta página compila informações sobre ${conductData.title} como material de estudo. Sempre consulte literatura médica atualizada e orientação profissional.`
            }
          }
        ]
      };

      // Breadcrumbs Schema
      const breadcrumbsSchema = {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "PedBook",
            "item": "https://pedb.com.br"
          },
          {
            "@type": "ListItem",
            "position": 2,
            "name": "Condutas e Manejos",
            "item": "https://pedb.com.br/condutas-e-manejos"
          },
          {
            "@type": "ListItem",
            "position": 3,
            "name": categoryName,
            "item": `https://pedb.com.br/condutas-e-manejos/${category.slug}`
          },
          {
            "@type": "ListItem",
            "position": 4,
            "name": conductData.title,
            "item": conductUrl
          }
        ]
      };

      // Gerar JSON-LD para todos os schemas
      const guidelineJsonLd = `<script type="application/ld+json">${JSON.stringify(medicalGuidelineSchema, null, 2)}</script>`;
      const faqJsonLd = `<script type="application/ld+json">${JSON.stringify(faqSchema, null, 2)}</script>`;
      const breadcrumbsJsonLd = `<script type="application/ld+json">${JSON.stringify(breadcrumbsSchema, null, 2)}</script>`;

      customHtml = customHtml.replace('</head>', `  ${guidelineJsonLd}\n  ${faqJsonLd}\n  ${breadcrumbsJsonLd}\n</head>`);

      // Salvar arquivo
      fs.writeFileSync(path.join(conductDir, 'index.html'), customHtml);
      console.log(`✅ Criado: /condutas-e-manejos/${category.slug}/${slug}/index.html`);
      successCount++;

    } catch (err) {
      console.error(`❌ Erro ao gerar ${slug}:`, err.message);
    }
  }

  console.log(`\n🎉 Geradas ${successCount}/${conductsData.length} páginas de condutas!`);
  return { successCount, conductsData };
}

// Executar
async function main() {
  const conductsData = await fetchConductsData();
  const categoriesData = await fetchCategoriesData();

  if (conductsData && conductsData.length > 0) {
    const result = await generateConductPages(conductsData);
    console.log(`\n🎉 Processo de condutas concluído!`);
    console.log(`📄 ${result.successCount} páginas de condutas geradas!`);
  }

  console.log(`\n📊 Resumo:`);
  console.log(`📄 ${conductsData.length} condutas individuais`);
  console.log(`📂 ${categoriesData.length} categorias`);
  console.log(`🎯 Total de páginas SEO: ${conductsData.length + categoriesData.length + 1} (+ página principal)`);
}

main().catch(console.error);
