
import { useState } from 'react';
import { supabase } from "@/integrations/supabase/client";
import type { Question } from "@/types/question";
import { useToast } from "@/hooks/use-toast";
import { useQueryClient } from '@tanstack/react-query';
import { useUserData } from '@/hooks/useUserData';

export const useAnswerSubmission = (
  sessionId: string,
  onAnswerSubmitted: () => void
) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { isAdmin } = useUserData();

  const submitAnswer = async (
    userId: string,
    question: Question,
    selectedAnswer: string,
    timeSpent: number
  ) => {
    if (isSubmitting) {
      return false;
    }

    setIsSubmitting(true);

    try {
      // Verificar se o usuário está autenticado
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('❌ [useAnswerSubmission] Usuário não autenticado');
        toast({
          title: "Erro de autenticação",
          description: "Você precisa estar logado para salvar respostas.",
          variant: "destructive"
        });
        return false;
      }

      // Verificar se o userId fornecido corresponde ao usuário autenticado
      if (user.id !== userId && !isAdmin) {
        console.error('❌ [useAnswerSubmission] Tentativa de submeter resposta para outro usuário');
        toast({
          title: "Erro de permissão",
          description: "Você não tem permissão para salvar respostas para outro usuário.",
          variant: "destructive"
        });
        return false;
      }



      if (question.answer_type === 'DISCURSIVE') {
        // Verificar se specialty_id existe, pois é NOT NULL na tabela
        if (!question.specialty?.id) {
          throw new Error("Especialidade é obrigatória para salvar a resposta");
        }

        const { data: userAnswer, error: answerError } = await supabase
          .from('user_answers')
          .insert({
            user_id: userId,
            question_id: question.id,
            text_answer: selectedAnswer,
            is_correct: true,
            specialty_id: question.specialty.id,
            theme_id: question.theme?.id,
            focus_id: question.focus?.id,
            session_id: sessionId,
            time_spent: timeSpent,
            year: question.year || new Date().getFullYear()
          })
          .select()
          .single();

        if (answerError) {
          throw answerError;
        }

        try {
          const { error: eventError } = await supabase
            .from('session_events')
            .insert({
              session_id: sessionId,
              question_id: question.id,
              response_status: true,
              response_time: timeSpent,
              theme_id: question.theme?.id,
              specialty_id: question.specialty?.id,
              focus_id: question.focus?.id
            });

          if (eventError) {
            // Se o erro for relacionado à tabela não existir ou permissão negada, apenas logamos e continuamos
            if (eventError.code === '42P01' || eventError.code === '42501') {
              // Tabela não acessível, continuar sem salvar evento
            } else {
              throw eventError;
            }
          }
        } catch (eventError) {
          // Capturamos erros relacionados ao evento, mas não interrompemos o fluxo principal
        }

        toast({
          title: "Resposta salva",
          description: "Sua resposta discursiva foi registrada com sucesso",
        });

      } else {
        // Parse the selected answer and correct answer
        const selectedAnswerNum = parseInt(selectedAnswer);

        // Get the correct answer from the question - ensure it's a number
        let correctAnswerNum = typeof question.correct_answer === 'number'
          ? question.correct_answer
          : parseInt(String(question.correct_answer));



        // Check if the selected answer (1-based) matches the correct answer (0-based)
        // We need to adjust since selectedAnswer is 1-based but correctAnswer is 0-based
        const isCorrect = selectedAnswerNum === correctAnswerNum + 1;



        // Verificar se specialty_id existe, pois é NOT NULL na tabela
        // Usar question.specialty?.id ou question.specialty_id como fallback
        const specialtyId = question.specialty?.id || question.specialty_id;
        if (!specialtyId) {
          console.error('❌ [useAnswerSubmission] Erro: Especialidade não encontrada para a questão', question.id);
          console.error('❌ [useAnswerSubmission] Dados da questão:', {
            specialty: question.specialty,
            specialty_id: question.specialty_id,
            theme: question.theme,
            theme_id: question.theme_id,
            focus: question.focus,
            focus_id: question.focus_id
          });
          throw new Error("Especialidade é obrigatória para salvar a resposta");
        }



        const { error: answerError } = await supabase
          .from('user_answers')
          .insert({
            user_id: userId,
            question_id: question.id,
            selected_answer: selectedAnswerNum,
            is_correct: isCorrect,
            specialty_id: specialtyId,
            theme_id: question.theme?.id || question.theme_id,
            focus_id: question.focus?.id || question.focus_id,
            session_id: sessionId,
            time_spent: timeSpent,
            year: question.year || question.exam_year || new Date().getFullYear()
          });

        if (answerError) {
          console.error('❌ [useAnswerSubmission] Erro ao salvar resposta de múltipla escolha:', answerError);
          throw answerError;
        }



        // Preparar dados para session_events
        const sessionEventData = {
          session_id: sessionId,
          question_id: question.id,
          response_status: isCorrect,
          response_time: timeSpent,
          theme_id: question.theme?.id || question.theme_id,
          specialty_id: specialtyId,
          focus_id: question.focus?.id || question.focus_id,
          selected_answer: selectedAnswerNum,
          response: selectedAnswer
        };



        try {
          const { data: eventData, error: eventError } = await supabase
            .from('session_events')
            .insert(sessionEventData)
            .select();

          if (eventError) {
            console.error('❌ [useAnswerSubmission] Erro detalhado ao salvar evento:', {
              code: eventError.code,
              message: eventError.message,
              details: eventError.details,
              hint: eventError.hint,
              sessionEventData
            });

            // Se o erro for relacionado à tabela não existir ou permissão negada, apenas continuamos
            if (eventError.code === '42P01') {
              // Tabela session_events não encontrada
            } else if (eventError.code === '42501') {
              // Permissão negada para acessar a tabela session_events
            } else {
              throw eventError;
            }
          } else {

          }
        } catch (eventError) {
          // Capturamos erros relacionados ao evento, mas não interrompemos o fluxo principal
          console.error('❌ [useAnswerSubmission] Erro ao processar o evento de sessão, mas continuando:', eventError);
        }

        // ✅ Invalidar caches relevantes após cada resposta
        queryClient.invalidateQueries({ queryKey: ['correct-questions'] });
        queryClient.invalidateQueries({ queryKey: ['user-statistics'] });

        // Se a resposta estiver correta, fazer invalidação adicional
        if (isCorrect) {
          // Invalidação adicional para respostas corretas
        }
      }

      try {


        const { data: session, error: sessionError } = await supabase
          .from('study_sessions')
          .select('current_question_index, total_questions')
          .eq('id', sessionId)
          .single();

        if (sessionError) {
          // Se o erro for relacionado à tabela não existir ou permissão negada, apenas logamos e continuamos
          if (sessionError.code === '42P01') {
            console.warn('⚠️ [useAnswerSubmission] Tabela study_sessions não encontrada. Continuando sem atualizar a sessão:', sessionError);
          } else if (sessionError.code === '42501') {
            console.warn('⚠️ [useAnswerSubmission] Permissão negada para acessar a tabela study_sessions. Continuando sem atualizar a sessão:', sessionError);
          } else {
            console.error('❌ [useAnswerSubmission] Erro ao buscar dados da sessão:', sessionError);
            throw sessionError;
          }
        } else {


          if (session) {
            const newIndex = session.current_question_index + 1;
            const isCompleted = newIndex >= session.total_questions;





            const { error: updateError } = await supabase
              .from('study_sessions')
              .update({
                current_question_index: newIndex,
                status: isCompleted ? 'completed' : 'in_progress',
                completed_at: isCompleted ? new Date().toISOString() : null
              })
              .eq('id', sessionId);

            if (updateError) {
              // Se o erro for relacionado à tabela não existir ou permissão negada, apenas logamos e continuamos
              if (updateError.code === '42P01') {
                console.warn('⚠️ [useAnswerSubmission] Tabela study_sessions não encontrada. Continuando sem atualizar a sessão:', updateError);
              } else if (updateError.code === '42501') {
                console.warn('⚠️ [useAnswerSubmission] Permissão negada para atualizar a tabela study_sessions. Continuando sem atualizar a sessão:', updateError);
              } else {
                console.error('❌ [useAnswerSubmission] Erro ao atualizar sessão:', updateError);
                throw updateError;
              }
            } else {
              // ✅ Invalidar TODOS os caches relacionados quando sessão é completada
              if (isCompleted) {
                // ✅ FORÇAR REFETCH em vez de apenas invalidar
                await queryClient.refetchQueries({ queryKey: ['user-statistics'] });
                await queryClient.refetchQueries({ queryKey: ['user-study-stats'] });

                // ✅ FORÇAR REFETCH das sessões (incluindo todas as páginas)
                await queryClient.refetchQueries({
                  predicate: (query) => {
                    const key = query.queryKey[0] as string;
                    const isStudySessions = key === 'study-sessions';
                    const isFlashcardSessions = key === 'flashcard-sessions';
                    return isStudySessions || isFlashcardSessions;
                  }
                });

                // Invalidar dados de progresso
                await queryClient.invalidateQueries({ queryKey: ['progress'] });
                await queryClient.invalidateQueries({ queryKey: ['session-history'] });

                // Invalidar qualquer query que contenha 'session' ou 'statistics'
                await queryClient.invalidateQueries({
                  predicate: (query) => {
                    const key = query.queryKey[0] as string;
                    return key?.includes('session') || key?.includes('statistics') || key?.includes('progress');
                  }
                });

                // ✅ Aguardar um pouco para garantir que as queries sejam re-executadas
                await new Promise(resolve => setTimeout(resolve, 100));
              }
            }
          }
        }
      } catch (sessionError) {
        // Capturamos erros relacionados à sessão, mas não interrompemos o fluxo principal
        console.error('❌ [useAnswerSubmission] Erro ao processar a sessão, mas continuando:', sessionError);
      }


      onAnswerSubmitted();
      return true;

    } catch (error: any) {

      toast({
        title: "Erro ao salvar resposta",
        description: "Ocorreu um erro ao tentar salvar sua resposta. Por favor, tente novamente.",
        variant: "destructive"
      });
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    submitAnswer,
    isSubmitting
  };
};
