import { Card } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";

interface GlucoseAdjustmentProps {
  isLow: boolean;
  onReturn: () => void;
}

export const GlucoseAdjustment = ({ isLow, onReturn }: GlucoseAdjustmentProps) => {
  return (
    <Card className="p-6 space-y-4">
      <h3 className="text-lg font-semibold">
        {isLow ? "Ajuste para Glicemia Baixa" : "Ajuste para Glicemia Alta"}
      </h3>
      <Alert className="bg-yellow-50 border-yellow-200">
        <AlertDescription className="space-y-2">
          <h4 className="font-medium text-yellow-800">Recomendações:</h4>
          <ul className="list-disc pl-4 text-yellow-700">
            {isLow ? (
              <>
                <li>Aumentar a TIG</li>
                <li>Manter insulinoterapia</li>
                <p className="text-yellow-700 mt-2">
                  O aumento da TIG poderá ser realizado através do aumento da concentração 
                  de glicose no soro, observando-se o limite máximo de 12,5% no acesso periférico.
                </p>
              </>
            ) : (
              <>
                <li>Reduzir a TIG</li>
                <li>Manter insulinoterapia</li>
                <p className="text-yellow-700 mt-2">
                  TIG mínima de 2,5-3,5 mg/kg/minuto. Se atingir a TIG mínima não reduzir a TIG.
                </p>
              </>
            )}
          </ul>
        </AlertDescription>
      </Alert>
      <Button onClick={onReturn} className="w-full">
        Continuar Monitoramento
      </Button>
    </Card>
  );
};