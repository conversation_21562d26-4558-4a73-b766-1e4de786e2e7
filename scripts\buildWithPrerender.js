/**
 * Script de build personalizado com prerendering
 * Gera HTMLs estáticos para cada medicamento e página importante
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { generateRoutes, generateSitemap } from './generateRoutes.js';

// Função para gerar novas seções
async function generateNewSections() {
  try {
    console.log('🔧 Executando geração de novas seções...');
    execSync('node scripts/generateNewSections.js', { stdio: 'inherit' });
    console.log('✅ Novas seções geradas com sucesso!');
  } catch (error) {
    console.error('❌ Erro ao gerar novas seções:', error.message);
  }
}

console.log('🚀 Iniciando build com prerendering...');

async function buildWithPrerender() {
  try {
    // Passo 1: Gerar rotas dinamicamente
    console.log('\n📋 Passo 1: Gerando rotas...');
    const routes = await generateRoutes();
    
    // Passo 2: Gerar sitemap
    console.log('\n🗺️ Passo 2: Gerando sitemap...');
    await generateSitemap(routes);
    
    // Passo 3: Build normal do Vite
    console.log('\n🔨 Passo 3: Executando build do Vite...');
    execSync('npm run build', { stdio: 'inherit' });

    // Passo 3.5: Gerar páginas das novas seções (incluindo medicamentos na amamentação)
    console.log('\n🔧 Passo 3.5: Gerando páginas das novas seções...');
    await generateNewSections();

    // Passo 4: Prerender páginas específicas
    console.log('\n🎭 Passo 4: Pré-renderizando páginas...');
    await prerenderPages(routes);
    
    // Passo 5: Gerar meta tags específicas
    console.log('\n🏷️ Passo 5: Gerando meta tags...');
    await generateMetaTags();
    
    console.log('\n✅ Build com prerendering concluído!');
    console.log(`📊 Total de páginas geradas: ${routes.length}`);
    
  } catch (error) {
    console.error('\n❌ Erro no build:', error);
    process.exit(1);
  }
}

/**
 * Pré-renderiza páginas específicas usando Puppeteer
 */
async function prerenderPages(routes) {
  const puppeteer = await import('puppeteer');
  
  console.log('🎭 Iniciando prerendering com Puppeteer...');
  
  const browser = await puppeteer.default.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const distPath = path.join(process.cwd(), 'dist');
  
  // Páginas prioritárias para prerender - TODAS as páginas importantes
  const priorityRoutes = routes.filter(route =>
    route.includes('/medicamentos/') ||
    route.includes('/medicamentos-amamentacao/') ||
    route === '/medicamentos-amamentacao' ||
    route.includes('/bulas-profissionais/') ||
    route.includes('/condutas-e-manejos/') ||
    route.includes('/calculadoras/') ||
    route.includes('/puericultura/') ||
    route.includes('/poisonings/') ||
    route.includes('/fluxogramas/') ||
    route === '/' ||
    route === '/medicamentos/painel' ||
    route === '/icd' ||
    route === '/dnpm'
  ); // Remover limite - pegar TODAS as páginas importantes
  
  console.log(`🎯 Pré-renderizando ${priorityRoutes.length} páginas prioritárias...`);

  // Categorizar rotas para logs detalhados
  const routeCategories = {
    homepage: priorityRoutes.filter(r => r === '/'),
    medications: priorityRoutes.filter(r => r.includes('/medicamentos/') && !r.includes('/medicamentos-amamentacao')),
    breastfeedingMedications: priorityRoutes.filter(r => r.includes('/medicamentos-amamentacao')),
    professionalLabels: priorityRoutes.filter(r => r.includes('/bulas-profissionais/')),
    conducts: priorityRoutes.filter(r => r.includes('/condutas-e-manejos/')),
    calculators: priorityRoutes.filter(r => r.includes('/calculadoras/')),
    childcare: priorityRoutes.filter(r => r.includes('/puericultura/') || r === '/dnpm'),
    poisonings: priorityRoutes.filter(r => r.includes('/poisonings/')),
    flowcharts: priorityRoutes.filter(r => r.includes('/fluxogramas/')),
    icd: priorityRoutes.filter(r => r === '/icd'),
    other: priorityRoutes.filter(r =>
      !r.includes('/medicamentos/') &&
      !r.includes('/medicamentos-amamentacao') &&
      !r.includes('/bulas-profissionais/') &&
      !r.includes('/condutas-e-manejos/') &&
      !r.includes('/calculadoras/') &&
      !r.includes('/puericultura/') &&
      !r.includes('/poisonings/') &&
      !r.includes('/fluxogramas/') &&
      r !== '/icd' &&
      r !== '/dnpm' &&
      r !== '/'
    )
  };

  console.log('\n📊 PÁGINAS PARA PRERENDER POR CATEGORIA:');
  console.log(`🏠 Homepage: ${routeCategories.homepage.length} páginas`);
  console.log(`💊 Medicamentos: ${routeCategories.medications.length} páginas`);
  console.log(`🤱 Medicamentos na Amamentação: ${routeCategories.breastfeedingMedications.length} páginas`);
  console.log(`📋 Bulas Profissionais: ${routeCategories.professionalLabels.length} páginas`);
  console.log(`📚 Condutas e Manejos: ${routeCategories.conducts.length} páginas`);
  console.log(`🧮 Calculadoras: ${routeCategories.calculators.length} páginas`);
  console.log(`👶 Puericultura: ${routeCategories.childcare.length} páginas`);
  console.log(`☠️ Intoxicações: ${routeCategories.poisonings.length} páginas`);
  console.log(`🌊 Fluxogramas: ${routeCategories.flowcharts.length} páginas`);
  console.log(`🏥 CID-10: ${routeCategories.icd.length} páginas`);
  console.log(`📄 Outras: ${routeCategories.other.length} páginas\n`);

  let processedCount = 0;

  for (const route of priorityRoutes) {
    try {
      const page = await browser.newPage();

      // Configurar página
      await page.setViewport({ width: 1200, height: 800 });
      await page.setUserAgent('Mozilla/5.0 (compatible; PedBookBot/1.0; +https://pedb.com.br)');

      // Navegar para a página
      const url = `http://localhost:8080${route}`;
      await page.goto(url, {
        waitUntil: 'networkidle0',
        timeout: 30000
      });

      // Aguardar renderização
      await page.waitForTimeout(2000);

      // Obter HTML renderizado
      const html = await page.content();

      // Salvar HTML
      const filePath = route === '/' ?
        path.join(distPath, 'index.html') :
        path.join(distPath, route.slice(1), 'index.html');

      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      fs.writeFileSync(filePath, html);

      // Log categorizado
      let category = '📄';
      if (route === '/') category = '🏠';
      else if (route.includes('/medicamentos-amamentacao')) category = '🤱';
      else if (route.includes('/medicamentos/')) category = '💊';
      else if (route.includes('/bulas-profissionais/')) category = '📋';
      else if (route.includes('/condutas-e-manejos/')) category = '📚';
      else if (route.includes('/calculadoras/')) category = '🧮';
      else if (route.includes('/puericultura/') || route === '/dnpm') category = '👶';
      else if (route.includes('/poisonings/')) category = '☠️';
      else if (route.includes('/fluxogramas/')) category = '🌊';
      else if (route === '/icd') category = '🏥';

      processedCount++;
      console.log(`${category} [${processedCount}/${priorityRoutes.length}] Gerado: ${route}`);

      await page.close();

    } catch (error) {
      console.warn(`⚠️ Erro ao renderizar ${route}:`, error.message);
    }
  }
  
  await browser.close();
  console.log('🎉 Prerendering concluído!');
}

/**
 * Gera meta tags específicas para todas as seções
 */
async function generateMetaTags() {
  console.log('🏷️ Gerando meta tags específicas para todas as seções...');

  // Carregar dados das rotas
  const routesData = JSON.parse(
    fs.readFileSync(path.join(process.cwd(), 'prerender-routes.json'), 'utf8')
  );

  const distPath = path.join(process.cwd(), 'dist');

  // Gerar meta tags para medicamentos
  await generateMedicationMetaTags(routesData, distPath);

  // Gerar meta tags para bulas profissionais
  await generateProfessionalLabelsMetaTags(routesData, distPath);

  // Gerar meta tags para condutas e manejos
  await generateConductsMetaTags(routesData, distPath);

  // Gerar meta tags para calculadoras
  await generateCalculatorMetaTags(routesData, distPath);

  // Gerar meta tags para puericultura
  await generateChildcareMetaTags(routesData, distPath);

  // Gerar meta tags para intoxicações
  await generatePoisoningMetaTags(routesData, distPath);

  // Gerar meta tags para fluxogramas
  await generateFlowchartMetaTags(routesData, distPath);

  // Gerar meta tags para medicamentos na amamentação
  await generateBreastfeedingMedicationMetaTags(routesData, distPath);

  console.log('🎉 Meta tags geradas para todas as seções!');
}

/**
 * Gera meta tags específicas para medicamentos
 */
async function generateMedicationMetaTags(routesData, distPath) {
  console.log('💊 Gerando meta tags para medicamentos...');

  // Processar cada medicamento
  routesData.medications.forEach(med => {
    if (!med.slug) return;
    
    const htmlPath = path.join(distPath, 'medicamentos', med.slug, 'index.html');
    
    if (fs.existsSync(htmlPath)) {
      let html = fs.readFileSync(htmlPath, 'utf8');
      
      // Substituir meta tags genéricas por específicas
      const specificTitle = `${med.name} - Dose Pediátrica | PedBook`;
      const specificDescription = `Dose pediátrica do ${med.name}: calculadora automática, indicações, contraindicações e posologia completa para pediatria. ${med.description || ''}`.substring(0, 160);
      const specificKeywords = `${med.name.toLowerCase()}, dose pediátrica, posologia infantil, calculadora dose, pediatria, ${med.category || ''}`;
      
      // Substituir title
      html = html.replace(
        /<title>.*?<\/title>/,
        `<title>${specificTitle}</title>`
      );
      
      // Substituir description
      html = html.replace(
        /<meta name="description" content=".*?">/,
        `<meta name="description" content="${specificDescription}">`
      );
      
      // Substituir keywords
      html = html.replace(
        /<meta name="keywords" content=".*?">/,
        `<meta name="keywords" content="${specificKeywords}">`
      );
      
      // Substituir Open Graph
      html = html.replace(
        /<meta property="og:title" content=".*?">/,
        `<meta property="og:title" content="${specificTitle}">`
      );
      
      html = html.replace(
        /<meta property="og:description" content=".*?">/,
        `<meta property="og:description" content="${specificDescription}">`
      );
      
      html = html.replace(
        /<meta property="og:url" content=".*?">/,
        `<meta property="og:url" content="https://pedb.com.br/medicamentos/${med.slug}">`
      );
      
      // Adicionar JSON-LD específico para medicamento
      const jsonLd = {
        "@context": "https://schema.org",
        "@type": "Drug",
        "name": med.name,
        "description": specificDescription,
        "url": `https://pedb.com.br/medicamentos/${med.slug}`,
        "manufacturer": "Diversos laboratórios",
        "activeIngredient": med.name,
        "dosageForm": "Suspensão oral, Comprimido",
        "specialty": "Pediatria",
        "audience": {
          "@type": "MedicalAudience",
          "audienceType": "Profissionais de Saúde"
        }
      };
      
      // Inserir JSON-LD antes do </head>
      html = html.replace(
        '</head>',
        `  <script type="application/ld+json">${JSON.stringify(jsonLd, null, 2)}</script>\n</head>`
      );
      
      fs.writeFileSync(htmlPath, html);
      console.log(`✅ Meta tags atualizadas: ${med.name}`);
    }
  });

  console.log('💊 Meta tags de medicamentos concluídas!');
}

/**
 * Gera meta tags específicas para calculadoras
 */
async function generateCalculatorMetaTags(routesData, distPath) {
  console.log('🧮 Gerando meta tags para calculadoras...');

  routesData.calculators.forEach(calc => {
    if (!calc.slug) return;

    const htmlPath = path.join(distPath, 'calculadoras', calc.slug, 'index.html');

    if (fs.existsSync(htmlPath)) {
      let html = fs.readFileSync(htmlPath, 'utf8');

      const specificTitle = `${calc.name} | PedBook`;
      const specificDescription = `${calc.name}: calculadora pediátrica automática com resultados precisos. Ferramenta essencial para profissionais da saúde.`;

      // Atualizar meta tags
      html = html.replace(/<title>.*?<\/title>/, `<title>${specificTitle}</title>`);
      html = html.replace(/<meta name="description" content=".*?">/, `<meta name="description" content="${specificDescription}">`);

      fs.writeFileSync(htmlPath, html);
      console.log(`🧮 Meta tags atualizadas: ${calc.name}`);
    }
  });

  console.log('🧮 Meta tags de calculadoras concluídas!');
}

/**
 * Gera meta tags específicas para puericultura
 */
async function generateChildcareMetaTags(routesData, distPath) {
  console.log('👶 Gerando meta tags para puericultura...');

  const childcarePages = [
    { slug: 'puericultura', name: 'Puericultura' },
    { slug: 'puericultura/curva-de-crescimento', name: 'Curvas de Crescimento' },
    { slug: 'puericultura/calendario-vacinal', name: 'Calendário Vacinal' },
    { slug: 'puericultura/formulas', name: 'Fórmulas Infantis' },
    { slug: 'puericultura/suplementacao-infantil', name: 'Suplementação Infantil' },
    { slug: 'puericultura/patient-overview', name: 'Visão Geral do Paciente' },
    { slug: 'dnpm', name: 'DNPM' }
  ];

  childcarePages.forEach(page => {
    const htmlPath = path.join(distPath, page.slug, 'index.html');

    if (fs.existsSync(htmlPath)) {
      let html = fs.readFileSync(htmlPath, 'utf8');

      const specificTitle = `${page.name} - Puericultura | PedBook`;
      const specificDescription = `${page.name}: ferramenta especializada para acompanhamento pediátrico e desenvolvimento infantil.`;

      // Atualizar meta tags
      html = html.replace(/<title>.*?<\/title>/, `<title>${specificTitle}</title>`);
      html = html.replace(/<meta name="description" content=".*?">/, `<meta name="description" content="${specificDescription}">`);

      fs.writeFileSync(htmlPath, html);
      console.log(`👶 Meta tags atualizadas: ${page.name}`);
    }
  });

  console.log('👶 Meta tags de puericultura concluídas!');
}

/**
 * Gera meta tags específicas para intoxicações
 */
async function generatePoisoningMetaTags(routesData, distPath) {
  console.log('☠️ Gerando meta tags para intoxicações...');

  const poisoningPages = [
    { slug: 'poisonings', name: 'Intoxicações' },
    { slug: 'poisonings/benzodiazepinicos', name: 'Benzodiazepínicos' },
    { slug: 'poisonings/opioides', name: 'Opioides' },
    { slug: 'poisonings/anticolinergicos', name: 'Anticolinérgicos' },
    { slug: 'poisonings/simpatomimeticos', name: 'Simpatomiméticos' },
    { slug: 'poisonings/colinergicos', name: 'Colinérgicos' },
    { slug: 'poisonings/metemoglobinemia', name: 'Metemoglobinemia' },
    { slug: 'poisonings/paracetamol', name: 'Paracetamol' },
    { slug: 'poisonings/antidepressivos_triciclicos', name: 'Antidepressivos Tricíclicos' },
    { slug: 'poisonings/betabloqueadores', name: 'Betabloqueadores' }
  ];

  poisoningPages.forEach(page => {
    const htmlPath = path.join(distPath, page.slug, 'index.html');

    if (fs.existsSync(htmlPath)) {
      let html = fs.readFileSync(htmlPath, 'utf8');

      const specificTitle = `${page.name} - Intoxicações | PedBook`;
      const specificDescription = `${page.name}: manejo de intoxicação pediátrica com antídotos e doses específicas para emergências.`;

      // Atualizar meta tags
      html = html.replace(/<title>.*?<\/title>/, `<title>${specificTitle}</title>`);
      html = html.replace(/<meta name="description" content=".*?">/, `<meta name="description" content="${specificDescription}">`);

      fs.writeFileSync(htmlPath, html);
      console.log(`☠️ Meta tags atualizadas: ${page.name}`);
    }
  });

  console.log('☠️ Meta tags de intoxicações concluídas!');
}

/**
 * Gera meta tags específicas para fluxogramas
 */
async function generateFlowchartMetaTags(routesData, distPath) {
  console.log('🌊 Gerando meta tags para fluxogramas...');

  const flowchartPages = [
    { slug: 'fluxogramas/asma', name: 'Asma' },
    { slug: 'fluxogramas/anafilaxia', name: 'Anafilaxia' },
    { slug: 'fluxogramas/convulsao', name: 'Convulsão' },
    { slug: 'fluxogramas/cetoacidose', name: 'Cetoacidose' },
    { slug: 'fluxogramas/dengue', name: 'Dengue' },
    { slug: 'fluxogramas/pecarn', name: 'PECARN' },
    { slug: 'fluxogramas/animais-peconhentos/botrópico', name: 'Acidente Botrópico' },
    { slug: 'fluxogramas/animais-peconhentos/crotálico', name: 'Acidente Crotálico' },
    { slug: 'fluxogramas/animais-peconhentos/elapídico', name: 'Acidente Elapídico' },
    { slug: 'fluxogramas/animais-peconhentos/loxoscélico', name: 'Acidente Loxoscélico' },
    { slug: 'fluxogramas/animais-peconhentos/phoneutria', name: 'Acidente por Phoneutria' },
    { slug: 'fluxogramas/animais-peconhentos/escorpiao', name: 'Acidente Escorpiônico' }
  ];

  flowchartPages.forEach(page => {
    const htmlPath = path.join(distPath, page.slug, 'index.html');

    if (fs.existsSync(htmlPath)) {
      let html = fs.readFileSync(htmlPath, 'utf8');

      const specificTitle = `${page.name} - Fluxograma | PedBook`;
      const specificDescription = `${page.name}: fluxograma pediátrico para diagnóstico e manejo clínico baseado em evidências.`;

      // Atualizar meta tags
      html = html.replace(/<title>.*?<\/title>/, `<title>${specificTitle}</title>`);
      html = html.replace(/<meta name="description" content=".*?">/, `<meta name="description" content="${specificDescription}">`);

      fs.writeFileSync(htmlPath, html);
      console.log(`🌊 Meta tags atualizadas: ${page.name}`);
    }
  });

  console.log('🌊 Meta tags de fluxogramas concluídas!');
}

/**
 * Gera meta tags específicas para bulas profissionais
 */
async function generateProfessionalLabelsMetaTags(routesData, distPath) {
  console.log('📋 Gerando meta tags para bulas profissionais...');

  if (routesData.professionalLabels) {
    routesData.professionalLabels.forEach(label => {
      if (!label.slug) return;

      const htmlPath = path.join(distPath, 'bulas-profissionais', label.slug, 'index.html');

      if (fs.existsSync(htmlPath)) {
        let html = fs.readFileSync(htmlPath, 'utf8');

        const specificTitle = `${label.title} - Bula Profissional | PedBook`;
        const specificDescription = `${label.title}: bula profissional completa com informações técnicas, posologia, contraindicações e orientações para prescrição pediátrica.`;

        // Atualizar meta tags
        html = html.replace(/<title>.*?<\/title>/, `<title>${specificTitle}</title>`);
        html = html.replace(/<meta name="description" content=".*?">/, `<meta name="description" content="${specificDescription}">`);

        fs.writeFileSync(htmlPath, html);
        console.log(`📋 Meta tags atualizadas: ${label.title}`);
      }
    });
  }

  console.log('📋 Meta tags de bulas profissionais concluídas!');
}

/**
 * Gera meta tags específicas para condutas e manejos
 */
async function generateConductsMetaTags(routesData, distPath) {
  console.log('📚 Gerando meta tags para condutas e manejos...');

  // Página principal
  const mainPage = { slug: 'condutas-e-manejos', name: 'Condutas e Manejos' };
  const htmlPath = path.join(distPath, mainPage.slug, 'index.html');

  if (fs.existsSync(htmlPath)) {
    let html = fs.readFileSync(htmlPath, 'utf8');

    const specificTitle = `${mainPage.name} - Protocolos Pediátricos | PedBook`;
    const specificDescription = `${mainPage.name}: protocolos e diretrizes clínicas para manejo pediátrico baseado em evidências científicas.`;

    // Atualizar meta tags
    html = html.replace(/<title>.*?<\/title>/, `<title>${specificTitle}</title>`);
    html = html.replace(/<meta name="description" content=".*?">/, `<meta name="description" content="${specificDescription}">`);

    fs.writeFileSync(htmlPath, html);
    console.log(`📚 Meta tags atualizadas: ${mainPage.name}`);
  }

  // Páginas específicas de condutas
  if (routesData.conducts) {
    routesData.conducts.forEach(conduct => {
      if (!conduct.slug) return;

      const htmlPath = path.join(distPath, 'condutas-e-manejos', conduct.slug, 'index.html');

      if (fs.existsSync(htmlPath)) {
        let html = fs.readFileSync(htmlPath, 'utf8');

        const specificTitle = `${conduct.title} - Conduta Pediátrica | PedBook`;
        const specificDescription = `${conduct.title}: protocolo clínico pediátrico com diretrizes baseadas em evidências para manejo adequado.`;

        // Atualizar meta tags
        html = html.replace(/<title>.*?<\/title>/, `<title>${specificTitle}</title>`);
        html = html.replace(/<meta name="description" content=".*?">/, `<meta name="description" content="${specificDescription}">`);

        fs.writeFileSync(htmlPath, html);
        console.log(`📚 Meta tags atualizadas: ${conduct.title}`);
      }
    });
  }

  console.log('📚 Meta tags de condutas e manejos concluídas!');
}

/**
 * Gera meta tags específicas para medicamentos na amamentação
 */
async function generateBreastfeedingMedicationMetaTags(routesData, distPath) {
  console.log('🤱 Gerando meta tags para medicamentos na amamentação...');

  // Página principal
  const mainPagePath = path.join(distPath, 'medicamentos-amamentacao', 'index.html');
  if (fs.existsSync(mainPagePath)) {
    let html = fs.readFileSync(mainPagePath, 'utf8');

    const specificTitle = 'Medicamentos na Amamentação | PedBook';
    const specificDescription = 'Guia completo sobre segurança de medicamentos durante a amamentação com classificação de compatibilidade e orientações específicas para lactação.';

    // Atualizar meta tags
    html = html.replace(/<title>.*?<\/title>/, `<title>${specificTitle}</title>`);
    html = html.replace(/<meta name="description" content=".*?">/, `<meta name="description" content="${specificDescription}">`);

    fs.writeFileSync(mainPagePath, html);
    console.log('🤱 Meta tags atualizadas: Página principal de medicamentos na amamentação');
  }

  // Medicamentos individuais não têm páginas dedicadas (só dialogs)
  // Removido: geração de meta tags para páginas individuais
  console.log('🤱 Meta tags: medicamentos individuais não precisam (só dialogs)');

  console.log('🤱 Meta tags de medicamentos na amamentação concluídas!');
}

// Executar build
buildWithPrerender();
