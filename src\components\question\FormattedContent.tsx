
import { useEffect, useState } from 'react';

interface FormattedContentProps {
  content: string;
}

export const FormattedContent = ({ content }: FormattedContentProps) => {
  const [formattedContent, setFormattedContent] = useState<string>("");

  useEffect(() => {
    if (!content) {
      setFormattedContent("");
      return;
    }

    // Verificar o tipo de conteúdo e formatá-lo adequadamente
    const containsNewlineN = content.includes('\n');
    const containsNewlineRN = content.includes('\r\n');

    // Formatação do conteúdo com quebras de linha
    const formatted = formatContentWithBreaks(content);
    setFormattedContent(formatted);
  }, [content]);

  return (
    <div dangerouslySetInnerHTML={{ __html: formattedContent }} />
  );
};

// Função para formatar conteúdo preservando quebras de linha
const formatContentWithBreaks = (content: string): string => {
  if (!content) return '';

  // Substituir quebras de linha por <br> para HTML
  let formattedText = content
    .replace(/\r\n/g, '<br>')
    .replace(/\n/g, '<br>');

  // Substituir espaços múltiplos por &nbsp; para preservar formatação
  formattedText = formattedText.replace(/  /g, ' &nbsp;');

  return formattedText;
};

export default FormattedContent;
