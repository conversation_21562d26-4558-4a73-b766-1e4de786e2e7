export interface Tag {
  name: string;
  type: "fixed" | "multiplier" | "age" | "fixed_by_weight" | "multiplier_by_fixed_age";
  multiplier?: number;
  maxValue?: number;
  ageRanges?: AgeRange[];
  weightRanges?: WeightRange[];
  roundResult?: boolean;
}

export interface DosageFormData {
  medication_id: string;
  name: string;
  summary?: string;
  dosage_template: string;
  tags: Tag[];
  use_case_id?: string;
  age_group: "neonatal" | "pediatric" | "adult";
}

export interface AgeRange {
  startMonth: number;
  endMonth: number;
  value: number;
}

export interface WeightRange {
  startWeight: number;
  endWeight: number;
  value: number;
}

export interface TagData {
  medication_id: string;
  name: string;
  multiplier?: number;
  type: "fixed" | "multiplier" | "age" | "fixed_by_weight" | "multiplier_by_fixed_age";
  max_value?: number;
  start_month?: number;
  end_month?: number;
  start_weight?: number;
  end_weight?: number;
  round_result?: boolean;
}

export interface AgeTagData extends TagData {
  start_month: number;
  end_month: number;
}

export interface WeightTagData extends TagData {
  start_weight: number;
  end_weight: number;
}

export interface OtherTagData extends TagData {
  max_value?: number;
}