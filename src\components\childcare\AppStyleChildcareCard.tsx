import React from "react";
import { Link } from "react-router-dom";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

interface AppStyleChildcareCardProps {
  title: string;
  description?: string;
  icon: LucideIcon;
  color: string;
  path: string;
  badge?: string;
}

const AppStyleChildcareCard: React.FC<AppStyleChildcareCardProps> = ({
  title,
  description,
  icon: Icon,
  color,
  path,
  badge,
}) => {
  // Extract color name from the color class (e.g., "bg-amber-50" -> "amber")
  const colorName = color.includes("-") ? color.split("-")[1] : color;

  // Cor do texto baseada na cor do card
  const getTextColorClass = () => {
    if (color.includes("yellow")) return "text-yellow-700 dark:text-yellow-300";
    if (color.includes("purple")) return "text-purple-700 dark:text-purple-300";
    if (color.includes("blue")) return "text-blue-700 dark:text-blue-300";
    if (color.includes("pink")) return "text-pink-700 dark:text-pink-300";
    if (color.includes("green")) return "text-green-700 dark:text-green-300";
    if (color.includes("amber")) return "text-amber-700 dark:text-amber-300";
    if (color.includes("red")) return "text-red-700 dark:text-red-300";
    if (color.includes("cyan")) return "text-cyan-700 dark:text-cyan-300";
    if (color.includes("indigo")) return "text-indigo-700 dark:text-indigo-300";
    if (color.includes("rose")) return "text-rose-700 dark:text-rose-300";
    return "text-primary dark:text-blue-400";
  };

  // Cor de fundo do ícone baseada na cor do card
  const getBackgroundColorClass = () => {
    if (color.includes("yellow")) return "bg-yellow-50 dark:bg-yellow-900/30";
    if (color.includes("purple")) return "bg-purple-50 dark:bg-purple-900/30";
    if (color.includes("blue")) return "bg-blue-50 dark:bg-blue-900/30";
    if (color.includes("pink")) return "bg-pink-50 dark:bg-pink-900/30";
    if (color.includes("green")) return "bg-green-50 dark:bg-green-900/30";
    if (color.includes("amber")) return "bg-amber-50 dark:bg-amber-900/30";
    if (color.includes("red")) return "bg-red-50 dark:bg-red-900/30";
    if (color.includes("cyan")) return "bg-cyan-50 dark:bg-cyan-900/30";
    if (color.includes("indigo")) return "bg-indigo-50 dark:bg-indigo-900/30";
    if (color.includes("rose")) return "bg-rose-50 dark:bg-rose-900/30";
    return "bg-gray-50 dark:bg-slate-800";
  };

  // Cor da barra superior
  const getTopBarColorClass = () => {
    if (color.includes("yellow")) return "bg-yellow-500";
    if (color.includes("purple")) return "bg-purple-500";
    if (color.includes("blue")) return "bg-blue-500";
    if (color.includes("pink")) return "bg-pink-500";
    if (color.includes("green")) return "bg-green-500";
    if (color.includes("amber")) return "bg-amber-500";
    if (color.includes("red")) return "bg-red-500";
    if (color.includes("cyan")) return "bg-cyan-500";
    if (color.includes("indigo")) return "bg-indigo-500";
    if (color.includes("rose")) return "bg-rose-500";
    return "bg-primary";
  };

  return (
    <Link to={path} className="block h-full">
      <div
        className={cn(
          "relative h-full p-3 sm:p-5 rounded-xl transition-all duration-300 cursor-pointer",
          "bg-white/90 dark:bg-slate-800/90 backdrop-blur-md shadow-md hover:shadow-lg",
          "border border-gray-100 dark:border-gray-700/50",
          "hover:-translate-y-1"
        )}
      >
        {/* Barra de cor na parte superior para aparência de app */}
        <div className={cn(
          "absolute top-0 left-0 right-0 h-1.5 rounded-t-xl",
          getTopBarColorClass()
        )} />

        <div className="flex flex-col items-center text-center h-full justify-between pt-2">
          <div className={cn(
            "w-10 h-10 sm:w-14 sm:h-14 rounded-lg flex items-center justify-center mb-2 sm:mb-3 shadow-sm",
            getBackgroundColorClass(),
          )}>
            <Icon className={cn(
              "w-5 h-5 sm:w-7 sm:h-7",
              getTextColorClass()
            )} />
          </div>

          <h3 className="font-bold text-sm sm:text-base text-gray-800 dark:text-gray-200 line-clamp-2">
            {title}
          </h3>

          {description && (
            <p className="text-[10px] sm:text-xs text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
              {description}
            </p>
          )}

          {/* Badge container */}
          {badge && (
            <div className="mt-2">
              <Badge variant="outline" className="bg-gray-100/80 dark:bg-slate-700/80 border-none text-gray-700 dark:text-gray-300 text-[10px] font-medium px-2 py-0.5 h-auto">
                {badge}
              </Badge>
            </div>
          )}
        </div>
      </div>
    </Link>
  );
};

export default AppStyleChildcareCard;
