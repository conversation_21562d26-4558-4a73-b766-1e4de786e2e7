import { Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { useState } from "react";
import { PrescriptionForm } from "./PrescriptionForm";
import type { Session } from "@supabase/supabase-js";
import { useQueryClient } from "@tanstack/react-query";

interface CreatePrescriptionProps {
  session: Session;
  onSuccess?: () => void;
}

export function CreatePrescription({ session, onSuccess }: CreatePrescriptionProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const handleSuccess = () => {
    setIsOpen(false);
    
    // Invalidate and refetch all prescription-related queries
    queryClient.invalidateQueries({
      queryKey: ["prescription-categories"],
      exact: false,
      refetchType: "all"
    });

    queryClient.invalidateQueries({
      queryKey: ["uncategorized-prescriptions"],
      exact: false,
      refetchType: "all"
    });

    toast({
      title: "Prescrição criada com sucesso!",
      description: "A prescrição foi adicionada à sua lista.",
    });

    onSuccess?.();

    // Reload the page after a short delay to ensure the toast is visible
    setTimeout(() => {
      window.location.reload();
    }, 500);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <Button onClick={() => setIsOpen(true)}>
        <Plus className="h-4 w-4 mr-2" />
        Nova Prescrição
      </Button>

      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto rounded-2xl">
        <DialogHeader>
          <DialogTitle>Nova Prescrição</DialogTitle>
          <DialogDescription>
            Crie uma nova prescrição preenchendo os campos abaixo.
          </DialogDescription>
        </DialogHeader>
        <PrescriptionForm
          session={session}
          onSuccess={handleSuccess}
          onClose={() => setIsOpen(false)}
        />
      </DialogContent>
    </Dialog>
  );
}