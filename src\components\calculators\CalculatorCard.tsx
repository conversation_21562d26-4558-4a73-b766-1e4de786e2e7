
import { Link } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { useAuth } from "@/hooks/useAuth";

interface CalculatorCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  color: string;
  path: string;
  requiresAuth?: boolean;
  badge?: string;
  isHighlighted?: boolean;
}

const CalculatorCard = ({
  title,
  description,
  icon: Icon,
  color,
  path,
  requiresAuth,
  badge,
  isHighlighted,
}: CalculatorCardProps) => {
  const { user } = useAuth();

  // Helper function to get border color based on color name
  const getBorderColor = (colorName: string) => {
    switch(colorName) {
      case 'blue': return 'border-blue-500 dark:border-blue-600';
      case 'orange': return 'border-orange-500 dark:border-orange-600';
      case 'purple': return 'border-purple-500 dark:border-purple-600';
      case 'red': return 'border-red-500 dark:border-red-600';
      case 'yellow': return 'border-yellow-500 dark:border-yellow-600';
      case 'indigo': return 'border-indigo-500 dark:border-indigo-600';
      case 'green': return 'border-green-500 dark:border-green-600';
      default: return 'border-gray-300 dark:border-gray-600';
    }
  };

  // Helper function to get icon background color based on color name
  const getIconBgColor = (colorName: string) => {
    switch(colorName) {
      case 'blue': return 'bg-blue-50 dark:bg-blue-900/30';
      case 'orange': return 'bg-orange-50 dark:bg-orange-900/30';
      case 'purple': return 'bg-purple-50 dark:bg-purple-900/30';
      case 'red': return 'bg-red-50 dark:bg-red-900/30';
      case 'yellow': return 'bg-yellow-50 dark:bg-yellow-900/30';
      case 'indigo': return 'bg-indigo-50 dark:bg-indigo-900/30';
      case 'green': return 'bg-green-50 dark:bg-green-900/30';
      default: return 'bg-gray-50 dark:bg-gray-800/30';
    }
  };

  // Helper function to get text color based on color name
  const getTextColor = (colorName: string) => {
    switch(colorName) {
      case 'blue': return 'text-blue-700 dark:text-blue-400';
      case 'orange': return 'text-orange-700 dark:text-orange-400';
      case 'purple': return 'text-purple-700 dark:text-purple-400';
      case 'red': return 'text-red-700 dark:text-red-400';
      case 'yellow': return 'text-yellow-700 dark:text-yellow-400';
      case 'indigo': return 'text-indigo-700 dark:text-indigo-400';
      case 'green': return 'text-green-700 dark:text-green-400';
      default: return 'text-gray-700 dark:text-gray-400';
    }
  };

  return (
    <Link
      to={path}
      className={cn(
        "block group h-full",
        isHighlighted && "w-full max-w-4xl mx-auto"
      )}
    >
      <div
        className={cn(
          "relative h-full p-3 sm:p-5 rounded-xl transition-all duration-300 hover:shadow-lg hover:-translate-y-1",
          "bg-white/90 dark:bg-slate-800/90 backdrop-blur-md shadow-md",
          "border border-gray-100 dark:border-gray-700/50",
          isHighlighted && "text-center flex flex-col items-center justify-center space-y-4 py-8"
        )}
      >
        {/* Barra de cor na parte superior para aparência de app */}
        <div className={cn(
          "absolute top-0 left-0 right-0 h-1.5 rounded-t-xl",
          color === 'blue' ? "bg-blue-500" :
          color === 'orange' ? "bg-orange-500" :
          color === 'purple' ? "bg-purple-500" :
          color === 'red' ? "bg-red-500" :
          color === 'yellow' ? "bg-yellow-500" :
          color === 'indigo' ? "bg-indigo-500" :
          color === 'green' ? "bg-green-500" :
          "bg-primary"
        )} />
        {isHighlighted ? (
          <>
            <div className="space-y-3 max-w-2xl">
              <div className="flex items-center justify-center gap-2">
                <h3 className="text-xl md:text-2xl font-semibold text-gray-800 dark:text-gray-100">
                  {title}
                </h3>
              </div>
              {description && (
                <p className="text-sm md:text-base text-gray-600 dark:text-gray-300 leading-relaxed">
                  {description}
                </p>
              )}
            </div>
          </>
        ) : (
          <div className="flex flex-col items-center text-center h-full">
            <div className={cn(
              "w-10 h-10 sm:w-14 sm:h-14 rounded-xl flex items-center justify-center mb-2 sm:mb-3 shadow-sm transition-transform group-hover:scale-110",
              "border-2",
              getBorderColor(color),
              getIconBgColor(color)
            )}>
              <Icon className={cn(
                "w-5 h-5 sm:w-7 sm:h-7",
                getTextColor(color)
              )} />
            </div>
            <div className="space-y-2 flex-1">
              <h3 className="text-sm sm:text-base font-semibold text-gray-800 dark:text-gray-100 line-clamp-2">
                {title}
              </h3>
              {description && (
                <p className="text-[10px] sm:text-xs text-gray-600 dark:text-gray-300 mt-1 line-clamp-2">
                  {description}
                </p>
              )}
              {requiresAuth && !user && (
                <div className="mt-2">
                  <Badge variant="outline" className="bg-white/80 dark:bg-gray-800/80 border-primary/20 dark:border-primary/30 text-primary dark:text-primary/80 shadow-sm">
                    Requer login
                  </Badge>
                </div>
              )}
              {badge && (
                <div className="mt-2">
                  <Badge variant="outline" className="bg-white/80 dark:bg-gray-800/80 border-primary/20 dark:border-primary/30 text-primary dark:text-primary/80 shadow-sm">
                    {badge}
                  </Badge>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </Link>
  );
};

export default CalculatorCard;
