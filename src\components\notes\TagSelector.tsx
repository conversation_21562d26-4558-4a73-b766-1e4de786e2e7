import React, { useState } from "react";
import { Tag as TagIcon, Plus, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useNotes } from "@/hooks/useNotes";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface TagSelectorProps {
  noteId: string;
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
  availableTags?: string[];
}

export const TagSelector: React.FC<TagSelectorProps> = ({ 
  noteId,
  selectedTags,
  onTagsChange,
  availableTags = []
}) => {
  const [isAddingTag, setIsAddingTag] = useState(false);
  const [newTagName, setNewTagName] = useState("");
  const { addTag, removeTag } = useNotes();

  const handleAddTag = async () => {
    if (!newTagName.trim()) return;

    if (noteId) {
      await addTag.mutateAsync({ 
        noteId, 
        tagName: newTagName.trim() 
      });
    }
    
    if (!selectedTags.includes(newTagName.trim())) {
      onTagsChange([...selectedTags, newTagName.trim()]);
    }
    
    setNewTagName("");
    setIsAddingTag(false);
  };

  const handleSelectExistingTag = (tagName: string) => {
    if (!selectedTags.includes(tagName)) {
      onTagsChange([...selectedTags, tagName]);
      setIsAddingTag(false);
    }
  };

  const handleRemoveTag = async (tag: string) => {
    if (noteId) {
      await removeTag.mutateAsync({ noteId, tagName: tag });
    }
    onTagsChange(selectedTags.filter(t => t !== tag));
  };

  // Remover duplicatas das tags disponíveis e filtrar as já selecionadas
  const uniqueAvailableTags = Array.from(new Set(availableTags))
    .filter(tag => !selectedTags.includes(tag));

  return (
    <div className="space-y-2">
      <div className="flex flex-wrap gap-2">
        {selectedTags.map((tag) => (
          <Badge key={tag} variant="secondary" className="flex items-center gap-1">
            <TagIcon className="h-3 w-3" />
            {tag}
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 hover:bg-transparent"
              onClick={(e) => {
                e.preventDefault();
                handleRemoveTag(tag);
              }}
            >
              <X className="h-3 w-3" />
            </Button>
          </Badge>
        ))}
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="h-6"
          onClick={(e) => {
            e.preventDefault();
            setIsAddingTag(true);
          }}
        >
          <Plus className="h-4 w-4 mr-1" />
          Adicionar Tag
        </Button>
      </div>

      {isAddingTag && (
        <div className="space-y-2">
          {uniqueAvailableTags.length > 0 && (
            <Select onValueChange={handleSelectExistingTag}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Selecione uma tag existente" />
              </SelectTrigger>
              <SelectContent>
                {uniqueAvailableTags.map(tag => (
                  <SelectItem key={tag} value={tag}>
                    {tag}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
          
          <div className="flex items-center gap-2">
            <Input
              type="text"
              placeholder="Ou crie uma nova tag"
              value={newTagName}
              onChange={(e) => setNewTagName(e.target.value)}
              className="h-8"
            />
            <Button 
              type="button"
              size="sm" 
              className="h-8"
              onClick={handleAddTag}
            >
              Adicionar
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};