import { useState, useRef } from "react";
import { Download } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import jsPDF from "jspdf";

interface GrowthCurveDialogProps {
  curve: any;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function GrowthCurveDialog({ curve, isOpen, onOpenChange }: GrowthCurveDialogProps) {
  const [scale, setScale] = useState(1);
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && containerRef.current) {
      const newX = e.clientX - dragStart.x;
      const newY = e.clientY - dragStart.y;

      // Calculate boundaries
      const container = containerRef.current;
      const rect = container.getBoundingClientRect();
      const image = container.querySelector('img');
      if (image) {
        const imageRect = image.getBoundingClientRect();

        // Calculate maximum boundaries based on scale
        const maxX = (imageRect.width * scale - rect.width) / 2;
        const maxY = (imageRect.height * scale - rect.height) / 2;

        // Allow movement within the scaled boundaries
        setPosition({
          x: Math.max(Math.min(newX, maxX), -maxX),
          y: Math.max(Math.min(newY, maxY), -maxY)
        });
      }
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleZoomChange = (value: number[]) => {
    setScale(value[0]);
    // Reset position when zooming out completely
    if (value[0] === 1) {
      setPosition({ x: 0, y: 0 });
    }
  };

  const handleDownloadPDF = async () => {
    if (!curve?.image_url) return;

    try {
      const response = await fetch(curve.image_url);
      const blob = await response.blob();
      const imageUrl = URL.createObjectURL(blob);

      const pdf = new jsPDF({
        orientation: "landscape",
        unit: "px",
        format: [800, 600]
      });

      pdf.addImage(imageUrl, "JPEG", 0, 0, 800, 600);
      pdf.save(`${curve.title}.pdf`);

      URL.revokeObjectURL(imageUrl);
    } catch (error) {
      console.error("Error downloading PDF:", error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>{curve?.title}</DialogTitle>
        </DialogHeader>
        {curve?.image_url && (
          <div className="space-y-4">
            <div
              ref={containerRef}
              className="relative overflow-hidden cursor-grab h-[calc(90vh-200px)] no-swipe"
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
              data-zoom="true"
              data-draggable="true"
            >
              <img
                src={curve.image_url}
                alt={curve.title}
                className="w-full h-full object-contain transition-transform duration-300"
                style={{
                  transform: `scale(${scale}) translate(${position.x}px, ${position.y}px)`,
                  transformOrigin: 'center center',
                  cursor: isDragging ? 'grabbing' : 'grab'
                }}
                draggable={false}
              />
            </div>
            <div className="flex items-center gap-4 px-2">
              <span className="text-sm text-gray-500 whitespace-nowrap">Zoom:</span>
              <Slider
                value={[scale]}
                onValueChange={handleZoomChange}
                min={1}
                max={2}
                step={0.1}
                className="flex-1"
              />
              <Button
                variant="outline"
                onClick={handleDownloadPDF}
                className="whitespace-nowrap flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Baixar PDF
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}