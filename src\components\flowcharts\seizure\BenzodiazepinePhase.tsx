import { Card } from "@/components/ui/card";
import { Syringe, AlertCircle } from "lucide-react";

interface BenzodiazepinePhaseProps {
  weight: number;
}

export const BenzodiazepinePhase = ({ weight }: BenzodiazepinePhaseProps) => {
  const diazepamDose = (weight * 0.2).toFixed(1);
  const midazolamDose = (weight * 0.2).toFixed(1);
  const maxDiazepam = Math.min(parseFloat(diazepamDose), 5).toFixed(1);
  const diazepamRectalMinDose = (weight * 0.2).toFixed(1);
  const diazepamRectalMaxDose = (weight * 0.5).toFixed(1);

  return (
    <div className="space-y-6">
      <Card className="p-6 bg-white/80 backdrop-blur-sm border border-blue-200">
        <div className="flex items-start gap-4">
          <div className="mt-1">
            <Syringe className="h-6 w-6 text-blue-600" />
          </div>
          <div className="space-y-3">
            <h3 className="font-semibold text-lg text-blue-800">
              Benzodiazepínicos (1ª Linha)
            </h3>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>
                Diazepam: {maxDiazepam} mg IV (máx. 5 mg) por 1 minuto
              </li>
              <li>
                Se não houver acesso venoso:
                <ul className="list-disc list-inside ml-4 mt-1">
                  <li>Midazolam: {midazolamDose} mg IM ou nasal</li>
                  <li>
                    Diazepam: {diazepamRectalMinDose}–{diazepamRectalMaxDose} mg via retal
                  </li>
                </ul>
              </li>
            </ul>
          </div>
        </div>
      </Card>

      <Card className="p-6 bg-white/80 backdrop-blur-sm border border-yellow-200">
        <div className="flex items-start gap-4">
          <div className="mt-1">
            <AlertCircle className="h-6 w-6 text-yellow-600" />
          </div>
          <div className="space-y-3">
            <h3 className="font-semibold text-lg text-yellow-800">
              Avaliação da Resposta
            </h3>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>Reavaliar após 5 minutos</li>
              <li>Se a crise não cessar, repetir a dose do Benzodiazepínico</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};