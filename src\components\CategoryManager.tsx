import { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { CategoryForm } from "./category-manager/CategoryForm";
import { CategoryList } from "./category-manager/CategoryList";
import type { Category } from "./category-manager/types";

const CategoryManager = () => {
  const { toast } = useToast();
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedType, setSelectedType] = useState<"specialty" | "theme" | "focus">("specialty");
  const [selectedParentId, setSelectedParentId] = useState<string | undefined>();

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('study_categories')
        .select('*');

      if (error) throw error;

      const formattedCategories = data.map(cat => ({
        id: cat.id,
        name: cat.name,
        type: cat.type as "specialty" | "theme" | "focus",
        parentId: cat.parent_id
      }));

      setCategories(formattedCategories);
    } catch (error: any) {
      toast({
        title: "Erro ao carregar categorias",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const addCategory = async (newCategoryName: string) => {
    if (!newCategoryName.trim()) {
      toast({
        title: "Erro",
        description: "O nome da categoria não pode estar vazio",
        variant: "destructive"
      });
      return;
    }

    try {
      const parentId = selectedParentId;

      // Se for tema, precisa ter uma especialidade pai
      if (selectedType === "theme" && !parentId) {
        toast({
          title: "Erro",
          description: "Selecione uma especialidade para o tema",
          variant: "destructive"
        });
        return;
      }

      // Se for foco, precisa ter um tema pai
      if (selectedType === "focus" && !parentId) {
        toast({
          title: "Erro",
          description: "Selecione um tema para o foco",
          variant: "destructive"
        });
        return;
      }

      const { data, error } = await supabase
        .from('study_categories')
        .insert({
          name: newCategoryName,
          type: selectedType,
          parent_id: parentId
        })
        .select()
        .single();

      if (error) throw error;

      const newCategory: Category = {
        id: data.id,
        name: data.name,
        type: data.type as "specialty" | "theme" | "focus",
        parentId: data.parent_id
      };

      setCategories(prev => [...prev, newCategory]);
      toast({
        title: "Sucesso",
        description: "Categoria adicionada com sucesso!"
      });
    } catch (error: any) {
      toast({
        title: "Erro ao adicionar categoria",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  const getParentOptions = () => {
    switch (selectedType) {
      case "theme":
        return categories.filter(cat => cat.type === "specialty");
      case "focus":
        return categories.filter(cat => cat.type === "theme");
      default:
        return [];
    }
  };

  return (
    <Card className="p-6 space-y-6">
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Gerenciar Categorias</h2>
        
        <Tabs value={selectedType} onValueChange={(value: "specialty" | "theme" | "focus") => {
          setSelectedType(value);
          setSelectedParentId(undefined);
        }}>
          <TabsList className="w-full">
            <TabsTrigger value="specialty" className="flex-1">Especialidades</TabsTrigger>
            <TabsTrigger value="theme" className="flex-1">Temas</TabsTrigger>
            <TabsTrigger value="focus" className="flex-1">Focos</TabsTrigger>
          </TabsList>

          <TabsContent value={selectedType} className="space-y-4 mt-4">
            <div className="grid gap-4">
              <CategoryForm
                selectedType={selectedType}
                selectedParentId={selectedParentId}
                onAddCategory={addCategory}
                onParentChange={setSelectedParentId}
                getParentOptions={getParentOptions}
              />
              <CategoryList
                categories={categories}
                selectedType={selectedType}
              />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </Card>
  );
};

export default CategoryManager;