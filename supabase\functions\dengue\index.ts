import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Content-Type': 'application/json'
}

interface DengueRequest {
  weight: number;
  age: number;
  currentStep: string;
  answers?: Record<string, boolean>;
}

interface DengueResponse {
  nextStep?: string;
  nextQuestion?: string;
  result?: {
    group: string;
    color: string;
    instructions: string[];
    showHydration?: boolean;
  };
  hydration?: {
    formula: string;
    totalVolume: number;
    sro: number;
    caseiros: number;
  };
  medication?: {
    paracetamol: {
      drops: number;
      maxDaily: string;
    };
    dipirona: {
      drops: number;
      solution: number;
    };
  };
}

function calculateHydration(age: number, weight: number) {
  if (!weight) return null;

  const volume = weight * 100;
  const sro = Math.round(volume / 3);
  const caseiros = Math.round((volume * 2) / 3);

  return {
    formula: `${weight} kg x 100 mL = ${volume} mL em 24h`,
    totalVolume: volume,
    sro,
    caseiros
  };
}

function formatMedication(weight: number, age: number) {
  if (!weight || age < 0) return null;

  return {
    paracetamol: {
      drops: Math.round(weight * 0.75),
      maxDaily: "Dose máxima: 4 doses/dia"
    },
    dipirona: {
      drops: Math.min(Math.round(weight * 1), 40),
      solution: Math.min(Math.round(weight * 0.3), 15)
    }
  };
}

function processStep(data: DengueRequest): DengueResponse {
  const { weight, age, currentStep, answers = {} } = data;
  console.log('Processing step:', { currentStep, answers, weight, age });

  switch (currentStep) {
    case "start":
      return {
        nextStep: answers[currentStep] ? "gravidade" : "sangramento",
        nextQuestion: answers[currentStep] ? 
          "Paciente apresenta sinais de gravidade?" : 
          "Pesquisar sangramento espontâneo de pele ou induzido (Prova do laço, condição clínica especial, risco social ou comorbidades)."
      };

    case "gravidade":
      if (answers[currentStep]) {
        // Tem sinais de gravidade -> Grupo D
        return {
          result: {
            group: "Grupo D - Dengue Grave",
            color: "bg-red-50",
            instructions: [
              `Conduta:`,
              `Realizar expansão volêmica imediata: ${(weight * 20).toFixed(1)} mL de solução salina em 20 minutos em qualquer nível de complexidade.`,
              "Acompanhamento Em leito de UTI até estabilização – mínimo de 48h.",
              "Exames complementares obrigatórios e monitoramento contínuo."
            ]
          },
          nextQuestion: "grupo_d_melhora"
        };
      } else {
        // Não tem sinais de gravidade -> Grupo C
        return {
          result: {
            group: "Grupo C - Dengue com Sinais de Alarme",
            color: "bg-orange-50",
            instructions: [
              `Realizar expansão volêmica: ${(weight * 10).toFixed(1)} mL de solução salina na primeira hora.`,
              "Acompanhamento em leito de internação até estabilização – mínimo de 48h.",
              "Monitoramento contínuo de sinais vitais."
            ]
          },
          nextQuestion: "grupo_c_melhora"
        };
      }

    case "sangramento":
      if (answers[currentStep]) {
        // Tem sangramento/condições especiais -> Grupo B
        return {
          result: {
            group: "Grupo B - Dengue com Condição Especial",
            color: "bg-yellow-50",
            instructions: [
              "Acompanhamento em leito de observação até resultado de exames.",
              "Hidratação oral conforme orientação.",
              "Retorno em 24h para reavaliação."
            ],
            showHydration: true
          },
          hydration: calculateHydration(age, weight),
          medication: formatMedication(weight, age)
        };
      } else {
        // Não tem sangramento/condições especiais -> Grupo A
        return {
          result: {
            group: "Grupo A - Dengue sem Gravidade",
            color: "bg-green-50",
            instructions: [
              "Hidratação oral conforme orientação.",
              "Acompanhamento ambulatorial.",
              "Retorno imediato se sinais de alarme."
            ],
            showHydration: true
          },
          hydration: calculateHydration(age, weight),
          medication: formatMedication(weight, age)
        };
      }

    default:
      throw new Error(`Step not implemented: ${currentStep}`);
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        { status: 405, headers: corsHeaders }
      );
    }

    const data: DengueRequest = await req.json();
    console.log('Received request:', data);

    if (!data.weight || data.weight < 3.5) {
      return new Response(
        JSON.stringify({ error: 'Invalid weight. Must be >= 3.5' }),
        { status: 400, headers: corsHeaders }
      );
    }

    if (data.age < 0) {
      return new Response(
        JSON.stringify({ error: 'Invalid age' }),
        { status: 400, headers: corsHeaders }
      );
    }

    const result = processStep(data);
    console.log('Processed result:', result);

    return new Response(
      JSON.stringify(result),
      { headers: corsHeaders }
    );

  } catch (error) {
    console.error('Error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error', details: error.message }),
      { status: 500, headers: corsHeaders }
    );
  }
});