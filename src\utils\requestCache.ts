/**
 * Cache global para evitar múltiplas chamadas simultâneas para a mesma operação
 */
class RequestCache {
  private cache = new Map<string, Promise<any>>();

  /**
   * Executa uma operação apenas uma vez, mesmo se chamada múltiplas vezes simultaneamente
   */
  async executeOnce<T>(key: string, operation: () => Promise<T>): Promise<T> {
    if (this.cache.has(key)) {
      return this.cache.get(key);
    }

    const promise = operation().finally(() => {
      // Remove do cache após completar para permitir novas chamadas
      this.cache.delete(key);
    });

    this.cache.set(key, promise);
    return promise;
  }

  /**
   * Limpa o cache
   */
  clear() {
    this.cache.clear();
  }
}

export const requestCache = new RequestCache();
