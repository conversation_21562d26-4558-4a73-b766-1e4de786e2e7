import { useMemo } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { useQuery } from '@tanstack/react-query';
import { CACHE_STRATEGIES, CACHE_KEYS } from '@/utils/cacheConfig';

interface UserProfile {
  id: string;
  full_name: string;
  formation_area: string;
  graduation_year: string;
  is_student: boolean;
  preparation_type: string | null;
  specialty: string | null;
  is_admin: boolean;
  premium: boolean;
  premium_requested: boolean;
  premium_requested_history: boolean;
}

/**
 * Hook adaptado para PedBook - gerencia dados do usuário
 * Sempre retorna "pediatria" como domínio para compatibilidade
 */
export const useUserData = () => {
  const { user } = useAuth();

  // React Query para buscar dados completos do perfil
  const { data: profile, isLoading, error } = useQuery({
    queryKey: CACHE_KEYS.USER_PROFILE(user?.id || ''),
    queryFn: async (): Promise<UserProfile | null> => {
      if (!user?.id) return null;

      // Buscar todos os dados do perfil em uma única chamada
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('❌ [useUserData] Error fetching profile:', error);
        throw error;
      }

      return data as UserProfile;
    },
    enabled: !!user?.id,
    ...CACHE_STRATEGIES.USER_DATA,
  });

  // Memoizar o retorno para evitar re-renders desnecessários
  return useMemo(() => ({
    user,
    profile,
    isLoading,
    error,
    isAdmin: profile?.is_admin || false,
    isPremium: profile?.premium || false,
    isPremiumRequested: profile?.premium_requested || false,
    isPremiumRequestedHistory: profile?.premium_requested_history || false,
    domain: 'residencia', // PedBook usa domínio residencia para acessar todas as especialidades
    isResidencia: true, // PedBook acessa dados de residência
    isReady: !isLoading && !!profile,
    hasAccess: true, // PedBook tem acesso livre
    hasCompletedOnboarding: true // Simplificado para PedBook
  }), [user, profile, isLoading, error]);
};
