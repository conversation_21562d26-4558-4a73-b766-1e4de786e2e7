import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle, AlertTriangle, AlertOctagon } from 'lucide-react';
import { useAge } from '@/hooks/useAge';

interface PhoneutriaResultProps {
  group: string;
  color: string;
  instructions: string[];
  nextQuestion?: string;
  onContinue?: (step: string) => void;
}

export const PhoneutriaResult: React.FC<PhoneutriaResultProps> = ({
  group,
  color,
  instructions,
  nextQuestion,
  onContinue,
}) => {
  const { age } = useAge();
  const icon = group.includes('Leve') ? (
    <AlertCircle className="h-6 w-6 text-emerald-600" />
  ) : group.includes('Moderado') ? (
    <AlertTriangle className="h-6 w-6 text-amber-600" />
  ) : (
    <AlertOctagon className="h-6 w-6 text-rose-600" />
  );

  return (
    <Card className={`p-8 max-w-2xl mx-auto ${color} shadow-md border-2`}>
      <div className="space-y-6">
        <div className="flex items-center gap-3">
          {icon}
          <h2 className="text-2xl font-bold text-gray-800">{group}</h2>
        </div>

        <div className="space-y-4">
          <ul className="list-disc pl-6 space-y-2">
            {instructions.map((instruction, index) => (
              <li key={index} className="text-gray-700">
                {instruction}
              </li>
            ))}
          </ul>

          {nextQuestion && onContinue && (
            <div className="pt-4">
              <p className="font-medium text-gray-700 mb-4">{nextQuestion}</p>
              <div className="space-y-3">
                <Button
                  variant="outline"
                  onClick={() => onContinue('under7')}
                  className="w-full justify-between bg-white hover:bg-gray-50 text-gray-700 border-2"
                >
                  Menor que 7 anos
                </Button>
                <Button
                  variant="outline"
                  onClick={() => onContinue('over7')}
                  className="w-full justify-between bg-white hover:bg-gray-50 text-gray-700 border-2"
                >
                  7 anos ou mais
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};