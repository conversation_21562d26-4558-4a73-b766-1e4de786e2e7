import { useQueryClient } from "@tanstack/react-query";
import { CreatePrescription } from "./CreatePrescription";
import { PrescriptionSearch } from "./sidebar/PrescriptionSearch";
import { PrescriptionList } from "./sidebar/PrescriptionList";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronDown } from "lucide-react";
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

interface PrescriptionSidebarProps {
  selectedPrescription: string | null;
  onPrescriptionSelect: (id: string) => void;
  searchTerm: string;
  onSearchChange: (term: string) => void;
  session: any;
}

export const PrescriptionSidebar = ({
  selectedPrescription,
  onPrescriptionSelect,
  searchTerm,
  onSearchChange,
  session,
}: PrescriptionSidebarProps) => {
  const queryClient = useQueryClient();
  const [isExpanded, setIsExpanded] = useState(false);

  const { data: categories, isLoading: isCategoriesLoading } = useQuery({
    queryKey: ["prescription-categories", session?.user?.id],
    queryFn: async () => {
      if (!session?.user?.id) throw new Error("Not authenticated");

      const { data: categoriesData, error: categoriesError } = await supabase
        .from("pedbook_prescription_categories")
        .select("*")
        .eq("user_id", session.user.id)
        .order("name");

      if (categoriesError) {
        console.error("Error fetching categories:", categoriesError);
        throw categoriesError;
      }

      const { data: prescriptionsData, error: prescriptionsError } = await supabase
        .from("pedbook_prescriptions")
        .select("*")
        .eq("user_id", session.user.id)
        .not("category_id", "is", null)
        .order("created_at", { ascending: false });

      if (prescriptionsError) {
        console.error("Error fetching categorized prescriptions:", prescriptionsError);
        throw prescriptionsError;
      }

      const categoriesWithPrescriptions = categoriesData.map(category => ({
        ...category,
        pedbook_prescriptions: prescriptionsData.filter(
          prescription => prescription.category_id === category.id
        )
      }));

      return categoriesWithPrescriptions;
    },
    enabled: !!session?.user?.id,
    staleTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });

  const { data: uncategorizedPrescriptions, isLoading: isUncategorizedLoading } = useQuery({
    queryKey: ["uncategorized-prescriptions", session?.user?.id],
    queryFn: async () => {
      if (!session?.user?.id) throw new Error("Not authenticated");

      const { data, error } = await supabase
        .from("pedbook_prescriptions")
        .select("*")
        .eq("user_id", session.user.id)
        .is("category_id", null)
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data;
    },
    enabled: !!session?.user?.id,
    staleTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });

  // Encontrar o nome da prescrição selecionada
  const getSelectedPrescriptionName = () => {
    if (!selectedPrescription) return "Selecione uma prescrição";
    
    // Procurar nas prescrições categorizadas
    for (const category of categories || []) {
      const prescription = category.pedbook_prescriptions?.find(
        (p: any) => p.id === selectedPrescription
      );
      if (prescription) return prescription.name;
    }
    
    // Procurar nas prescrições sem categoria
    const uncategorizedPrescription = uncategorizedPrescriptions?.find(
      p => p.id === selectedPrescription
    );
    if (uncategorizedPrescription) return uncategorizedPrescription.name;
    
    return "Selecione uma prescrição";
  };

  return (
    <aside className="lg:w-80 bg-white/80 backdrop-blur-sm rounded-xl shadow-lg p-6 lg:h-[calc(100vh-8rem)] flex flex-col">
      <div className="space-y-6">
        <div className="flex flex-col items-center justify-center space-y-4">
          {session && (
            <CreatePrescription 
              session={session} 
              onSuccess={() => {
                queryClient.invalidateQueries({
                  queryKey: ["prescription-categories"],
                  exact: false,
                  refetchType: "all"
                });
                queryClient.invalidateQueries({
                  queryKey: ["uncategorized-prescriptions"],
                  exact: false,
                  refetchType: "all"
                });
              }}
            />
          )}
        </div>

        <div className="block lg:hidden">
          <Button
            variant="outline"
            className="w-full flex items-center justify-between p-4"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <span className="font-medium">
              {getSelectedPrescriptionName()}
            </span>
            <ChevronDown 
              className={`h-4 w-4 transition-transform ${isExpanded ? "transform rotate-180" : ""}`}
            />
          </Button>
          
          {isExpanded && (
            <div className="mt-2">
              <PrescriptionSearch 
                searchTerm={searchTerm}
                onSearchChange={onSearchChange}
              />
            </div>
          )}
        </div>

        <div className="hidden lg:block">
          <PrescriptionSearch 
            searchTerm={searchTerm}
            onSearchChange={onSearchChange}
          />
        </div>

        <div className={`${isExpanded ? 'block' : 'hidden'} lg:block`}>
          <PrescriptionList
            categories={categories}
            uncategorizedPrescriptions={uncategorizedPrescriptions}
            selectedPrescription={selectedPrescription}
            searchTerm={searchTerm}
            onPrescriptionSelect={(id) => {
              onPrescriptionSelect(id);
              setIsExpanded(false);
            }}
          />
        </div>
      </div>
    </aside>
  );
};