
import React from "react";
import { Link } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { FlowchartSEO } from "@/components/seo/FlowchartSEO";
import { FLOWCHART_SEO_DATA } from "@/data/flowchartSEOData";
import { ScrollArea } from "@/components/ui/scroll-area";
import { BothropicQuestion } from "@/components/flowcharts/bothropic/BothropicQuestion";
import { BothropicResult } from "@/components/flowcharts/bothropic/BothropicResult";
import { BothropicSpecialConsiderations } from "@/components/flowcharts/bothropic/BothropicSpecialConsiderations";
import { useBothropicFlow } from "@/components/flowcharts/bothropic/useBothropicFlow";

const BothropicFlowchart = () => {
  const {
    currentStep,
    answers,
    handleAnswer,
    handleContinue,
    resetFlow,
    getCurrentQuestion,
    getCurrentResult,
  } = useBothropicFlow();

  const seoData = FLOWCHART_SEO_DATA['bothropic'];

  const renderContent = () => {
    const question = getCurrentQuestion();
    const result = getCurrentResult();

    if (question) {
      if (currentStep === "severity") {
        return (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-center mb-6 text-gray-800 dark:text-gray-100">
              {question}
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div
                onClick={() => handleAnswer("mild")}
                className="p-6 rounded-lg bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700/50 
                         hover:bg-green-100 dark:hover:bg-green-800/40 transition-all cursor-pointer space-y-2"
              >
                <h3 className="font-semibold text-green-800 dark:text-green-300">Leve</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Manifestações apenas locais
                </p>
              </div>

              <div
                onClick={() => handleAnswer("moderate")}
                className="p-6 rounded-lg bg-orange-50 dark:bg-orange-900/30 border border-orange-200 dark:border-orange-700/50 
                         hover:bg-orange-100 dark:hover:bg-orange-800/40 transition-all cursor-pointer space-y-2"
              >
                <h3 className="font-semibold text-orange-800 dark:text-orange-300">Moderado</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Manifestações locais extensas
                </p>
              </div>

              <div
                onClick={() => handleAnswer("severe")}
                className="p-6 rounded-lg bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700/50 
                         hover:bg-red-100 dark:hover:bg-red-800/40 transition-all cursor-pointer space-y-2"
              >
                <h3 className="font-semibold text-red-800 dark:text-red-300">Grave</h3>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Manifestações sistêmicas graves
                </p>
              </div>
            </div>
          </div>
        );
      }

      return (
        <BothropicQuestion
          question={question}
          onAnswer={handleAnswer}
          selectedAnswer={answers[currentStep] as boolean}
        />
      );
    }

    if (result) {
      return (
        <BothropicResult
          {...result}
          onReset={resetFlow}
          nextQuestion={currentStep === "observation" ? "initial" : undefined}
          onContinue={handleContinue}
        />
      );
    }

    return null;
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-green-50 via-white to-green-50 dark:from-green-900/20 dark:via-slate-900 dark:to-green-900/10">
      <FlowchartSEO {...seoData} />

      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto space-y-8">
          <div className="flex items-center gap-4">
            <Link
              to="/flowcharts/venomous"
              className="hidden sm:inline-flex items-center gap-2 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Voltar para Animais Peçonhentos</span>
            </Link>
          </div>

          <div className="text-center space-y-4">
            <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-400 dark:to-emerald-400">
              Acidente Botrópico
            </h1>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Fluxograma para manejo de acidentes botrópicos em pediatria
            </p>
          </div>

          <ScrollArea className="h-[calc(100vh-300px)] pr-4">
            <div className="space-y-6">
              {renderContent()}
              <BothropicSpecialConsiderations />
            </div>
          </ScrollArea>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default BothropicFlowchart;
