import { X } from "lucide-react";

export const Problem = () => {
  const problems = [
    "Sem planejamento, você não aprende direito",
    "Falta revisão e você esquece tudo",
    "<PERSON><PERSON><PERSON> frequentes nas provas e pouco progresso",
  ];

  return (
    <div className="py-20 px-4 bg-gradient-to-b from-white to-gray-50 relative overflow-hidden">
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-5" />
      <div className="max-w-6xl mx-auto relative z-10">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600 animate-fadeIn">
          O Problema
        </h2>
        <div className="grid md:grid-cols-3 gap-8">
          {problems.map((problem, index) => (
            <div 
              key={index}
              className="group bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 flex items-start gap-4 transform hover:-translate-y-2 animate-fadeIn"
              style={{
                animationDelay: `${index * 150}ms`,
              }}
            >
              <div className="p-2 bg-red-100 rounded-lg group-hover:bg-red-200 transition-colors">
                <X className="w-6 h-6 text-red-500" />
              </div>
              <p className="text-lg text-gray-700">{problem}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};