import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle, AlertTriangle, AlertOctagon } from 'lucide-react';
import { MILD_SYMPTOMS, MODERATE_SYMPTOMS, SEVERE_SYMPTOMS } from '../constants';

interface ClinicalPicturesGridProps {
  onSelectPicture: (severity: 'mild' | 'moderate' | 'severe') => void;
}

export const ClinicalPicturesGrid: React.FC<ClinicalPicturesGridProps> = ({ onSelectPicture }) => {
  const clinicalPictures = [
    {
      title: 'Quadro Leve',
      description: "Predomínio de manifestações locais: Dor, edema, eritema, irradiação, sudorese, parestesia, taquicardia e agitação secundárias à dor.",
      severity: 'mild' as const,
      color: 'bg-[#F2FCE2]',
      borderColor: 'border-[#E8F7D4]',
      icon: <AlertCircle className="h-6 w-6 text-emerald-600" />
    },
    {
      title: 'Quadro Moderado',
      description: "Manifestações locais associadas à sudorese, taquicardia, vômitos ocasionais, agitação, aumento da PA.",
      severity: 'moderate' as const,
      color: 'bg-[#FEF7CD]',
      borderColor: 'border-[#FDF2B8]',
      icon: <AlertTriangle className="h-6 w-6 text-amber-600" />
    },
    {
      title: 'Quadro Grave',
      description: "Prostação, sudorese profusa, aumento da PA, priapismo, diarreia, diminuição da FC, arritmias cardíacas, convulsões, cianose, edema pulmonar, choque.",
      severity: 'severe' as const,
      color: 'bg-[#FFDEE2]',
      borderColor: 'border-[#FFD4D9]',
      icon: <AlertOctagon className="h-6 w-6 text-rose-600" />
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto p-4">
      {clinicalPictures.map((picture) => (
        <Card 
          key={picture.severity}
          className={`p-6 transition-all duration-300 hover:scale-105 border-2 ${picture.color} ${picture.borderColor}`}
        >
          <div className="flex items-center gap-3 mb-4">
            {picture.icon}
            <h3 className="text-xl font-semibold text-gray-800">
              {picture.title}
            </h3>
          </div>
          <p className="text-gray-600 mb-6 min-h-[100px]">{picture.description}</p>
          <Button
            variant="outline"
            className="w-full justify-between border-2 hover:bg-white/50 text-gray-700"
            onClick={() => onSelectPicture(picture.severity)}
          >
            Ver tratamento
          </Button>
        </Card>
      ))}
    </div>
  );
};