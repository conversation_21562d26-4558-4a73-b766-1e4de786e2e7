
import { User, Calendar, Weight, Ruler, Baby, Brain, Activity, Target, Clock } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { formatRelativeAge } from "@/utils/formatRelativeAge";
import { calculateBirthWeightClassification, getClassificationEmoji, getClassificationColor } from "@/utils/pigAigGigClassification";

import { formatGestationalAge } from "@/utils/formatGestationalAge";
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";

interface PatientSummaryHeaderProps {
  patientData: {
    name?: string;
    birthDate?: string;
    age: number;
    gender: "male" | "female";
    weight?: number;
    birthWeight?: number;
    height?: number;
    headCircumference?: number;
    maturity: "Term" | "Pre-term";
    exclusiveBreastfeeding?: boolean;
    hasRiskFactors?: boolean;
    riskFactors?: string[];
    gestationalAge?: number;
    deliveryType?: string;
    maternalComorbidities?: string[];
  };
  onBack: () => void;
  onPatientDataUpdate?: (updatedData: Partial<PatientSummaryHeaderProps['patientData']>) => void;
  useCorrectedAge?: boolean;
  onCorrectedAgeChange?: (useCorrected: boolean) => void;
}

export function PatientSummaryHeader({
  patientData,
  onBack,
  onPatientDataUpdate,
  useCorrectedAge = false,
  onCorrectedAgeChange
}: PatientSummaryHeaderProps) {
  // Estado para percentis calculados
  const [percentiles, setPercentiles] = useState<{
    weight?: { percentile: number; color: string; interpretation: string };
    height?: { percentile: number; color: string; interpretation: string };
    head?: { percentile: number; color: string; interpretation: string };
  }>({});

  // Verificar se é prematuro
  const isPreterm = patientData.maturity === "Pre-term";

  // Função para alterar idade corrigida
  const handleCorrectedAgeChange = (checked: boolean) => {
    if (onCorrectedAgeChange) {
      onCorrectedAgeChange(checked);
    }
  };

  // Calcular percentis usando o mesmo método dos gráficos
  useEffect(() => {
    const calculatePercentiles = async () => {


      try {
        // Buscar dados WHO do Supabase (mesmo método dos gráficos)
        const { data: weightData } = await supabase
          .from('pedbook_growth_curve_metadata')
          .select('data')
          .eq('gender', patientData.gender)
          .eq('type', 'weight')
          .single();

        const { data: heightData } = await supabase
          .from('pedbook_growth_curve_metadata')
          .select('data')
          .eq('gender', patientData.gender)
          .eq('type', 'height')
          .single();

        const { data: headData } = await supabase
          .from('pedbook_growth_curve_metadata')
          .select('data')
          .eq('gender', patientData.gender)
          .eq('type', 'head-circumference')
          .single();



        const newPercentiles: typeof percentiles = {};

        // Função para interpolar e calcular percentil (mesmo método dos gráficos)
        const calculatePercentileFromData = (value: number, data: any[], ageInMonths: number) => {
          if (!data || data.length === 0) return null;

          // Interpolar para a idade específica (mesmo método dos gráficos)
          const sortedData = data.sort((a: any, b: any) => a.age_months - b.age_months);

          let lowerPoint = sortedData[0];
          let upperPoint = sortedData[sortedData.length - 1];

          for (let i = 0; i < sortedData.length - 1; i++) {
            if (ageInMonths >= sortedData[i].age_months && ageInMonths <= sortedData[i + 1].age_months) {
              lowerPoint = sortedData[i];
              upperPoint = sortedData[i + 1];
              break;
            }
          }

          // Interpolar percentis
          const ratio = (ageInMonths - lowerPoint.age_months) / (upperPoint.age_months - lowerPoint.age_months);
          const p3 = lowerPoint.percentiles["3rd"] + (upperPoint.percentiles["3rd"] - lowerPoint.percentiles["3rd"]) * ratio;
          const p15 = lowerPoint.percentiles["15th"] + (upperPoint.percentiles["15th"] - lowerPoint.percentiles["15th"]) * ratio;
          const p50 = lowerPoint.percentiles["50th"] + (upperPoint.percentiles["50th"] - lowerPoint.percentiles["50th"]) * ratio;
          const p85 = lowerPoint.percentiles["85th"] + (upperPoint.percentiles["85th"] - lowerPoint.percentiles["85th"]) * ratio;
          const p97 = lowerPoint.percentiles["97th"] + (upperPoint.percentiles["97th"] - lowerPoint.percentiles["97th"]) * ratio;

          // Determinar percentil baseado na posição (mesmo método dos gráficos)
          let percentile = 50;
          let color = 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
          let interpretation = 'normal';

          if (value <= p3) {
            percentile = 3;
            color = 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
            interpretation = 'muito_baixo';
          } else if (value <= p15) {
            percentile = Math.round(3 + ((value - p3) / (p15 - p3)) * 12);
            color = 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
            interpretation = 'baixo';
          } else if (value <= p50) {
            percentile = Math.round(15 + ((value - p15) / (p50 - p15)) * 35);
            color = 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
            interpretation = 'normal';
          } else if (value <= p85) {
            percentile = Math.round(50 + ((value - p50) / (p85 - p50)) * 35);
            color = 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
            interpretation = 'normal';
          } else if (value <= p97) {
            percentile = Math.round(85 + ((value - p85) / (p97 - p85)) * 12);
            color = 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
            interpretation = 'alto';
          } else {
            percentile = 97;
            color = 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
            interpretation = 'muito_alto';
          }

          return { percentile, color, interpretation };
        };

        // Calcular peso (em kg, mesmo que os gráficos)
        if (patientData.weight && weightData?.data) {
          const result = calculatePercentileFromData(patientData.weight, weightData.data, patientData.age);
          if (result) newPercentiles.weight = result;
        }

        // Calcular altura
        if (patientData.height && heightData?.data) {
          const result = calculatePercentileFromData(patientData.height, heightData.data, patientData.age);
          if (result) newPercentiles.height = result;
        }

        // Calcular perímetro cefálico
        if (patientData.headCircumference && headData?.data) {
          const result = calculatePercentileFromData(patientData.headCircumference, headData.data, patientData.age);
          if (result) newPercentiles.head = result;
        }
        setPercentiles(newPercentiles);
      } catch (error) {
        console.error("Erro ao calcular percentis:", error);
      }
    };

    calculatePercentiles();
  }, [patientData.weight, patientData.height, patientData.headCircumference, patientData.age, patientData.gender]);

  const getGenderColor = () => {
    return patientData.gender === "male"
      ? "bg-blue-100 text-blue-600 dark:bg-blue-900/40 dark:text-blue-400"
      : "bg-pink-100 text-pink-600 dark:bg-pink-900/40 dark:text-pink-400";
  };

  const formatMaturityLabel = () => {
    if (patientData.maturity === "Term") {
      return "A termo";
    } else {
      return "Pré-termo";
    }
  };

  // Formatar peso de forma legível
  const formatWeight = (weightInKg: number) => {
    const weightInGrams = Math.round(weightInKg * 1000);

    // Para pesos menores que 1kg, mostrar apenas gramas
    if (weightInKg < 1) {
      return `${weightInGrams}g`;
    }

    // Para pesos maiores ou iguais a 1kg, mostrar kg e gramas
    const kg = Math.floor(weightInKg);
    const remainingGrams = weightInGrams - (kg * 1000);

    if (remainingGrams === 0) {
      return `${kg}kg`;
    } else {
      return `${kg}kg ${remainingGrams}g`;
    }
  };

  // Formatar idade de forma limpa
  const formatCleanAge = (ageInMonths: number) => {
    const totalMonths = Math.floor(ageInMonths);
    const days = Math.round((ageInMonths - totalMonths) * 30.44);

    // Para idades menores que 1 mês, mostrar apenas dias
    if (totalMonths === 0) {
      return `${days} ${days === 1 ? 'dia' : 'dias'}`;
    }

    // Para idades maiores ou iguais a 12 meses, mostrar anos e meses
    if (totalMonths >= 12) {
      const years = Math.floor(totalMonths / 12);
      const remainingMonths = totalMonths % 12;

      if (remainingMonths === 0 && days === 0) {
        return `${years} ${years === 1 ? 'ano' : 'anos'}`;
      } else if (remainingMonths === 0) {
        return `${years} ${years === 1 ? 'ano' : 'anos'} e ${days} ${days === 1 ? 'dia' : 'dias'}`;
      } else if (days === 0) {
        return `${years} ${years === 1 ? 'ano' : 'anos'} e ${remainingMonths} ${remainingMonths === 1 ? 'mês' : 'meses'}`;
      } else {
        return `${years} ${years === 1 ? 'ano' : 'anos'}, ${remainingMonths} ${remainingMonths === 1 ? 'mês' : 'meses'} e ${days} ${days === 1 ? 'dia' : 'dias'}`;
      }
    }

    // Para idades menores que 12 meses, mostrar meses e dias
    if (days === 0) {
      return `${totalMonths} ${totalMonths === 1 ? 'mês' : 'meses'}`;
    } else {
      return `${totalMonths} ${totalMonths === 1 ? 'mês' : 'meses'} e ${days} ${days === 1 ? 'dia' : 'dias'}`;
    }
  };

  // Mapear fatores de risco para labels legíveis
  const getRiskFactorLabels = (riskFactors: string[] = []) => {
    const riskFactorMap: Record<string, string> = {
      "prematurity": "Prematuridade",
      "low_birth_weight": "Baixo peso ao nascer",
      "poor_iron_diet": "Alimentação pobre em ferro",
      "exclusive_breastfeeding_gt_6m_without_supplement": "AME > 6m sem ferro",
      "multiple_pregnancy": "Gestação múltipla",
      "maternal_anemia": "Anemia materna",
      "frequent_infections": "Infecções frequentes",
      "multiple_gestation": "Gestação múltipla",
      "early_cow_milk_exposure": "Leite de vaca precoce",
      "low_socioeconomic_status": "Baixo nível socioeconômico",
      "vegetarian_diet_without_supplement": "Dieta vegetariana sem suplemento"
    };

    return riskFactors.map(factor => riskFactorMap[factor] || factor).join(", ");
  };

  // Classificação detalhada da idade gestacional
  const getGestationalAgeClassification = (weeks: number): string => {
    if (weeks < 28) return "🔴 Extremamente prematuro";
    if (weeks >= 28 && weeks < 32) return "🟠 Muito prematuro";
    if (weeks >= 32 && weeks < 34) return "🟡 Prematuro moderado";
    if (weeks >= 34 && weeks < 37) return "🟡 Prematuro tardio";
    if (weeks >= 37 && weeks < 39) return "🟢 Pré-termo limítrofe";
    if (weeks >= 39 && weeks < 41) return "🟢 Termo completo";
    if (weeks >= 41 && weeks < 42) return "🟢 Termo tardio";
    if (weeks >= 42) return "🔵 Pós-termo";
    return "A termo";
  };



  return (
    <div className="space-y-4">


      <div className="space-y-4">
        {/* Header Responsivo */}
        <Card className="p-3 sm:p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-blue-200 dark:border-blue-800">
          {/* Layout Mobile: Vertical */}
          <div className="block sm:hidden space-y-3">
            {/* Linha 1: Avatar + Nome */}
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg shadow-sm flex-shrink-0">
                <User className="h-4 w-4 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <span className="font-medium text-gray-900 dark:text-white">
                  {patientData.name || "Paciente"}
                </span>
              </div>
            </div>

            {/* Linha 2: Badges */}
            <div className="flex flex-wrap items-center gap-1.5">
              <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getGenderColor()}`}>
                {patientData.gender === "male" ? "♂ Masc" : "♀ Fem"}
              </span>
              <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                patientData.maturity === "Pre-term"
                  ? "text-yellow-700 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400"
                  : "text-green-700 bg-green-100 dark:bg-green-900/20 dark:text-green-400"
              }`}>
                {patientData.maturity === "Pre-term" ? "Pré-termo" : "A termo"}
              </span>
            </div>

            {/* Linha 3: Idade + Toggle */}
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-1 text-gray-600 dark:text-gray-400 text-sm">
                <Calendar className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">
                  {useCorrectedAge ? "Corrigida:" : "Cronológica:"} {formatCleanAge(patientData.age)}
                </span>
              </span>

              {/* Toggle Mobile */}
              {isPreterm && (
                <div className="flex items-center gap-1.5 p-1.5 bg-white/70 dark:bg-gray-800/70 rounded-lg border border-blue-200 dark:border-blue-700 flex-shrink-0">
                  <Clock className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                  <Switch
                    checked={useCorrectedAge}
                    onCheckedChange={handleCorrectedAgeChange}
                    className="data-[state=checked]:bg-blue-600 scale-75"
                  />
                </div>
              )}
            </div>
          </div>

          {/* Layout Desktop: Horizontal */}
          <div className="hidden sm:flex items-center justify-between">
            <div className="flex items-center gap-3 min-w-0 flex-1">
              <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg shadow-sm flex-shrink-0">
                <User className="h-5 w-5 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <div className="mb-1">
                  <span className="font-medium text-gray-900 dark:text-white">
                    {patientData.name || "Paciente"}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm flex-wrap">
                  <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getGenderColor()}`}>
                    {patientData.gender === "male" ? "♂ Masculino" : "♀ Feminino"}
                  </span>
                  <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                    patientData.maturity === "Pre-term"
                      ? "text-yellow-700 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400"
                      : "text-green-700 bg-green-100 dark:bg-green-900/20 dark:text-green-400"
                  }`}>
                    {patientData.maturity === "Pre-term" ? "Pré-termo" : "A termo"}
                  </span>
                  <span className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
                    <Calendar className="h-3 w-3" />
                    {useCorrectedAge ? "Corrigida:" : "Cronológica:"} {formatCleanAge(patientData.age)}
                  </span>
                </div>
              </div>
            </div>

            {/* Toggle Desktop */}
            {isPreterm && (
              <div className="flex items-center gap-2 p-2 bg-white/70 dark:bg-gray-800/70 rounded-lg border border-blue-200 dark:border-blue-700 flex-shrink-0">
                <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                  {useCorrectedAge ? "Corrigida" : "Cronológica"}
                </span>
                <Switch
                  checked={useCorrectedAge}
                  onCheckedChange={handleCorrectedAgeChange}
                  className="data-[state=checked]:bg-blue-600 scale-75"
                />
              </div>
            )}
          </div>
        </Card>

        {/* Medidas com Percentis - Responsivo */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4">
          {/* Medidas Antropométricas com Percentis */}
          <Card className="p-3 sm:p-4 bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700 min-w-0">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
              <Ruler className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              Medidas & Percentis OMS
            </h3>

            {/* Classificação ao Nascer - Integrada */}
            {patientData.birthWeight && patientData.gestationalAge && (
              <div className="mb-3 p-2 bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    <Target className="h-4 w-4 text-purple-600 dark:text-purple-400 flex-shrink-0" />
                    <div>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {patientData.birthWeight}g
                      </span>
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Peso ao nascer ({formatGestationalAge(patientData.gestationalAge)})
                      </div>
                    </div>
                  </div>
                  {(() => {
                    const classification = calculateBirthWeightClassification(
                      patientData.birthWeight,
                      patientData.gestationalAge
                    );
                    return (
                      <div className="text-right">
                        <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getClassificationColor(classification.classification)}`}>
                          {getClassificationEmoji(classification.classification)} {classification.classification} (P{classification.percentile})
                        </span>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {classification.classification === 'PIG' ? 'Risco de hipoglicemia' :
                           classification.classification === 'GIG' ? 'Risco de parto traumático' :
                           'Crescimento adequado'}
                        </div>
                      </div>
                    );
                  })()}
                </div>
              </div>
            )}
            <div className="space-y-2">
              {/* Idade Gestacional */}
              {patientData.gestationalAge && (
                <div className="flex items-center justify-between p-2 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg min-w-0">
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    <Calendar className="h-4 w-4 text-indigo-600 dark:text-indigo-400 flex-shrink-0" />
                    <div>
                      <span className="text-sm text-gray-700 dark:text-gray-300">IG</span>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {getGestationalAgeClassification(patientData.gestationalAge)}
                      </div>
                    </div>
                  </div>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {formatGestationalAge(patientData.gestationalAge)}
                  </span>
                </div>
              )}

              {patientData.weight && (
                <div className="flex items-center justify-between p-2 bg-orange-50 dark:bg-orange-900/20 rounded-lg min-w-0">
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    <Weight className="h-4 w-4 text-orange-600 dark:text-orange-400 flex-shrink-0" />
                    <span className="text-sm text-gray-700 dark:text-gray-300 truncate">Peso</span>
                  </div>
                  <div className="flex items-center gap-1.5 sm:gap-2 flex-shrink-0">
                    <span className="font-medium text-gray-900 dark:text-white">
                      {formatWeight(patientData.weight)}
                    </span>
                    {percentiles.weight ? (
                      <div className={`text-xs font-bold px-1.5 sm:px-2 py-1 rounded-full ${percentiles.weight.color} whitespace-nowrap`}>
                        P{percentiles.weight.percentile}
                      </div>
                    ) : (
                      <div className="text-xs font-bold px-1.5 sm:px-2 py-1 rounded-full text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400 whitespace-nowrap">
                        P...
                      </div>
                    )}
                  </div>
                </div>
              )}

              {patientData.height && (
                <div className="flex items-center justify-between p-2 bg-green-50 dark:bg-green-900/20 rounded-lg min-w-0">
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    <Ruler className="h-4 w-4 text-green-600 dark:text-green-400 flex-shrink-0" />
                    <span className="text-sm text-gray-700 dark:text-gray-300 truncate">Altura</span>
                  </div>
                  <div className="flex items-center gap-1.5 sm:gap-2 flex-shrink-0">
                    <span className="font-medium text-gray-900 dark:text-white">
                      {patientData.height} cm
                    </span>
                    {percentiles.height ? (
                      <div className={`text-xs font-bold px-1.5 sm:px-2 py-1 rounded-full ${percentiles.height.color} whitespace-nowrap`}>
                        P{percentiles.height.percentile}
                      </div>
                    ) : (
                      <div className="text-xs font-bold px-1.5 sm:px-2 py-1 rounded-full text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400 whitespace-nowrap">
                        P...
                      </div>
                    )}
                  </div>
                </div>
              )}

              {patientData.headCircumference && (
                <div className="flex items-center justify-between p-2 bg-teal-50 dark:bg-teal-900/20 rounded-lg min-w-0">
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    <Brain className="h-4 w-4 text-teal-600 dark:text-teal-400 flex-shrink-0" />
                    <span className="text-sm text-gray-700 dark:text-gray-300 truncate">PC</span>
                  </div>
                  <div className="flex items-center gap-1.5 sm:gap-2 flex-shrink-0">
                    <span className="font-medium text-gray-900 dark:text-white">
                      {patientData.headCircumference} cm
                    </span>
                    {percentiles.head ? (
                      <div className={`text-xs font-bold px-1.5 sm:px-2 py-1 rounded-full ${percentiles.head.color} whitespace-nowrap`}>
                        P{percentiles.head.percentile}
                      </div>
                    ) : (
                      <div className="text-xs font-bold px-1.5 sm:px-2 py-1 rounded-full text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400 whitespace-nowrap">
                        P...
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </Card>

          {/* Dados Clínicos - Responsivo */}
          <Card className="p-3 sm:p-4 bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700 min-w-0">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
              <Activity className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              Dados Clínicos
            </h3>
            <div className="space-y-2">
              {patientData.exclusiveBreastfeeding !== undefined && (
                <div className="flex items-center justify-between p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    <Baby className="h-4 w-4 text-purple-600 dark:text-purple-400 flex-shrink-0" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">Amamentação</span>
                  </div>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {patientData.exclusiveBreastfeeding ? "Exclusiva" : "Não exclusiva"}
                  </span>
                </div>
              )}

              {patientData.riskFactors && patientData.riskFactors.length > 0 && (
                <div className="p-2 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
                  <div className="flex items-center gap-2 mb-1">
                    <Activity className="h-4 w-4 text-amber-600 dark:text-amber-400 flex-shrink-0" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Fatores de Risco</span>
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400 leading-relaxed">
                    {getRiskFactorLabels(patientData.riskFactors)}
                  </div>
                </div>
              )}
            </div>
          </Card>
        </div>




      </div>
    </div>
  );
}
