
import React, { useState, useEffect } from "react";
import { useSupabaseClient } from "@supabase/auth-helpers-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Shield, ShieldCheck, Users } from "lucide-react";
import { AdminModulePermissions } from "@/components/admin/AdminModulePermissions";

interface AdminUser {
  id: string;
  full_name: string | null;
  is_admin: boolean;
  is_super_admin: boolean;
  permissions: string[];
}

export default function AdminUsers() {
  const supabase = useSupabaseClient();
  const [admins, setAdmins] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedAdmin, setSelectedAdmin] = useState<AdminUser | null>(null);
  const [permissionsOpen, setPermissionsOpen] = useState(false);

  console.log("🔍 [AdminUsers] Renderizando lista de administradores");

  useEffect(() => {
    fetchAdmins();
  }, []);

  const fetchAdmins = async () => {
    try {
      setLoading(true);
      console.log("🔄 [AdminUsers] Buscando lista de administradores");

      const { data: profiles, error } = await supabase
        .from("secure_profiles")
        .select("id, full_name, is_admin")
        .eq("is_admin", true)
        .order("full_name", { ascending: true });

      if (error) throw error;

      // Fetch role information to identify super admins
      const { data: adminRoleData, error: adminRoleError } = await supabase
        .from("admin_roles")
        .select("id, name")
        .eq("name", "super_admin")
        .maybeSingle();

      if (adminRoleError) throw adminRoleError;

      const superAdminRoleId = adminRoleData?.id;

      // Fetch role assignments for each admin
      const adminsWithRoles = await Promise.all(
        profiles.map(async (profile) => {
          // Check if the user is a super admin
          const { data: roleData, error: roleError } = await supabase
            .from("admin_user_roles")
            .select("role_id")
            .eq("user_id", profile.id);

          if (roleError) throw roleError;

          // Get permissions for the user
          const { data: permissions, error: permissionsError } = await supabase
            .from("admin_user_permissions")
            .select("resource")
            .eq("user_id", profile.id)
            .order("resource", { ascending: true });

          if (permissionsError) throw permissionsError;

          const isSuperAdmin = roleData.some(role => role.role_id === superAdminRoleId);

          return {
            ...profile,
            is_super_admin: isSuperAdmin,
            permissions: permissions?.map(p => p.resource) || []
          };
        })
      );

      setAdmins(adminsWithRoles);
      console.log("✅ [AdminUsers] Administradores carregados:", adminsWithRoles.length);
    } catch (error: any) {
      console.error("❌ [AdminUsers] Erro ao carregar administradores:", error);
      toast({
        variant: "destructive",
        title: "Erro ao carregar administradores",
        description: error.message,
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePermissionClick = (admin: AdminUser) => {
    setSelectedAdmin(admin);
    setPermissionsOpen(true);
  };

  const getRoleLabel = (admin: AdminUser) => {
    if (admin.is_super_admin) {
      return (
        <div className="flex items-center gap-1 text-amber-600 font-semibold">
          <ShieldCheck className="h-4 w-4" />
          <span>Super Admin</span>
        </div>
      );
    }
    return (
      <div className="flex items-center gap-1 text-blue-600">
        <Shield className="h-4 w-4" />
        <span>Admin</span>
      </div>
    );
  };

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader className="bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-2xl font-bold flex items-center gap-2">
                <Users className="h-6 w-6 text-primary" />
                Gerenciamento de Administradores
              </CardTitle>
              <CardDescription className="text-base mt-2">
                Gerencie usuários administrativos e suas permissões no sistema
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          {loading ? (
            <div className="flex justify-center p-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[250px]">Nome</TableHead>
                  <TableHead>Cargo</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {admins.map((admin) => (
                  <TableRow key={admin.id}>
                    <TableCell className="font-medium">{admin.full_name || "Sem nome"}</TableCell>
                    <TableCell>{getRoleLabel(admin)}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        className="gap-1"
                        onClick={() => handlePermissionClick(admin)}
                        disabled={admin.is_super_admin}
                      >
                        {admin.is_super_admin ? (
                          <>
                            <ShieldCheck className="h-4 w-4" />
                            <span>Acesso Total</span>
                          </>
                        ) : (
                          <>
                            <Shield className="h-4 w-4" />
                            <span>Permissões</span>
                          </>
                        )}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      <Dialog open={permissionsOpen} onOpenChange={setPermissionsOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-xl">
              <Shield className="h-5 w-5 text-primary" />
              Permissões do Administrador
            </DialogTitle>
          </DialogHeader>

          {selectedAdmin && (
            <AdminModulePermissions
              admin={selectedAdmin}
              onSaved={() => {
                fetchAdmins();
                setPermissionsOpen(false);
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
