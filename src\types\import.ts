
export interface ImportQuestion {
  statement_text?: string;
  statement?: string;
  alternatives: string[];
  correct_answer: string | number;
  specialty: string;
  theme: string;
  focus: string;
  topics?: string[]; // Array de tópicos relacionados
  institution_id?: string; // Nome da instituição (prioritário sobre location)
  location?: string; // Compatibilidade com formato antigo
  year: number;
  answer_type?: 'MULTIPLE_CHOICE' | 'MULTIPLE_CHOICE_FOUR' | 'DISCURSIVE' | 'TRUE_OR_FALSE'; // Entrada aceita em inglês, mas sempre convertido para português
  domain?: string;
  images?: string | string[]; // String vazia, string única ou array de URLs
  tipo?: string; // Tipo da questão (ex: teorica-1)
  numero?: number; // Número da questão
  is_annulled?: boolean; // Indica se a questão foi anulada
}

export interface ImportResults {
  success: number;
  errors: string[];
  created: {
    specialties: Map<string, CategoryResult>;
    themes: Map<string, CategoryResult>;
    focuses: Map<string, CategoryResult>;
    locations: Map<string, LocationResult>;
    years: Set<number>;
  };
}

export interface CategoryResult {
  id: string;
  name: string;
  type: string;
}

export interface LocationResult {
  id: string;
  name: string;
}
