import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Content-Type': 'application/json'
}

interface HydrationResult {
  dailyVolume: number;
  hourlyRate: number;
  minuteRate: number;
  aliquots: {
    volume: number;
    infusionRate: number;
    dropRate: number;
    infusionTime: number;
  }[];
  components: {
    glucose: number;
    sodium: number;
    potassium: number;
  };
}

function calculateHydration(weightKg: number): HydrationResult | null {
  if (weightKg < 3.5) return null;

  // Calculate daily volume (Holliday-Segar)
  let dailyVolume = 0;
  if (weightKg <= 10) {
    dailyVolume = 100 * weightKg;
  } else if (weightKg <= 20) {
    dailyVolume = 1000 + 50 * (weightKg - 10);
  } else {
    dailyVolume = 1500 + 20 * (weightKg - 20);
  }

  // Maximum volume limit
  if (dailyVolume > 2400) dailyVolume = 2400;

  // Calculate aliquots
  const aliquots: HydrationResult['aliquots'] = [];
  let remainingVolume = dailyVolume;
  let remainingTime = 24;

  while (remainingVolume > 0) {
    const volume = Math.min(500, remainingVolume);
    const infusionTime = (volume / dailyVolume) * 24;
    const infusionRate = volume / infusionTime;
    const dropRate = Math.round((infusionRate * 20) / 60);

    aliquots.push({
      volume: Math.round(volume),
      infusionRate,
      dropRate,
      infusionTime: Number(infusionTime.toFixed(2))
    });

    remainingVolume -= volume;
    remainingTime -= infusionTime;
  }

  return {
    dailyVolume,
    hourlyRate: dailyVolume / 24,
    minuteRate: dailyVolume / 1440,
    aliquots,
    components: {
      glucose: 475,
      sodium: 20,
      potassium: 5
    }
  };
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const url = new URL(req.url)
    const weightParam = url.searchParams.get('weight')

    if (!weightParam) {
      return new Response(
        JSON.stringify({ error: 'Weight parameter is required' }),
        { status: 400, headers: corsHeaders }
      )
    }

    const weight = parseFloat(weightParam)
    
    if (isNaN(weight) || weight < 3.5) {
      return new Response(
        JSON.stringify({ error: 'Invalid weight value. Must be a number >= 3.5' }),
        { status: 400, headers: corsHeaders }
      )
    }

    const result = calculateHydration(weight)

    return new Response(
      JSON.stringify(result),
      { headers: corsHeaders }
    )

  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: corsHeaders }
    )
  }
})