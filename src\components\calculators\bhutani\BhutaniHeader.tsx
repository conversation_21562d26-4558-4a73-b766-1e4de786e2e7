import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";

export function BhutaniHeader() {
  const navigate = useNavigate();

  return (
    <div className="max-w-3xl mx-auto">
      <Button
        variant="ghost"
        className="text-primary hover:text-primary/80 transition-colors w-fit mb-4 flex items-center gap-2"
        onClick={() => navigate('/calculadoras')}
      >
        <ArrowLeft className="h-4 w-4" />
        Voltar para calculadoras
      </Button>

      <div className="text-center space-y-4 mt-4">
        <h1 className="text-2xl md:text-3xl font-bold text-primary">
          Nomograma de Bhutani
        </h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          O Nomograma de Bhutani é uma ferramenta para avaliação do risco de hiperbilirrubinemia significativa em recém-nascidos.
        </p>
      </div>
    </div>
  );
}