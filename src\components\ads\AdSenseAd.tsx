import React, { useEffect, useRef } from 'react';

interface AdSenseAdProps {
  adSlot: string;
  adFormat?: 'auto' | 'rectangle' | 'vertical' | 'horizontal';
  adLayout?: string;
  adLayoutKey?: string;
  style?: React.CSSProperties;
  className?: string;
  fullWidthResponsive?: boolean;
}

declare global {
  interface Window {
    adsbygoogle: any[];
  }
}

/**
 * Componente para exibir anúncios do Google AdSense
 * Otimizado para performance e carregamento lazy
 */
export const AdSenseAd: React.FC<AdSenseAdProps> = ({
  adSlot,
  adFormat = 'auto',
  adLayout,
  adLayoutKey,
  style = { display: 'block' },
  className = '',
  fullWidthResponsive = true,
}) => {
  const adRef = useRef<HTMLDivElement>(null);
  const isAdPushed = useRef(false);

  useEffect(() => {
    // Verificar se o AdSense está carregado
    if (typeof window !== 'undefined' && window.adsbygoogle && !isAdPushed.current) {
      try {
        // Aguardar um pouco para garantir que o DOM está pronto
        setTimeout(() => {
          if (adRef.current && !isAdPushed.current) {
            (window.adsbygoogle = window.adsbygoogle || []).push({});
            isAdPushed.current = true;
          }
        }, 100);
      } catch (error) {
        console.error('Erro ao carregar anúncio AdSense:', error);
      }
    }
  }, [adSlot]);

  // Resetar quando o slot mudar
  useEffect(() => {
    isAdPushed.current = false;
  }, [adSlot]);

  return (
    <div 
      ref={adRef}
      className={`adsense-container ${className}`}
      style={{ 
        minHeight: '90px', // Evitar layout shift
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        ...style 
      }}
    >
      <ins
        className="adsbygoogle"
        style={style}
        data-ad-client="ca-pub-4018898302361000"
        data-ad-slot={adSlot}
        data-ad-format={adFormat}
        data-ad-layout={adLayout}
        data-ad-layout-key={adLayoutKey}
        data-full-width-responsive={fullWidthResponsive.toString()}
      />
    </div>
  );
};

/**
 * Componente para anúncio responsivo padrão
 */
export const ResponsiveAd: React.FC<{ 
  adSlot: string; 
  className?: string;
  minHeight?: string;
}> = ({ 
  adSlot, 
  className = '',
  minHeight = '90px'
}) => {
  return (
    <AdSenseAd
      adSlot={adSlot}
      adFormat="auto"
      fullWidthResponsive={true}
      className={className}
      style={{ 
        display: 'block',
        minHeight,
        width: '100%'
      }}
    />
  );
};

/**
 * Componente para anúncio em formato banner
 */
export const BannerAd: React.FC<{ 
  adSlot: string; 
  className?: string;
}> = ({ adSlot, className = '' }) => {
  return (
    <AdSenseAd
      adSlot={adSlot}
      adFormat="horizontal"
      fullWidthResponsive={true}
      className={className}
      style={{ 
        display: 'block',
        minHeight: '90px',
        width: '100%'
      }}
    />
  );
};

/**
 * Componente para anúncio quadrado/retângulo
 */
export const SquareAd: React.FC<{ 
  adSlot: string; 
  className?: string;
}> = ({ adSlot, className = '' }) => {
  return (
    <AdSenseAd
      adSlot={adSlot}
      adFormat="rectangle"
      fullWidthResponsive={false}
      className={className}
      style={{ 
        display: 'inline-block',
        width: '300px',
        height: '250px'
      }}
    />
  );
};

/**
 * Hook para verificar se o AdSense está carregado
 */
export const useAdSenseLoaded = () => {
  const [isLoaded, setIsLoaded] = React.useState(false);

  useEffect(() => {
    const checkAdSense = () => {
      if (typeof window !== 'undefined' && window.adsbygoogle) {
        setIsLoaded(true);
      }
    };

    // Verificar imediatamente
    checkAdSense();

    // Verificar periodicamente até carregar
    const interval = setInterval(() => {
      if (!isLoaded) {
        checkAdSense();
      } else {
        clearInterval(interval);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [isLoaded]);

  return isLoaded;
};
