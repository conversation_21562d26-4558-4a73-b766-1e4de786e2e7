import React from "react";
import { <PERSON> } from "react-router-dom";
import { ArrowLeft } from "lucide-react";

export const ScorpionHeader = () => {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Link
          to="/flowcharts/venomous"
          className="hidden sm:inline-flex items-center gap-2 text-yellow-600 hover:text-yellow-700 transition-colors"
        >
          <ArrowLeft className="h-5 w-5" />
          <span>Voltar para Animais Peçonhentos</span>
        </Link>
      </div>

      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-yellow-600 to-orange-600">
          Acidente Escorpiônico
        </h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Fluxograma para manejo de acidentes escorpiônicos em pediatria
        </p>
      </div>
    </div>
  );
};