import { useRef, useEffect } from "react";
import { Chart } from "chart.js/auto";

interface BhutaniChartProps {
  chartRef: React.RefObject<HTMLCanvasElement>;
  chartInstance: React.MutableRefObject<Chart | null>;
  continuousEntry: boolean;
  dataPointsP40: number[];
  dataPointsP75: number[];
  dataPointsP95: number[];
  interpolate: (dataPoints: number[]) => number[];
}

export function BhutaniChart({
  chartRef,
  chartInstance,
  continuousEntry,
  dataPointsP40,
  dataPointsP75,
  dataPointsP95,
  interpolate,
}: BhutaniChartProps) {
  useEffect(() => {
    if (chartRef.current) {
      const ctx = chartRef.current.getContext("2d");
      if (ctx) {
        if (chartInstance.current) {
          chartInstance.current.destroy();
        }

        const labels = Array.from({ length: 12 }, (_, i) => (i + 1) * 12);

        chartInstance.current = new Chart(ctx, {
          type: "line",
          data: {
            labels,
            datasets: [
              {
                label: "P40 (mg/dL)",
                data: interpolate(dataPointsP40),
                borderColor: "blue",
                borderWidth: 1,
                fill: false,
                tension: 0.1,
                pointRadius: 0
              },
              {
                label: "P75 (mg/dL)",
                data: interpolate(dataPointsP75),
                borderColor: "green",
                borderWidth: 1,
                fill: false,
                tension: 0.1,
                pointRadius: 0
              },
              {
                label: "P95 (mg/dL)",
                data: interpolate(dataPointsP95),
                borderColor: "red",
                borderWidth: 1,
                fill: false,
                tension: 0.1,
                pointRadius: 0
              },
              {
                label: "Bilirrubina Total (mg/dL)",
                data: [],
                borderColor: "orange",
                pointBackgroundColor: "orange",
                borderWidth: 2,
                fill: false,
                pointRadius: 5,
                showLine: continuousEntry
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              x: {
                type: "linear",
                position: "bottom",
                min: 12,
                max: 144,
                ticks: {
                  stepSize: 12
                }
              },
              y: {
                beginAtZero: true,
                max: 20,
                ticks: {
                  stepSize: 2
                }
              },
            },
            animation: false
          },
        });
      }
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [continuousEntry, chartRef, chartInstance, dataPointsP40, dataPointsP75, dataPointsP95, interpolate]);

  return (
    <div className="h-[400px] md:h-[300px]">
      <canvas ref={chartRef} />
    </div>
  );
}