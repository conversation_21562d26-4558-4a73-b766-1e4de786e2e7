import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Clock, Brain, Target, Award } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { SessionSkillTree } from './SessionSkillTree';
import type { QuestionStats } from "@/types/question";

interface DetailedStudyReportProps {
  stats: QuestionStats;
}

export const DetailedStudyReport = ({ stats }: DetailedStudyReportProps) => {
  const totalQuestions = stats.correct_answers + stats.incorrect_answers;
  const accuracy = totalQuestions > 0 
    ? Math.round((stats.correct_answers / totalQuestions) * 100) 
    : 0;
  const timePerQuestion = totalQuestions > 0 
    ? Math.round(stats.time_spent / totalQuestions) 
    : 0;

  console.log('📊 DetailedStudyReport - Estatísticas recebidas:', {
    stats,
    totalQuestions,
    accuracy,
    timePerQuestion
  });

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle><PERSON><PERSON><PERSON></CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-gray-600">
                <Brain className="h-5 w-5" />
                <span>Total de Questões</span>
              </div>
              <p className="text-2xl font-bold">{totalQuestions}</p>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-gray-600">
                <Target className="h-5 w-5" />
                <span>Taxa de Acerto</span>
              </div>
              <p className="text-2xl font-bold">{accuracy}%</p>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-gray-600">
                <Clock className="h-5 w-5" />
                <span>Tempo Total</span>
              </div>
              <p className="text-2xl font-bold">{Math.round(stats.time_spent / 60)} min</p>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-gray-600">
                <Clock className="h-5 w-5" />
                <span>Tempo por Questão</span>
              </div>
              <p className="text-2xl font-bold">{timePerQuestion}s</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <SessionSkillTree stats={stats} />
    </div>
  );
};