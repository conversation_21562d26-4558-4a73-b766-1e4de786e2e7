import { Input } from "@/components/ui/input";

interface TagNameInputProps {
  tagName: string;
  onTagNameChange: (value: string) => void;
}

export function TagNameInput({ tagName, onTagNameChange }: TagNameInputProps) {
  return (
    <div className="flex-1">
      <Input
        placeholder="Nome da tag"
        value={tagName}
        onChange={(e) => onTagNameChange(e.target.value)}
      />
    </div>
  );
}