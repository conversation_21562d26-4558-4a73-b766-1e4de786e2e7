import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Eye, Plus, Trash2, Check } from "lucide-react";
import type { PrescriptionWithMedications } from "../types";
import { PrescriptionStatusIndicator } from "../PrescriptionStatusIndicator";
import { PrescriptionReactions } from "./PrescriptionReactions";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

interface PrescriptionCardProps {
  prescription: PrescriptionWithMedications;
  userId?: string;
  onViewDetails: (prescription: PrescriptionWithMedications) => void;
  onAdd?: (prescription: PrescriptionWithMedications) => void;
  onRemove?: (prescriptionId: string) => void;
}

export const PrescriptionCard = ({
  prescription,
  userId,
  onViewDetails,
  onAdd,
  onRemove,
}: PrescriptionCardProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Query to check if the user has already added this prescription
  const { data: hasAdded } = useQuery({
    queryKey: ["prescription-added", prescription.id, userId],
    queryFn: async () => {
      if (!userId) return false;
      
      const { data } = await supabase
        .from("pedbook_prescriptions")
        .select("id")
        .eq("user_id", userId)
        .eq("name", prescription.name)
        .maybeSingle();
      
      return !!data;
    },
    enabled: !!userId && prescription.user_id !== userId,
  });
  
  const handleViewDetails = (e: React.MouseEvent) => {
    e.preventDefault();
    if (prescription.is_public) {
      toast({
        title: "Prescrição Compartilhada",
        description: "Esta prescrição está disponível publicamente para todos os usuários.",
      });
    }
    onViewDetails(prescription);
  };

  const handleAdd = async () => {
    if (onAdd) {
      await onAdd(prescription);
      // Invalidate the query to immediately update the UI
      queryClient.invalidateQueries({
        queryKey: ["prescription-added", prescription.id, userId],
      });
    }
  };

  return (
    <Card className="bg-white">
      <div className="p-4 space-y-4">
        <div className="flex items-start justify-between gap-4">
          <div>
            <h3 className="font-medium">{prescription.name}</h3>
            {prescription.description && (
              <p className="text-sm text-muted-foreground mt-1">
                {prescription.description}
              </p>
            )}
          </div>
          <PrescriptionStatusIndicator isPublic={prescription.is_public} />
        </div>

        {prescription.profiles && (
          <div className="text-sm text-muted-foreground">
            por {prescription.profiles.full_name}
          </div>
        )}

        <div className="flex flex-col gap-2">
          <Button
            variant="outline"
            size="sm"
            className="w-full justify-center"
            onClick={handleViewDetails}
          >
            <Eye className="h-4 w-4 mr-2" />
            Ver detalhes
          </Button>

          {userId && prescription.user_id !== userId && onAdd && (
            hasAdded ? (
              <Button
                variant="outline"
                size="sm"
                className="w-full justify-center bg-muted cursor-not-allowed"
                disabled
              >
                <Check className="h-4 w-4 mr-2" />
                Já adicionada
              </Button>
            ) : (
              <Button
                variant="outline"
                size="sm"
                className="w-full justify-center"
                onClick={handleAdd}
              >
                <Plus className="h-4 w-4 mr-2" />
                Adicionar
              </Button>
            )
          )}

          {userId && prescription.user_id === userId && onRemove && (
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-center text-destructive hover:text-destructive"
              onClick={() => onRemove(prescription.id)}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Remover
            </Button>
          )}
        </div>

        {prescription.is_public && (
          <PrescriptionReactions
            prescriptionId={prescription.id}
            userId={userId}
            initialLikes={prescription.likes_count}
            initialDislikes={prescription.dislikes_count}
          />
        )}
      </div>
    </Card>
  );
};