/**
 * Script de teste para gerar rotas básicas
 */

import fs from 'fs';
import path from 'path';

console.log('🚀 Testando geração de rotas...');

// Rotas básicas para teste
const routes = [
  '/',
  '/medicamentos/painel',
  '/calculadoras',
  '/puericultura',
  '/medicamentos/paracetamol',
  '/medicamentos/amoxicilina',
  '/medicamentos/dipirona',
  '/calculadoras/imc',
  '/calculadoras/hidratacao'
];

console.log(`✅ Rotas geradas: ${routes.length}`);

// Salvar rotas
const routesData = {
  routes,
  medications: [
    { slug: 'paracetamol', name: 'Paracetamol', description: 'Analgésico e antitérmico' },
    { slug: 'amoxicilina', name: 'Amoxicilina', description: 'Antibiótico betalactâmico' },
    { slug: 'dipirona', name: '<PERSON><PERSON><PERSON>', description: 'Analgésico e antitérmico' }
  ],
  calculators: [
    { slug: 'imc', name: 'Calculadora de IMC' },
    { slug: 'hidratacao', name: 'Calculadora de Hidratação' }
  ],
  generatedAt: new Date().toISOString(),
  totalRoutes: routes.length
};

const outputPath = path.join(process.cwd(), 'prerender-routes.json');
fs.writeFileSync(outputPath, JSON.stringify(routesData, null, 2));

console.log(`📄 Rotas salvas em: ${outputPath}`);

// Gerar sitemap básico
const baseUrl = 'https://pedb.com.br';
const currentDate = new Date().toISOString().split('T')[0];

let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

routes.forEach(route => {
  let priority = '0.5';
  if (route === '/') priority = '1.0';
  else if (route.includes('/medicamentos/')) priority = '0.9';
  else if (route.includes('/calculadoras/')) priority = '0.8';

  sitemap += `
  <url>
    <loc>${baseUrl}${route}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>${priority}</priority>
  </url>`;
});

sitemap += `
</urlset>`;

const sitemapPath = path.join(process.cwd(), 'public', 'sitemap.xml');
fs.writeFileSync(sitemapPath, sitemap);

console.log(`✅ Sitemap gerado: ${sitemapPath}`);
console.log('🎉 Teste concluído!');
