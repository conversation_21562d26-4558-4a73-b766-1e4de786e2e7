
import React from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowRight, Info } from "lucide-react";
import { Stage } from "./types";

interface PhoneutriaQuestionProps {
  onAnswer: (answer: Stage) => void;
  stage: 'initial' | 'unidentifiedSpider' | 'identifiedSpider';
}

export const PhoneutriaQuestion: React.FC<PhoneutriaQuestionProps> = ({
  onAnswer,
  stage,
}) => {
  if (stage === 'initial') {
    return (
      <Card className="p-8 max-w-2xl mx-auto bg-white shadow-md border-2 border-gray-100 dark:bg-slate-800/90 dark:border-gray-700">
        <h2 className="text-2xl font-bold mb-6 flex items-center gap-2 text-gray-800 dark:text-gray-100">
          <Info className="h-6 w-6 text-blue-500 dark:text-blue-400" />
          Início
        </h2>
        <p className="text-gray-600 dark:text-gray-300 mb-8">
          Suspeita de acidente com aranha armadeira (Phoneutria sp.)
        </p>
        <div className="space-y-6">
          <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200">A aranha foi identificada?</h3>
          <div className="flex flex-col gap-4">
            <Button
              variant="outline"
              onClick={() => onAnswer('unidentifiedSpider')}
              className="w-full flex justify-between items-center bg-[#D3E4FD] hover:bg-[#BED6F6] text-gray-700 border-2 border-[#BED6F6] dark:bg-blue-900/30 dark:hover:bg-blue-900/50 dark:border-blue-800/50 dark:text-gray-100"
            >
              <span>Aranha NÃO identificada</span>
              <ArrowRight className="h-4 w-4 ml-2 flex-shrink-0" />
            </Button>
            <Button
              variant="outline"
              onClick={() => onAnswer('identifiedSpider')}
              className="w-full flex justify-between items-center bg-[#F2FCE2] hover:bg-[#E8F7D4] text-gray-700 border-2 border-[#E8F7D4] dark:bg-green-900/30 dark:hover:bg-green-900/50 dark:border-green-800/50 dark:text-gray-100"
            >
              <span>Aranha identificada</span>
              <ArrowRight className="h-4 w-4 ml-2 flex-shrink-0" />
            </Button>
          </div>
        </div>
      </Card>
    );
  }

  const title = stage === 'unidentifiedSpider'
    ? "Aranha NÃO Identificada"
    : "Aranha Identificada";
  
  const description = stage === 'unidentifiedSpider'
    ? "Paciente com suspeita de acidente por Phoneutria sp., mas sem confirmação visual do animal."
    : "Paciente com confirmação visual de acidente por Phoneutria sp.";

  return (
    <Card className="p-8 max-w-2xl mx-auto bg-white shadow-md border-2 border-gray-100 dark:bg-slate-800/90 dark:border-gray-700">
      <h2 className="text-2xl font-bold mb-6 text-gray-800 dark:text-gray-100">{title}</h2>
      <p className="text-gray-600 dark:text-gray-300 mb-8">{description}</p>
      <div className="space-y-6">
        <h3 className="text-xl font-medium text-gray-700 dark:text-gray-200">
          O paciente apresenta sinais/sintomas clínicos?
        </h3>
        <div className="grid gap-4">
          <Button
            onClick={() => onAnswer('clinicalPictures')}
            className="w-full justify-between bg-[#FEF7CD] hover:bg-[#FDF2B8] text-gray-700 border-2 border-[#FDF2B8] dark:bg-yellow-900/30 dark:hover:bg-yellow-900/50 dark:border-yellow-800/50 dark:text-gray-100"
          >
            Sim
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            onClick={() => onAnswer('discharge')}
            className="w-full justify-between bg-[#F1F0FB] hover:bg-gray-100 text-gray-700 border-2 border-gray-200 dark:bg-slate-800 dark:hover:bg-slate-700 dark:border-gray-700 dark:text-gray-200"
          >
            Não
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </Card>
  );
};
