import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface DeleteTopicDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  topicId: string;
  onDelete: () => void;
}

export function DeleteTopicDialog({
  open,
  onOpenChange,
  topicId,
  onDelete,
}: DeleteTopicDialogProps) {
  const { toast } = useToast();

  const handleDelete = async () => {
    console.log('🗑️ Deleting topic:', { topicId });
    
    if (!topicId) {
      console.error('❌ Missing required ID for deletion', { topicId });
      toast({
        variant: "destructive",
        title: "Erro ao remover tema",
        description: "ID necessário não encontrado.",
      });
      return;
    }

    try {
      console.log('🔄 Attempting to delete topic...');
      const { error } = await supabase
        .from('study_schedule_items')
        .delete()
        .eq('id', topicId);

      if (error) throw error;

      console.log('✅ Topic deleted successfully!');
      toast({
        title: "Tema removido",
        description: "O tema foi removido com sucesso do cronograma.",
      });
      
      console.log('🔄 Calling onDelete callback');
      onDelete();
      onOpenChange(false);
    } catch (error: any) {
      console.error('❌ Error deleting topic:', error);
      toast({
        variant: "destructive",
        title: "Erro ao remover tema",
        description: "Não foi possível remover o tema. Tente novamente.",
      });
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Remover tema</AlertDialogTitle>
          <AlertDialogDescription>
            Tem certeza que deseja remover este tema do cronograma? Esta ação não pode ser desfeita.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
            Remover
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}