import { useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { DoseDialog } from "./DoseDialog";
import { VaccineDialog } from "./VaccineDialog";
import { VaccineCard } from "./VaccineCard";

interface VaccineListProps {
  vaccines: any[];
}

export function VaccineList({ vaccines }: VaccineListProps) {
  const [selectedVaccine, setSelectedVaccine] = useState<string | null>(null);
  const [editingVaccine, setEditingVaccine] = useState<any>(null);
  const [deletingVaccine, setDeletingVaccine] = useState<any>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: vaccineRelationships } = useQuery({
    queryKey: ['vaccine-relationships'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_vaccine_relationships')
        .select(`
          parent_vaccine_id,
          child_vaccine:pedbook_vaccines!child_vaccine_id (
            id,
            name
          )
        `);
      
      if (error) throw error;
      
      const relationships: Record<string, any[]> = {};
      data.forEach(rel => {
        if (!relationships[rel.parent_vaccine_id]) {
          relationships[rel.parent_vaccine_id] = [];
        }
        relationships[rel.parent_vaccine_id].push(rel.child_vaccine);
      });
      
      return relationships;
    },
  });

  const { data: vaccinesData, error } = useQuery({
    queryKey: ['vaccines'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_vaccines')
        .select(`
          id,
          name,
          description,
          pedbook_vaccine_doses (
            id,
            dose_number,
            age_recommendation,
            description,
            type,
            dose_type
          )
        `);
      
      if (error) throw error;
      
      return data;
    },
  });

  if (error) {
    console.error("Error fetching vaccines:", error);
    return <div>Error fetching vaccines</div>;
  }

  const handleDeleteVaccine = async () => {
    try {
      const { error } = await supabase
        .from('pedbook_vaccines')
        .delete()
        .eq('id', deletingVaccine.id);

      if (error) throw error;

      toast({
        title: "Vacina removida com sucesso!",
        description: `A vacina ${deletingVaccine.name} foi removida.`,
        className: "fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
      });

      queryClient.invalidateQueries({ queryKey: ['vaccines'] });
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao remover vacina",
        description: error.message,
        className: "fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
      });
    } finally {
      setDeletingVaccine(null);
    }
  };

  const handleDeleteDose = async (doseId: string, doseName: string) => {
    try {
      const { error } = await supabase
        .from('pedbook_vaccine_doses')
        .delete()
        .eq('id', doseId);

      if (error) throw error;

      toast({
        title: "Dose removida com sucesso!",
        description: `A dose ${doseName} foi removida.`,
        className: "fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
      });

      queryClient.invalidateQueries({ queryKey: ['vaccines'] });
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao remover dose",
        description: error.message,
        className: "fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
      });
    }
  };

  const formatDoseNumber = (number: number, type: string) => {
    if (type === 'dose') {
      return `${number}ª dose`;
    } else if (type === 'reforço') {
      return `${number}º reforço`;
    }
    return `${number}ª dose`;
  };

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {vaccinesData?.map((vaccine) => (
        <VaccineCard
          key={vaccine.id}
          vaccine={vaccine}
          vaccineRelationships={vaccineRelationships || {}}
          onAddDose={(id) => setSelectedVaccine(id)}
          onEdit={(vaccine) => setEditingVaccine(vaccine)}
          onDelete={(vaccine) => setDeletingVaccine(vaccine)}
          formatDoseNumber={formatDoseNumber}
          handleDeleteDose={handleDeleteDose}
        />
      ))}
      
      <DoseDialog
        open={!!selectedVaccine}
        onOpenChange={() => setSelectedVaccine(null)}
        vaccineId={selectedVaccine || ""}
      />

      <VaccineDialog
        open={!!editingVaccine}
        onOpenChange={() => setEditingVaccine(null)}
        vaccine={editingVaccine}
      />

      <AlertDialog open={!!deletingVaccine} onOpenChange={() => setDeletingVaccine(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Você tem certeza?</AlertDialogTitle>
            <AlertDialogDescription>
              Esta ação não pode ser desfeita. Isso excluirá permanentemente a vacina
              {deletingVaccine?.name} e todas as suas doses.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteVaccine}>
              Continuar
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}