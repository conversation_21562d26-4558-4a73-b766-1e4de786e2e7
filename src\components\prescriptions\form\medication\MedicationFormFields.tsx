import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

interface MedicationFormFieldsProps {
  formData: {
    name: string;
    categoryId: string;
    description: string;
    brands: string;
  };
  onChange: (field: string, value: string) => void;
}

export function MedicationFormFields({ formData, onChange }: MedicationFormFieldsProps) {
  const { data: categories } = useQuery({
    queryKey: ['medication-categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_medication_categories')
        .select('*');
      
      if (error) throw error;
      return data;
    }
  });

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="name">Nome do Medicamento</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => onChange("name", e.target.value)}
          required
        />
      </div>

      <div>
        <Label htmlFor="category">Categoria</Label>
        <Select
          value={formData.categoryId}
          onValueChange={(value) => onChange("categoryId", value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Selecione uma categoria" />
          </SelectTrigger>
          <SelectContent>
            {categories?.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="description">Descrição</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => onChange("description", e.target.value)}
        />
      </div>

      <div>
        <Label htmlFor="brands">Marcas Comerciais</Label>
        <Input
          id="brands"
          value={formData.brands}
          onChange={(e) => onChange("brands", e.target.value)}
        />
      </div>
    </div>
  );
}