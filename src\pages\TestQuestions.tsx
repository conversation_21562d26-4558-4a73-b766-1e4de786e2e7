import { useState } from 'react';
import { QuestionCard } from '@/components/question/QuestionCard';
import { Question } from '@/types/question';

// Questão de teste para verificar se os componentes estão funcionando
const testQuestion: Question = {
  id: 'test-1',
  question_content: 'Esta é uma questão de teste para verificar se os componentes estão funcionando corretamente. Qual é a resposta correta?',
  response_choices: [
    'Primeira alternativa de teste',
    'Segunda alternativa de teste (correta)',
    'Terceira alternativa de teste',
    'Quarta alternativa de teste'
  ],
  correct_choice: 1, // Segunda alternativa (índice 1)
  question_format: 'ALTERNATIVAS',
  knowledge_domain: 'pediatria',
  specialty: {
    id: 'pediatria',
    name: 'Pediatri<PERSON>'
  },
  theme: {
    id: 'test-theme',
    name: 'Te<PERSON> de Teste'
  },
  exam_year: 2024
};

export const TestQuestions = () => {
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [hasAnswered, setHasAnswered] = useState(false);
  const [timeSpent, setTimeSpent] = useState(0);

  const handleSelectAnswer = (answer: string) => {
    setSelectedAnswer(answer);
  };

  const handleSubmitAnswer = async (time: number) => {
    setTimeSpent(time);
    setHasAnswered(true);
    console.log('Resposta enviada:', selectedAnswer, 'Tempo:', time);
  };

  const handleNext = () => {
    console.log('Próxima questão');
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">Teste dos Componentes de Questão</h1>
      
      <QuestionCard
        question={testQuestion}
        selectedAnswer={selectedAnswer}
        hasAnswered={hasAnswered}
        onSelectAnswer={handleSelectAnswer}
        onSubmitAnswer={handleSubmitAnswer}
        onNext={handleNext}
        userId="test-user"
        sessionId="test-session"
        timeSpent={timeSpent}
      />
    </div>
  );
};
