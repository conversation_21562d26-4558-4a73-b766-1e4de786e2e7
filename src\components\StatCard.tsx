
import { Card } from "@/components/ui/card";

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
}

const StatCard = ({ title, value, icon, trend, className }: StatCardProps) => {
  return (
    <Card className={`stat-card p-5 ${className || ''}`}>
      <div className="flex flex-col items-center text-center">
        <div className="mb-3 bg-primary/10 p-3 rounded-full">
          <div className="text-primary">
            {icon}
          </div>
        </div>
        
        <div className="space-y-1 w-full">
          <p className="text-sm font-medium text-gray-500 uppercase tracking-wider">
            {title}
          </p>
          <h3 className="text-2xl font-bold text-gray-800">
            {value}
          </h3>
          
          {trend && (
            <p className={`text-sm mt-2 ${trend.isPositive ? "text-green-600" : "text-red-600"}`}>
              {trend.isPositive ? "+" : "-"}{trend.value}% desde semana passada
            </p>
          )}
        </div>
      </div>
    </Card>
  );
};

export default StatCard;
