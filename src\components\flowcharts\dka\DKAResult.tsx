import { AlertDialog, AlertDialogContent, AlertDialogDescription, AlertDialog<PERSON>ooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";

interface DKAResult {
  severity: "confirmed" | "mild" | "moderate" | "severe" | null;
  recommendations: string[];
}

interface DKAResultProps {
  result: DKAResult | null;
  showResult: boolean;
  onReset: () => void;
  weight?: number;
}

export const DKAResult = ({ result, showResult, onReset, weight }: DKAResultProps) => {
  if (!showResult || !result) return null;

  const getHydrationRecommendation = () => {
    if (!weight) return "";
    const minVolume = Math.round(weight * 10);
    const maxVolume = Math.round(weight * 20);
    return `SF 0,9% ${minVolume}-${maxVolume} mL/kg EV em mais 1 hora`;
  };

  return (
    <AlertDialog open={showResult}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {result.severity === null
              ? "Avaliação"
              : "Cetoacidose Diabética"}
          </AlertDialogTitle>
          <AlertDialogDescription className="space-y-4">
            {result.recommendations.map((rec, index) => (
              <p key={index} className="whitespace-pre-line">
                {rec}
              </p>
            ))}
            {result.severity === "confirmed" && weight && (
              <p className="whitespace-pre-line">
                {getHydrationRecommendation()}
              </p>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <Button onClick={onReset}>Recomeçar</Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};