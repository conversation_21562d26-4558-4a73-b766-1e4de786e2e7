
import React from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

interface MarkdownWithTOCProps {
  content: string;
  className?: string;
}

export const MarkdownWithTOC: React.FC<MarkdownWithTOCProps> = ({ 
  content,
  className 
}) => {
  console.log('Content received:', content);

  const processContent = () => {
    console.log('Processing content for rendering:', content);

    // Processa os títulos para remover os marcadores ## e ##;
    const processedContent = content
      .replace(/##\./g, '')
      .replace(/##;/g, '')
      .replace(
        /(<h[2-4]><strong>)(.*?)(<\/strong><\/h[2-4]>)/g,
        '$1$2$3'
      );

    const sections = processedContent.split(/(?=<h[2-4]>)/);
    console.log('Processing sections:', sections);

    return sections.map((section, index) => {
      console.log(`Processing section ${index}:`, section);

      return (
        <motion.div
          key={`section-${index}`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
          className="prose max-w-none mb-6"
          dangerouslySetInnerHTML={{ __html: section }}
        />
      );
    });
  };

  return (
    <div className={cn("container mx-auto p-6", className)}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-gray-100"
      >
        {processContent()}
      </motion.div>
    </div>
  );
};

