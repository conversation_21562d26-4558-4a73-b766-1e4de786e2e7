import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

interface PrescriptionBasicInfoProps {
  name: string;
  description: string;
  notes: string;
  onNameChange: (value: string) => void;
  onDescriptionChange: (value: string) => void;
  onNotesChange: (value: string) => void;
}

export function PrescriptionBasicInfo({
  name,
  description,
  notes,
  onNameChange,
  onDescriptionChange,
  onNotesChange,
}: PrescriptionBasicInfoProps) {
  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="name">Nome da Prescrição</Label>
        <Input
          id="name"
          value={name}
          onChange={(e) => onNameChange(e.target.value)}
          placeholder="Ex: IVAS"
          required
        />
      </div>

      <div>
        <Label htmlFor="description">Descrição</Label>
        <Textarea
          id="description"
          value={description}
          onChange={(e) => onDescriptionChange(e.target.value)}
          placeholder="Descrição da prescrição (opcional)"
        />
      </div>

      <div>
        <Label htmlFor="notes">Observações</Label>
        <Textarea
          id="notes"
          value={notes}
          onChange={(e) => onNotesChange(e.target.value)}
          placeholder="Observações adicionais (opcional)"
        />
      </div>
    </div>
  );
}