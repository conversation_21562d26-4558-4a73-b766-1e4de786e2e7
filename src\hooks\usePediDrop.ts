import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface PediDropPost {
  id: string;
  title: string;
  summary: string;
  main_topic?: string;
  reading_time: number;
  category?: string;
  attention_points?: string;
  pub_date: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  is_published: boolean;
  views_count: number;
  sections?: PediDropSection[];
  references?: PediDropReference[];
}

export interface PediDropSection {
  id: string;
  post_id: string;
  title: string;
  content: string;
  section_order: number;
  created_at: string;
  updated_at: string;
}

export interface PediDropReference {
  id: string;
  post_id: string;
  reference_text: string; // Manter para compatibilidade
  title?: string; // Novo campo para título
  url?: string; // Novo campo para URL
  reference_order: number;
  created_at: string;
}

export interface CreatePediDropData {
  title: string;
  summary: string;
  main_topic?: string;
  reading_time?: number;
  pub_date?: string;
  is_published?: boolean;
  sections: Array<{
    title: string;
    content: string;
  }>;
  references: Array<{title: string; url: string} | string>; // Suportar ambos os formatos
}

export interface UpdatePediDropData extends CreatePediDropData {
  id: string;
}

// Função para obter data/hora atual (assumindo que o sistema já está em horário brasileiro)
const getCurrentDateTime = () => {
  return new Date();
};

// Hook para buscar PediDrops com paginação
export function usePediDrops(options?: {
  limit?: number;
  offset?: number;
  category?: string;
  searchTerm?: string;
  includeUnpublished?: boolean;
}) {
  const { limit = 10, offset = 0, category, searchTerm, includeUnpublished = false } = options || {};

  return useQuery({
    queryKey: ['pedidrop-posts', limit, offset, category, searchTerm, includeUnpublished],
    queryFn: async () => {
      // Obter data/hora atual
      const now = getCurrentDateTime();
      const nowISO = now.toISOString();



      let query = supabase
        .from('pedidrop_posts')
        .select(`
          *,
          pedidrop_sections (
            id,
            title,
            content,
            section_order
          ),
          pedidrop_references (
            id,
            reference_text,
            title,
            url,
            reference_order
          )
        `)
        .order('pub_date', { ascending: false })
        .range(offset, offset + limit - 1);

      // Filtrar por publicados se não for admin
      if (!includeUnpublished) {
        query = query.eq('is_published', true);
      }

      // FILTRO PRINCIPAL: Apenas posts de agora para trás
      if (!includeUnpublished) {
        query = query.lte('pub_date', nowISO);

      }

      if (category) {
        query = query.eq('category', category);
      }

      if (searchTerm) {
        query = query.or(`title.ilike.%${searchTerm}%,summary.ilike.%${searchTerm}%,main_topic.ilike.%${searchTerm}%`);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(error.message);
      }



      // Ordenar seções e referências
      const processedData = data?.map(post => ({
        ...post,
        sections: post.pedidrop_sections?.sort((a, b) => a.section_order - b.section_order) || [],
        references: post.pedidrop_references?.sort((a, b) => a.reference_order - b.reference_order) || []
      })) || [];

      return processedData as PediDropPost[];
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 30 * 60 * 1000, // 30 minutos
  });
}

// Hook para buscar contagem total de PediDrops
export function usePediDropsCount(options?: {
  category?: string;
  searchTerm?: string;
  includeUnpublished?: boolean;
}) {
  const { category, searchTerm, includeUnpublished = false } = options || {};

  return useQuery({
    queryKey: ['pedidrop-posts-count', category, searchTerm, includeUnpublished],
    queryFn: async () => {
      // Obter data/hora atual
      const now = getCurrentDateTime();
      const nowISO = now.toISOString();

      let query = supabase
        .from('pedidrop_posts')
        .select('id', { count: 'exact', head: true });

      if (!includeUnpublished) {
        query = query.eq('is_published', true);
      }

      // FILTRO PRINCIPAL: Apenas posts de agora para trás
      if (!includeUnpublished) {
        query = query.lte('pub_date', nowISO);
      }

      if (category) {
        query = query.eq('category', category);
      }

      if (searchTerm) {
        query = query.or(`title.ilike.%${searchTerm}%,summary.ilike.%${searchTerm}%,main_topic.ilike.%${searchTerm}%`);
      }

      const { count, error } = await query;

      if (error) {
        throw new Error(error.message);
      }

      return count || 0;
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
  });
}

// Hook para buscar um PediDrop específico
export function usePediDrop(id: string) {
  return useQuery({
    queryKey: ['pedidrop-post', id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedidrop_posts')
        .select(`
          *,
          pedidrop_sections (
            id,
            title,
            content,
            section_order
          ),
          pedidrop_references (
            id,
            reference_text,
            title,
            url,
            reference_order
          )
        `)
        .eq('id', id)
        .single();

      if (error) {
        throw new Error(error.message);
      }

      // Ordenar seções e referências
      const processedData = {
        ...data,
        sections: data.pedidrop_sections?.sort((a, b) => a.section_order - b.section_order) || [],
        references: data.pedidrop_references?.sort((a, b) => a.reference_order - b.reference_order) || []
      };

      return processedData as PediDropPost;
    },
    enabled: !!id,
    staleTime: 10 * 60 * 1000,
    gcTime: 60 * 60 * 1000,
  });
}

// Hook para criar PediDrop
export function useCreatePediDrop() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreatePediDropData) => {
      // Inserir post principal
      const { data: postData, error: postError } = await supabase
        .from('pedidrop_posts')
        .insert([{
          title: data.title,
          summary: data.summary,
          main_topic: data.main_topic,
          reading_time: data.reading_time || 5,
          category: data.category,
          attention_points: data.attention_points,
          pub_date: data.pub_date || new Date().toISOString(),
          is_published: data.is_published || false,
          created_by: (await supabase.auth.getUser()).data.user?.id
        }])
        .select()
        .single();

      if (postError) throw postError;

      const postId = postData.id;

      // Inserir seções
      if (data.sections && data.sections.length > 0) {
        const sectionsToInsert = data.sections.map((section, index) => ({
          post_id: postId,
          title: section.title,
          content: section.content,
          section_order: index + 1
        }));

        const { error: sectionsError } = await supabase
          .from('pedidrop_sections')
          .insert(sectionsToInsert);

        if (sectionsError) throw sectionsError;
      }

      // Inserir referências
      if (data.references && data.references.length > 0) {
        const referencesToInsert = data.references
          .filter(ref => typeof ref === 'string' ? ref.trim() !== '' : ref.title.trim() !== '' || ref.url.trim() !== '')
          .map((reference, index) => {
            if (typeof reference === 'string') {
              // Formato antigo (compatibilidade)
              return {
                post_id: postId,
                reference_text: reference,
                reference_order: index + 1
              };
            } else {
              // Novo formato com título e URL
              return {
                post_id: postId,
                reference_text: reference.title, // Manter compatibilidade
                title: reference.title,
                url: reference.url,
                reference_order: index + 1
              };
            }
          });

        if (referencesToInsert.length > 0) {
          const { error: referencesError } = await supabase
            .from('pedidrop_references')
            .insert(referencesToInsert);

          if (referencesError) throw referencesError;
        }
      }

      return postData;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pedidrop-posts'] });
      queryClient.invalidateQueries({ queryKey: ['pedidrop-posts-count'] });
    },
  });
}

// Hook para atualizar PediDrop
export function useUpdatePediDrop() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdatePediDropData) => {
      // Atualizar post principal
      const { data: postData, error: postError } = await supabase
        .from('pedidrop_posts')
        .update({
          title: data.title,
          summary: data.summary,
          main_topic: data.main_topic,
          reading_time: data.reading_time || 5,
          category: data.category,
          attention_points: data.attention_points,
          pub_date: data.pub_date,
          is_published: data.is_published || false,
        })
        .eq('id', data.id)
        .select()
        .single();

      if (postError) throw postError;

      // Remover seções existentes
      await supabase
        .from('pedidrop_sections')
        .delete()
        .eq('post_id', data.id);

      // Remover referências existentes
      await supabase
        .from('pedidrop_references')
        .delete()
        .eq('post_id', data.id);

      // Inserir novas seções
      if (data.sections && data.sections.length > 0) {
        const sectionsToInsert = data.sections.map((section, index) => ({
          post_id: data.id,
          title: section.title,
          content: section.content,
          section_order: index + 1
        }));

        const { error: sectionsError } = await supabase
          .from('pedidrop_sections')
          .insert(sectionsToInsert);

        if (sectionsError) throw sectionsError;
      }

      // Inserir novas referências
      if (data.references && data.references.length > 0) {
        const referencesToInsert = data.references
          .filter(ref => typeof ref === 'string' ? ref.trim() !== '' : ref.title.trim() !== '' || ref.url.trim() !== '')
          .map((reference, index) => {
            if (typeof reference === 'string') {
              // Formato antigo (compatibilidade)
              return {
                post_id: data.id,
                reference_text: reference,
                reference_order: index + 1
              };
            } else {
              // Novo formato com título e URL
              return {
                post_id: data.id,
                reference_text: reference.title, // Manter compatibilidade
                title: reference.title,
                url: reference.url,
                reference_order: index + 1
              };
            }
          });

        if (referencesToInsert.length > 0) {
          const { error: referencesError } = await supabase
            .from('pedidrop_references')
            .insert(referencesToInsert);

          if (referencesError) throw referencesError;
        }
      }

      return postData;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['pedidrop-posts'] });
      queryClient.invalidateQueries({ queryKey: ['pedidrop-posts-count'] });
      queryClient.invalidateQueries({ queryKey: ['pedidrop-post', variables.id] });
    },
  });
}

// Hook para deletar PediDrop
export function useDeletePediDrop() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('pedidrop_posts')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pedidrop-posts'] });
      queryClient.invalidateQueries({ queryKey: ['pedidrop-posts-count'] });
    },
  });
}

// Hook para buscar categorias de PediDrop
export function usePediDropCategories() {
  return useQuery({
    queryKey: ['pedidrop-categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedidrop_posts')
        .select('category')
        .not('category', 'is', null)
        .neq('category', '');

      if (error) throw error;

      // Extrair categorias únicas
      const categories = [...new Set(data.map(item => item.category))].filter(Boolean);
      return categories as string[];
    },
    staleTime: 30 * 60 * 1000, // 30 minutos
    gcTime: 60 * 60 * 1000, // 1 hora
  });
}
