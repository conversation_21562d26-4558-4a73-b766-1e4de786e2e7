import { useQuery } from "@tanstack/react-query";
import { useQueryClient } from "@tanstack/react-query";
import { Pencil, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface DosageListProps {
  selectedMedicationId: string;
  onEdit: (dosage: any) => void;
}

export function DosageList({ selectedMedicationId, onEdit }: DosageListProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: dosages } = useQuery({
    queryKey: ["medication-dosages", selectedMedicationId],
    queryFn: async () => {
      if (!selectedMedicationId) return [];
      
      const { data, error } = await supabase
        .from("pedbook_medication_dosages")
        .select(`
          *,
          pedbook_medications (
            name,
            slug
          )
        `)
        .eq("medication_id", selectedMedicationId)
        .order("name");

      if (error) throw error;
      return data;
    },
    enabled: !!selectedMedicationId,
  });

  const handleDelete = async (dosageId: string, dosageName: string) => {
    try {
      const { error } = await supabase
        .from("pedbook_medication_dosages")
        .delete()
        .eq("id", dosageId);

      if (error) throw error;

      toast({
        title: "Dosagem excluída com sucesso!",
        description: `A dosagem ${dosageName} foi excluída.`,
      });

      queryClient.invalidateQueries({ queryKey: ["medication-dosages"] });
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Erro ao excluir dosagem",
        description: error.message,
      });
    }
  };

  if (!selectedMedicationId) {
    return (
      <div className="text-center text-gray-500 py-8">
        Selecione um medicamento para ver suas dosagens
      </div>
    );
  }

  if (!dosages?.length) {
    return (
      <div className="text-center text-gray-500 py-8">
        Nenhuma dosagem cadastrada para este medicamento
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {dosages?.map((dosage) => (
        <div
          key={dosage.id}
          className="flex justify-between items-start p-4 bg-gray-50 rounded-lg"
        >
          <div className="space-y-2">
            <h4 className="font-medium">{dosage.name}</h4>
            {dosage.summary && (
              <p className="text-sm text-gray-600">{dosage.summary}</p>
            )}
            {dosage.explanation && (
              <p className="text-sm text-gray-600 whitespace-pre-line">
                {dosage.explanation}
              </p>
            )}
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => onEdit(dosage)}
              className="hover:bg-primary/10"
            >
              <Pencil className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleDelete(dosage.id, dosage.name)}
              className="hover:bg-destructive/10"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
}