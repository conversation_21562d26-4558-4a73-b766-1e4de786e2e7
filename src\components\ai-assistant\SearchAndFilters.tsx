import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { symptomCategories } from "./symptomData";

interface SearchAndFiltersProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  selectedCategory: string | null;
  setSelectedCategory: (category: string | null) => void;
}

export function SearchAndFilters({
  searchTerm,
  setSearchTerm,
  selectedCategory,
  setSelectedCategory
}: SearchAndFiltersProps) {
  return (
    <div className="space-y-4">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-500" />
        <Input
          placeholder="Buscar sintomas..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 h-12 text-lg bg-white/50 backdrop-blur-sm w-full"
        />
      </div>
      
      {/* Mobile Select */}
      <div className="md:hidden">
        <Select
          value={selectedCategory || "all"}
          onValueChange={(value) => setSelectedCategory(value === "all" ? null : value)}
        >
          <SelectTrigger className="w-full bg-white/50 backdrop-blur-sm border-primary/20">
            <SelectValue placeholder="Selecione uma categoria" />
          </SelectTrigger>
          <SelectContent className="bg-white/95 backdrop-blur-sm">
            <SelectItem value="all">Todos</SelectItem>
            {symptomCategories.map((category) => (
              <SelectItem key={category.name} value={category.name}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Desktop Badges */}
      <div className="hidden md:flex flex-wrap gap-2">
        <Badge
          variant={!selectedCategory ? "default" : "outline"}
          className="cursor-pointer hover:bg-primary/90 transition-colors text-sm"
          onClick={() => setSelectedCategory(null)}
        >
          Todos
        </Badge>
        {symptomCategories.map((category) => (
          <Badge
            key={category.name}
            variant={selectedCategory === category.name ? "default" : "outline"}
            className="cursor-pointer hover:bg-primary/90 transition-colors text-sm"
            onClick={() => setSelectedCategory(category.name)}
          >
            {category.name}
          </Badge>
        ))}
      </div>
    </div>
  );
}