import { Card } from "@/components/ui/card";
import { Syringe, AlertCircle } from "lucide-react";

interface SecondLinePhaseProps {
  weight: number;
}

export const SecondLinePhase = ({ weight }: SecondLinePhaseProps) => {
  const fenitoinaDose = Math.round(weight * 25);
  const maxFenitoinaDose = Math.round(weight * 30);

  return (
    <div className="space-y-6">
      <Card className="p-6 bg-white/80 backdrop-blur-sm border border-purple-200">
        <div className="flex items-start gap-4">
          <div className="mt-1">
            <Syringe className="h-6 w-6 text-purple-600" />
          </div>
          <div className="space-y-3">
            <h3 className="font-semibold text-lg text-purple-800">
              Fenitoína ou Fosfenitoína
            </h3>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>
                Fenitoína: {fenitoinaDose}-{maxFenitoinaDose} mg IV (máx. 50
                mg/min)
              </li>
              <li>
                Fosfenitoína: {fenitoinaDose}-{maxFenitoinaDose} mg IV (máx. 150
                mg/min)
              </li>
            </ul>
          </div>
        </div>
      </Card>

      <Card className="p-6 bg-white/80 backdrop-blur-sm border border-red-200">
        <div className="flex items-start gap-4">
          <div className="mt-1">
            <AlertCircle className="h-6 w-6 text-red-600" />
          </div>
          <div className="space-y-3">
            <h3 className="font-semibold text-lg text-red-800">Atenção</h3>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>
                Infusão rápida pode causar arritmias cardíacas e hipotensão
              </li>
              <li>
                Em pacientes em uso crônico de Fenitoína, prefira Fenobarbital
              </li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};