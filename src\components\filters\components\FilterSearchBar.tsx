import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";

interface FilterSearchBarProps {
  placeholder: string;
  value: string;
  onChange: (value: string) => void;
}

export const FilterSearchBar = ({ placeholder, value, onChange }: FilterSearchBarProps) => {
  return (
    <div className="relative">
      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
      <Input
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="pl-10 bg-gray-100 border-none"
      />
    </div>
  );
};