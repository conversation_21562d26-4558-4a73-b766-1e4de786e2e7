
import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { getThemeClasses } from "@/components/ui/theme-utils";

interface DKAQuestion {
  id: string;
  text: string;
  type: "number" | "select";
  options?: string[];
  unit?: string;
  validation?: {
    min?: number;
    max?: number;
  };
}

interface DKAQuestionsProps {
  currentQuestion: string;
  questions: Record<string, DKAQuestion>;
  onAnswer: (value: any) => void;
  showDehydrationQuestion?: boolean;
  dehydrationAttempts?: number;
}

export const DKAQuestions = ({ 
  currentQuestion, 
  questions, 
  onAnswer,
  showDehydrationQuestion = false,
  dehydrationAttempts = 0
}: DKAQuestionsProps) => {
  const [inputValue, setInputValue] = useState<string>("");
  const question = questions[currentQuestion];

  if (!question) return null;

  const handleSubmit = () => {
    if (question.type === "number") {
      const numValue = parseFloat(inputValue);
      if (!isNaN(numValue)) {
        onAnswer(numValue);
        setInputValue("");
      }
    }
  };

  if (showDehydrationQuestion) {
    return (
      <Card className={getThemeClasses.card("p-6 space-y-4 border-primary/20 dark:border-primary/30")}>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          {dehydrationAttempts > 0 
            ? "Após mais 1 hora, mantém desidratação?"
            : "Após 1 hora, mantém desidratação?"}
        </h3>
        <div className="space-y-4">
          <RadioGroup onValueChange={(value) => onAnswer(value === "true")}>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="true" id="yes" />
              <Label htmlFor="yes" className="text-gray-700 dark:text-gray-300">Sim</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="false" id="no" />
              <Label htmlFor="no" className="text-gray-700 dark:text-gray-300">Não</Label>
            </div>
          </RadioGroup>
        </div>
      </Card>
    );
  }

  return (
    <Card className={getThemeClasses.card("p-6 space-y-4 border-primary/20 dark:border-primary/30")}>
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{question.text}</h3>
      {question.type === "number" ? (
        <div className="flex items-center gap-2">
          <Input
            type="number"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            min={question.validation?.min}
            max={question.validation?.max}
            className={getThemeClasses.input("flex-1")}
          />
          {question.unit && <span className="text-sm text-gray-600 dark:text-gray-400">{question.unit}</span>}
          <Button onClick={handleSubmit}>Confirmar</Button>
        </div>
      ) : (
        <div className="flex flex-col gap-2">
          {question.options?.map((option) => (
            <Button
              key={option}
              variant="outline"
              className="justify-start hover:bg-primary/10 dark:hover:bg-primary/20 dark:border-gray-700 dark:text-gray-200"
              onClick={() => onAnswer(option)}
            >
              {option}
            </Button>
          ))}
        </div>
      )}
    </Card>
  );
};
