
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { calculatePrescriptionDosage } from "@/lib/calculatePrescriptionDosage";
import { useEffect, useState } from "react";
import { EmptyState } from "./EmptyState";
import { PrescriptionDetails } from "./PrescriptionDetails";
import { useSession } from "@supabase/auth-helpers-react";
import type { PrescriptionWithMedications } from "./types";
import type { DosageResult } from "@/lib/dosageCalculator";
import { motion } from "framer-motion";
import { useToast } from "@/hooks/use-toast";

interface PrescriptionContentProps {
  prescriptionId: string;
  onWeightChange: (weight: string) => void;
  onAgeChange: (age: string) => void;
  onSelectPrescription: (id: string) => void;
}

export const PrescriptionContent = ({
  prescriptionId,
  onWeightChange,
  onAgeChange,
  onSelectPrescription,
}: PrescriptionContentProps) => {
  const [calculatedDosages, setCalculatedDosages] = useState<Record<string, DosageResult | string>>({});
  const [isUpdating, setIsUpdating] = useState(false);
  const session = useSession();
  const { toast } = useToast();

  const { data: prescription, error } = useQuery<PrescriptionWithMedications>({
    queryKey: ["prescription", prescriptionId],
    queryFn: async () => {
      if (!prescriptionId) return null;
      
      const { data, error } = await supabase
        .from("pedbook_prescriptions")
        .select(`
          *,
          pedbook_prescription_medications (
            id,
            medication_id,
            dosage_id,
            prescription_id,
            notes,
            section_title,
            display_order,
            created_at,
            updated_at,
            quantity,
            pedbook_medications (
              name,
              brands,
              description
            ),
            pedbook_medication_dosages (
              name,
              dosage_template,
              summary
            )
          )
        `)
        .eq('id', prescriptionId)
        .maybeSingle();

      if (error) {
        console.error("❌ Error fetching prescription:", error);
        toast({
          variant: "destructive",
          title: "Erro ao carregar prescrição",
          description: "Não foi possível carregar os detalhes da prescrição.",
        });
        throw error;
      }

      if (!data) {
        console.warn("⚠️ No prescription data found");
        toast({
          variant: "destructive",
          title: "Prescrição não encontrada",
          description: "A prescrição solicitada não foi encontrada ou você não tem permissão para acessá-la.",
        });
        return null;
      }

      // Ordenar os medicamentos por display_order
      if (data.pedbook_prescription_medications) {
        data.pedbook_prescription_medications.sort((a, b) => 
          (a.display_order || 0) - (b.display_order || 0)
        );
      }

      return data;
    },
    enabled: !!prescriptionId,
  });

  useEffect(() => {
    const updateDosages = async () => {
      if (!prescription?.pedbook_prescription_medications) return;
      
      console.log("Updating dosages in PrescriptionContent");
      setIsUpdating(true);
      const newDosages: Record<string, DosageResult | string> = {};
      for (const med of prescription.pedbook_prescription_medications) {
        const dosageTemplate = med.pedbook_medication_dosages?.dosage_template || med.dosage_template;
        if (dosageTemplate) {
          try {
            const calculatedDosage = await calculatePrescriptionDosage(
              dosageTemplate,
              prescription.patient_weight || 0,
              prescription.patient_age || 0,
              med.medication_id
            );
            newDosages[med.id] = calculatedDosage;
          } catch (error) {
            console.error("Error calculating dosage:", error);
            newDosages[med.id] = { text: "Erro ao calcular dosagem" };
          }
        }
      }
      
      setCalculatedDosages(newDosages);
      setIsUpdating(false);
    };

    updateDosages();
  }, [prescription]);

  if (!prescriptionId) {
    return (
      <motion.div 
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-gradient-to-br from-white/80 to-primary/5 dark:from-slate-900/90 dark:to-slate-800/80 backdrop-blur-sm rounded-xl shadow-lg p-4 md:p-8 min-h-[calc(100vh-8rem)] flex flex-col items-center justify-center text-center space-y-6"
      >
        <EmptyState
          prescriptionsCount={0}
          session={session}
          onSelectPrescription={onSelectPrescription}
        />
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div 
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-gradient-to-br from-white/80 to-primary/5 dark:from-slate-900/90 dark:to-slate-800/80 backdrop-blur-sm rounded-xl shadow-lg p-4 md:p-8 min-h-[calc(100vh-8rem)] flex flex-col items-center justify-center text-center space-y-6"
      >
        <div className="text-destructive">
          <p>Erro ao carregar a prescrição.</p>
          <p className="text-sm text-muted-foreground">Por favor, tente novamente mais tarde.</p>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div 
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className="bg-gradient-to-br from-white/80 to-primary/5 dark:from-slate-900/90 dark:to-slate-800/80 backdrop-blur-sm rounded-xl shadow-lg p-4 md:p-8 min-h-[calc(100vh-8rem)] flex flex-col"
    >
      {prescription && (
        <PrescriptionDetails
          prescription={prescription}
          calculatedDosages={calculatedDosages}
          isUpdating={isUpdating}
          onWeightChange={onWeightChange}
          onAgeChange={onAgeChange}
        />
      )}
    </motion.div>
  );
};
