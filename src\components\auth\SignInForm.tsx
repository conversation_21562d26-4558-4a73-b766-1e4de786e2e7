import { useState, useRef, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON>rkles, KeyRound, Loader2, AlertCircle } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { useNotification } from "@/context/NotificationContext";

const formSchema = z.object({
  email: z.string().email("Email inválido"),
  password: z.string().min(6, "A senha deve ter pelo menos 6 caracteres"),
});

interface SignInFormProps {
  onModeChange: () => void;
  onResetPassword: () => void;
  onSuccess: () => void;
}

const SignInForm = ({ onModeChange, onResetPassword, onSuccess }: SignInFormProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loginStage, setLoginStage] = useState<'idle' | 'authenticating' | 'loading-profile' | 'success' | 'error'>('idle');
  const [progress, setProgress] = useState(0);
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [errorDetails, setErrorDetails] = useState<string | null>(null);
  const loginTimerRef = useRef<NodeJS.Timeout | null>(null);
  const { showNotification } = useNotification();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  // Efeito para simular o progresso de carregamento
  useEffect(() => {
    if (isLoading && loginStage !== 'error' && loginStage !== 'success') {
      const interval = setInterval(() => {
        setProgress((prev) => {
          // Progresso mais lento entre 70-90% para dar tempo de carregar o perfil
          if (prev >= 70 && prev < 90 && loginStage === 'loading-profile') {
            return prev + 0.5;
          }
          // Progresso normal
          return Math.min(prev + 2, 95);
        });
      }, 100);

      return () => clearInterval(interval);
    } else if (loginStage === 'success') {
      setProgress(100);
    }
  }, [isLoading, loginStage]);

  // Efeito para timeout de login
  useEffect(() => {
    if (isLoading) {
      // Definir um timeout para o login (10 segundos)
      loginTimerRef.current = setTimeout(() => {
        if (loginStage !== 'success') {
          setLoginStage('error');
          setErrorDetails('Tempo limite excedido. A rede pode estar lenta ou instável.');
          setIsLoading(false);

          showNotification({
            title: "Tempo limite excedido",
            description: "A conexão está lenta. Tente novamente ou verifique sua internet.",
            type: "error",
            buttonText: "Tentar novamente"
          });
        }
      }, 10000);
    }

    return () => {
      if (loginTimerRef.current) {
        clearTimeout(loginTimerRef.current);
      }
    };
  }, [isLoading, loginStage, showNotification]);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      // Resetar estados
      setIsLoading(true);
      setProgress(0);
      setLoginStage('authenticating');
      setErrorDetails(null);
      setLoginAttempts(prev => prev + 1);

      // Etapa 1: Autenticação

      const { data, error } = await supabase.auth.signInWithPassword({
        email: values.email,
        password: values.password,
      });

      if (error) {
        setLoginStage('error');
        throw error;
      }

      // Etapa 2: Carregando perfil
      setLoginStage('loading-profile');

      // Simular um pequeno atraso para mostrar o progresso
      await new Promise(resolve => setTimeout(resolve, 500));

      setLoginStage('success');

      // Obter o nome do usuário do perfil
      const userProfile = data.user?.user_metadata;
      const userName = userProfile?.full_name || '';

      // Determinar saudação com base na hora do dia
      const hora = new Date().getHours();
      let saudacao = "Bem-vindo";

      if (hora >= 5 && hora < 12) {
        saudacao = "Bom dia";
      } else if (hora >= 12 && hora < 18) {
        saudacao = "Boa tarde";
      } else {
        saudacao = "Boa noite";
      }

      // Personalizar mensagem com o nome do usuário
      const mensagem = userName
        ? `${saudacao}, ${userName.split(' ')[0]}! Que bom ter você de volta.`
        : `${saudacao}! Que bom ter você de volta.`;

      // Mostrar notificação de login bem-sucedido
      showNotification({
        title: "Login realizado com sucesso!",
        description: mensagem,
        type: "success",
        buttonText: "Continuar",
        onButtonClick: onSuccess
      });

      // Pequeno atraso para mostrar o progresso completo
      setTimeout(() => {
        // onSuccess será chamado quando o usuário clicar no botão da notificação
      }, 300);

    } catch (error: any) {
      // Determinar mensagem de erro mais específica
      let errorMessage = "Verifique suas credenciais e tente novamente.";

      if (error.message?.includes("Invalid login")) {
        errorMessage = "Email ou senha incorretos.";
      } else if (error.message?.includes("network")) {
        errorMessage = "Problema de conexão. Verifique sua internet.";
      } else if (error.message?.includes("timeout")) {
        errorMessage = "Tempo limite excedido. Tente novamente mais tarde.";
      }

      setErrorDetails(errorMessage);
      setLoginStage('error');
    } finally {
      if (loginStage !== 'success') {
        setIsLoading(false);
      }
    }
  };

  return (
    <div className="space-y-6 py-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 w-full max-w-sm mx-auto">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm sm:text-base font-medium">Email</FormLabel>
                <FormControl>
                  <Input
                    placeholder="<EMAIL>"
                    {...field}
                    type="email"
                    autoComplete="email"
                    className="w-full text-sm sm:text-base bg-white/5 border-primary/20 focus:border-primary transition-all"
                  />
                </FormControl>
                <FormMessage className="text-xs sm:text-sm" />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm sm:text-base font-medium">Senha</FormLabel>
                <FormControl>
                  <Input
                    type="password"
                    autoComplete="current-password"
                    {...field}
                    className="w-full text-sm sm:text-base bg-white/5 border-primary/20 focus:border-primary transition-all"
                  />
                </FormControl>
                <FormMessage className="text-xs sm:text-sm" />
              </FormItem>
            )}
          />
          <div className="flex flex-col gap-4">
            {isLoading && (
              <div className="mb-2 space-y-2">
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>
                    {loginStage === 'authenticating' && 'Verificando credenciais...'}
                    {loginStage === 'loading-profile' && 'Carregando seu perfil...'}
                    {loginStage === 'success' && 'Login realizado com sucesso!'}
                    {loginStage === 'error' && 'Erro ao fazer login'}
                  </span>
                  <span>{Math.round(progress)}%</span>
                </div>
                <Progress value={progress} className="h-1.5" />
              </div>
            )}

            <Button
              type="submit"
              className="w-full text-sm sm:text-base bg-primary hover:bg-primary/90 text-white transition-all relative"
              disabled={isLoading}
            >
              {isLoading ? (
                <span className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  {loginStage === 'authenticating' && 'Autenticando...'}
                  {loginStage === 'loading-profile' && 'Carregando perfil...'}
                  {loginStage === 'success' && 'Redirecionando...'}
                  {loginStage === 'error' && 'Erro ao entrar'}
                </span>
              ) : (
                "Entrar"
              )}
            </Button>

            {errorDetails && (
              <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md border border-destructive/30 flex items-start gap-2">
                <AlertCircle className="h-5 w-5 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="font-medium">Não foi possível fazer login</p>
                  <p className="text-xs mt-1">{errorDetails}</p>
                </div>
              </div>
            )}

            <Button
              type="button"
              variant="ghost"
              className="text-sm text-muted-foreground hover:text-primary flex items-center gap-2 justify-center"
              onClick={onResetPassword}
              disabled={isLoading}
            >
              <KeyRound className="h-4 w-4" />
              Esqueceu sua senha?
            </Button>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  ou
                </span>
              </div>
            </div>

            <Button
              type="button"
              variant="outline"
              className="w-full text-sm sm:text-base group relative overflow-hidden bg-gradient-to-r from-accent-purple/20 via-accent-blue/20 to-accent-pink/20 hover:from-accent-purple/30 hover:via-accent-blue/30 hover:to-accent-pink/30 border-primary/20 transition-all duration-300"
              onClick={onModeChange}
              disabled={isLoading}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-accent-purple/10 via-accent-blue/10 to-accent-pink/10 group-hover:opacity-100 transition-opacity" />
              <span className="relative flex items-center justify-center gap-2">
                <Sparkles className="h-4 w-4 text-primary animate-pulse" />
                Cadastre-se GRATUITAMENTE!
              </span>
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default SignInForm;