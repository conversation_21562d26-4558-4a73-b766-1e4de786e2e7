/**
 * Sistema de cache persistente para React Query
 * Salva dados importantes no localStorage para sobreviver a recarregamentos
 */

import { QueryClient } from '@tanstack/react-query';
import { CACHE_KEYS } from './cacheConfig';

interface CacheEntry {
  data: any;
  timestamp: number;
  expiresAt: number;
}

class PersistentCache {
  private readonly STORAGE_KEY = 'pedbook-cache';
  private readonly MAX_STORAGE_SIZE = 5 * 1024 * 1024; // 5MB

  /**
   * Chaves que devem ser persistidas
   * IMPORTANTE: Medicamentos removidos para garantir dados sempre atualizados
   */
  private readonly PERSISTENT_KEYS = [
    'site-settings',
    'categories', // Categorias podem ser persistidas (mudam raramente)
    // 'medications', // REMOVIDO: Medicamentos não devem ser persistidos
    'user-profile',
    'age',
    'weight'
  ];

  /**
   * Salva dados no cache persistente
   */
  save(key: string, data: any, ttl: number = 24 * 60 * 60 * 1000): void {
    try {
      if (!this.shouldPersist(key)) return;

      const cache = this.getCache();
      const entry: CacheEntry = {
        data,
        timestamp: Date.now(),
        expiresAt: Date.now() + ttl
      };

      cache[key] = entry;

      // Limpar entradas expiradas antes de salvar
      this.cleanExpired(cache);

      // Verificar tamanho e limpar se necessário
      this.ensureStorageLimit(cache);

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(cache));
    } catch (error) {
      console.warn('Failed to save to persistent cache:', error);
      // Se falhar, tentar limpar cache antigo
      this.clear();
    }
  }

  /**
   * Recupera dados do cache persistente
   */
  get(key: string): any | null {
    try {
      const cache = this.getCache();
      const entry = cache[key];

      if (!entry) return null;

      // Verificar se expirou
      if (Date.now() > entry.expiresAt) {
        delete cache[key];
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(cache));
        return null;
      }

      return entry.data;
    } catch (error) {
      console.warn('Failed to get from persistent cache:', error);
      return null;
    }
  }

  /**
   * Remove entrada específica do cache
   */
  remove(key: string): void {
    try {
      const cache = this.getCache();
      delete cache[key];
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(cache));
    } catch (error) {
      console.warn('Failed to remove from persistent cache:', error);
    }
  }

  /**
   * Limpa todo o cache persistente
   */
  clear(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear persistent cache:', error);
    }
  }

  /**
   * Inicializa o cache persistente com React Query
   */
  initializeWithQueryClient(queryClient: QueryClient): void {
    try {
      const cache = this.getCache();

      // Restaurar dados persistidos no React Query
      Object.entries(cache).forEach(([key, entry]) => {
        if (Date.now() <= entry.expiresAt) {
          // Converter chave de volta para array se necessário
          const queryKey = this.parseQueryKey(key);
          queryClient.setQueryData(queryKey, entry.data);
        }
      });

      // Limpar entradas expiradas
      this.cleanExpired(cache);
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(cache));
    } catch (error) {
      console.warn('Failed to initialize persistent cache:', error);
    }
  }

  /**
   * Configura listeners para salvar dados automaticamente
   */
  setupAutoSave(queryClient: QueryClient): void {
    // Salvar dados importantes quando mudarem
    queryClient.getQueryCache().subscribe((event) => {
      if (event.type === 'updated' && event.query.state.data) {
        const key = this.stringifyQueryKey(event.query.queryKey);
        if (this.shouldPersist(key)) {
          this.save(key, event.query.state.data);
        }
      }
    });
  }

  /**
   * Obtém o cache atual do localStorage
   */
  private getCache(): Record<string, CacheEntry> {
    try {
      const cached = localStorage.getItem(this.STORAGE_KEY);
      return cached ? JSON.parse(cached) : {};
    } catch (error) {
      console.warn('Failed to parse cache from localStorage:', error);
      return {};
    }
  }

  /**
   * Verifica se uma chave deve ser persistida
   */
  private shouldPersist(key: string): boolean {
    return this.PERSISTENT_KEYS.some(persistentKey =>
      key.includes(persistentKey)
    );
  }

  /**
   * Limpa entradas expiradas do cache
   */
  private cleanExpired(cache: Record<string, CacheEntry>): void {
    const now = Date.now();
    Object.keys(cache).forEach(key => {
      if (cache[key].expiresAt < now) {
        delete cache[key];
      }
    });
  }

  /**
   * Garante que o cache não exceda o limite de tamanho
   */
  private ensureStorageLimit(cache: Record<string, CacheEntry>): void {
    const cacheString = JSON.stringify(cache);

    if (cacheString.length > this.MAX_STORAGE_SIZE) {
      // Remover entradas mais antigas até ficar dentro do limite
      const entries = Object.entries(cache)
        .sort(([, a], [, b]) => a.timestamp - b.timestamp);

      while (JSON.stringify(cache).length > this.MAX_STORAGE_SIZE && entries.length > 0) {
        const [oldestKey] = entries.shift()!;
        delete cache[oldestKey];
      }
    }
  }

  /**
   * Converte queryKey para string
   */
  private stringifyQueryKey(queryKey: unknown[]): string {
    return JSON.stringify(queryKey);
  }

  /**
   * Converte string de volta para queryKey
   */
  private parseQueryKey(key: string): unknown[] {
    try {
      return JSON.parse(key);
    } catch {
      return [key];
    }
  }

  /**
   * Obtém estatísticas do cache
   */
  getStats(): {
    totalEntries: number;
    totalSize: number;
    expiredEntries: number;
  } {
    try {
      const cache = this.getCache();
      const now = Date.now();
      const entries = Object.values(cache);

      return {
        totalEntries: entries.length,
        totalSize: JSON.stringify(cache).length,
        expiredEntries: entries.filter(entry => entry.expiresAt < now).length
      };
    } catch {
      return { totalEntries: 0, totalSize: 0, expiredEntries: 0 };
    }
  }
}

export const persistentCache = new PersistentCache();
