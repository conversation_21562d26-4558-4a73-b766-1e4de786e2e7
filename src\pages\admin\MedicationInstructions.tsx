import React, { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { <PERSON>, CardContent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2, Check, FileText } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { MedicationInstructionsForm } from "@/components/admin/medication/MedicationInstructionsForm";

export default function MedicationInstructions() {
  const [selectedMedicationId, setSelectedMedicationId] = useState<string | null>(null);
  const { toast } = useToast();

  // Fetch medications
  const { data: medications, isLoading: isMedicationsLoading } = useQuery({
    queryKey: ["medications"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_medications")
        .select("id, name, category_id, pedbook_medication_categories(name)")
        .order("name");

      if (error) throw error;
      return data;
    },
  });

  // Fetch medication instructions if a medication is selected
  const { data: instructions, isLoading: isInstructionsLoading, refetch: refetchInstructions } = useQuery({
    queryKey: ["medication-instructions", selectedMedicationId],
    queryFn: async () => {
      if (!selectedMedicationId) return null;

      const { data, error } = await supabase
        .from("pedbook_medication_instructions")
        .select("*")
        .eq("medication_id", selectedMedicationId)
        .maybeSingle();

      if (error) {
        // Don't throw error if no instructions found
        if (error.code === "PGRST116") {
          return null;
        }
        throw error;
      }

      return data;
    },
    enabled: !!selectedMedicationId,
  });

  const handleSelectMedication = (medicationId: string) => {
    setSelectedMedicationId(medicationId);
  };

  const getSelectedMedicationName = () => {
    if (!selectedMedicationId || !medications) return "";
    const medication = medications.find((med) => med.id === selectedMedicationId);
    return medication?.name || "";
  };

  return (
    <div className="container py-8">
      <h1 className="text-2xl font-bold mb-6">Gerenciar Bulas de Medicamentos</h1>

      <Tabs defaultValue="list" className="space-y-4">
        <TabsList>
          <TabsTrigger value="list">Selecionar Medicamento</TabsTrigger>
          {selectedMedicationId && (
            <TabsTrigger value="edit">Editar Bula</TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Medicamentos</CardTitle>
            </CardHeader>
            <CardContent>
              {isMedicationsLoading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                  {medications?.map((medication) => (
                    <Card
                      key={medication.id}
                      className={`cursor-pointer hover:shadow-md transition-shadow ${
                        medication.id === selectedMedicationId ? "border-primary border-2" : ""
                      }`}
                      onClick={() => handleSelectMedication(medication.id)}
                    >
                      <CardContent className="p-4 flex justify-between items-center">
                        <div>
                          <p className="font-medium">{medication.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {(medication.pedbook_medication_categories as any)?.name || "Sem categoria"}
                          </p>
                        </div>
                        {medication.id === selectedMedicationId && (
                          <Check className="h-5 w-5 text-primary" />
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="edit">
          {selectedMedicationId && (
            <MedicationInstructionsForm
              medicationId={selectedMedicationId}
              medicationName={getSelectedMedicationName()}
              existingInstructions={instructions}
              onSuccess={() => {
                refetchInstructions();
                toast({
                  title: "Bula salva com sucesso",
                  description: "O conteúdo da bula foi atualizado",
                });
              }}
            />
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
