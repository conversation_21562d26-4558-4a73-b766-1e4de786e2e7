/**
 * Hook especializado para dados de medicamentos
 * Garante que os dados estejam sempre atualizados para cálculos de dose
 */

import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { CACHE_STRATEGIES, CACHE_KEYS } from '@/utils/cacheConfig';

/**
 * Hook para buscar medicamentos com cache otimizado
 * Cache curto para garantir dados sempre atualizados
 */
export const useMedications = () => {
  return useQuery({
    queryKey: CACHE_KEYS.MEDICATIONS,
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_medications')
        .select(`
          *,
          pedbook_medication_categories (
            id,
            name
          ),
          pedbook_medication_dosages (
            id,
            name,
            dosage_template,
            summary,
            age_group
          )
        `);

      if (error) throw error;
      return data;
    },
    ...CACHE_STRATEGIES.MEDICATIONS, // Cache curto (5 minutos)
  });
};

/**
 * Hook para buscar categorias de medicamentos com medicamentos
 * Cache otimizado para performance
 */
export const useMedicationCategoriesWithMedications = () => {
  return useQuery({
    queryKey: ["medication-categories", "with-medications", "v2"], // Versão 2 para invalidar cache
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_medication_categories')
        .select(`
          *,
          pedbook_medications!category_id (
            id,
            name,
            slug,
            brands
          )
        `)
        .order('name');

      if (error) {
        throw error;
      }

      return data;
    },
    staleTime: 15 * 60 * 1000, // 15 minutos
    gcTime: 60 * 60 * 1000, // 1 hora
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    retry: 1
  });
};

/**
 * Hook para buscar categorias de medicamentos (sem medicamentos)
 * Cache um pouco mais longo pois categorias mudam raramente
 */
export const useMedicationCategories = () => {
  return useQuery({
    queryKey: CACHE_KEYS.MEDICATION_CATEGORIES,
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_medication_categories')
        .select('*')
        .order('name');

      if (error) throw error;
      return data;
    },
    ...CACHE_STRATEGIES.MEDICATIONS,
  });
};

/**
 * Hook para buscar tags de medicamento específico
 * Cache otimizado para evitar múltiplas requisições
 */
export const useMedicationTags = (medicationId: string) => {
  return useQuery({
    queryKey: ["medication-tags", medicationId],
    queryFn: async () => {
      if (!medicationId) {
        return [];
      }

      const { data, error } = await supabase
        .from('pedbook_medication_tags')
        .select('name, multiplier, max_value, type, start_month, end_month, start_weight, end_weight, round_result, is_user_medication')
        .eq('medication_id', medicationId)
        .eq('is_user_medication', false);

      if (error) {
        throw error;
      }

      return data || [];
    },
    enabled: !!medicationId,
    staleTime: 10 * 60 * 1000, // 10 minutos - tags raramente mudam
    gcTime: 30 * 60 * 1000, // 30 minutos
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook para buscar detalhes de um medicamento específico
 * Cache curto para garantir dados atualizados
 */
export const useMedicationDetail = (slug: string) => {
  return useQuery({
    queryKey: CACHE_KEYS.MEDICATION_DETAIL(slug),
    queryFn: async () => {
      if (!slug) return null;

      const { data, error } = await supabase
        .from('pedbook_medications')
        .select(`
          *,
          pedbook_medication_categories (
            id,
            name,
            slug
          ),
          pedbook_medication_dosages (
            id,
            name,
            type,
            summary,
            description,
            dosage_template,
            age_group,
            use_case:pedbook_medication_use_cases(
              name
            )
          )
        `)
        .eq('slug', slug)
        .maybeSingle();

      if (error) {
        if (error.code === 'PGRST116') {
          return null;
        }
        throw error;
      }
      return data;
    },
    enabled: !!slug,
    ...CACHE_STRATEGIES.MEDICATIONS, // Cache curto (5 minutos)
  });
};

/**
 * Hook para invalidar cache de medicamentos
 * Útil quando sabemos que dados foram atualizados
 */
export const useInvalidateMedicationCache = () => {
  const { invalidateMedicationsCache } = require('./useCacheInvalidation');

  return {
    invalidateAll: invalidateMedicationsCache,
    invalidateSpecific: (medicationId: string) => {
      // Invalidar cache específico de um medicamento
      const { invalidateSpecific } = require('./useCacheInvalidation');
      invalidateSpecific(CACHE_KEYS.MEDICATION_TAGS(medicationId));
    }
  };
};

/**
 * Hook para forçar atualização de dados críticos de medicamentos
 * Usar quando precisar garantir dados 100% atualizados
 */
export const useRefreshMedicationData = () => {
  const { data: medications, refetch: refetchMedications } = useMedications();
  const { data: categories, refetch: refetchCategories } = useMedicationCategories();

  const refreshAll = async () => {
    await Promise.all([
      refetchMedications(),
      refetchCategories()
    ]);
  };

  return {
    refreshAll,
    refreshMedications: refetchMedications,
    refreshCategories: refetchCategories,
    isStale: false // Sempre considerar como fresh devido ao cache curto
  };
};
