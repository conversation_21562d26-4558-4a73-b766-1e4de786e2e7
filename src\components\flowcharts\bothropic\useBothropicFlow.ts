import { useState } from "react";
import { bothropicQuestions, bothropicResults } from "./constants";

type Step = "initial" | "observation" | "severity" | "discharge" | "mild" | "moderate" | "severe";

export const useBothropicFlow = () => {
  const [currentStep, setCurrentStep] = useState<Step>("initial");
  const [answers, setAnswers] = useState<Record<string, boolean | string>>({});

  const handleAnswer = (answer: boolean | string) => {
    const newAnswers = { ...answers, [currentStep]: answer };
    setAnswers(newAnswers);

    if (currentStep === "initial") {
      if (answer === false) {
        setCurrentStep("observation");
      } else {
        setCurrentStep("severity");
      }
    } else if (currentStep === "observation") {
      if (answer === false) {
        setCurrentStep("discharge");
      } else {
        setCurrentStep("severity");
      }
    } else if (currentStep === "severity") {
      setCurrentStep(answer as Step);
    }
  };

  const handleContinue = (nextStep: string) => {
    setCurrentStep(nextStep as Step);
  };

  const resetFlow = () => {
    setCurrentStep("initial");
    setAnswers({});
  };

  const getCurrentQuestion = () => {
    return bothropicQuestions[currentStep as keyof typeof bothropicQuestions];
  };

  const getCurrentResult = () => {
    // Apenas mostra o resultado "noSigns" quando está em observação e não teve sinais iniciais
    if (currentStep === "observation" && answers.initial === false) {
      return bothropicResults.noSigns;
    }

    // Mostra o resultado "discharge" (picada seca) apenas quando vem da observação sem clínica
    if (currentStep === "discharge" && answers.observation === false) {
      return bothropicResults.discharge;
    }

    // Para os outros casos, mostra o resultado correspondente ao passo atual
    return bothropicResults[currentStep as keyof typeof bothropicResults];
  };

  return {
    currentStep,
    answers,
    handleAnswer,
    handleContinue,
    resetFlow,
    getCurrentQuestion,
    getCurrentResult,
  };
};