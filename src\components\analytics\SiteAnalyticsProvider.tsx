import React, { useEffect } from 'react';
import { usePageAnalytics } from '@/hooks/usePageAnalytics';

/**
 * Componente provider que ativa o tracking automático de analytics
 * para todo o site. Deve ser colocado no nível mais alto da aplicação.
 */
export const SiteAnalyticsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Ativar tracking automático de page views
  usePageAnalytics();

  useEffect(() => {
    // Tracking de eventos globais do navegador
    const handleBeforeUnload = () => {
      // Tracking quando o usuário está saindo do site
      if (navigator.sendBeacon) {
        const sessionId = sessionStorage.getItem('site_session_id');
        if (sessionId) {
          const data = JSON.stringify({
            session_id: sessionId,
            action_type: 'session_end',
            page_url: window.location.pathname,
            timestamp: new Date().toISOString()
          });
          
          navigator.sendBeacon('/api/analytics/beacon', data);
        }
      }
    };

    // Tracking de erros JavaScript
    const handleError = (event: ErrorEvent) => {
      // Só trackear erros que não sejam de analytics para evitar loops
      if (!event.filename?.includes('analytics') && !event.message?.includes('analytics')) {
        setTimeout(() => {
          try {
            const sessionId = sessionStorage.getItem('site_session_id');
            if (sessionId) {
              // Fazer tracking do erro (não-bloqueante)
              fetch('/api/analytics/error', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  session_id: sessionId,
                  error_message: event.message,
                  error_filename: event.filename,
                  error_line: event.lineno,
                  error_column: event.colno,
                  page_url: window.location.pathname,
                  user_agent: navigator.userAgent
                })
              }).catch(() => {
                // Silenciar erros de tracking de erro
              });
            }
          } catch (e) {
            // Silenciar erros de tracking de erro
          }
        }, 0);
      }
    };

    // Tracking de performance (Web Vitals básico)
    const trackPerformance = () => {
      if ('performance' in window && 'getEntriesByType' in performance) {
        setTimeout(() => {
          try {
            const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
            if (navigation) {
              const sessionId = sessionStorage.getItem('site_session_id');
              if (sessionId) {
                fetch('/api/analytics/performance', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    session_id: sessionId,
                    page_url: window.location.pathname,
                    load_time: navigation.loadEventEnd - navigation.loadEventStart,
                    dom_content_loaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                    first_paint: navigation.responseEnd - navigation.requestStart,
                    dns_lookup: navigation.domainLookupEnd - navigation.domainLookupStart,
                    tcp_connection: navigation.connectEnd - navigation.connectStart
                  })
                }).catch(() => {
                  // Silenciar erros de tracking de performance
                });
              }
            }
          } catch (e) {
            // Silenciar erros de tracking de performance
          }
        }, 2000); // Aguardar 2 segundos para garantir que a página carregou
      }
    };

    // Adicionar listeners
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('error', handleError);
    
    // Trackear performance após carregamento
    if (document.readyState === 'complete') {
      trackPerformance();
    } else {
      window.addEventListener('load', trackPerformance);
    }

    // Cleanup
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('error', handleError);
      window.removeEventListener('load', trackPerformance);
    };
  }, []);

  return <>{children}</>;
};

/**
 * Hook para tracking manual de eventos específicos
 */
export const useAnalyticsEvents = () => {
  const trackCustomEvent = (eventName: string, properties?: Record<string, any>) => {
    try {
      const sessionId = sessionStorage.getItem('site_session_id');
      if (sessionId) {
        fetch('/api/analytics/custom', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            session_id: sessionId,
            event_name: eventName,
            properties: properties || {},
            page_url: window.location.pathname,
            timestamp: new Date().toISOString()
          })
        }).catch(() => {
          // Silenciar erros de analytics
        });
      }
    } catch (e) {
      // Silenciar erros de analytics
    }
  };

  return { trackCustomEvent };
};
