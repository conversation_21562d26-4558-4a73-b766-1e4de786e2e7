
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ExternalLink, Sparkles, Code, MessageSquare } from "lucide-react";

export const MedUnityBanner = () => {
 // console.log("Rendering MedUnity promotional banner as category card");

  return (
    <div className="h-full group transform transition-all duration-300 hover:-translate-y-1 animate-fade-in">
      <div className="relative h-full p-4 sm:p-5 rounded-xl overflow-hidden shadow-md hover:shadow-lg bg-gradient-to-r from-blue-600 to-indigo-600">
        {/* Padrão de fundo moderno para aparência de app */}
        <div className="absolute inset-0 opacity-10">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" strokeWidth="0.5" />
              </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#grid)" />
          </svg>
        </div>

        {/* Círculos decorativos */}
        <div className="absolute -right-4 -top-4 w-20 h-20 bg-white/10 rounded-full blur-lg"></div>
        <div className="absolute -left-4 -bottom-4 w-20 h-20 bg-white/10 rounded-full blur-lg"></div>

        <div className="relative z-10 flex flex-col h-full justify-between gap-2">
          <div className="space-y-1">
            {/* Título com ícone */}
            <h3 className="text-base font-bold flex items-center gap-1.5 text-white">
              <Sparkles className="h-4 w-4 text-yellow-300" />
              MedUnity
            </h3>

            {/* Descrição compacta para mobile e desktop */}
            <p className="text-white/90 text-xs line-clamp-2">
              Soluções digitais para a área da saúde
            </p>

            {/* Tags de serviços - visíveis em todos os dispositivos */}
            <div className="flex flex-wrap gap-1.5 pt-1">
              <div className="flex items-center gap-1 bg-white/20 backdrop-blur-sm px-1.5 py-0.5 rounded-md">
                <Code className="h-2.5 w-2.5" />
                <span className="text-[10px] text-white">Websites</span>
              </div>
              <div className="flex items-center gap-1 bg-white/20 backdrop-blur-sm px-1.5 py-0.5 rounded-md">
                <MessageSquare className="h-2.5 w-2.5" />
                <span className="text-[10px] text-white">Automações</span>
              </div>
            </div>
          </div>

          {/* Botão com estilo mais moderno */}
          <Button
            onClick={() => window.open("https://medunity.com.br/", "_blank")}
            className="mt-2 w-full bg-white/90 hover:bg-white text-blue-600 shadow-md hover:shadow-lg transition-all duration-300 group rounded-md"
            size="sm"
          >
            <span className="text-xs font-medium">Fale Conosco</span>
            <ExternalLink className="ml-1 h-3 w-3 group-hover:translate-x-1 transition-transform" />
          </Button>
        </div>
      </div>
    </div>
  );
};
