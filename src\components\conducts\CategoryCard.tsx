
import React from "react";
import { motion } from "framer-motion";
import { ChevronRight, Clock, Zap } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { cn } from "@/lib/utils";

interface CategoryCardProps {
  category: {
    id: string;
    name: string;
    description?: string | null;
    icon?: string | null;
    color?: string | null;
    slug: string;
    image_url?: string | null;
    coming_soon?: boolean;
    summary_count?: number;
    isFuzzyMatch?: boolean;
    topics?: Array<{
      id: string;
      name: string;
      slug: string;
    }>;
  };
  index: number;
  searchTerm?: string;
}

export const CategoryCard = ({ category, index, searchTerm }: CategoryCardProps) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (!category.coming_soon) {
      navigate(`/condutas-e-manejos/${category.slug}`);
    }
  };

  // Função para destacar o texto pesquisado
  const highlightText = (text: string) => {
    if (!searchTerm || searchTerm.trim() === "") return text;
    
    const parts = text.split(new RegExp(`(${searchTerm})`, 'gi'));
    return (
      <>
        {parts.map((part, i) => 
          part.toLowerCase() === searchTerm.toLowerCase() ? 
            <span key={i} className="bg-yellow-200 dark:bg-yellow-900 font-semibold">{part}</span> : 
            part
        )}
      </>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="h-full"
    >
      <div
        onClick={handleClick}
        className={cn(
          "relative h-full flex flex-col rounded-xl overflow-hidden",
          "bg-white dark:bg-slate-800/90 backdrop-blur-sm shadow-md",
          "border border-blue-100 dark:border-slate-700",
          "transition-all duration-300",
          !category.coming_soon
            ? "cursor-pointer hover:shadow-lg hover:border-blue-200 dark:hover:border-blue-800/50"
            : "opacity-80 cursor-not-allowed"
        )}
      >
        {/* Colorful top accent */}
        <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary to-blue-400" />

        <div className="p-5 flex items-start gap-4">
          {/* Category icon/image */}
          <div className="flex-shrink-0">
            <div className="w-16 h-16 rounded-lg overflow-hidden bg-blue-100 dark:bg-blue-900/40 flex items-center justify-center">
              {category.image_url ? (
                <img
                  src={category.image_url}
                  alt={category.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-blue-200 dark:bg-blue-800 flex items-center justify-center text-blue-600 dark:text-blue-300 text-2xl font-bold">
                  {category.name.charAt(0)}
                </div>
              )}
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 flex-wrap">
              <h3 className="text-xl font-bold text-gray-800 dark:text-white">
                {highlightText(category.name)}
              </h3>
              {category.isFuzzyMatch && (
                <span className="bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-800/50 flex items-center gap-1 text-xs px-2 py-0.5 whitespace-nowrap rounded-full" title="Você quis dizer este resultado?">
                  <Zap className="h-3 w-3" />
                  <span>Você quis dizer?</span>
                </span>
              )}
              {category.coming_soon && (
                <span className="bg-amber-50 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300 border border-amber-200 dark:border-amber-800/50 flex items-center gap-1 text-xs px-2 py-0.5 whitespace-nowrap rounded-full">
                  <Clock className="h-3 w-3" />
                  <span>Em breve</span>
                </span>
              )}
            </div>
            
            {category.description && (
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                {highlightText(category.description)}
              </p>
            )}
            
            {category.summary_count !== undefined && category.summary_count > 0 && (
              <div className="mt-2 text-xs text-blue-600 dark:text-blue-400 font-medium">
                {category.summary_count} {category.summary_count === 1 ? 'tópico' : 'tópicos'}
              </div>
            )}

            {searchTerm && category.topics && category.topics.length > 0 && (
              <div className="mt-2 space-y-1">
                {category.topics
                  .filter(topic => 
                    topic.name.toLowerCase().includes(searchTerm.toLowerCase()))
                  .slice(0, 3)
                  .map(topic => (
                    <div 
                      key={topic.id} 
                      className="text-xs bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 py-1 px-2 rounded inline-block mr-1 mb-1"
                    >
                      {highlightText(topic.name)}
                    </div>
                  ))
                }
                {category.topics.filter(topic => 
                  topic.name.toLowerCase().includes(searchTerm.toLowerCase())).length > 3 && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    +{category.topics.filter(topic => 
                      topic.name.toLowerCase().includes(searchTerm.toLowerCase())).length - 3} mais
                  </span>
                )}
              </div>
            )}
          </div>

          {!category.coming_soon && (
            <div className="flex-shrink-0 self-center">
              <div className="p-2 rounded-full bg-blue-50 dark:bg-blue-900/20">
                <ChevronRight className="h-5 w-5 text-primary dark:text-blue-400" />
              </div>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};
