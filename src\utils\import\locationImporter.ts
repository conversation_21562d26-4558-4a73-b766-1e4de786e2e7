
import { supabase } from "@/integrations/supabase/client";
import type { LocationResult } from "@/types/import";

export async function getOrCreateLocation(name: string): Promise<LocationResult> {
  // console.log('🏫 Verificando local de prova:', name);
  
  try {
    const { data: existing, error: searchError } = await supabase
      .from('exam_locations')
      .select('*')
      .ilike('name', name)
      .maybeSingle();

    if (searchError) throw searchError;

    if (existing) {
      return {
        id: existing.id,
        name: existing.name
      };
    }

    const { data: created, error: createError } = await supabase
      .from('exam_locations')
      .insert({ name })
      .select()
      .single();

    if (createError) throw createError;
    
    return {
      id: created.id,
      name: created.name
    };
  } catch (error) {
    // console.error('❌ Erro ao processar local:', error);
    throw error;
  }
}
