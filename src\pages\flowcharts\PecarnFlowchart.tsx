
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { FlowchartSEO } from "@/components/seo/FlowchartSEO";
import { FLOWCHART_SEO_DATA } from "@/data/flowchartSEOData";
import { PecarnContent } from "@/components/flowcharts/pecarn/PecarnContent";
import { getThemeClasses } from "@/components/ui/theme-utils";

const PecarnFlowchart = () => {
  const seoData = FLOWCHART_SEO_DATA['pecarn'];

  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen flex flex-col from-blue-100 via-white to-blue-50 dark:from-blue-950 dark:via-slate-900 dark:to-blue-950")}>
      <FlowchartSEO {...seoData} />

      <Header />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-8">
          <div className="flex flex-col items-center gap-4">
            <div className="w-full flex items-center">
              <Link to="/flowcharts">
                <Button variant="ghost" size="icon" className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                  <ArrowLeft className="h-5 w-5" />
                </Button>
              </Link>
            </div>
            <h1 className={getThemeClasses.gradientHeading("text-3xl font-bold from-blue-600 to-blue-800 dark:from-blue-400 dark:to-blue-600")}>
              PECARN - Trauma Craniano
            </h1>
          </div>

          <Card className={getThemeClasses.card("p-6")}>
            <PecarnContent />
          </Card>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default PecarnFlowchart;
