
import { useState } from "react";
import { WeightInput } from "./WeightInput";
import { InitialMeasures } from "./InitialMeasures";
import { BenzodiazepinePhase } from "./BenzodiazepinePhase";
import { SecondLinePhase } from "./SecondLinePhase";
import { StatusEpilepticusPhase } from "./StatusEpilepticusPhase";
import { ICUPhase } from "./ICUPhase";
import { Button } from "@/components/ui/button";
import { ArrowRight, AlertCircle, Play } from "lucide-react";
import { Card } from "@/components/ui/card";
import { getThemeClasses } from "@/components/ui/theme-utils";

export const SeizureFlowContent = () => {
  const [weight, setWeight] = useState(20);
  const [currentPhase, setCurrentPhase] = useState<
    "intro" | "input" | "initial" | "benzo" | "second" | "status" | "icu"
  >("intro");

  const handleWeightSubmit = (weight: number) => {
    setCurrentPhase("initial");
  };

  const handleNextPhase = () => {
    switch (currentPhase) {
      case "intro":
        setCurrentPhase("input");
        break;
      case "initial":
        setCurrentPhase("benzo");
        break;
      case "benzo":
        setCurrentPhase("second");
        break;
      case "second":
        setCurrentPhase("status");
        break;
      case "status":
        setCurrentPhase("icu");
        break;
      default:
        break;
    }
  };

  const renderPhase = () => {
    switch (currentPhase) {
      case "intro":
        return (
          <div className="space-y-6 animate-slide-in-up">
            <Card className={getThemeClasses.gradientCard("orange", "p-6")}>
              <div className="flex items-start gap-4">
                <div className="mt-1">
                  <AlertCircle className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                </div>
                <div className="space-y-4">
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    Este fluxograma foi projetado para orientar o manejo de crises convulsivas prolongadas, 
                    especialmente em situações críticas na emergência pediátrica, onde a crise dura mais de 
                    5 minutos ou há crises recorrentes sem recuperação da consciência.
                  </p>
                  
                  <div className="space-y-2">
                    <h3 className="font-semibold text-lg text-orange-800 dark:text-orange-300">
                      Quando usar este fluxograma:
                    </h3>
                    <ul className="list-disc list-inside space-y-2 text-gray-700 dark:text-gray-300">
                      <li>Crises com duração superior a 5 minutos.</li>
                      <li>
                        Pacientes que apresentam múltiplas crises sem recuperação completa 
                        entre elas (possível estado de mal epiléptico).
                      </li>
                      <li>
                        Casos de crise focal ou generalizada (com manifestações motoras ou não).
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </Card>

            <Button
              onClick={handleNextPhase}
              className="w-full flex items-center justify-center gap-2 bg-orange-500 hover:bg-orange-600 dark:bg-orange-600 dark:hover:bg-orange-700"
            >
              Iniciar Fluxograma
              <Play className="h-4 w-4" />
            </Button>
          </div>
        );
      case "input":
        return (
          <div className="space-y-6 animate-slide-in-up">
            <div className="text-center">
              <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100">
                Manejo de Crise Convulsiva
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mt-2">
                Insira o peso do paciente para iniciar o fluxograma
              </p>
            </div>
            <WeightInput
              value={weight}
              onChange={setWeight}
              onCommit={() => {}}
            />
            <Button
              onClick={() => handleWeightSubmit(weight)}
              className="w-full flex items-center justify-center gap-2 bg-primary hover:bg-primary/90 dark:bg-blue-600 dark:hover:bg-blue-700"
            >
              Prosseguir com o Fluxograma
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        );
      case "initial":
        return (
          <div className="space-y-6">
            <InitialMeasures weight={weight} />
            <Button
              onClick={handleNextPhase}
              className="w-full flex items-center justify-center gap-2 bg-primary hover:bg-primary/90 dark:bg-blue-600 dark:hover:bg-blue-700"
            >
              Crise persiste após 5 minutos?
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        );
      case "benzo":
        return (
          <div className="space-y-6">
            <BenzodiazepinePhase weight={weight} />
            <Button
              onClick={handleNextPhase}
              className="w-full flex items-center justify-center gap-2 bg-primary hover:bg-primary/90 dark:bg-blue-600 dark:hover:bg-blue-700"
            >
              Crise persiste após 10 minutos?
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        );
      case "second":
        return (
          <div className="space-y-6">
            <SecondLinePhase weight={weight} />
            <Button
              onClick={handleNextPhase}
              className="w-full flex items-center justify-center gap-2 bg-primary hover:bg-primary/90 dark:bg-blue-600 dark:hover:bg-blue-700"
            >
              Crise persiste após 60 minutos?
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        );
      case "status":
        return (
          <div className="space-y-6">
            <StatusEpilepticusPhase weight={weight} />
            <Button
              onClick={handleNextPhase}
              className="w-full flex items-center justify-center gap-2 bg-primary hover:bg-primary/90 dark:bg-blue-600 dark:hover:bg-blue-700"
            >
              Necessário cuidados em UTI?
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        );
      case "icu":
        return (
          <div className="space-y-6">
            <ICUPhase weight={weight} />
            <p className="text-sm text-gray-500 dark:text-gray-400 text-center mt-8 italic">
              Fonte: Adaptado de "Manejo das Crises Convulsivas na Emergência Pediátrica", Juliana Beirão de Almeida Guaragna et al.
            </p>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className={getThemeClasses.gradientBackground("min-h-screen")}>
      <div className="max-w-3xl mx-auto p-6">
        <div className={getThemeClasses.card("bg-white dark:bg-slate-800 p-6")}>
          {renderPhase()}
          {currentPhase !== "intro" && currentPhase !== "icu" && (
            <p className="text-sm text-gray-500 dark:text-gray-400 text-center mt-8 italic">
              Fonte: Adaptado de "Manejo das Crises Convulsivas na Emergência Pediátrica", Juliana Beirão de Almeida Guaragna et al.
            </p>
          )}
        </div>
      </div>
    </div>
  );
};
