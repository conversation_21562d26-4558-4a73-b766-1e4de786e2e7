
import type { Config } from "tailwindcss";

export default {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
    "./index.html",
  ],
  prefix: "",
  // Otimizações de performance
  future: {
    hoverOnlyWhenSupported: true,
  },
  experimental: {
    optimizeUniversalDefaults: true,
  },
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        primary: "#0066FF", // Restored blue as primary color
        "primary-light": "#E6F0FF",
        accent: {
          pink: "#FFE5EC",
          yellow: "#FFF9E5",
          green: "#E5F4E7",
          blue: "#E5F0FF",
          purple: "#F0E5FF"
        },
        // Enhanced accent colors with higher saturation for better visibility
        "accent-enhanced": {
          pink: "#FFD0DC",
          yellow: "#FFE89B",
          green: "#C2E7BF",
          blue: "#C7DFFF",
          purple: "#DBC5FF",
          amber: "#FFCF8C",
          orange: "#FFB799",
          red: "#FFC0C0",
          emerald: "#A7F0C1",
          indigo: "#C5C1FF",
          cyan: "#A8E9F5",
          rose: "#FFB9C5"
        },
        destructive: {
          DEFAULT: "#FF4444",
          foreground: "#FFFFFF",
        },
        // Hackathon colors from MedEvo
        hackathon: {
          yellow: "#FFD700",
          red: "#FF4444",
          green: "#00CC66",
          black: "#000000",
          lightBg: "#F8F9FA",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        popover: {
          DEFAULT: "white",
          foreground: "hsl(var(--popover-foreground))",
        },
      },
      fontFamily: {
        sans: ["Inter", "system-ui", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "sans-serif"],
        inter: ["Inter", "sans-serif"],
      },
      typography: {
        DEFAULT: {
          css: {
            maxWidth: 'none',
            color: 'inherit',
            a: {
              color: 'inherit',
              textDecoration: 'none',
            },
            h3: {
              color: 'inherit',
            },
            strong: {
              color: 'inherit',
            },
            code: {
              color: 'inherit',
            },
          },
        },
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        "slide-in-up": {
          "0%": {
            transform: "translateY(100px)",
            opacity: "0"
          },
          "100%": {
            transform: "translateY(0)",
            opacity: "1"
          }
        },
        "fade-in": {
          "0%": {
            opacity: "0",
            transform: "translateY(10px)"
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)"
          }
        }
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "slide-in-up": "slide-in-up 0.6s ease-out forwards",
        "fade-in": "fade-in 0.5s ease-out forwards"
      },
      boxShadow: {
        'button': '4px 4px 0px 0px rgba(0, 0, 0, 1)',
        'card': '6px 6px 0px 0px rgba(0, 0, 0, 1)',
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    require("tailwind-scrollbar"),
    require('@tailwindcss/typography'),
  ],
} satisfies Config;
