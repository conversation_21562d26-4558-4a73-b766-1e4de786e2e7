
import { Search, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Command } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import React, { useRef, useState, useEffect, lazy, Suspense, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useSearchResults } from "./hooks/useSearchResults";
import { SearchInput } from "./SearchInput";
import type { SearchResult } from "./types";
import { formatBrands } from "./utils/formatBrands";
import { useDebounce } from "@/hooks/useDebounce";

// Lazy load SearchResults para melhor LCP
const SearchResults = lazy(() => import("./SearchResults").then(module => ({ default: module.SearchResults })));

interface SearchBarProps {
  customPlaceholder?: string;
}

export const SearchBar = ({ customPlaceholder }: SearchBarProps) => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [hasFocus, setHasFocus] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Debounce do termo de busca para evitar muitas queries
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const { searchResults, isLoading, isFetching } = useSearchResults(debouncedSearchTerm);





  // Estados limpos - logs removidos

  // Fechar dropdown com Escape
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setIsSearchOpen(false);
        setHasFocus(false);
        inputRef.current?.blur();
      }
    };

    if (isSearchOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isSearchOpen]);

  const handleSelectResult = useCallback((result: SearchResult) => {
    setIsSearchOpen(false);
    setSearchTerm('');

    if (result.type === 'category') {
      navigate('/medicamentos/painel', { state: { selectedCategory: result.id } });
    } else if (result.type === 'medication') {
      navigate(`/medicamentos/${result.slug}`);
    } else if (result.type === 'icd10') {
      navigate(`/icd?category=${result.id}`);
    } else if (result.type === 'calculator') {
      navigate(result.path || '/calculators');
    } else if (result.type === 'flowchart') {
      navigate(result.path || '/flowcharts');
    } else if (result.type === 'childcare') {
      navigate(result.path || '/childcare');
    } else if (result.type === 'toxidrome') {
      navigate(result.path || '/poisonings');
    } else if (result.type === 'conduct') {
      navigate(result.path || '/condutas-e-manejos');
    } else if (result.type === 'vaccine') {
      navigate('/puericultura/calendario-vacinal');
    } else if (result.type === 'leaflet') {
      // Para bulas profissionais, usar o path que já vem formatado
      navigate(result.path || '/bulas-profissionais');
    }
  }, [navigate]);

  return (
    <div className="flex-1 sm:max-w-xl w-full mx-auto px-1 sm:px-2">
      <Popover
        open={isSearchOpen}
        onOpenChange={(open) => {
          setIsSearchOpen(open);
          if (!open) {
            setHasFocus(false);
          }
        }}
      >
        <PopoverTrigger asChild>
          <div className="relative group cursor-text">
            <SearchInput
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              customPlaceholder={customPlaceholder}
              onFocus={() => {
                setHasFocus(true);
                // Usar setTimeout para evitar conflito com onClick
                setTimeout(() => {
                  setIsSearchOpen(true);
                }, 0);
              }}
              inputRef={inputRef}
            />
          </div>
        </PopoverTrigger>
        <PopoverContent
          className="w-[calc(100vw-2rem)] sm:w-[500px] p-0 bg-white/90 dark:bg-slate-800/90 backdrop-blur-md border-none shadow-xl max-h-[80vh] rounded-xl overflow-hidden"
          align="center"
          side="bottom"
          sideOffset={8}
          onOpenAutoFocus={(e) => {
            e.preventDefault();
            // Não forçar foco aqui para evitar conflitos
          }}
        >
          <Command className="bg-transparent">
            <Suspense fallback={
              <div className="p-4 text-center">
                <div className="flex items-center justify-center gap-2 text-muted-foreground">
                  <div className="w-2 h-2 bg-primary/60 rounded-full animate-pulse" />
                  <p className="text-sm">Carregando busca...</p>
                </div>
              </div>
            }>
              <SearchResults
                results={searchResults}
                onSelect={handleSelectResult}
                formatBrands={formatBrands}
                isLoading={isLoading}
                isFetching={isFetching}
                showEmptyMessage={isSearchOpen && !searchTerm}
                searchTerm={debouncedSearchTerm}
                currentInput={searchTerm}
                onClose={() => setIsSearchOpen(false)}
              />
            </Suspense>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
};
