import { Card } from "@/components/ui/card";
import { Stethoscope, Brain, Hospital } from "lucide-react";

interface ICUPhaseProps {
  weight: number;
}

export const ICUPhase = ({ weight }: ICUPhaseProps) => {
  const midazolamBolus = (weight * 0.2).toFixed(1);
  const midazolamInfusion = (weight * 0.1).toFixed(1);
  const tiopentalMin = Math.round(weight * 1);
  const tiopentalMax = Math.round(weight * 3);
  const tiopentalInfusionMin = Math.round(weight * 3);
  const tiopentalInfusionMax = Math.round(weight * 5);
  const propofolBolus = Math.round(weight * 1);
  const propofolInfusionMin = Math.round(weight * 2);
  const propofolInfusionMax = Math.round(weight * 10);
  const lidocainaBolusMin = Math.round(weight * 2);
  const lidocainaBolusMax = Math.round(weight * 3);
  const lidocainaInfusionMin = Math.round(weight * 4);
  const lidocainaInfusionMax = Math.round(weight * 10);

  return (
    <div className="space-y-6">
      <Card className="p-6 bg-white/80 backdrop-blur-sm border border-indigo-200">
        <div className="flex items-start gap-4">
          <div className="mt-1">
            <Hospital className="h-6 w-6 text-indigo-600" />
          </div>
          <div className="space-y-3">
            <h3 className="font-semibold text-lg text-indigo-800">
              Drogas de Monitoramento Contínuo
            </h3>
            <ul className="list-disc list-inside space-y-3 text-gray-700">
              <li>
                Midazolam:
                <ul className="list-disc list-inside ml-4 mt-1">
                  <li>Dose de ataque: {midazolamBolus} mg IV</li>
                  <li>Infusão contínua: {midazolamInfusion} mg/kg/h</li>
                </ul>
              </li>
              <li>
                Tiopental:
                <ul className="list-disc list-inside ml-4 mt-1">
                  <li>
                    Dose de ataque: {tiopentalMin}-{tiopentalMax} mg IV
                  </li>
                  <li>
                    Infusão contínua: {tiopentalInfusionMin}-
                    {tiopentalInfusionMax} mg/kg/h
                  </li>
                </ul>
              </li>
              <li>
                Propofol:
                <ul className="list-disc list-inside ml-4 mt-1">
                  <li>Dose inicial: {propofolBolus} mg IV</li>
                  <li>
                    Manutenção: {propofolInfusionMin}-{propofolInfusionMax}{" "}
                    mg/kg/h
                  </li>
                </ul>
              </li>
              <li>
                Lidocaína:
                <ul className="list-disc list-inside ml-4 mt-1">
                  <li>
                    Dose inicial: {lidocainaBolusMin}-{lidocainaBolusMax} mg IV
                  </li>
                  <li>
                    Manutenção: {lidocainaInfusionMin}-{lidocainaInfusionMax}{" "}
                    mg/kg/h
                  </li>
                </ul>
              </li>
            </ul>
          </div>
        </div>
      </Card>

      <Card className="p-6 bg-white/80 backdrop-blur-sm border border-blue-200">
        <div className="flex items-start gap-4">
          <div className="mt-1">
            <Stethoscope className="h-6 w-6 text-blue-600" />
          </div>
          <div className="space-y-3">
            <h3 className="font-semibold text-lg text-blue-800">
              Suporte Avançado
            </h3>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>Monitorização contínua de sinais vitais</li>
              <li>Controle ventilatório e hemodinâmico</li>
            </ul>
          </div>
        </div>
      </Card>

      <Card className="p-6 bg-white/80 backdrop-blur-sm border border-green-200">
        <div className="flex items-start gap-4">
          <div className="mt-1">
            <Brain className="h-6 w-6 text-green-600" />
          </div>
          <div className="space-y-3">
            <h3 className="font-semibold text-lg text-green-800">
              Investigação Complementar
            </h3>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>EEG (eletroencefalograma)</li>
              <li>
                Ressonância magnética (RM) ou tomografia computadorizada (TC) de
                crânio
              </li>
              <li>
                Punção lombar, caso não haja contraindicações (como hipertensão
                intracraniana)
              </li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};