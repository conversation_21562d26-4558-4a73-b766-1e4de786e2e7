export type GrowthCurvePercentiles = {
  "1st": number;
  "3rd": number;
  "5th": number;
  "15th": number;
  "25th": number;
  "50th": number;
  "75th": number;
  "85th": number;
  "95th": number;
  "97th": number;
  "99th": number;
};

export type GrowthCurveDataPoint = {
  age_months: number;
  L: number;
  M: number;
  S: number;
  SD?: number; // Added SD field as optional
  percentiles: GrowthCurvePercentiles;
};

export type GrowthCurveType = "weight" | "height" | "bmi" | "head-circumference";
export type GrowthCurveGender = "male" | "female";

export interface GrowthCurveMetadata {
  id: string;
  type: GrowthCurveType;
  gender: GrowthCurveGender;
  data: GrowthCurveDataPoint[];
}