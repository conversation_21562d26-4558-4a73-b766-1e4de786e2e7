
import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

// Declarar tipos globais para interfaces Android
declare global {
  interface Window {
    Android?: {
      onBackPressed?: () => boolean;
      setBackButtonHandler?: (handler: () => boolean) => void;
    };
    AndroidInterface?: {
      onBackPressed?: () => boolean;
    };
  }
}

/**
 * Hook para interceptar o botão de voltar físico do dispositivo móvel
 * e navegar dentro do aplicativo em vez de sair dele
 * Versão otimizada para WebView Android
 */
export const useBackButton = () => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Detectar se está rodando em WebView Android
    const isAndroidWebView = () => {
      const userAgent = navigator.userAgent.toLowerCase();
      return (
        /android/i.test(userAgent) &&
        (/wv/.test(userAgent) || /webview/.test(userAgent) ||
         'Android' in window || 'AndroidInterface' in window)
      );
    };

    // Definir as rotas principais do aplicativo
    const mainRoutes = [
      "/", // Home
      "/medicamentos",
      "/medicamentos/painel",
      "/puericultura",
      "/dr-will",
      "/calculadoras",
      "/flowcharts",
      "/condutas",
      "/bulas",
      "/interacoes",
      "/cid",
      "/plataformadeestudos",
      "/feedback",
      "/perfil",
      "/busca",
      "/configuracoes",
      "/newsletters", // Notícias Diárias
      "/pedidrop", // PediDrop
    ];

    // Função para navegar internamente
    const navigateInternally = (): boolean => {
      // Se estiver na página inicial, permitir sair do app
      if (location.pathname === '/') {
        return false;
      }

      // Se estamos em uma rota principal, navegar para a home
      if (mainRoutes.includes(location.pathname) && location.pathname !== "/") {
        navigate("/");
        return true;
      }

      // Se estamos em uma subrota, encontrar a rota principal correspondente
      for (const route of mainRoutes) {
        if (route !== "/" && location.pathname.startsWith(route + "/")) {
          navigate(route);
          return true;
        }
      }

      // Se não encontramos uma rota principal correspondente, navegar para trás
      navigate(-1);
      return true;
    };

    // Função para lidar com o evento de voltar do navegador/dispositivo
    const handleBackButton = (event: PopStateEvent) => {
      // Previne o comportamento padrão
      event.preventDefault();

      // Navegar internamente
      navigateInternally();
    };

    // SOLUÇÃO ESPECÍFICA PARA WEBVIEW ANDROID
    if (isAndroidWebView()) {
      console.log('🤖 WebView Android detectado - Configurando interceptação do botão voltar');

      // Método 1: Interface Android direta
      if (window.Android && window.Android.setBackButtonHandler) {
        window.Android.setBackButtonHandler(() => {
          return navigateInternally();
        });
      }

      // Método 2: Interface AndroidInterface
      if (window.AndroidInterface) {
        window.AndroidInterface.onBackPressed = () => {
          return navigateInternally();
        };
      }

      // Método 3: Expor função global para o Android chamar
      (window as any).handleWebViewBackButton = () => {
        return navigateInternally();
      };

      // Método 4: Interceptar tecla física (funciona em alguns WebViews)
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Escape' || event.keyCode === 27) {
          event.preventDefault();
          navigateInternally();
        }
      };

      document.addEventListener('keydown', handleKeyDown);

      // Método 5: Manipular histórico de forma mais agressiva para WebView
      const preventExit = () => {
        window.history.pushState(null, '', location.pathname);
      };

      // Adicionar entrada extra no histórico
      preventExit();

      // Listener para popstate (funciona em alguns WebViews)
      window.addEventListener('popstate', handleBackButton);

      // Cleanup para WebView
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
        window.removeEventListener('popstate', handleBackButton);

        // Limpar interfaces Android
        if (window.Android && window.Android.setBackButtonHandler) {
          window.Android.setBackButtonHandler(() => false);
        }
        if (window.AndroidInterface) {
          delete window.AndroidInterface.onBackPressed;
        }
        delete (window as any).handleWebViewBackButton;
      };
    } else {
      // SOLUÇÃO PADRÃO PARA NAVEGADORES WEB

      // Garante que temos uma entrada no histórico para o estado atual
      window.history.pushState(null, '', location.pathname);

      // Adiciona um listener para o evento popstate (botão voltar)
      window.addEventListener('popstate', handleBackButton);

      // Limpa o listener quando o componente é desmontado
      return () => {
        window.removeEventListener('popstate', handleBackButton);
      };
    }
  }, [location.pathname, navigate]);
};
