import HelmetWrapper from "@/components/utils/HelmetWrapper";

export const CapurroMetaTags = () => {
  const pageTitle = "PedBook | Capurro Somático - Calculadora de Idade Gestacional";
  const description = "Calculadora do método Capurro Somático para avaliação da idade gestacional do recém-nascido. Ferramenta essencial para pediatras e neonatologistas baseada em características físicas observáveis.";
  const pageUrl = "https://pedb.com.br/calculadoras/capurro";
  const keywords = [
    "capurro somático",
    "calculadora capurro",
    "idade gestacional",
    "avaliação neonatal",
    "pediatria calculadora",
    "neonatologia",
    "características físicas",
    "textura da pele",
    "forma da orelha",
    "glândula mamária",
    "pregas plantares",
    "formação do mamilo"
  ].join(", ");

  return (
    <HelmetWrapper>
      <title>{pageTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="robots" content="index, follow, max-image-preview:large" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta charSet="UTF-8" />
      <link rel="canonical" href={pageUrl} />

      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content="website" />
      <meta property="og:url" content={pageUrl} />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />
      <meta property="og:image" content="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/calculadora.webp" />
      <meta property="og:image:alt" content="Ícone da calculadora do Método Capurro" />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />

      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo/calculadora.webp" />
      <meta name="twitter:site" content="@PedBook" />

      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "MedicalWebPage",
          "name": pageTitle,
          "description": description,
          "url": pageUrl,
          "keywords": keywords.split(", "),
          "inLanguage": "pt-BR",
          "mainEntity": {
            "@type": "MedicalCalculator",
            "name": "Calculadora do Método Capurro Somático",
            "description": description,
            "medicalSpecialty": {
              "@type": "MedicalSpecialty",
              "name": "Pediatria"
            },
            "relevantSpecialty": [
              {
                "@type": "MedicalSpecialty",
                "name": "Neonatologia"
              },
              {
                "@type": "MedicalSpecialty",
                "name": "Pediatria"
              }
            ]
          },
          "about": {
            "@type": "MedicalCondition",
            "name": "Avaliação da Idade Gestacional",
            "description": "Determinação da idade gestacional através de características físicas do recém-nascido"
          },
          "audience": {
            "@type": "MedicalAudience",
            "audienceType": "Profissionais de Saúde"
          },
          "publisher": {
            "@type": "Organization",
            "name": "PedBook",
            "url": "https://pedb.com.br",
            "logo": {
              "@type": "ImageObject",
              "url": "https://pedb.com.br/logo.png"
            }
          }
        })}
      </script>
    </HelmetWrapper>
  );
};