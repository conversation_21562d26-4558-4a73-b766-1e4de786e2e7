import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { ArrowDown } from "lucide-react";

interface CrotalicResultProps {
  group: string;
  color: string;
  instructions: string[];
  onReset?: () => void;
  nextQuestion?: string;
  nextStep?: string;
  onContinue?: (step: string) => void;
}

export const CrotalicResult = ({
  group,
  color,
  instructions,
  nextQuestion,
  nextStep,
  onContinue,
}: CrotalicResultProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-8"
    >
      <div className={`p-6 rounded-xl ${color} border border-${color.split('-')[1]}-200 glass-card relative overflow-hidden`}>
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent pointer-events-none" />
        
        <h3 className={`text-xl font-bold text-${color.split('-')[1]}-800 mb-4`}>
          {group}
        </h3>
        
        <div className={`space-y-3 text-${color.split('-')[1]}-700`}>
          {instructions.map((instruction, index) => (
            <p key={index} className="relative z-10">
              {instruction}
            </p>
          ))}
        </div>

        {(nextQuestion || nextStep) && (
          <motion.div 
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="mt-6 flex flex-col items-center"
          >
            <ArrowDown className="w-8 h-8 text-primary animate-bounce" />
            <p className="text-sm text-primary mt-2">Continue o manejo abaixo</p>
          </motion.div>
        )}
      </div>

      {nextQuestion && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex justify-center"
        >
          <div 
            onClick={() => onContinue?.(nextQuestion)}
            className="p-4 rounded-lg bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 
                     hover:from-primary/20 hover:to-primary/10 transition-all cursor-pointer backdrop-blur-sm"
          >
            <p className="text-primary font-medium text-center">Próxima Avaliação</p>
          </div>
        </motion.div>
      )}

      {nextStep && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex justify-center"
        >
          <div 
            onClick={() => onContinue?.(nextStep)}
            className="p-4 rounded-lg bg-gradient-to-r from-purple-500/10 to-purple-500/5 border border-purple-500/20 
                     hover:from-purple-500/20 hover:to-purple-500/10 transition-all cursor-pointer backdrop-blur-sm"
          >
            <p className="text-purple-600 font-medium text-center">Próxima Etapa</p>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
};