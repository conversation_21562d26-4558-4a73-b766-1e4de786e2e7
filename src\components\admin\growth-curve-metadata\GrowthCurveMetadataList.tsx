import { useQuery } from "@tanstack/react-query";
import { Chart<PERSON>ine, User2, ChevronDown, ChevronUp } from "lucide-react";
import { GrowthCurveMetadata, GrowthCurveType, GrowthCurveGender } from "./types";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";

const CURVE_TYPES: { type: GrowthCurveType; label: string }[] = [
  { type: "weight", label: "Peso" },
  { type: "height", label: "Altura" },
  { type: "bmi", label: "IMC" },
  { type: "head-circumference", label: "Perímetro Cefálico" },
];

const GENDERS: { type: GrowthCurveGender; label: string }[] = [
  { type: "male", label: "Masculino" },
  { type: "female", label: "Feminino" },
];

export const GrowthCurveMetadataList = () => {
  const [openItems, setOpenItems] = useState<string[]>([]);

  const toggleItem = (id: string) => {
    setOpenItems(current =>
      current.includes(id)
        ? current.filter(item => item !== id)
        : [...current, id]
    );
  };

  const { data: curves, isLoading } = useQuery<GrowthCurveMetadata[]>({
    queryKey: ["growth-curve-metadata"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_growth_curve_metadata')
        .select('*');

      if (error) throw error;

      if (!data || data.length === 0) {
        // Se não houver dados, retorna a estrutura padrão
        return CURVE_TYPES.flatMap((curve) =>
          GENDERS.map((gender) => ({
            id: `${curve.type}-${gender.type}`,
            type: curve.type as GrowthCurveType,
            gender: gender.type as GrowthCurveGender,
            data: [],
          }))
        );
      }

      // Converte os dados do Supabase para o formato esperado
      return data.map((item): GrowthCurveMetadata => ({
        id: item.id,
        type: item.type as GrowthCurveType,
        gender: item.gender as GrowthCurveGender,
        data: (item.data as any[]).map(point => ({
          age_months: point.age_months,
          L: point.L,
          M: point.M,
          S: point.S,
          percentiles: {
            "1st": point.percentiles["1st"],
            "3rd": point.percentiles["3rd"],
            "5th": point.percentiles["5th"],
            "15th": point.percentiles["15th"],
            "25th": point.percentiles["25th"],
            "50th": point.percentiles["50th"],
            "75th": point.percentiles["75th"],
            "85th": point.percentiles["85th"],
            "95th": point.percentiles["95th"],
            "97th": point.percentiles["97th"],
            "99th": point.percentiles["99th"],
          }
        }))
      }));
    },
  });

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="h-20 bg-gray-100 animate-pulse rounded-lg" />
        <div className="h-20 bg-gray-100 animate-pulse rounded-lg" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {curves?.map((curve) => (
        <Collapsible
          key={curve.id}
          open={openItems.includes(curve.id)}
          onOpenChange={() => toggleItem(curve.id)}
        >
          <div
            className={`p-4 bg-white rounded-lg shadow-sm border transition-colors ${
              curve.data.length > 0
                ? "border-primary/20"
                : "border-gray-100 opacity-50"
            }`}
          >
            <CollapsibleTrigger className="w-full">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <ChartLine
                    className={`h-5 w-5 ${
                      curve.data.length > 0 ? "text-primary" : "text-gray-400"
                    }`}
                  />
                  <div>
                    <h3 className="font-medium">
                      {curve.type === "weight" && "Peso"}
                      {curve.type === "height" && "Altura"}
                      {curve.type === "bmi" && "IMC"}
                      {curve.type === "head-circumference" && "Perímetro Cefálico"}
                    </h3>
                    <p className="text-sm text-gray-500 flex items-center gap-1">
                      <User2 className="h-3 w-3" />
                      {curve.gender === "male" ? "Masculino" : "Feminino"}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-sm text-gray-500">
                    {curve.data.length > 0
                      ? `${curve.data.length} pontos de dados`
                      : "Nenhum dado importado"}
                  </div>
                  {openItems.includes(curve.id) ? (
                    <ChevronUp className="h-4 w-4 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  )}
                </div>
              </div>
            </CollapsibleTrigger>

            <CollapsibleContent className="pt-4">
              {curve.data.length > 0 ? (
                <div className="rounded-lg border overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Ano</TableHead>
                        <TableHead>Mês</TableHead>
                        <TableHead>L</TableHead>
                        <TableHead>M</TableHead>
                        <TableHead>S</TableHead>
                        <TableHead>1º</TableHead>
                        <TableHead>3º</TableHead>
                        <TableHead>5º</TableHead>
                        <TableHead>15º</TableHead>
                        <TableHead>25º</TableHead>
                        <TableHead>50º</TableHead>
                        <TableHead>75º</TableHead>
                        <TableHead>85º</TableHead>
                        <TableHead>95º</TableHead>
                        <TableHead>97º</TableHead>
                        <TableHead>99º</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {curve.data.map((point, index) => (
                        <TableRow key={index}>
                          <TableCell>{Math.floor(point.age_months / 12)}</TableCell>
                          <TableCell>{point.age_months % 12}</TableCell>
                          <TableCell>{point.L.toFixed(4)}</TableCell>
                          <TableCell>{point.M.toFixed(4)}</TableCell>
                          <TableCell>{point.S.toFixed(5)}</TableCell>
                          <TableCell>{point.percentiles["1st"].toFixed(1)}</TableCell>
                          <TableCell>{point.percentiles["3rd"].toFixed(1)}</TableCell>
                          <TableCell>{point.percentiles["5th"].toFixed(1)}</TableCell>
                          <TableCell>{point.percentiles["15th"].toFixed(1)}</TableCell>
                          <TableCell>{point.percentiles["25th"].toFixed(1)}</TableCell>
                          <TableCell>{point.percentiles["50th"].toFixed(1)}</TableCell>
                          <TableCell>{point.percentiles["75th"].toFixed(1)}</TableCell>
                          <TableCell>{point.percentiles["85th"].toFixed(1)}</TableCell>
                          <TableCell>{point.percentiles["95th"].toFixed(1)}</TableCell>
                          <TableCell>{point.percentiles["97th"].toFixed(1)}</TableCell>
                          <TableCell>{point.percentiles["99th"].toFixed(1)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="text-center text-gray-500 py-4">
                  Nenhum dado disponível para exibição
                </div>
              )}
            </CollapsibleContent>
          </div>
        </Collapsible>
      ))}
    </div>
  );
};