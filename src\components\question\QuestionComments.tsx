import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { QuestionComment } from "./QuestionComment";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import type { Comment } from "@/types/comments";

interface QuestionCommentsProps {
  comments: Comment[];
  newComment: string;
  setNewComment: (comment: string) => void;
  onAddComment: (text: string) => void;
  onReplyComment: (commentId: string | number, replyText: string) => void;
  onLikeComment: (commentId: string | number, isLike: boolean) => void;
  onReplyLike?: (commentId: string | number, replyId: string | number, isLike: boolean) => void;
  userId?: string;
  questionId?: string;
}

export const QuestionComments = ({ 
  comments: initialComments = [], 
  newComment, 
  setNewComment, 
  onAddComment,
  onReplyComment,
  onLikeComment,
  onReplyLike,
  userId,
  questionId
}: QuestionCommentsProps) => {
  const { toast } = useToast();
  const [localComments, setLocalComments] = useState<Comment[]>(initialComments);

  useEffect(() => {
    setLocalComments(initialComments);
  }, [initialComments]);

  const handleAddComment = async () => {
    if (!newComment.trim() || !questionId || !userId) return;

    try {
      const timestamp = new Date().toISOString();

      const newCommentObj: Comment = {
        id: crypto.randomUUID(),
        text: newComment.trim(),
        user: userId,
        timestamp: timestamp,
        replies: [],
        likes: 0,
        dislikes: 0,
        likedBy: [],
        dislikedBy: []
      };

      const { data: question, error: fetchError } = await supabase
        .from('questions')
        .select('comments')
        .eq('id', questionId)
        .single();

      if (fetchError) throw fetchError;

      const updatedComments = [...(question.comments || []), newCommentObj];

      const { error: updateError } = await supabase
        .from('questions')
        .update({ comments: updatedComments })
        .eq('id', questionId);

      if (updateError) throw updateError;

      setLocalComments(updatedComments);
      onAddComment(newComment);
      setNewComment('');

      toast({
        title: "Comentário adicionado",
        description: "Seu comentário foi adicionado com sucesso.",
      });

    } catch (error) {
      toast({
        title: "Erro ao adicionar comentário",
        description: "Não foi possível adicionar seu comentário. Tente novamente.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-4">
      <div className="space-y-4">
        {localComments.map((comment) => (
          <QuestionComment 
            key={comment.id} 
            comment={comment} 
            onReply={onReplyComment}
            onLike={onLikeComment}
            onReplyLike={onReplyLike}
            userId={userId}
          />
        ))}
      </div>
      <div className="space-y-2">
        <Textarea
          placeholder="Adicione um comentário..."
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
        />
        <Button onClick={handleAddComment}>
          Comentar
        </Button>
      </div>
    </div>
  );
};
