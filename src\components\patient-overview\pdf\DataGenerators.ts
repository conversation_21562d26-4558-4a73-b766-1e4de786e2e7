import { PDFUtils } from './PDFUtils';
import { calculateExactPercentileSync } from '@/utils/exactPercentileCalculation';
import { calculateBirthWeightClassification } from '@/utils/pigAigGigClassification';
import { calculateSupplementation } from '@/utils/supplementationCalculator';
import { SupplementationInput, Maturity, RiskFactor } from '@/types/supplementation';
import { supabase } from '@/integrations/supabase/client';

// Tipos para os dados gerados
export interface WeightAnalysis {
  percentile: number;
  status: 'normal' | 'attention';
  interpretation: string;
  classification: 'PIG' | 'AIG' | 'GIG';
}

export interface HeightAnalysis {
  percentile: number;
  status: 'normal' | 'attention';
  interpretation: string;
}

export interface HeadCircumferenceAnalysis {
  percentile: number;
  status: 'normal' | 'attention';
  interpretation: string;
}

export interface AnalysisData {
  weightAnalysis: WeightAnalysis;
  heightAnalysis: HeightAnalysis;
  headCircumferenceAnalysis: HeadCircumferenceAnalysis;
}

export interface SupplementationRecommendation {
  vitamin: string;
  dosage: string;
  duration: string;
  notes?: string;
}

export interface SupplementationData {
  recommendations: SupplementationRecommendation[];
}

export interface VaccineItem {
  name: string;
  age: string;
}

export interface VaccineData {
  applied: VaccineItem[];
  upcoming: VaccineItem[];
}

export interface PatientData {
  name?: string;
  age: number;
  gender: 'male' | 'female';
  weight: number;
  height: number;
  headCircumference: number;
  birthWeight: number;
  gestationalAge: number;
  maturity?: Maturity;
  exclusiveBreastfeeding: boolean;
  riskFactors?: RiskFactor[];
}

/**
 * Geradores de dados para análises médicas
 */
export class DataGenerators {
  /**
   * Gera dados de análise antropométrica usando cálculos reais
   */
  static async generateAnalysisData(patientData: PatientData): Promise<AnalysisData> {
    // Usar EXATAMENTE o mesmo método do cabeçalho
    try {
      // Buscar dados WHO do Supabase
      const { data: weightData } = await supabase
        .from('pedbook_growth_curve_metadata')
        .select('data')
        .eq('gender', patientData.gender)
        .eq('type', 'weight')
        .single();

      const { data: heightData } = await supabase
        .from('pedbook_growth_curve_metadata')
        .select('data')
        .eq('gender', patientData.gender)
        .eq('type', 'height')
        .single();

      const { data: headData } = await supabase
        .from('pedbook_growth_curve_metadata')
        .select('data')
        .eq('gender', patientData.gender)
        .eq('type', 'head-circumference')
        .single();

      // Função para interpolar e calcular percentil (mesmo método do cabeçalho)
      const calculatePercentileFromData = (value: number, data: any[], ageInMonths: number) => {
        if (!data || data.length === 0) return { percentile: 50, status: 'normal' as const, interpretation: 'Normal' };

        const sortedData = data.sort((a: any, b: any) => a.age_months - b.age_months);

        let lowerPoint = sortedData[0];
        let upperPoint = sortedData[sortedData.length - 1];

        for (let i = 0; i < sortedData.length - 1; i++) {
          if (ageInMonths >= sortedData[i].age_months && ageInMonths <= sortedData[i + 1].age_months) {
            lowerPoint = sortedData[i];
            upperPoint = sortedData[i + 1];
            break;
          }
        }

        const ratio = (ageInMonths - lowerPoint.age_months) / (upperPoint.age_months - lowerPoint.age_months);
        const p3 = lowerPoint.percentiles["3rd"] + (upperPoint.percentiles["3rd"] - lowerPoint.percentiles["3rd"]) * ratio;
        const p15 = lowerPoint.percentiles["15th"] + (upperPoint.percentiles["15th"] - lowerPoint.percentiles["15th"]) * ratio;
        const p50 = lowerPoint.percentiles["50th"] + (upperPoint.percentiles["50th"] - lowerPoint.percentiles["50th"]) * ratio;
        const p85 = lowerPoint.percentiles["85th"] + (upperPoint.percentiles["85th"] - lowerPoint.percentiles["85th"]) * ratio;
        const p97 = lowerPoint.percentiles["97th"] + (upperPoint.percentiles["97th"] - lowerPoint.percentiles["97th"]) * ratio;

        let percentile = 50;
        let status: 'normal' | 'attention' = 'normal';
        let interpretation = 'Normal';

        if (value <= p3) {
          percentile = 3;
          status = 'attention';
          interpretation = 'Muito baixo';
        } else if (value <= p15) {
          percentile = Math.round(3 + ((value - p3) / (p15 - p3)) * 12);
          status = 'attention';
          interpretation = 'Baixo';
        } else if (value <= p50) {
          percentile = Math.round(15 + ((value - p15) / (p50 - p15)) * 35);
          status = 'normal';
          interpretation = 'Normal';
        } else if (value <= p85) {
          percentile = Math.round(50 + ((value - p50) / (p85 - p50)) * 35);
          status = 'normal';
          interpretation = 'Normal';
        } else if (value <= p97) {
          percentile = Math.round(85 + ((value - p85) / (p97 - p85)) * 12);
          status = 'attention';
          interpretation = 'Alto';
        } else {
          percentile = 97;
          status = 'attention';
          interpretation = 'Muito alto';
        }

        return { percentile, status, interpretation };
      };

      // Calcular percentis
      const weightResult = weightData?.data ?
        calculatePercentileFromData(patientData.weight, weightData.data, patientData.age) :
        { percentile: 50, status: 'normal' as const, interpretation: 'Normal' };

      const heightResult = heightData?.data ?
        calculatePercentileFromData(patientData.height, heightData.data, patientData.age) :
        { percentile: 50, status: 'normal' as const, interpretation: 'Normal' };

      const headResult = headData?.data ?
        calculatePercentileFromData(patientData.headCircumference, headData.data, patientData.age) :
        { percentile: 50, status: 'normal' as const, interpretation: 'Normal' };

      // Calcular classificação AIG/PIG/GIG usando a mesma função da aplicação
      const birthClassification = calculateBirthWeightClassification(
        patientData.birthWeight,
        patientData.gestationalAge,
        patientData.gender
      );

      return {
        weightAnalysis: {
          percentile: Math.round(weightResult.percentile),
          status: weightResult.status,
          interpretation: weightResult.interpretation,
          classification: birthClassification.classification
        },
        heightAnalysis: {
          percentile: Math.round(heightResult.percentile),
          status: heightResult.status,
          interpretation: heightResult.interpretation
        },
        headCircumferenceAnalysis: {
          percentile: Math.round(headResult.percentile),
          status: headResult.status,
          interpretation: headResult.interpretation
        }
      };
    } catch (error) {
      console.error('Erro ao gerar dados de análise:', error);
      // Fallback para valores padrão
      return {
        weightAnalysis: {
          percentile: 50,
          status: 'normal',
          interpretation: 'Normal',
          classification: 'AIG'
        },
        heightAnalysis: {
          percentile: 50,
          status: 'normal',
          interpretation: 'Normal'
        },
        headCircumferenceAnalysis: {
          percentile: 50,
          status: 'normal',
          interpretation: 'Normal'
        }
      };
    }
  }



  /**
   * Gera recomendações de suplementação usando os mesmos cálculos da aplicação
   */
  static generateSupplementationData(patientData: PatientData): SupplementationData {
    const recommendations: SupplementationRecommendation[] = [];

    // Preparar input para o calculador de suplementação
    const input: SupplementationInput = {
      ageInDays: Math.round(patientData.age * 30), // Converter meses para dias
      currentWeight: patientData.weight * 1000, // Converter kg para gramas
      birthWeight: patientData.birthWeight,
      maturity: patientData.maturity || (patientData.gestationalAge >= 37 ? 'Term' : 'Pre-term'),
      exclusiveBreastfeeding: patientData.exclusiveBreastfeeding,
      riskFactors: patientData.riskFactors || []
    };

    // Calcular suplementação usando a mesma função da aplicação
    const supplementationResult = calculateSupplementation(input);

    // Converter resultado para formato do PDF
    if (supplementationResult.vitaminD && !supplementationResult.vitaminD.includes('não está mais indicada')) {
      recommendations.push({
        vitamin: 'D',
        dosage: this.extractDosage(supplementationResult.vitaminD),
        duration: this.extractDuration(supplementationResult.vitaminD),
        notes: this.extractNotes(supplementationResult.vitaminD)
      });
    }

    if (supplementationResult.iron && !supplementationResult.iron.includes('não está mais indicada')) {
      recommendations.push({
        vitamin: 'Ferro',
        dosage: this.extractDosage(supplementationResult.iron),
        duration: this.extractDuration(supplementationResult.iron),
        notes: this.extractNotes(supplementationResult.iron)
      });
    }

    if (supplementationResult.vitaminA && !supplementationResult.vitaminA.includes('não está mais indicada')) {
      recommendations.push({
        vitamin: 'A',
        dosage: this.extractDosage(supplementationResult.vitaminA),
        duration: this.extractDuration(supplementationResult.vitaminA),
        notes: this.extractNotes(supplementationResult.vitaminA)
      });
    }

    return { recommendations };
  }

  /**
   * Extrai dosagem de uma string de recomendação
   */
  private static extractDosage(text: string): string {
    // Procurar por padrões de dosagem específicos
    const dosagePatterns = [
      /(\d+(?:\.\d+)?\s*gotas\s*\(\d+\s*UI\))/i, // "3 gotas (600 UI)"
      /(\d+(?:\.\d+)?\s*mg\s*de\s*ferro\s*elementar\/dia)/i, // "5.2 mg de ferro elementar/dia"
      /(\d+(?:\.\d+)?\s*UI\s*de\s*vitamina\s*A)/i, // "200.000 UI de vitamina A"
      /(\d+(?:\.\d+)?\s*mg\/kg\/dia)/i, // "1mg/kg/dia"
      /(\d+(?:\.\d+)?\s*(?:mg|UI|gotas))/i // Padrão geral
    ];

    for (const pattern of dosagePatterns) {
      const match = text.match(pattern);
      if (match) {
        return PDFUtils.cleanText(match[1]);
      }
    }

    return 'Conforme orientacao medica';
  }

  /**
   * Extrai duração de uma string de recomendação
   */
  private static extractDuration(text: string): string {
    // Procurar por padrões de duração específicos
    const durationPatterns = [
      /(ate \d+ anos de idade)/i,
      /(ate \d+ meses)/i,
      /(a cada \d+ meses)/i,
      /(todos os dias)/i,
      /(diariamente)/i
    ];

    for (const pattern of durationPatterns) {
      const match = text.match(pattern);
      if (match) {
        return PDFUtils.cleanText(match[1]);
      }
    }

    return 'Conforme orientacao medica';
  }

  /**
   * Extrai notas adicionais (fatores de risco, observações)
   */
  private static extractNotes(text: string): string {
    // Procurar por fatores de risco ou observações especiais
    const notesPatterns = [
      /Fatores de risco:\s*([^.]+\.)/i,
      /(pre-termo[^.]*)/i,
      /(areas endemicas[^.]*)/i,
      /(criancas desnutridas[^.]*)/i
    ];

    const notes: string[] = [];

    for (const pattern of notesPatterns) {
      const match = text.match(pattern);
      if (match) {
        notes.push(PDFUtils.cleanText(match[1]));
      }
    }

    // Se não encontrou notas específicas, retornar apenas indicação básica
    if (notes.length === 0) {
      if (text.includes('pre-termo')) {
        return 'Indicado para pre-termo';
      } else if (text.includes('areas endemicas')) {
        return 'Para areas endemicas ou risco aumentado';
      } else {
        return 'Administrar conforme orientacao';
      }
    }

    return notes.join('. ');
  }

  /**
   * Gera dados do calendário vacinal baseado na idade
   */
  static generateVaccineData(patientData: PatientData): VaccineData {
    const ageInMonths = patientData.age;
    const applied: VaccineItem[] = [];
    const upcoming: VaccineItem[] = [];

    // Vacinas já aplicadas (baseado na idade)
    if (ageInMonths >= 0) {
      applied.push(
        { name: 'BCG', age: 'Ao nascer' },
        { name: 'Hepatite B', age: 'Ao nascer' }
      );
    }

    if (ageInMonths >= 2) {
      applied.push(
        { name: 'Pentavalente', age: '2 meses' },
        { name: 'VIP', age: '2 meses' },
        { name: 'Pneumococica', age: '2 meses' },
        { name: 'Rotavirus', age: '2 meses' }
      );
    }

    if (ageInMonths >= 4) {
      applied.push(
        { name: 'Pentavalente (2a dose)', age: '4 meses' },
        { name: 'VIP (2a dose)', age: '4 meses' },
        { name: 'Pneumococica (2a dose)', age: '4 meses' },
        { name: 'Rotavirus (2a dose)', age: '4 meses' }
      );
    }

    if (ageInMonths >= 6) {
      applied.push(
        { name: 'Pentavalente (3a dose)', age: '6 meses' },
        { name: 'VIP (3a dose)', age: '6 meses' }
      );
    }

    if (ageInMonths >= 12) {
      applied.push(
        { name: 'Triplice viral', age: '12 meses' },
        { name: 'Pneumococica (reforco)', age: '12 meses' }
      );
    }

    // Próximas vacinas
    if (ageInMonths < 4) {
      upcoming.push(
        { name: 'Pentavalente (2a dose)', age: '4 meses' },
        { name: 'VIP (2a dose)', age: '4 meses' },
        { name: 'Pneumococica (2a dose)', age: '4 meses' },
        { name: 'Rotavirus (2a dose)', age: '4 meses' }
      );
    } else if (ageInMonths < 6) {
      upcoming.push(
        { name: 'Pentavalente (3a dose)', age: '6 meses' },
        { name: 'VIP (3a dose)', age: '6 meses' }
      );
    } else if (ageInMonths < 12) {
      upcoming.push(
        { name: 'Triplice viral', age: '12 meses' },
        { name: 'Pneumococica (reforco)', age: '12 meses' }
      );
    } else if (ageInMonths < 15) {
      upcoming.push(
        { name: 'DTP (reforco)', age: '15 meses' },
        { name: 'VOP (reforco)', age: '15 meses' }
      );
    }

    return { applied, upcoming };
  }
}
