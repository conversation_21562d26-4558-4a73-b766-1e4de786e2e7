import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { PrescriptionForm } from "../PrescriptionForm";
import type { Session } from "@supabase/supabase-js";

interface CreatePrescriptionDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  session: Session;
  onSuccess: () => void;
}

export const CreatePrescriptionDialog = ({ isOpen, onOpenChange, session, onSuccess }: CreatePrescriptionDialogProps) => {
  if (!session) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Nova Prescrição</DialogTitle>
          <DialogDescription>
            Crie uma nova prescrição preenchendo os campos abaixo.
          </DialogDescription>
        </DialogHeader>
        <PrescriptionForm
          session={session}
          onSuccess={onSuccess}
        />
      </DialogContent>
    </Dialog>
  );
};