import { Star } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { Formula } from "./types";

interface FormulaDetailsProps {
  formula: Formula;
}

const formatDescription = (text: string | null) => {
  if (!text) return "";
  
  // Handle bold text (wrapped in **text**)
  const boldFormatted = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  
  // Handle line breaks
  return boldFormatted.split('\n').map((line, i) => (
    <p key={i} className="mb-2">{
      <span dangerouslySetInnerHTML={{ __html: line }} />
    }</p>
  ));
};

export const FormulaDetails = ({ formula }: FormulaDetailsProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="bg-white rounded-xl p-6 shadow-lg"
    >
      <div className="flex flex-col lg:flex-row gap-8">
        <AnimatePresence mode="wait">
          {formula.image_url && (
            <motion.div
              key={formula.image_url}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="w-full lg:w-1/3 flex items-center justify-center"
            >
              <motion.img
                key={formula.image_url}
                src={formula.image_url}
                alt={formula.name}
                className="w-full max-w-[300px] h-auto rounded-lg object-contain"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.3 }}
                loading="eager"
              />
            </motion.div>
          )}
        </AnimatePresence>

        <div className="flex-1 space-y-6">
          <div className="flex items-center gap-2">
            <Star className="w-6 h-6 text-yellow-400 fill-current" />
            <h3 className="text-2xl font-semibold text-gray-900">{formula.name}</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-gray-600 text-sm">Idade recomendada:</p>
              <p className="font-medium text-gray-900">{formula.age_range}</p>
            </div>
            
            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-gray-600 text-sm">Preço médio:</p>
              <p className="font-medium text-gray-900">
                {formula.price ? `R$ ${formula.price.toFixed(2)}` : 'Não informado'}
              </p>
            </div>
            
            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-gray-600 text-sm">Marca:</p>
              <p className="font-medium text-gray-900">{formula.brand}</p>
            </div>
          </div>

          {formula.description && (
            <div className="prose max-w-none">
              <h4 className="text-lg font-medium text-gray-900 mb-2">Descrição:</h4>
              <div className="text-gray-700">
                {formatDescription(formula.description)}
              </div>
            </div>
          )}
          
          {formula.nutrients && (
            <div className="space-y-3">
              <h4 className="text-lg font-medium text-gray-900">Nutrientes por 100ml:</h4>
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4">
                {Object.entries(formula.nutrients).map(([key, value]) => (
                  <div key={key} className="bg-blue-50 p-3 rounded-lg">
                    <p className="text-gray-600 text-sm">{key}:</p>
                    <p className="font-medium text-gray-900">{String(value)}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};