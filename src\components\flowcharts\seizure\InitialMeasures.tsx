import { Card } from "@/components/ui/card";
import { <PERSON>ert<PERSON>ircle, Stethoscope, Syringe } from "lucide-react";

interface InitialMeasuresProps {
  weight: number;
}

export const InitialMeasures = ({ weight }: InitialMeasuresProps) => {

  
  const glucoseVolume = (weight * 2.5).toFixed(1);
  const glucagonValue = weight * 0.03;
  const glucagonDose = Math.min(glucagonValue, 1).toFixed(2);

  return (
    <div className="space-y-6">
      {/* Via Aérea */}
      <Card className="p-6 bg-white/80 backdrop-blur-sm border border-yellow-200">
        <div className="flex items-start gap-4">
          <div className="mt-1">
            <Stethoscope className="h-6 w-6 text-yellow-600" />
          </div>
          <div className="space-y-3">
            <h3 className="font-semibold text-lg text-yellow-800">1.1 Proteção da Via Aérea</h3>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>Administração de oxigênio a 100% (máscara facial ou cateter nasal)</li>
              <li>Garantir boa posição do paciente (de lado) para prevenir aspiração</li>
              <li>Monitoramento contínuo de oximetria de pulso</li>
            </ul>
          </div>
        </div>
      </Card>

      {/* Exames */}
      <Card className="p-6 bg-white/80 backdrop-blur-sm border border-blue-200">
        <div className="flex items-start gap-4">
          <div className="mt-1">
            <AlertCircle className="h-6 w-6 text-blue-600" />
          </div>
          <div className="space-y-3">
            <h3 className="font-semibold text-lg text-blue-800">1.2 Coleta de Exames</h3>
            <div className="space-y-2 text-gray-700">
              <p className="font-medium">Exames laboratoriais básicos:</p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>Hemograma completo</li>
                <li>Dosagem de glicose e eletrólitos (incluindo magnésio e fósforo)</li>
                <li>Função hepática e renal</li>
                <li>Hemocultura ou urocultura, se necessário</li>
              </ul>
            </div>
          </div>
        </div>
      </Card>

      {/* Controle de Glicemia */}
      <Card className="p-6 bg-white/80 backdrop-blur-sm border border-green-200">
        <div className="flex items-start gap-4">
          <div className="mt-1">
            <Syringe className="h-6 w-6 text-green-600" />
          </div>
          <div className="space-y-3">
            <h3 className="font-semibold text-lg text-green-800">1.3 Controle de Glicemia</h3>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>Avaliar glicemia capilar à beira do leito</li>
              <li>Se hipoglicemia: Administrar soro glicosado 10% {glucoseVolume} mL IV</li>
              <li>Se não for possível acesso periférico: Glucagon {glucagonDose} mg IM ou SC (máximo 1 mg)</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};