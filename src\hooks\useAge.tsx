import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";

const AGE_KEY = "patient-age";
const DEFAULT_AGE = 12;

export const useAge = () => {
  const queryClient = useQueryClient();
  const [tempAge, setTempAge] = useState<number | null>(null);

  const { data: age = DEFAULT_AGE } = useQuery({
    queryKey: [AGE_KEY],
    queryFn: () => {
      const savedAge = localStorage.getItem(AGE_KEY);
      return savedAge ? parseFloat(savedAge) : DEFAULT_AGE;
    },
    staleTime: Infinity,
  });

  const setAge = (newAge: number) => {
    setTempAge(null);
    localStorage.setItem(AGE_KEY, newAge.toString());
    queryClient.setQueryData([AGE_KEY], newAge);
  };

  const setTempAgeValue = (value: number) => {
    setTempAge(value);
  };

  return { 
    age, 
    setAge, 
    displayAge: tempAge ?? age,
    setTempAge: setTempAgeValue,
    ageInMonths: age, // Add this line
    commitAge: setAge // Add this line
  };
};