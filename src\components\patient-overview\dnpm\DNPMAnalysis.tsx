import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Card } from "@/components/ui/card";
import { Brain, Milestone, ArrowRight } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { formatAge } from "@/utils/formatAge";

interface DNPMAnalysisProps {
  ageInMonths: number;
}

interface Milestone {
  id: string;
  age_type: string;
  age_years: number | null;
  age_months: number;
  social_emotional: string | null;
  language_communication: string | null;
  cognition: string | null;
  motor_physical: string | null;
  image_url: string | null;
}

export function DNPMAnalysis({ ageInMonths }: DNPMAnalysisProps) {
  const { data: milestones, isLoading } = useQuery({
    queryKey: ["dnpm-milestones"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("pedbook_dnpm_milestones")
        .select("*")
        .order("age_months");

      if (error) throw error;
      return data as Milestone[];
    },
  });

  if (isLoading || !milestones) {
    return (
      <Card className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="h-20 bg-gray-200 rounded"></div>
        </div>
      </Card>
    );
  }

  const currentMilestone = milestones
    .filter(m => m.age_months <= ageInMonths)
    .slice(-1)[0];

  const nextMilestone = milestones
    .find(m => m.age_months > ageInMonths);

  // Se não há marco atual (idade muito pequena), mostrar apenas o próximo marco
  if (!currentMilestone && nextMilestone) {
    return (
      <div className="space-y-4">
        <Alert>
          <Brain className="h-4 w-4" />
          <AlertTitle>DNPM</AlertTitle>
          <AlertDescription className="text-yellow-700">
            Com base na idade do paciente ({formatAge(ageInMonths)})
          </AlertDescription>
        </Alert>

        <Card className="p-4 bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800/50">
          <h3 className="font-semibold text-blue-700 dark:text-blue-300 mb-2">
            Próximo Marco ({nextMilestone.age_months} {nextMilestone.age_months === 1 ? 'mês' : 'meses'})
          </h3>
          <div className="space-y-3">
            {nextMilestone.social_emotional && (
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p className="text-xs font-medium text-blue-600 dark:text-blue-400">Social e Emocional</p>
                  <p className="text-blue-600 dark:text-blue-200/90 text-sm">{nextMilestone.social_emotional}</p>
                </div>
              </div>
            )}
            {nextMilestone.language_communication && (
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p className="text-xs font-medium text-purple-600 dark:text-purple-400">Linguagem e Comunicação</p>
                  <p className="text-purple-600 dark:text-purple-200/90 text-sm">{nextMilestone.language_communication}</p>
                </div>
              </div>
            )}
            {nextMilestone.cognition && (
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p className="text-xs font-medium text-green-600 dark:text-green-400">Cognição</p>
                  <p className="text-green-600 dark:text-green-200/90 text-sm">{nextMilestone.cognition}</p>
                </div>
              </div>
            )}
            {nextMilestone.motor_physical && (
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <p className="text-xs font-medium text-orange-600 dark:text-orange-400">Motora/Física</p>
                  <p className="text-orange-600 dark:text-orange-200/90 text-sm">{nextMilestone.motor_physical}</p>
                </div>
              </div>
            )}
          </div>
        </Card>
      </div>
    );
  }

  // Se não há marcos disponíveis
  if (!currentMilestone && !nextMilestone) {
    return (
      <Alert>
        <Brain className="h-4 w-4" />
        <AlertTitle>DNPM</AlertTitle>
        <AlertDescription>
          Não há marcos de desenvolvimento disponíveis para esta idade ({formatAge(ageInMonths)}).
        </AlertDescription>
      </Alert>
    );
  }

  const MilestoneCard = ({ title, content, bgColor, textColor }: { title: string, content: string | null, bgColor: string, textColor: string }) => {
    if (!content) return null;
    return (
      <div className={`${bgColor} rounded-lg p-4 shadow-sm`}>
        <h4 className={`text-sm font-medium ${textColor} mb-2 flex items-center gap-2`}>
          <span className="w-2 h-2 rounded-full bg-current"></span>
          {title}
        </h4>
        <p className={`text-sm ${textColor} break-words whitespace-pre-wrap`}>{content}</p>
      </div>
    );
  };

  const renderMilestoneSection = (milestone: Milestone, isNext: boolean = false) => {
    const milestones = [
      {
        title: "Social e Emocional",
        content: milestone.social_emotional,
        bgColor: isNext ? "bg-blue-50 dark:bg-blue-900/20" : "bg-blue-100 dark:bg-blue-900/30",
        textColor: isNext ? "text-blue-700 dark:text-blue-300" : "text-blue-800 dark:text-blue-200"
      },
      {
        title: "Linguagem e Comunicação",
        content: milestone.language_communication,
        bgColor: isNext ? "bg-purple-50 dark:bg-purple-900/20" : "bg-purple-100 dark:bg-purple-900/30",
        textColor: isNext ? "text-purple-700 dark:text-purple-300" : "text-purple-800 dark:text-purple-200"
      },
      {
        title: "Cognição",
        content: milestone.cognition,
        bgColor: isNext ? "bg-green-50 dark:bg-green-900/20" : "bg-green-100 dark:bg-green-900/30",
        textColor: isNext ? "text-green-700 dark:text-green-300" : "text-green-800 dark:text-green-200"
      },
      {
        title: "Motora/Física",
        content: milestone.motor_physical,
        bgColor: isNext ? "bg-orange-50 dark:bg-orange-900/20" : "bg-orange-100 dark:bg-orange-900/30",
        textColor: isNext ? "text-orange-700 dark:text-orange-300" : "text-orange-800 dark:text-orange-200"
      }
    ];

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {milestones.map((item, index) => (
          <MilestoneCard
            key={index}
            title={item.title}
            content={item.content}
            bgColor={item.bgColor}
            textColor={item.textColor}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <Alert className="bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800/50">
        <Brain className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
        <AlertTitle className="text-yellow-800 dark:text-yellow-300">Marcos do Desenvolvimento</AlertTitle>
        <AlertDescription className="text-yellow-700 dark:text-yellow-200">
          Com base na idade do paciente ({formatAge(ageInMonths)})
        </AlertDescription>
      </Alert>

      <div className="space-y-8">
        <div className="w-full">
          <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300 mb-6">
            <Milestone className="h-5 w-5" />
            <h3 className="text-xl font-semibold">Marco Atual ({currentMilestone.age_months} meses)</h3>
          </div>
          {renderMilestoneSection(currentMilestone)}
        </div>

        {nextMilestone && (
          <div className="w-full">
            <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300 mb-6">
              <ArrowRight className="h-5 w-5" />
              <h3 className="text-xl font-semibold">Próximo Marco ({nextMilestone.age_months} meses)</h3>
            </div>
            {renderMilestoneSection(nextMilestone, true)}
          </div>
        )}
      </div>

      <p className="text-center text-sm text-gray-500 dark:text-gray-400 mt-6">
        Referência: Cartilha de Desenvolvimento 2m-5anos, CDC/SBP
      </p>
    </div>
  );
}