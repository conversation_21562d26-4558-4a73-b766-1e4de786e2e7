
import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Loader2, AlertTriangle, CheckCircle, RefreshCw } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import AdminNav from '@/components/admin/AdminNav';
import { getUnanalyzedQuestions, getThemeAnalysisStats, analyzeQuestion } from '@/services/themeAnalysisService';
import type { Question } from '@/services/themeAnalysisService';

export const FormatThemes = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [domain, setDomain] = useState<'pediatria' | 'oftalmologia'>('pediatria');
  const [questions, setQuestions] = useState<Question[]>([]);
  const [batchSize, setBatchSize] = useState(5);
  const [analysisResults, setAnalysisResults] = useState<Record<string, any>>({});
  const [stats, setStats] = useState({ total: 0, analyzed: 0, remaining: 0 });
  const [currentIndex, setCurrentIndex] = useState(0);

  // Carregar estatísticas iniciais
  useEffect(() => {
    loadStats();
  }, [domain]);

  const loadStats = async () => {
    try {
      setIsLoading(true);
      const statsData = await getThemeAnalysisStats(domain);
      setStats(statsData);
    } catch (error) {
      toast({
        title: 'Erro ao carregar estatísticas',
        description: 'Não foi possível obter as estatísticas das questões.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadQuestions = async () => {
    try {
      setIsLoading(true);
      const questionsData = await getUnanalyzedQuestions(domain, batchSize);
      console.log(`📝 Carregadas ${questionsData.length} questões para análise`);

      if (questionsData.length === 0) {
        toast({
          title: 'Nenhuma questão disponível',
          description: 'Não há mais questões para análise neste domínio.',
        });
      }

      setQuestions(questionsData);
      // Limpar resultados de análises anteriores
      setAnalysisResults({});
      setCurrentIndex(0);
    } catch (error) {
      console.error('❌ Erro ao carregar questões:', error);
      toast({
        title: 'Erro ao carregar questões',
        description: 'Não foi possível obter as questões para análise.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const analyzeCurrentQuestion = async () => {
    if (questions.length === 0 || currentIndex >= questions.length) {
      toast({
        title: 'Sem questões',
        description: 'Não há questões disponíveis para análise.',
        variant: 'destructive',
      });
      return;
    }

    const currentQuestion = questions[currentIndex];

    try {
      setIsAnalyzing(true);
      const result = await analyzeQuestion(currentQuestion.id, domain);

      // Atualizar resultados de análise
      setAnalysisResults(prev => ({
        ...prev,
        [currentQuestion.id]: result
      }));

      toast({
        title: 'Análise concluída',
        description: 'A questão foi analisada com sucesso.'
      });

    } catch (error) {
      toast({
        title: 'Erro na análise',
        description: 'Não foi possível analisar a questão.',
        variant: 'destructive',
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleNextQuestion = () => {
    if (currentIndex < questions.length - 1) {
      setCurrentIndex(currentIndex + 1);
    } else {
      toast({
        title: 'Último item',
        description: 'Este é o último item do lote.',
      });
    }
  };

  const handlePreviousQuestion = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    } else {
      toast({
        title: 'Primeiro item',
        description: 'Este é o primeiro item do lote.',
      });
    }
  };

  const refreshStats = async () => {
    await loadStats();
    toast({
      title: 'Estatísticas atualizadas',
      description: 'As estatísticas foram atualizadas com sucesso.'
    });
  };

  const currentQuestion = questions[currentIndex];
  const currentResult = currentQuestion ? analysisResults[currentQuestion.id] : null;
  const hasAnalysisResult = !!currentResult;

  const setQuestionManuallyReviewed = async () => {
    if (!currentQuestion) return;

    try {
      setIsLoading(true);

      // Atualizar o campo 'tags' da questão atual para marcar como revisado manualmente
      const currentTags = currentQuestion.tags || {};
      const updatedTags = {
        ...currentTags,
        theme_analyzed: true,
        analyzed_at: new Date().toISOString(),
        analyzed_domain: domain,
        manually_reviewed: true
      };

      const { error } = await supabase
        .from('questions')
        .update({ tags: updatedTags })
        .eq('id', currentQuestion.id);

      if (error) throw error;

      toast({
        title: 'Questão marcada como revisada',
        description: 'Esta questão foi marcada como revisada manualmente.'
      });

      // Remove a questão atual do array
      const updatedQuestions = [...questions];
      updatedQuestions.splice(currentIndex, 1);

      if (updatedQuestions.length === 0) {
        setQuestions([]);
        setCurrentIndex(0);
        await loadStats(); // Atualizar estatísticas
        return;
      }

      // Ajusta o índice atual se necessário
      const newIndex = currentIndex >= updatedQuestions.length
        ? updatedQuestions.length - 1
        : currentIndex;

      setQuestions(updatedQuestions);
      setCurrentIndex(newIndex);
      await loadStats(); // Atualizar estatísticas

    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Não foi possível marcar a questão como revisada.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <AdminNav />
      <div className="container mx-auto p-6 max-w-5xl">
        <div className="mb-6">
          <h1 className="text-2xl font-bold mb-2">Análise de Temas de Questões</h1>
          <p className="text-gray-600">
            Use esta ferramenta para analisar e recomendar temas e focos para questões
          </p>
        </div>

        <Tabs defaultValue="pediatria" value={domain} onValueChange={(v) => setDomain(v as 'pediatria' | 'oftalmologia')}>
          <TabsList className="mb-4">
            <TabsTrigger value="pediatria">Pediatria</TabsTrigger>
            <TabsTrigger value="oftalmologia">Oftalmologia</TabsTrigger>
          </TabsList>

          <TabsContent value="pediatria" className="space-y-4">
            <StatisticsCard stats={stats} isLoading={isLoading} onRefresh={refreshStats} />
            <QuestionLoaderCard
              batchSize={batchSize}
              setBatchSize={setBatchSize}
              loadQuestions={loadQuestions}
              isLoading={isLoading}
            />
            {questions.length > 0 && (
              <QuestionAnalysisCard
                question={currentQuestion}
                result={currentResult}
                isAnalyzing={isAnalyzing}
                onAnalyze={analyzeCurrentQuestion}
                onNext={handleNextQuestion}
                onPrevious={handlePreviousQuestion}
                currentIndex={currentIndex}
                totalQuestions={questions.length}
                onMarkReviewed={setQuestionManuallyReviewed}
              />
            )}
          </TabsContent>

          <TabsContent value="oftalmologia" className="space-y-4">
            <StatisticsCard stats={stats} isLoading={isLoading} onRefresh={refreshStats} />
            <QuestionLoaderCard
              batchSize={batchSize}
              setBatchSize={setBatchSize}
              loadQuestions={loadQuestions}
              isLoading={isLoading}
            />
            {questions.length > 0 && (
              <QuestionAnalysisCard
                question={currentQuestion}
                result={currentResult}
                isAnalyzing={isAnalyzing}
                onAnalyze={analyzeCurrentQuestion}
                onNext={handleNextQuestion}
                onPrevious={handlePreviousQuestion}
                currentIndex={currentIndex}
                totalQuestions={questions.length}
                onMarkReviewed={setQuestionManuallyReviewed}
              />
            )}
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

interface StatisticsCardProps {
  stats: { total: number; analyzed: number; remaining: number };
  isLoading: boolean;
  onRefresh: () => void;
}

const StatisticsCard = ({ stats, isLoading, onRefresh }: StatisticsCardProps) => {
  const percent = stats.total ? Math.round((stats.analyzed / stats.total) * 100) : 0;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle>Estatísticas</CardTitle>
        <Button variant="outline" size="sm" onClick={onRefresh} disabled={isLoading}>
          <RefreshCw className="h-4 w-4 mr-1" />
          Atualizar
        </Button>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-sm font-medium text-gray-500">Total</div>
            <div className="text-2xl font-bold">{isLoading ? '...' : stats.total}</div>
          </div>
          <div className="text-center">
            <div className="text-sm font-medium text-green-600">Analisadas</div>
            <div className="text-2xl font-bold text-green-600">{isLoading ? '...' : stats.analyzed}</div>
          </div>
          <div className="text-center">
            <div className="text-sm font-medium text-blue-600">Restantes</div>
            <div className="text-2xl font-bold text-blue-600">{isLoading ? '...' : stats.remaining}</div>
          </div>
        </div>
        <div className="mt-4">
          <div className="text-sm font-medium text-gray-500 mb-1">Progresso: {percent}%</div>
          <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="h-full bg-green-500 transition-all duration-500"
              style={{ width: `${percent}%` }}
            ></div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

interface QuestionLoaderCardProps {
  batchSize: number;
  setBatchSize: (size: number) => void;
  loadQuestions: () => void;
  isLoading: boolean;
}

const QuestionLoaderCard = ({
  batchSize,
  setBatchSize,
  loadQuestions,
  isLoading
}: QuestionLoaderCardProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Carregar Questões</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-end gap-4">
          <div className="flex-1">
            <Label htmlFor="batchSize" className="mb-2 block">Quantidade de questões</Label>
            <Input
              id="batchSize"
              type="number"
              min="1"
              max="20"
              value={batchSize}
              onChange={(e) => setBatchSize(parseInt(e.target.value) || 5)}
            />
          </div>
          <Button
            onClick={loadQuestions}
            disabled={isLoading}
            className="flex-1"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Carregando...
              </>
            ) : 'Carregar Questões'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

interface QuestionAnalysisCardProps {
  question: Question;
  result: any;
  isAnalyzing: boolean;
  onAnalyze: () => void;
  onNext: () => void;
  onPrevious: () => void;
  currentIndex: number;
  totalQuestions: number;
  onMarkReviewed: () => void;
}

const QuestionAnalysisCard = ({
  question,
  result,
  isAnalyzing,
  onAnalyze,
  onNext,
  onPrevious,
  currentIndex,
  totalQuestions,
  onMarkReviewed
}: QuestionAnalysisCardProps) => {
  if (!question) return null;

  // Verificar se a questão já foi analisada anteriormente
  const isAlreadyAnalyzed = question.tags?.theme_analyzed === true;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle>
          Questão {currentIndex + 1} de {totalQuestions}
        </CardTitle>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onPrevious}
            disabled={isAnalyzing || currentIndex === 0}
          >
            Anterior
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={onNext}
            disabled={isAnalyzing || currentIndex === totalQuestions - 1}
          >
            Próxima
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {isAlreadyAnalyzed && (
          <Alert variant="warning" className="bg-amber-50 border-amber-200">
            <AlertTriangle className="h-4 w-4 text-amber-600" />
            <AlertDescription className="text-amber-800">
              Esta questão já foi analisada anteriormente.
            </AlertDescription>
          </Alert>
        )}

        <div>
          <h3 className="font-medium mb-2">Enunciado da Questão:</h3>
          <div className="bg-gray-50 p-3 rounded-md">
            <p>{question.statement}</p>
          </div>
        </div>

        {Array.isArray(question.alternatives) && question.alternatives.length > 0 && (
          <div>
            <h3 className="font-medium mb-2">Alternativas:</h3>
            <div className="bg-gray-50 p-3 rounded-md">
              <ol className="list-decimal list-inside">
                {question.alternatives.map((alt, i) => (
                  <li key={i} className="mb-1">{alt}</li>
                ))}
              </ol>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <h3 className="font-medium mb-2">Especialidade atual:</h3>
            <div className="bg-gray-50 p-3 rounded-md">
              <p>{question.specialty?.name || 'Não definido'}</p>
            </div>
          </div>
          <div>
            <h3 className="font-medium mb-2">Tema atual:</h3>
            <div className="bg-gray-50 p-3 rounded-md">
              <p>{question.theme?.name || 'Não definido'}</p>
            </div>
          </div>
          <div>
            <h3 className="font-medium mb-2">Foco atual:</h3>
            <div className="bg-gray-50 p-3 rounded-md">
              <p>{question.focus?.name || 'Não definido'}</p>
            </div>
          </div>
        </div>

        <Separator />

        {result ? (
          <div className="space-y-4">
            <Alert className="bg-green-50 border-green-200">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                Análise concluída com sucesso!
              </AlertDescription>
            </Alert>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium mb-2">Tema recomendado:</h3>
                <div className="bg-green-50 p-3 rounded-md">
                  <p className="font-medium">{result.recommendedTheme}</p>
                  {result.isNewTheme && (
                    <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full mt-1 inline-block">
                      Novo tema
                    </span>
                  )}
                </div>
              </div>
              <div>
                <h3 className="font-medium mb-2">Foco recomendado:</h3>
                <div className="bg-green-50 p-3 rounded-md">
                  <p className="font-medium">{result.recommendedFocus}</p>
                  {result.isNewFocus && (
                    <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full mt-1 inline-block">
                      Novo foco
                    </span>
                  )}
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-medium mb-2">Justificativa:</h3>
              <div className="bg-gray-50 p-3 rounded-md">
                <p>{result.justification}</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex flex-col md:flex-row justify-between gap-4">
            <Button
              onClick={onAnalyze}
              disabled={isAnalyzing}
              className="flex-1"
            >
              {isAnalyzing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Analisando...
                </>
              ) : 'Analisar Questão'}
            </Button>

            <Button
              variant="outline"
              onClick={onMarkReviewed}
              disabled={isAnalyzing}
              className="flex-1"
            >
              Marcar Como Revisada
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default FormatThemes;
