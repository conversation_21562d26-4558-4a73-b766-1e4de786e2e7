import React from "react";
import { Star } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface NotesFilterProps {
  selectedTag: string | null;
  onTagChange: (tag: string | null) => void;
  showFavorites: boolean;
  onFavoritesChange: (show: boolean) => void;
  availableTags: string[];
}

export const NotesFilter: React.FC<NotesFilterProps> = ({
  selectedTag,
  onTagChange,
  showFavorites,
  onFavoritesChange,
  availableTags,
}) => {
  return (
    <div className="flex items-center gap-4 mb-6">
      <Select
        value={selectedTag || "all"}
        onValueChange={(value) => onTagChange(value === "all" ? null : value)}
      >
        <SelectTrigger className="w-[200px]">
          <SelectValue placeholder="Filtrar por tag" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Todas as tags</SelectItem>
          {availableTags.map((tag) => (
            <SelectItem key={tag} value={tag}>
              {tag}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Button
        variant={showFavorites ? "default" : "outline"}
        size="sm"
        onClick={() => onFavoritesChange(!showFavorites)}
        className="gap-2"
      >
        <Star className={`h-4 w-4 ${showFavorites ? "fill-current" : ""}`} />
        Favoritos
      </Button>
    </div>
  );
};