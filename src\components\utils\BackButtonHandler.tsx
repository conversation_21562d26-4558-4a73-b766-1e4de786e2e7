
import { useBackButton } from "@/hooks/useBackButton";
import { useWebViewBackButton } from "@/hooks/useWebViewBackButton";

/**
 * Componente para gerenciar o botão de voltar físico do dispositivo móvel
 * Não renderiza nada, apenas aplica o comportamento
 * Versão aprimorada com suporte específico para WebView Android
 */
const BackButtonHandler: React.FC = () => {
  // Hook principal para navegadores e WebViews
  useBackButton();

  // Hook específico para WebView Android (estratégias adicionais)
  useWebViewBackButton();

  return null;
};

export default BackButtonHandler;
