import React from 'react';
import MDEditor from '@uiw/react-md-editor';
import './MarkdownRenderer.css';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

export function MarkdownRenderer({ content, className = '' }: MarkdownRendererProps) {
  if (!content) {
    return <span className="text-muted-foreground">Sem descrição disponível</span>;
  }

  return (
    <div className={`markdown-content ${className}`}>
      <MDEditor.Markdown 
        source={content} 
        style={{ 
          backgroundColor: 'transparent',
          color: 'inherit',
          fontSize: 'inherit',
          lineHeight: 'inherit'
        }}
      />
    </div>
  );
}
