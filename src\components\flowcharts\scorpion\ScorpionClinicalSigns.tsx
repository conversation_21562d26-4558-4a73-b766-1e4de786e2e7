
import React from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export const ScorpionClinicalSigns = () => {
  return (
    <div className="space-y-4 bg-white/50 rounded-lg p-4 backdrop-blur-sm border border-yellow-100 dark:bg-yellow-900/20 dark:border-yellow-800/30">
      <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-300">
        Sinais de envenenamento escorpiônico incluem:
      </h3>
      
      <div className="space-y-3 text-gray-700 dark:text-gray-200">
        <div>
          <p className="font-medium text-yellow-700 dark:text-yellow-400">Manifestações locais:</p>
          <p>Dor intensa, edema discreto ou ausente, parestesia, sudorese local.</p>
        </div>
        
        <div>
          <p className="font-medium text-yellow-700 dark:text-yellow-400">Manifestações sistêmicas leves:</p>
          <p>Náusea, vômito, sudorese, taquicardia, agitação.</p>
        </div>
        
        <div>
          <p className="font-medium text-yellow-700 dark:text-yellow-400">Manifestações graves:</p>
          <p>Sudorese profusa, vômitos incoercíveis, priapismo, insuficiência cardíaca, edema pulmonar.</p>
        </div>
      </div>

      <Dialog>
        <DialogTrigger asChild>
          <Button 
            variant="outline" 
            className="w-full mt-4 bg-yellow-100 hover:bg-yellow-200 border-yellow-300 text-yellow-800 dark:bg-yellow-900/40 dark:hover:bg-yellow-900/60 dark:border-yellow-700/50 dark:text-yellow-300"
          >
            Saiba Mais
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Detalhamento dos Sinais de Envenenamento Escorpiônico</DialogTitle>
            <DialogDescription className="space-y-4 pt-4">
              <div>
                <h4 className="font-semibold text-yellow-700 dark:text-yellow-400 mb-2">Manifestações Locais</h4>
                <ul className="list-disc pl-5 space-y-1 dark:text-gray-200">
                  <li>Dor local intensa e imediata</li>
                  <li>Edema discreto ou ausente</li>
                  <li>Parestesia local</li>
                  <li>Hiperemia</li>
                  <li>Sudorese local</li>
                  <li>Piloereção</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold text-yellow-700 dark:text-yellow-400 mb-2">Manifestações Sistêmicas Leves a Moderadas</h4>
                <ul className="list-disc pl-5 space-y-1 dark:text-gray-200">
                  <li>Náuseas e vômitos ocasionais</li>
                  <li>Sudorese discreta a moderada</li>
                  <li>Taquicardia</li>
                  <li>Agitação</li>
                  <li>Tremores</li>
                  <li>Alterações leves da pressão arterial</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold text-yellow-700 dark:text-yellow-400 mb-2">Manifestações Sistêmicas Graves</h4>
                <ul className="list-disc pl-5 space-y-1 dark:text-gray-200">
                  <li>Sudorese profusa</li>
                  <li>Vômitos incoercíveis</li>
                  <li>Salivação excessiva</li>
                  <li>Alternância de agitação e sonolência</li>
                  <li>Bradicardia ou taquicardia importantes</li>
                  <li>Insuficiência cardíaca</li>
                  <li>Edema pulmonar agudo</li>
                  <li>Choque</li>
                  <li>Priapismo</li>
                </ul>
              </div>
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </div>
  );
};
