import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://bxedpdmgvgatjdfxgxij.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ4ZWRwZG1ndmdhdGpkZnhneGlqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzIyNzk3MTgsImV4cCI6MjA0Nzg1NTcxOH0.cjoaggOXt1kY9WmVNbAipCOQ2dP4PWLP43KMf8cO8Wo';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testSupabaseConnection() {
  try {
    console.log('🔍 Testando conexão com Supabase...');
    
    // Teste simples: buscar apenas o nome do paracetamol
    const { data, error } = await supabase
      .from('pedbook_medications')
      .select('name, slug, brands')
      .eq('slug', 'paracetamol')
      .limit(1);

    if (error) {
      console.error('❌ Erro na API:', error);
      return false;
    }

    if (data && data.length > 0) {
      console.log('✅ Conexão funcionando!');
      console.log('📋 Dados encontrados:', data[0]);
      return true;
    } else {
      console.log('⚠️ Nenhum dado encontrado');
      return false;
    }
    
  } catch (err) {
    console.error('❌ Erro na conexão:', err.message);
    return false;
  }
}

// Executar teste
testSupabaseConnection().then(success => {
  if (success) {
    console.log('\n🎉 API do Supabase está funcionando perfeitamente!');
  } else {
    console.log('\n❌ Problema com a API do Supabase');
  }
  process.exit(success ? 0 : 1);
});
