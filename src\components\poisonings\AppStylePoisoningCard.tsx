import React from "react";
import { cn } from "@/lib/utils";
import { Pill } from "lucide-react";

interface AppStylePoisoningCardProps {
  name: string;
  color: string;
  onClick?: () => void;
}

const AppStylePoisoningCard: React.FC<AppStylePoisoningCardProps> = ({
  name,
  color,
  onClick,
}) => {
  // Extrair o nome da cor para definir a barra superior
  const colorName = color.split('-')[1];

  // Cor da barra superior
  const getTopBarColorClass = () => {
    if (colorName === 'blue') return "bg-blue-500";
    if (colorName === 'purple') return "bg-purple-500";
    if (colorName === 'emerald') return "bg-emerald-500";
    if (colorName === 'red') return "bg-red-500";
    if (colorName === 'orange') return "bg-orange-500";
    if (colorName === 'indigo') return "bg-indigo-500";
    if (colorName === 'yellow') return "bg-yellow-500";
    if (colorName === 'pink') return "bg-pink-500";
    if (colorName === 'rose') return "bg-rose-500";
    return "bg-primary";
  };

  // Cor do ícone
  const getIconColorClass = () => {
    if (colorName === 'blue') return "text-blue-700 dark:text-blue-300";
    if (colorName === 'purple') return "text-purple-700 dark:text-purple-300";
    if (colorName === 'emerald') return "text-emerald-700 dark:text-emerald-300";
    if (colorName === 'red') return "text-red-700 dark:text-red-300";
    if (colorName === 'orange') return "text-orange-700 dark:text-orange-300";
    if (colorName === 'indigo') return "text-indigo-700 dark:text-indigo-300";
    if (colorName === 'yellow') return "text-yellow-700 dark:text-yellow-300";
    if (colorName === 'pink') return "text-pink-700 dark:text-pink-300";
    if (colorName === 'rose') return "text-rose-700 dark:text-rose-300";
    return "text-primary dark:text-blue-400";
  };

  // Cor de fundo do ícone
  const getIconBgClass = () => {
    if (colorName === 'blue') return "bg-blue-50 dark:bg-blue-900/30";
    if (colorName === 'purple') return "bg-purple-50 dark:bg-purple-900/30";
    if (colorName === 'emerald') return "bg-emerald-50 dark:bg-emerald-900/30";
    if (colorName === 'red') return "bg-red-50 dark:bg-red-900/30";
    if (colorName === 'orange') return "bg-orange-50 dark:bg-orange-900/30";
    if (colorName === 'indigo') return "bg-indigo-50 dark:bg-indigo-900/30";
    if (colorName === 'yellow') return "bg-yellow-50 dark:bg-yellow-900/30";
    if (colorName === 'pink') return "bg-pink-50 dark:bg-pink-900/30";
    if (colorName === 'rose') return "bg-rose-50 dark:bg-rose-900/30";
    return "bg-gray-50 dark:bg-slate-800";
  };

  return (
    <button
      className={cn(
        "relative h-full p-3 sm:p-5 rounded-xl transition-all duration-300 cursor-pointer",
        "bg-white/90 dark:bg-slate-800/90 backdrop-blur-md shadow-md hover:shadow-lg",
        "border border-gray-100 dark:border-gray-700/50",
        "hover:-translate-y-1 text-center"
      )}
      onClick={onClick}
    >
      {/* Barra de cor na parte superior para aparência de app */}
      <div className={cn(
        "absolute top-0 left-0 right-0 h-1.5 rounded-t-xl",
        getTopBarColorClass()
      )} />

      <div className="flex flex-col items-center justify-center h-full pt-2">
        <div className={cn(
          "w-10 h-10 sm:w-12 sm:h-12 rounded-lg flex items-center justify-center mb-2 shadow-sm",
          getIconBgClass()
        )}>
          <Pill className={cn(
            "w-5 h-5 sm:w-6 sm:h-6",
            getIconColorClass()
          )} />
        </div>

        <h3 className="font-bold text-sm sm:text-base text-gray-800 dark:text-gray-200">
          {name}
        </h3>
      </div>
    </button>
  );
};

export default AppStylePoisoningCard;
