import { Card } from "@/components/ui/card";
import { motion } from "framer-motion";

interface PecarnResultProps {
  age: "under2" | "over2";
  hasSevereSymptoms: boolean;
  hasModerateSymptoms: boolean | null;
}

export const PecarnResult = ({
  age,
  hasSevereSymptoms,
  hasModerateSymptoms,
}: PecarnResultProps) => {
  const getResultContent = () => {
    if (hasSevereSymptoms) {
      return {
        title: "TC Craniana Recomendada",
        description: age === "under2"
          ? "13,9% da população tem 4,4% de risco de TCEi."
          : "14% da população tem 4,3% de risco de TCEi.",
        color: "blue",
      };
    }

    if (hasModerateSymptoms) {
      return {
        title: "Risco Moderado - Considerar TC ou Observação",
        description: age === "under2"
          ? "32,6% da população com 0,9% de risco de TCEi."
          : "27,7% da população com 0,9% de risco de TCEi.",
        color: "yellow",
        factors: [
          "Experiência do médico",
          "Achados múltiplos x isolados",
          "Piora dos sinais ou sintomas após observação na unidade de emergência",
          ...(age === "under2" ? ["Idade da criança < 3 meses"] : []),
        ],
      };
    }

    return {
      title: "TC Não Recomendada",
      description: age === "under2"
        ? "O risco de TCEi é < 0,02%"
        : "O risco de TCEi é < 0,05%",
      color: "green",
    };
  };

  const result = getResultContent();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <Card className={`p-6 bg-${result.color}-50 border-${result.color}-200`}>
        <h3 className={`text-xl font-semibold text-${result.color}-800 mb-4`}>
          {result.title}
        </h3>
        <p className={`text-${result.color}-700 mb-4`}>{result.description}</p>

        {result.factors && (
          <div className="mt-6 space-y-4">
            <h4 className="font-semibold text-gray-700">
              Fatores a considerar na decisão:
            </h4>
            <ul className="list-disc list-inside space-y-2 text-gray-600">
              {result.factors.map((factor, index) => (
                <li key={index}>{factor}</li>
              ))}
            </ul>
          </div>
        )}
      </Card>
    </motion.div>
  );
};