import { Json } from '@/integrations/supabase/types/json';

export interface SessionStats {
  total_correct: number;
  total_incorrect: number;
  avg_response_time: string;
  by_specialty: Record<string, { id: string; total: number; correct: number }>;
  by_theme: Record<string, { id: string; total: number; correct: number }>;
  by_focus: Record<string, { id: string; total: number; correct: number }>;
}

export interface StudySession {
  id: string;
  user_id: string;
  stats: SessionStats;
  started_at: string;
  status: string;
  specialty_id: string | null;
  theme_id: string | null;
  focus_id: string | null;
  completed_at: string | null;
  total_questions: number;
  current_question_index: number;
  total_correct: number;
  total_incorrect: number;
  avg_response_time: string;
  questions: string[];
}

export interface RawStudySession {
  id: string;
  user_id: string;
  stats: Json;
  started_at: string;
  status: string;
  specialty_id: string | null;
  theme_id: string | null;
  focus_id: string | null;
  completed_at: string | null;
  total_questions: number;
  current_question_index: number;
  total_correct: number;
  total_incorrect: number;
  avg_response_time: string;
  questions: string[];
}
