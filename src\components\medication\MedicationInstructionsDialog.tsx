
import React, { useEffect } from "react";
import { <PERSON><PERSON>ex<PERSON>, Loader2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface MedicationInstructionsDialogProps {
  medicationId: string;
  medicationName: string;
  slug?: string;
}

export function MedicationInstructionsDialog({
  medicationId,
  medicationName,
  slug,
}: MedicationInstructionsDialogProps) {
  const navigate = useNavigate();
  // Fetch medication instructions data
  const { data: instructions, isLoading } = useQuery({
    queryKey: ["medication-instructions", medicationId],
    queryFn: async () => {
      if (!medicationId) return null;

      const { data, error } = await supabase
        .from("pedbook_medication_instructions")
        .select("*")
        .eq("medication_id", medicationId)
        .eq("is_published", true)
        .maybeSingle();

      if (error) {
        console.log("Error fetching medication instructions:", error);
        // Don't throw error if no instructions found
        if (error.code === "PGRST116") {
          return null;
        }
        return null;
      }

      return data;
    },
    enabled: !!medicationId,
  });

  // Determinar o tipo de formatação
  const formatType = instructions?.format_type === 'simple' ? 'simple' : 'standard';

  useEffect(() => {
    // Set up image click handler for previewing images
    window.handleImageClick = (src: string) => {
      // Futuramente pode ser adicionado um visualizador de imagens
      window.open(src, '_blank');
    };

    return () => {
      // Clean up when component unmounts
      delete window.handleImageClick;
    };
  }, []);

  // Função para decodificar entidades HTML
  const decodeHtmlEntities = (text: string) => {
    const textarea = document.createElement('textarea');
    textarea.innerHTML = text;
    return textarea.value;
  };

  // Função para processar o conteúdo HTML no formato padrão
  const processStandardContent = (content: string) => {
    if (!content) return '';

    // Processa os títulos com formato "##. Título"
    return decodeHtmlEntities(content
      .replace(/<h2><strong>##\.\s*(.*?)<\/strong><\/h2>/g, '<h2 class="text-xl font-bold text-blue-600 dark:text-blue-400 mt-5 mb-2">$1</h2>')
      .replace(/<h3><strong>##\.\s*(.*?)<\/strong><\/h3>/g, '<h3 class="text-lg font-semibold text-blue-500 dark:text-blue-300 mt-3 mb-1">$1</h3>')
      // Processa os subtítulos com formato "##; Subtítulo"
      .replace(/<strong>##;\s*(.*?)<\/strong>/g, '<h4 class="text-base font-semibold text-blue-500 dark:text-blue-300 mt-2 mb-1">$1</h4>')
      .replace(/<h4><strong>(.*?)<\/strong><\/h4>/g, '<h4 class="text-base font-semibold text-blue-500 dark:text-blue-300 mt-2 mb-1">$1</h4>')
      .replace(/<strong>(.*?)<\/strong>/g, '<span class="font-semibold">$1</span>')
      .replace(/<p>/g, '<p class="mb-2">')
      .replace(/<ul>/g, '<ul class="list-disc pl-5 mb-2 space-y-1">')
      .replace(/<li><p>/g, '<li class="mb-1">')
      .replace(/<a([^>]*)target="_blank"([^>]*)>/g, '<a$1$2>')
      .replace(/<a([^>]*)rel="noopener noreferrer"([^>]*)>/g, '<a$1$2>')
      .replace(/<a([^>]*)href="([^"]*)"([^>]*)>/g, '<a$1href="$2"$3 target="_self">')
      .replace(/<img([^>]*)>/g, '<img$1 class="max-w-full h-auto rounded-lg my-3 cursor-pointer hover:opacity-90 transition-opacity" onclick="window.handleImageClick && window.handleImageClick(this.src)">')
    );
  };

  // Função para processar o conteúdo HTML no formato simples
  const processSimpleContent = (content: string) => {
    if (!content) return '';


    // Primeiro, vamos extrair os marcadores ##. e ##; mesmo quando estão dentro de tags HTML
    // Procurar por ##. em qualquer lugar do HTML, incluindo dentro de tags
    let processedContent = content.replace(
      /(<[^>]*>)*##\.\s*(.*?)(<\/[^>]*>)*/g,
      (match, openTag, title) => {
        // Remover tags HTML do título
        const cleanTitle = title.replace(/<\/?[^>]+(>|$)/g, "");
        return `<h2 class="text-xl font-bold text-blue-600 dark:text-blue-400 mt-5 mb-2">${cleanTitle}</h2>`;
      }
    );

    // Remover qualquer ocorrência restante de ##. e seu texto até a próxima quebra de linha
    processedContent = processedContent.replace(/##\.\s*[^<\n\r]*(?:<br>|<\/p>|$)/g, '');

    // Procurar por ##; em qualquer lugar do HTML, incluindo dentro de tags
    processedContent = processedContent.replace(
      /(<[^>]*>)*##;\s*(.*?)(<\/[^>]*>)*/g,
      (match, openTag, subtitle) => {
        // Remover tags HTML do subtítulo
        const cleanSubtitle = subtitle.replace(/<\/?[^>]+(>|$)/g, "");
        return `<h4 class="text-base font-semibold text-blue-500 dark:text-blue-300 mt-2 mb-1">${cleanSubtitle}</h4>`;
      }
    );

    // Remover qualquer ocorrência restante de ##; e seu texto até a próxima quebra de linha
    processedContent = processedContent.replace(/##;\s*[^<\n\r]*(?:<br>|<\/p>|$)/g, '');

    // Melhorar a formatação de listas
    // Primeiro, vamos garantir que as listas estejam corretamente formatadas

    // Garantir que as tags <ul> tenham a classe correta
    processedContent = processedContent
      .replace(/<ul>/g, '<ul class="list-disc pl-5 mb-2 space-y-1">');

    // Garantir que as tags <li> tenham a classe correta
    processedContent = processedContent
      .replace(/<li>/g, '<li class="mb-1">');

    // Corrigir quebras de linha extras dentro de listas
    // Remover <br> extras entre itens de lista
    processedContent = processedContent.replace(/<\/li><br><li/g, '</li><li');

    // Remover <br> no início de itens de lista
    processedContent = processedContent.replace(/<li class="mb-1"><br>/g, '<li class="mb-1">');

    // Remover <br> extras no final de itens de lista
    processedContent = processedContent.replace(/<br><\/li>/g, '</li>');

    // Preservar quebras de linha duplas (espaçamento entre parágrafos)
    processedContent = processedContent.replace(/<br><br>/g, '</p><p class="mb-4">');

    // Preservar quebras de linha triplas (espaçamento maior entre parágrafos)
    processedContent = processedContent.replace(/<br><br><br>/g, '</p><p class="mb-6">');

    // Remover <br> extras após tags de fechamento
    processedContent = processedContent.replace(/<\/ul><br>/g, '</ul>');
    processedContent = processedContent.replace(/<\/p><br>/g, '</p>');

    // Corrigir formatação de listas aninhadas
    processedContent = processedContent.replace(/<\/li><\/ul><li/g, '</li></ul><li');

    // Tratar quebras de linha dentro de itens de lista de forma especial
    // Quando há duas quebras de linha dentro de um item de lista, transformar o conteúdo após as quebras em um parágrafo separado
    processedContent = processedContent.replace(/<li class="mb-1">(.*?)<br><br>(.*?)<\/li>/g, '<li class="mb-1">$1</li></ul><p class="my-4">$2</p><ul class="list-disc pl-5 mb-2 space-y-1">');

    // Para quebras de linha simples dentro de itens de lista, adicionar espaçamento vertical
    processedContent = processedContent.replace(/<li class="mb-1">(.*?)<br>(?!<br>)(.*?)<\/li>/g, '<li class="mb-1">$1<div class="mt-3">$2</div></li>');

    // Corrigir possíveis problemas com tags aninhadas incorretamente
    processedContent = processedContent.replace(/<\/ul><p class="my-4">(.*?)<\/p><ul class="list-disc pl-5 mb-2 space-y-1"><\/li>/g, '</ul><p class="my-4">$1</p><ul class="list-disc pl-5 mb-2 space-y-1">');

    // Garantir que não haja <br> extras no final do conteúdo
    processedContent = processedContent.replace(/<br>$/g, '');

    // Agora aplicamos as formatações básicas para elementos HTML comuns
    processedContent = decodeHtmlEntities(processedContent
      .replace(/<strong>(.*?)<\/strong>/g, '<span class="font-semibold">$1</span>')
      .replace(/<p>/g, '<p class="mb-2">')
      .replace(/<li><p>/g, '<li class="mb-1">')
      .replace(/<a([^>]*)target="_blank"([^>]*)>/g, '<a$1$2>')
      .replace(/<a([^>]*)rel="noopener noreferrer"([^>]*)>/g, '<a$1$2>')
      .replace(/<a([^>]*)href="([^"]*)"([^>]*)>/g, '<a$1href="$2"$3 target="_self">')
      .replace(/<img([^>]*)>/g, '<img$1 class="max-w-full h-auto rounded-lg my-3 cursor-pointer hover:opacity-90 transition-opacity" onclick="window.handleImageClick && window.handleImageClick(this.src)">')
    );

    return processedContent;
  };

  // Função para processar o conteúdo HTML
  const processContent = (content: string) => {
    if (!content) return '';

    return formatType === 'simple'
      ? processSimpleContent(content)
      : processStandardContent(content);
  };

  // Função para extrair seções e subseções do conteúdo
  const extractSectionsFromContent = (content: string) => {
    if (!content) return [];


    const sections = [];

    // Para o formato padrão, usamos o regex original
    if (formatType !== 'simple') {
      const standardMainSectionRegex = /(?:<h[2-4]><strong>##\.\s*(.*?)<\/strong><\/h[2-4]>|<h2>##\.\s*(.*?)<\/h2>|<h2><strong>##\.\s*<\/strong>(.*?)<\/h2>)([\s\S]*?)(?=<h[2-4]><strong>##\.|<h2>##\.|$)/g;

      const mainSections = Array.from(content.matchAll(standardMainSectionRegex));

      if (mainSections.length === 0) {
        // Se não houver seções formatadas, tratar como uma única seção
        return [{
          title: "Informações Gerais",
          content: content,
          subsections: []
        }];
      }

      // Processar seções no formato padrão
      mainSections.forEach((sectionMatch, index) => {
        const title = (sectionMatch[1] || sectionMatch[2] || sectionMatch[3] || "").trim();
        let mainContent = sectionMatch[4] || "";

        // Extrair subsections (com formato ##;)
        const subsections = [];


        // Regex para encontrar subseções no formato padrão
        const standardSubsectionRegex = /<strong>##;\s*(.*?)<\/strong>([\s\S]*?)(?=<strong>##;|<h[2-4]><strong>##\.|$)/g;

        const subsectionMatches = Array.from(mainContent.matchAll(standardSubsectionRegex));

        // Processar cada subseção encontrada
        if (subsectionMatches.length > 0) {
          // Se houver subseções, limpar o conteúdo principal para não duplicar
          const firstSubsectionStart = mainContent.indexOf(subsectionMatches[0][0]);
          if (firstSubsectionStart !== -1) {
            mainContent = mainContent.substring(0, firstSubsectionStart).trim();
          }

          subsectionMatches.forEach((subMatch) => {
            const subTitle = subMatch[1].trim();
            const subContent = subMatch[2].trim();

            subsections.push({
              title: subTitle,
              content: subContent
            });
          });
        }

        sections.push({
          title,
          content: mainContent,
          subsections
        });
      });

      return sections;
    }


    // Primeiro, vamos dividir o conteúdo em linhas
    const lines = content.split(/(?:<br>|<\/p>|<p>)/);

    // Vamos processar linha por linha para identificar títulos e subtítulos
    let currentSectionTitle = '';
    let currentSubsectionTitle = '';
    let currentSectionContent = '';
    let currentSubsectionContent = '';
    let currentSectionSubsections: { title: string; content: string }[] = [];

    // Variável para controlar linhas vazias consecutivas
    let emptyLineCount = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Tratar linhas vazias de forma especial
      if (!line) {
        emptyLineCount++;

        // Adicionar quebra de linha para cada linha vazia (até um máximo de 2)
        if (emptyLineCount <= 2) {
          if (currentSubsectionTitle) {
            currentSubsectionContent += '<br>';
          } else if (currentSectionTitle) {
            currentSectionContent += '<br>';
          }
        }

        continue;
      }

      // Resetar contador de linhas vazias quando encontrar uma linha não vazia
      emptyLineCount = 0;


      // Verificar se a linha contém um título (##.)
      let titleMatch = line.match(/(?:<[^>]*>)*##\.\s*(.*?)(?:<\/[^>]*>)*/);

      // Se não encontrou no formato padrão, tenta encontrar no formato simples (##. seguido de texto)
      if (!titleMatch || !titleMatch[1]) {
        titleMatch = line.match(/##\.\s*(.*?)$/);
      }

      if (titleMatch && titleMatch[1]) {
        // Se já temos uma seção atual, finalizá-la antes de começar uma nova
        if (currentSectionTitle) {
          // Se temos uma subseção atual, finalizá-la antes de finalizar a seção
          if (currentSubsectionTitle) {
            currentSectionSubsections.push({
              title: currentSubsectionTitle,
              content: currentSubsectionContent.trim()
            });
            currentSubsectionTitle = '';
            currentSubsectionContent = '';
          }

          // Finalizar a seção atual
          sections.push({
            title: currentSectionTitle,
            content: currentSectionContent.trim(),
            subsections: currentSectionSubsections
          });

          // Resetar para a próxima seção
          currentSectionSubsections = [];
        }

        // Extrair o título (removendo tags HTML)
        const titleWithTags = titleMatch[1] || '';
        const title = titleWithTags.replace(/<\/?[^>]+(>|$)/g, "").trim();

        // Iniciar uma nova seção
        currentSectionTitle = title;
        currentSectionContent = '';
        continue; // Pular para a próxima linha
      }

      // Verificar se a linha contém um subtítulo (##;)
      let subtitleMatch = line.match(/(?:<[^>]*>)*##;\s*(.*?)(?:<\/[^>]*>)*/);

      // Se não encontrou no formato padrão, tenta encontrar no formato simples (##; seguido de texto)
      if (!subtitleMatch || !subtitleMatch[1]) {
        subtitleMatch = line.match(/##;\s*(.*?)$/);
      }

      if (subtitleMatch && subtitleMatch[1] && currentSectionTitle) {
        // Se já temos uma subseção atual, finalizá-la antes de começar uma nova
        if (currentSubsectionTitle) {
          currentSectionSubsections.push({
            title: currentSubsectionTitle,
            content: currentSubsectionContent.trim()
          });
          currentSubsectionContent = '';
        }

        // Extrair o subtítulo (removendo tags HTML)
        const subtitleWithTags = subtitleMatch[1] || '';
        const subtitle = subtitleWithTags.replace(/<\/?[^>]+(>|$)/g, "").trim();

        // Iniciar uma nova subseção
        currentSubsectionTitle = subtitle;
        currentSubsectionContent = '';
        continue; // Pular para a próxima linha
      }

      // Se não é título nem subtítulo, adicionar à seção ou subseção atual
      // Preservar a estrutura HTML original, especialmente para listas
      if (currentSubsectionTitle) {
        // Adicionar quebra de linha apenas se não for uma tag HTML de fechamento
        if (line.match(/<\/[^>]+>$/)) {
          currentSubsectionContent += line;
        } else {
          currentSubsectionContent += line + '<br>';
        }
      } else if (currentSectionTitle) {
        // Adicionar quebra de linha apenas se não for uma tag HTML de fechamento
        if (line.match(/<\/[^>]+>$/)) {
          currentSectionContent += line;
        } else {
          currentSectionContent += line + '<br>';
        }
      }
    }

    // Finalizar a última seção e subseção, se houver
    if (currentSectionTitle) {
      // Se temos uma subseção atual, finalizá-la antes de finalizar a seção
      if (currentSubsectionTitle) {
        currentSectionSubsections.push({
          title: currentSubsectionTitle,
          content: currentSubsectionContent.trim()
        });
      }

      // Finalizar a seção atual
      sections.push({
        title: currentSectionTitle,
        content: currentSectionContent.trim(),
        subsections: currentSectionSubsections
      });
    }

    // Se não houver seções, retornar todo o conteúdo como uma única seção
    if (sections.length === 0) {
      return [{
        title: "Informações Gerais",
        content: content,
        subsections: []
      }];
    }


    return sections;
  };

  // Don't show the button if no published instructions exist and not loading
  if (!instructions && !isLoading) {
    return null;
  }

  // Handle redirection to the bula page
  const handleRedirectToBula = () => {
    if (slug) {
      navigate(`/bulas-profissionais/${slug}`);
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="gap-1 sm:gap-2 border-primary/20 bg-white dark:bg-slate-700 shadow-sm hover:bg-primary/5 dark:hover:bg-primary/10 dark:text-white text-xs sm:text-sm px-2 sm:px-3 flex-shrink-0"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleRedirectToBula();
          }}
        >
          <FileText className="h-3 w-3 sm:h-4 sm:w-4 text-primary" />
          <span className="whitespace-nowrap">Bula Completa</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-3xl max-h-[85vh] overflow-y-auto p-4 md:p-6 dark:bg-slate-800">
        <DialogHeader className="mb-2">
          <DialogTitle className="text-xl font-bold text-primary dark:text-white">
            Bula: {medicationName}
          </DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-6">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
        ) : instructions?.content ? (
          <div className="space-y-2">
            <Accordion type="multiple" className="space-y-2">
              {extractSectionsFromContent(instructions.content).map((section, index) => (
                <AccordionItem
                  key={index}
                  value={`section-${index}`}
                  className="border border-blue-100 dark:border-blue-900 rounded-lg overflow-hidden"
                >
                  <AccordionTrigger className="px-3 py-2 hover:bg-blue-50/50 dark:hover:bg-blue-900/30 transition-colors">
                    <div className="flex items-center gap-2 text-left">
                      <span className="flex-shrink-0 w-5 h-5 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-800 text-blue-600 dark:text-blue-300 text-xs font-medium">
                        {index + 1}
                      </span>
                      <h2 className="text-sm font-medium text-blue-800 dark:text-blue-300">
                        {section.title}
                      </h2>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-3 pt-1 pb-3 bg-blue-50/30 dark:bg-blue-900/20">
                    {section.content ? (
                      <div className="prose prose-sm max-w-none prose-blue dark:prose-invert prose-headings:text-blue-700 dark:prose-headings:text-blue-400 mb-4">
                        <div
                          dangerouslySetInnerHTML={{
                            __html: processContent(section.content)
                          }}
                        />
                      </div>
                    ) : null}

                    {section.subsections.length > 0 && (
                      <Accordion type="multiple" className="space-y-2 mt-2">
                        {section.subsections.map((subsection, subIndex) => (
                          <AccordionItem
                            key={subIndex}
                            value={`subsection-${index}-${subIndex}`}
                            className="border border-blue-100 dark:border-slate-700 rounded-lg overflow-hidden"
                          >
                            <AccordionTrigger className="px-4 py-2 hover:bg-blue-50/50 dark:hover:bg-blue-900/20 transition-colors">
                              <div className="flex items-center gap-2 text-left">
                                <span className="flex-shrink-0 w-5 h-5 flex items-center justify-center rounded-full bg-blue-50 dark:bg-blue-900/30 text-blue-500 dark:text-blue-300 text-xs">
                                  {index + 1}.{subIndex + 1}
                                </span>
                                <h3 className="text-sm font-medium text-blue-700 dark:text-blue-300">
                                  {subsection.title}
                                </h3>
                              </div>
                            </AccordionTrigger>
                            <AccordionContent className="px-4 pt-2 pb-3 bg-blue-50/30 dark:bg-blue-900/10">
                              <div className="prose prose-sm max-w-none prose-blue dark:prose-invert prose-headings:text-blue-700 dark:prose-headings:text-blue-300">
                                <div
                                  dangerouslySetInnerHTML={{
                                    __html: processContent(subsection.content)
                                  }}
                                />
                              </div>
                            </AccordionContent>
                          </AccordionItem>
                        ))}
                      </Accordion>
                    )}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        ) : (
          <div className="text-center p-6 text-muted-foreground dark:text-gray-400">
            <p>Nenhuma informação disponível para este medicamento.</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
