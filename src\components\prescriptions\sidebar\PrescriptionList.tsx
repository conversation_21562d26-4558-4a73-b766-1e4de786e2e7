import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card } from "@/components/ui/card";

interface PrescriptionListProps {
  categories: any[];
  uncategorizedPrescriptions: any[];
  selectedPrescription: string | null;
  searchTerm: string;
  onPrescriptionSelect: (id: string) => void;
}

export const PrescriptionList = ({
  categories,
  uncategorizedPrescriptions,
  selectedPrescription,
  searchTerm,
  onPrescriptionSelect,
}: PrescriptionListProps) => {
  const filteredCategories = categories?.filter((category) =>
    category.pedbook_prescriptions?.some((prescription: any) =>
      prescription.name.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  const filteredUncategorized = uncategorizedPrescriptions?.filter((prescription) =>
    prescription.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Card className="p-2">
      <ScrollArea className="h-[300px]">
        <div className="space-y-4 pr-4">
          {filteredCategories?.map((category) => (
            <div key={category.id} className="space-y-2">
              <h3 className="font-semibold text-sm text-gray-500 uppercase tracking-wider">
                {category.name}
              </h3>
              <div className="space-y-1">
                {category.pedbook_prescriptions?.filter((prescription: any) =>
                  prescription.name
                    .toLowerCase()
                    .includes(searchTerm.toLowerCase())
                ).map((prescription: any) => (
                  <Button
                    key={prescription.id}
                    variant={
                      selectedPrescription === prescription.id
                        ? "default"
                        : "ghost"
                    }
                    className={`w-full justify-start text-left transition-all duration-200 ${
                      selectedPrescription === prescription.id
                        ? "bg-primary text-white shadow-md"
                        : "hover:bg-primary/5"
                    }`}
                    onClick={() => onPrescriptionSelect(prescription.id)}
                  >
                    <span className="font-medium line-clamp-1">
                      {prescription.name}
                    </span>
                  </Button>
                ))}
              </div>
            </div>
          ))}

          {filteredUncategorized && filteredUncategorized.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-semibold text-sm text-gray-500 uppercase tracking-wider">
                Sem categoria
              </h3>
              <div className="space-y-1">
                {filteredUncategorized.map((prescription) => (
                  <Button
                    key={prescription.id}
                    variant={
                      selectedPrescription === prescription.id
                        ? "default"
                        : "ghost"
                    }
                    className={`w-full justify-start text-left transition-all duration-200 ${
                      selectedPrescription === prescription.id
                        ? "bg-primary text-white shadow-md"
                        : "hover:bg-primary/5"
                    }`}
                    onClick={() => onPrescriptionSelect(prescription.id)}
                  >
                    <span className="font-medium line-clamp-1">
                      {prescription.name}
                    </span>
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>
      </ScrollArea>
    </Card>
  );
};