import { useEffect, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';

// Variáveis globais para compartilhar o estado entre instâncias do hook
const isProtectionActive = { current: false };
let originalFetch: typeof window.fetch | null = null;
let originalXHROpen: any = null;
let originalXHRSend: any = null;
let originalGetSession: any = null;
let originalGetUser: any = null;
let cachedSession: any = null;
let cachedUser: any = null;
let hookInitialized = false;

/**
 * Hook que impede a remontagem de componentes quando a tab ganha foco novamente.
 * Uma solução simples e direta para o problema de perda de estado ao alternar entre tabs.
 *
 * Esta versão é global e afeta todas as partes da aplicação.
 */
export function useTabFocusProtection(debug = true) {
  useEffect(() => {
    // Evitar inicialização múltipla
    if (hookInitialized) {
      return;
    }

    hookInitialized = true;

    // Armazenar os métodos originais
    originalFetch = window.fetch;
    originalXHROpen = XMLHttpRequest.prototype.open;
    originalXHRSend = XMLHttpRequest.prototype.send;
    originalGetSession = supabase.auth.getSession;
    originalGetUser = supabase.auth.getUser;

    // Armazenar a sessão e o usuário atual
    originalGetSession.call(supabase.auth).then((result: any) => {
      cachedSession = result;
    });

    originalGetUser.call(supabase.auth).then((result: any) => {
      cachedUser = result;
    });

    // Função para lidar com mudanças de visibilidade
    const handleVisibilityChange = () => {
      // Se a tab estiver voltando a ficar visível
      if (document.visibilityState === 'visible') {

        // Ativar flag para bloquear verificações
        isProtectionActive.current = true;

        // Substituir o fetch global para bloquear requisições
        window.fetch = function(input, init) {
          const url = typeof input === 'string'
            ? input
            : input instanceof URL
              ? input.toString()
              : input instanceof Request
                ? input.url
                : '';

          // Bloquear requisições que podem causar remontagem
          if (isProtectionActive.current &&
              (url.includes('auth') ||
               url.includes('admin') ||
               url.includes('breastfeeding') ||
               url.includes('medications'))) {


            // Retornar uma resposta vazia para evitar erros
            return Promise.resolve(new Response('{}', {
              status: 200,
              headers: { 'Content-Type': 'application/json' }
            }));
          }

          // Para outras requisições, usar o fetch original
          return originalFetch!(input, init);
        };

        // Substituir XMLHttpRequest para bloquear requisições
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
          if (isProtectionActive.current &&
              typeof url === 'string' &&
              (url.includes('auth') ||
               url.includes('admin') ||
               url.includes('breastfeeding') ||
               url.includes('medications'))) {

            // Não abrir a requisição
            return;
          }
          return originalXHROpen.apply(this, [method, url, ...args]);
        };

        // Substituir os métodos de autenticação do Supabase
        supabase.auth.getSession = async function() {
          if (isProtectionActive.current) {
            return cachedSession || { data: { session: null }, error: null };
          }
          return originalGetSession.apply(this);
        };

        supabase.auth.getUser = async function() {
          if (isProtectionActive.current) {
            return cachedUser || { data: { user: null }, error: null };
          }
          return originalGetUser.apply(this);
        };

        // Após 5 segundos, restaurar o comportamento normal
        setTimeout(() => {

          isProtectionActive.current = false;

          // Restaurar os métodos originais
          if (originalFetch) {
            window.fetch = originalFetch;
          }

          if (originalXHROpen) {
            XMLHttpRequest.prototype.open = originalXHROpen;
          }

          if (originalGetSession) {
            supabase.auth.getSession = originalGetSession;
          }

          if (originalGetUser) {
            supabase.auth.getUser = originalGetUser;
          }
        }, 5000);
      }
    };

    // Adicionar listener para mudanças de visibilidade
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup
    return () => {
      // Não remover o listener para manter a proteção global
      // document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [debug]);

  return {
    isProtectionActive: () => isProtectionActive.current
  };
}
