
import React from 'react';
import { Button } from "@/components/ui/button";

interface QuickActionButtonProps {
  icon: React.ReactNode;
  label: string;
  color: string;
  onClick: () => void;
}

export const QuickActionButton: React.FC<QuickActionButtonProps> = ({
  icon,
  label,
  color,
  onClick
}) => {
  const [isClicked, setIsClicked] = React.useState(false);

  const handleClick = () => {
    setIsClicked(true);

    // Pequeno atraso para mostrar o feedback visual antes de chamar onClick
    setTimeout(() => {
      // Call the original onClick
      onClick();

      // Manter o estado de clicado por um tempo para feedback visual
      setTimeout(() => {
        setIsClicked(false);
      }, 300);
    }, 100);
  };
  // Map color names to light/dark mode compatible gradients and hover states
  const colorMap: Record<string, { bg: string, hover: string, border: string, iconColor: string, textColor: string }> = {
    'purple': {
      bg: 'bg-gradient-to-r from-purple-100 to-purple-50 dark:from-purple-900/80 dark:to-purple-800/50',
      hover: 'hover:from-purple-200 hover:to-purple-100 dark:hover:from-purple-900/90 dark:hover:to-purple-800/70',
      border: 'border-purple-300/50 dark:border-purple-700/50',
      iconColor: 'text-purple-600 dark:text-purple-400',
      textColor: 'text-purple-900 dark:text-white'
    },
    'brown': {
      bg: 'bg-gradient-to-r from-amber-100 to-amber-50 dark:from-amber-800/70 dark:to-amber-700/60',
      hover: 'hover:from-amber-200 hover:to-amber-100 dark:hover:from-amber-800/80 dark:hover:to-amber-700/70',
      border: 'border-amber-300/50 dark:border-amber-700/50',
      iconColor: 'text-amber-600 dark:text-amber-400',
      textColor: 'text-amber-900 dark:text-white'
    },
    'teal': {
      bg: 'bg-gradient-to-r from-emerald-100 to-emerald-50 dark:from-emerald-800/60 dark:to-emerald-700/50',
      hover: 'hover:from-emerald-200 hover:to-emerald-100 dark:hover:from-emerald-800/70 dark:hover:to-emerald-700/60',
      border: 'border-emerald-300/50 dark:border-emerald-700/50',
      iconColor: 'text-emerald-600 dark:text-emerald-400',
      textColor: 'text-emerald-900 dark:text-white'
    },
    'blue': {
      bg: 'bg-gradient-to-r from-blue-100 to-blue-50 dark:from-blue-800/70 dark:to-blue-700/60',
      hover: 'hover:from-blue-200 hover:to-blue-100 dark:hover:from-blue-800/80 dark:hover:to-blue-700/70',
      border: 'border-blue-300/50 dark:border-blue-700/50',
      iconColor: 'text-blue-600 dark:text-blue-400',
      textColor: 'text-blue-900 dark:text-white'
    },
    // Legacy color mapping for backward compatibility
    'accent-pink': {
      bg: 'bg-gradient-to-r from-purple-100 to-purple-50 dark:from-purple-900/80 dark:to-purple-800/50',
      hover: 'hover:from-purple-200 hover:to-purple-100 dark:hover:from-purple-900/90 dark:hover:to-purple-800/70',
      border: 'border-purple-300/50 dark:border-purple-700/50',
      iconColor: 'text-purple-600 dark:text-purple-400',
      textColor: 'text-purple-900 dark:text-white'
    },
    'accent-yellow': {
      bg: 'bg-gradient-to-r from-amber-100 to-amber-50 dark:from-amber-800/70 dark:to-amber-700/60',
      hover: 'hover:from-amber-200 hover:to-amber-100 dark:hover:from-amber-800/80 dark:hover:to-amber-700/70',
      border: 'border-amber-300/50 dark:border-amber-700/50',
      iconColor: 'text-amber-600 dark:text-amber-400',
      textColor: 'text-amber-900 dark:text-white'
    },
    'accent-green': {
      bg: 'bg-gradient-to-r from-emerald-100 to-emerald-50 dark:from-emerald-800/60 dark:to-emerald-700/50',
      hover: 'hover:from-emerald-200 hover:to-emerald-100 dark:hover:from-emerald-800/70 dark:hover:to-emerald-700/60',
      border: 'border-emerald-300/50 dark:border-emerald-700/50',
      iconColor: 'text-emerald-600 dark:text-emerald-400',
      textColor: 'text-emerald-900 dark:text-white'
    },
  };

  const colorStyle = colorMap[color] || {
    bg: 'bg-gradient-to-r from-gray-100 to-gray-50 dark:from-slate-800/80 dark:to-slate-700/70',
    hover: 'hover:from-gray-200 hover:to-gray-100 dark:hover:from-slate-800/90 dark:hover:to-slate-700/80',
    border: 'border-gray-300/50 dark:border-gray-700/50',
    iconColor: 'text-gray-600 dark:text-gray-400',
    textColor: 'text-gray-900 dark:text-white'
  };

  return (
    <Button
      variant="outline"
      className={`flex items-center justify-start gap-3 w-full h-auto py-4 px-5
        border ${colorStyle.border} rounded-xl
        ${colorStyle.bg}
        transition-all duration-300 shadow-sm
        backdrop-blur-sm group
        ${isClicked ? 'opacity-80' : ''}`}
      onClick={handleClick}
      disabled={isClicked}
    >
      <span className={`${colorStyle.iconColor}`}>{icon}</span>
      <span className={`${colorStyle.textColor} font-medium`}>{label}</span>
      {isClicked && (
        <span className="ml-auto">
          <div className="w-4 h-4 border-2 border-t-transparent border-primary rounded-full animate-spin"></div>
        </span>
      )}
    </Button>
  );
};
